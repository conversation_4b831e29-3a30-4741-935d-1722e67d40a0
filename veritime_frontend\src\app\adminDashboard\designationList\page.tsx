"use client";
import { useEffect, useState } from "react";
import { Container, <PERSON>, Col, Button } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter.js";
import { useTheme } from "@material-ui/core/styles";
import Popover from "@mui/material/Popover";
import { styled } from "@mui/system";
import DeleteIcon from "@mui/icons-material/Delete";
import { TabPanel } from "../../../common-components/utils.js";
import Layout from "@/components/Layout";
import { API_URLS } from "@/constants/apiConstants.js";
import {
  deleteRequest,
  getRequest,
  postRequest,
} from "../../../services/apiService.js";
import { appConstants } from "@/constants/appConstants.js";
import { addIdKeyToData } from "@/services/utils.js";
import CreateDesignationModal from "../modals/CreateDesignationModal";
import DesignationDetailModal from "../modals/DesignationDetailModal";
import VisibilityIcon from "@mui/icons-material/Visibility";

import EditIcon from "@mui/icons-material/Edit";
import useLocalStorage from "@/services/localstorage";
import { showSuccessAlert2 } from "@/services/alertService.js";

const designationList = ({ toggleMenu, expanded }: any) => {
  // const [username, setusername] = useState(localStorage.getItem(appConstants?.username))

  const [username, setUsername] = useState("");
  const [isMounted, setIsMounted] = useState(false);
  const [role, setRole] = useLocalStorage("role", "");
  useEffect(() => {
    setIsMounted(true);
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem(appConstants?.username);
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }

    if (typeof window !== "undefined") {
      const storedRole = localStorage.getItem(appConstants?.role);
      if (storedRole) {
        setRole(storedRole);
      }
    }

    fetachdeptNames();
    //fetachBranchNames();
    fetchItems();
  }, []);

  const [openDetailModal, setOpenDetailModal] = useState(false);
  const [selectedDesignation, setSelectedDesignation] = useState<any>(null);

  const handleShowDetails = (row: any) => {
    setSelectedDesignation(row);
    setOpenDetailModal(true);
  };

  const handleCloseDetailModal = () => {
    setOpenDetailModal(false);
    setSelectedDesignation(null);
  };

  const theme = useTheme();
  const [value, setValue] = useState(0);
  const [rowToEdit, setRowToEdit] = useState<any>({});

  const [anchorEl, setAnchorEl] = useState(null);
  const [data, setData] = useState<string[]>([]);
  const [rows, setRows] = useState<string[]>([]);
  const [branchNames, setBranchNames] = useState<string[]>([]);

  const [deptNames, setdeptNames] = useState<string[]>([]);

  const [selectedDropdownData, setselectedDropdownData] = useState<any>({
    level: "",
    department: "",
    role: "",
  });

  const fetachBranchNames = async () => {
    try {
      //const branchResponse = await getRequest(`${API_URLS.GET_BRANCH}`);
      const payload = {
        // deptId:'',
        // branchId:'',
        // isActive:1,
        // offset:0,
        // limit:10
        type: "select",
      };

      const branchResponse = await postRequest(API_URLS.GET_BRANCH, payload);
      if (branchResponse && typeof branchResponse === "object") {
        const branchNamesArray = Object.values(branchResponse);
        setBranchNames(
          branchNamesArray.map((branch: any) => branch.branchName)
        );
      } else {
        // console.error("Invalid branch response:", branchResponse);
      }
    } catch (error) {
      // console.error("Error fetching branch names:", error);
    }
  };

  const fetachdeptNames = async () => {
    try {
      const departmentResponse = await getRequest(`${API_URLS.GET_DEPARTMENT}`);
      if (departmentResponse && departmentResponse.length > 0) {
        // const deptNamesArray = departmentResponse.map((department: any) => department.departmentName);
        setdeptNames(departmentResponse);
      }
      // if (departmentResponse && typeof departmentResponse === 'object') {
      //   const deptNamesArray = Object.values(departmentResponse);
      //   setdeptNames(deptNamesArray.map((department:any) => department.departmentName));
      // } else {
      //   console.error('Invalid department response:', departmentResponse);
      // }
    } catch (error) {
      // console.error("Error fetching department names:", error);
    }
  };

  const fetchItems = async () => {
    try {
      const data: any = await getRequest(`${API_URLS.DESIGNATION_LIST}`);

      const dataWithDefaultLevel = data.map((item: any) => ({
        ...item,
        level: "Level 1",
      }));

      setData(addIdKeyToData(dataWithDefaultLevel));
      setRows(addIdKeyToData(dataWithDefaultLevel));
    } catch (error) {
      // console.error("Error fetching items:", error);
    }
  };
  // const deleteRow = async (id: any) => {
  //   try {

  //     const response = await deleteRequest(`${API_URLS.DESIGNATION_DELETE}/${id}`);

  //     if (response) {
  //       setData(data.filter((el: any) => el.id !== id));
  //       setRows(rows.filter((el: any) => el.id !== id));
  //       showSuccessAlert2("Designation deleted successfully!");
  //     }
  //   } catch (error) {
  //     console.error('Error deleting designation:', error);
  //   }
  // };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const handleChange = (event: any, newValue: any) => {
    setValue(newValue);
  };

  const handleChangeIndex = (index: any) => {
    setValue(index);
  };

  const handleClick = (event: any) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleEdit = (row: any) => {
    setOpenModal(true);
    setRowToEdit(row);
  };
  const handleCloseModal = () => {
    setOpenModal(false);
    setRowToEdit(null);
  };
  const [openModal, setOpenModal] = useState(false);
  const handleOpen = () => {
    setRowToEdit(null);
    setOpenModal(true);
  };

  // const handleModalClose = () => setOpenModal(false);
  const columns = [
    { field: "srno", headerName: "Sr No", width: 150 },
    {
      field: "departmentName",
      headerName: "Department",
      width: 250,
    },
    {
      field: "name",
      headerName: "Designation",
      width: 350,
    },
    // {
    //   field: "level",
    //   headerName: "Level",
    //   width: 120,
    // },
    {
      field: "role",
      headerName: "Role",
      width: 150,
    },
    {
      field: "code",
      headerName: "Code",
      width: 100,
    },
    ...(role !== "admin" && role !== "superadmin"
      ? [
          {
            field: "action",
            headerName: "Action",
            width: 100,
            renderCell: (params: any) => {
              return (
                <div>
                  {/* Eye Icon to open modal for viewing details */}
                  <VisibilityIcon
                    style={{ cursor: "pointer", marginRight: "10px" }}
                    onClick={() => handleShowDetails(params.row)}
                  />

                  <EditIcon
                    style={{ cursor: "pointer" }}
                    onClick={() => handleEdit(params.row)}
                  />
                  {/* <button
                    className="deny-button"
                    style={{
                      background: "transparent",
                      border: "none",
                      cursor: "pointer",
                    }}
                    onClick={() => deleteRow(params.id)}
                  >
                    <DeleteIcon />
                  </button> */}
                </div>
              );
            },
          },
        ]
      : []),
  ];

  // const deleteRow = async (id: any) => {
  // try {
  //     const data = deleteData('departments/'+id);
  //     console.log(data,"data")
  // } catch (error) {
  //     console.error('Error fetching items:', error);
  // }
  //   setData(data.filter((el: any) => el.id !== id));
  // };

  const handleChangeFilter = (event: any) => {
    // console.log("event::: ", event.target.value);

    let dropData = {
      ...selectedDropdownData,
      [event.target.name]: event.target.value,
    };

    setselectedDropdownData(dropData);

    let filteredData: any = [...data];

    if (dropData.level) {
      filteredData = filteredData.filter(
        (el: any) => el.level == dropData.level
      );
    }

    if (dropData.role) {
      filteredData = filteredData.filter((el: any) => el.role == dropData.role);
    }

    if (!dropData.level && !dropData.role) {
      setRows(data);
    } else {
      setRows(filteredData);
    }
  };

  if (!isMounted) return null;
  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px", background: "#BDCDD6" }}>
        <Row className="my-3">
          <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
            {" "}
            <Row>
              <Col>
                <h4>{"Designation List"}</h4>
                {/* <span style={{ fontSize: "12px", color: "grey" }}>
                  Hi, {username ? username?.toLowerCase() : ""}. Your
                  organizations designations are listed here.
                </span> */}
              </Col>
            </Row>
            <Row>
              {/* <Button style={{
                width: '100px', margin: 'auto',
                marginRight: '0px'
              }} aria-describedby={id} variant="contained" className={"carousal-container"} onClick={handleClick}>
                <FilterAltIcon />
              </Button> */}
              <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "left",
                }}
              >
                <div>
                  <select
                    className="filter-select"
                    onChange={handleChangeFilter}
                  >
                    <option disabled selected>
                      Select Level
                    </option>
                    {Array.from({ length: 10 }, (_, i) => i + 1).map(
                      (level) => (
                        <option key={level} value={level}>
                          Level {level}
                        </option>
                      )
                    )}
                  </select>
                </div>
                <hr />

                <div>
                  <select
                    className="filter-select"
                    onChange={handleChangeFilter}
                  >
                    <option disabled selected>
                      Select Department
                    </option>
                    {deptNames.length &&
                      deptNames.map((el, i) => <option key={i}>{el}</option>)}
                  </select>
                </div>

                <div></div>
              </Popover>
            </Row>
          </Col>
          <Col md={8}>
            {role === "ceo" && (
              <Row style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  style={{
                    width: "200px",
                    background: "green",
                    marginRight: "0",
                  }}
                  onClick={handleOpen}
                >
                  + New Designation{" "}
                </Button>
              </Row>
            )}
            <Row className="filter-container">
              {/* <Col md={3}>
                <select
                  className="filter-select"
                  name="level"
                  onChange={handleChangeFilter}
                >
                  <option value={""} selected>
                    Select Level
                  </option>

                  {Array.from({ length: 10 }, (_, i) => i + 1).map((level) => (
                    <option key={level} value={level}>
                      Level {level}
                    </option>
                  ))}
                </select>
              </Col> */}
              <Col md={3}>
                <select
                  className="filter-select"
                  name="role"
                  onChange={handleChangeFilter}
                >
                  <option value={""} selected>
                    Select Role
                  </option>
                  {role === "ceo" && <option value={"ceo"}>Ceo</option>}
                  {role === "ceo" && (
                    <option value={"superadmin"}>Superadmin</option>
                  )}
                  <option value={"admin"}>Admin</option>
                  <option value={"supervisor"}>Supervisor</option>
                  <option value={"employee"}>Employee</option>
                </select>
              </Col>
              {/* <Col md={3}>
                <select className="filter-select" name='department' onChange={handleChangeFilter}>
                  <option value={""} selected>Select Department</option>
                  {deptNames.length && deptNames.map((el, i) => <option key={i}>{el}</option>)}
                </select>
              </Col> */}
            </Row>
          </Col>
          <Col></Col>
        </Row>
        <Row style={{ margin: "10px" }}>
          {/* <Col md={2} style={{ borderRight: '7px solid #BDCDD6', backgroundColor: "#6097b4" }}>
            <Tabs
              value={value}
              onChange={handleChange}
              indicatorColor="secondary"
              textColor="inherit"
              variant="fullWidth"
              aria-label="full width tabs example"
              className="tab-pannel-test "
            >
              <Tab label={`Nurse (${data.length})`} {...a11yProps(0)} style={{color: "#000", backgroundColor: "#6097b4"}}/>
              <Tab label={`Doctors (${data.length})`} {...a11yProps(1)} style={{color: "#000", backgroundColor: "#6097b4"}}/>
            </Tabs>
            {/* <Row className='carousal-container'>
              <CarouselComp setValue={setValue} arr={["Nurse", "Doctor"]} />
            </Row> */}
          {/* </Col> */}
          <Col md={10} style={{ background: "white", width: "100%" }}>
            <TabPanel value={value} index={0} dir={theme.direction}>
              <Row>
                <div id="tableWrapper">
                  <TableFilter columns={columns} rows={rows} />
                </div>
              </Row>
            </TabPanel>
            {/* <TabPanel value={value} index={1} dir={theme.direction}>
                <Row>
                  <div id="tableWrapper">
                    <TableFilter columns={columns} rows={data} />
                  </div>
                </Row>
              </TabPanel> */}
          </Col>
        </Row>
        {openModal && (
          <CreateDesignationModal
            show={openModal}
            handleClose={() => setOpenModal(false)}
            rowToEdit={rowToEdit}
            setRows={setRows}
          />
        )}
        {openDetailModal && (
          <DesignationDetailModal
            show={openDetailModal}
            onClose={handleCloseDetailModal}
            designationDetails={selectedDesignation}
          />
        )}
      </Container>
    </Layout>
  );
};

export default designationList;

const PopupBody = styled("div")(
  ({ theme }) => `
    width: max-content;
    padding: 12px 16px;
    margin: 8px;
    border-radius: 8px;
    border: 1px solid ${theme.palette.mode === "dark" ? grey[700] : grey[200]};
    background-color: ${theme.palette.mode === "dark" ? grey[900] : "#fff"};
    box-shadow: ${
      theme.palette.mode === "dark"
        ? `0px 4px 8px rgb(0 0 0 / 0.7)`
        : `0px 4px 8px rgb(0 0 0 / 0.1)`
    };
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    z-index: 1;
  `
);

const FilterButton = styled("button")(
  ({ theme }) => `
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: ${blue[500]};
    padding: 8px 16px;
    border-radius: 8px;
    color: white;
    transition: all 150ms ease;
    cursor: pointer;
    border: 1px solid ${blue[500]};
    box-shadow: 0 2px 4px ${
      theme.palette.mode === "dark"
        ? "rgba(0, 0, 0, 0.5)"
        : "rgba(0, 127, 255, 0.5)"
    }, inset 0 1.5px 1px ${blue[400]}, inset 0 -2px 1px ${blue[600]};
  
    &:hover {
      background-color: ${blue[600]};
    }
  
    &:active {
      background-color: ${blue[700]};
      box-shadow: none;
    }
  
    &:focus-visible {
      box-shadow: 0 0 0 4px ${
        theme.palette.mode === "dark" ? blue[300] : blue[200]
      };
      outline: none;
    }
  
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      box-shadow: none;
      &:hover {
        background-color: ${blue[500]};
      }
    }
  `
);

const grey = {
  50: "#F3F6F9",
  100: "#E5EAF2",
  200: "#DAE2ED",
  300: "#C7D0DD",
  400: "#B0B8C4",
  500: "#9DA8B7",
  600: "#6B7A90",
  700: "#434D5B",
  800: "#303740",
  900: "#1C2025",
};

const blue = {
  200: "#99CCFF",
  300: "#66B2FF",
  400: "#3399FF",
  500: "#007FFF",
  600: "#0072E5",
  700: "#0066CC",
};
