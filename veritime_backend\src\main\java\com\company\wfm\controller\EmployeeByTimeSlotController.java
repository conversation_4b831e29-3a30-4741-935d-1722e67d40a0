package com.company.wfm.controller;

import java.sql.Date;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/employees")
@CrossOrigin(origins = "*")
public class EmployeeByTimeSlotController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @GetMapping("/by-timeslot")
    public Map<String, Object> getEmployeesByTimeSlot(
            @RequestParam("time_slot_id") Long timeSlotId,
            @RequestParam("from_date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) java.time.LocalDate fromDate,
            @RequestParam("to_date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) java.time.LocalDate toDate) {

        long totalDays = ChronoUnit.DAYS.between(fromDate, toDate) + 1;

        String employeeSql = "SELECT e.EMP_ID, e.EMP_NAME, e.DEPARTMENT_ID, d.DEPARTMENT_NAME, COUNT(es.date) as scheduled_days " +
                "FROM t_employee_schedule es " +
                "INNER JOIN t_employee e ON es.EMP_ID = e.EMP_ID " +
                "INNER JOIN t_department d ON e.DEPARTMENT_ID = d.DEPARTMENT_ID " +
                "WHERE es.actual_shift = ? AND es.date BETWEEN ? AND ? " +
                "GROUP BY e.EMP_ID, e.EMP_NAME, e.DEPARTMENT_ID, d.DEPARTMENT_NAME " +
                "HAVING COUNT(es.date) = ?";

        List<Map<String, Object>> employees = jdbcTemplate.query(employeeSql, new Object[]{timeSlotId, Date.valueOf(fromDate), Date.valueOf(toDate), totalDays}, (rs, rowNum) -> {
            Map<String, Object> employee = new HashMap<>();
            employee.put("employee_id", rs.getLong("EMP_ID"));
            employee.put("employee_name", rs.getString("EMP_NAME"));
            employee.put("department_id", rs.getLong("DEPARTMENT_ID"));
            employee.put("department_name", rs.getString("DEPARTMENT_NAME"));
            return employee;
        });

        Map<String, Object> response = new HashMap<>();
        response.put("time_slot_id", timeSlotId);
        response.put("from_date", fromDate);
        response.put("to_date", toDate);
        response.put("employees", employees);

        return response;
    }
}