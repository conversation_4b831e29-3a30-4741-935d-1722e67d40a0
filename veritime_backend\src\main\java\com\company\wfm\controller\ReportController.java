package com.company.wfm.controller;

import com.company.wfm.service.impl.DownloadReportServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/report")
@CrossOrigin(origins = "*")
public class ReportController {


    @Autowired
    private DownloadReportServiceImpl reportService;
    @Autowired
    private WidgetsDashboardController widgets;
//    @GetMapping("/widgets")
//    public ResponseEntity<?> handleReport(@RequestParam String report,
//                                          @RequestParam(defaultValue = "false") Boolean download) {
//
//        if (download) {
//
//            switch (report) {
//                case "EmployeeOnLeave":
//
//                    return ResponseEntity.ok("Downloading Approvals report...");
//                case "Attendance":
//                    // Handle download logic for Attendance
//                    return ResponseEntity.ok("Downloading Attendance report...");
//                // Add more cases as needed
//                default:
//                    return ResponseEntity.badRequest().body("Unknown report type: " + report);
//            }
//        }
//
//        switch (report) {
//            case "Approvals":
//                // Handle download logic for Approvals
//                return ResponseEntity.ok("Downloading Approvals report...");
//            case "Attendance":
//                // Handle download logic for Attendance
//                return ResponseEntity.ok("Downloading Attendance report...");
//            // Add more cases as needed
//            default:
//                return ResponseEntity.badRequest().body("Unknown report type: " + report);
//        }
//        // Example response just for clarity
////        String response = "Report: " + report + ", Download: " + download;
////        return ResponseEntity.ok(response);
//    }


@PostMapping("/widgets")
public ResponseEntity<?> handleReport(@RequestParam String report,
                                      @RequestParam(defaultValue = "false") Boolean download) {
    try {
        // Sample static data for EmployeeOnLeaveToday
        List<Map<String, Object>> employees = new ArrayList<>();
//        Map<String, Object> emp1 = new HashMap<>();
//        emp1.put("empCode", "E001");
//        emp1.put("empName", "John Doe");
//        emp1.put("deptName", "HR");
//        emp1.put("desgnName", "Manager");
//        emp1.put("startDate", "2025-04-14");
//        emp1.put("endDate", "2025-04-14");
//        emp1.put("leaveDays", "1");
//        emp1.put("leavetype", "Sick Leave");
//        employees.add(emp1);

        // Handle report types
        switch (report) {
            case "EmployeeOnLeaveToday":
                employees = widgets.getEmployeesOnLeave1();
                break;

            case "EmployeesOnClockedInToday":
                employees = widgets.getEmployeeOnClockedInDetails();
                break;
            // Add other report types here
            // case "Attendance":
            //     // implement logic
            //     break;
            case "LeaveApproved":
                employees = widgets.getLeaveRequestApprovedPastDays();
                break;
            case "EmployeePresent":
                employees = widgets.getTotalEmployeesPresentList();
                break;
            case "LeaveApprovalsPending":
                employees = widgets.getPendingLeaveRequest();
                break;
            case "FacilityCount":
                employees = widgets.getFacilityDetails();
                break;
            case "EmployeeCount":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;
            case "DepartmentCount":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;
            case "TicketCreated":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;
            case "FacilityBasedTicketOveriew":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;
            case "DepartmentSpecificReport":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;
            case "ScheduleSpecificReport":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;
            case "Top3Facilities":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;
            case "Attendance":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;
            case "WorkingHours":
                //employees = widgets.getEmployeeOnClockedInDetails();
                break;

            default:
                return ResponseEntity.badRequest().body("Unknown report type: " + report);
        }
        if (download) {
            byte[] data = reportService.generateEmployeesOnLeaveTodayExcelFile(employees);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDisposition(ContentDisposition.builder("attachment").filename("employees.xlsx").build());
            try (FileOutputStream fos = new FileOutputStream("C:/Users/<USER>/Downloads/Testing Material/Documents/employees.xlsx")) {
                fos.write(data);
                System.out.println("Excel file saved locally.");
            } catch (IOException e) {
                e.printStackTrace();
            }
            return new ResponseEntity<>(data, headers, HttpStatus.OK);
        } else {
            return ResponseEntity.ok(employees);
        }

    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error: " + e.getMessage());
    }
}

}


