package com.company.wfm.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorEmployeeRequestDTO {
    private String type;
    private Integer offset;
    private Integer limit;
    private Long departmentId;

    public Integer getOffset() {
        return offset != null ? offset : 0; // Default to 0
    }

    public Integer getLimit() {
        return limit != null ? limit : 10; // Default to 10
    }


}
