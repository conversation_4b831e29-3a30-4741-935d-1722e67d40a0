package com.company.wfm.service;

import com.company.wfm.dto.BranchUpdateResponseDTO;
import com.company.wfm.dto.CreateBranchDTO;
import com.company.wfm.entity.Branch;

public interface BranchService {
    Branch createBranch(CreateBranchDTO branchDTO);

    void deleteDepartment(Long id);

    void createBranchAndDepartment(CreateBranchDTO branchDTO);

   // Branch updateBranch(Long id, CreateBranchDTO branchDTO);

    void deleteBranch(Long id);

    public BranchUpdateResponseDTO updateBranch(Long id, CreateBranchDTO branchDTO) ;
}
