import React, { useEffect, useState } from "react";
import { showErrorAlert, showSuccessAlert } from "@/services/alertService";
import { fileDownload, getRequest , getRequestWithSecurity} from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";

const DocumentPopup = ({ show, handleClose, empId }: any) => {
  const [documents, setDocuments] = useState([]); // Store documents
  const [loading, setLoading] = useState(false); // Loading state for fetching documents
  const [errorMessage, setErrorMessage] = useState(""); // Store error message if any

  // Function to fetch employee documents
  const getEmpDocuments = async (empId: any) => {
    if (!empId) {
      return;
    }

    try {
      setLoading(true);

      const empDetails = await getRequestWithSecurity(`${API_URLS.EMPLOYEE_LIST}/${empId}`, true);

      if (empDetails && empDetails.documents && Array.isArray(empDetails.documents)) {
        setDocuments(empDetails.documents);
      } else {
      }
    } catch (error) {
      setErrorMessage("Error fetching documents.");
    } finally {
      setLoading(false); 
    }
  };

  // Function to download a file
  const downloadFile = async (file: any) => {
    try {
      const fileName = file.split("/").pop();
      const response:any = await fileDownload(`${API_URLS.DOWNLOAD_EMPLOYEE_FILE}${file}`, true);

      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      const blob = await response.blob();
      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.target = "_blank";
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();

      showSuccessAlert(`File "${fileName}" downloaded successfully.`);
    } catch (error) {
      showErrorAlert("Error downloading file.");
    }
  };

  useEffect(() => {
    if (show && empId) {
      getEmpDocuments(empId); // Pass empId directly instead of row
    }
  }, [show, empId]);

  if (!show) return null; // Return nothing if `show` is false

  return (
    <div className="modal-backdrop">
      <div className="modal-container">
        <div className="modal-header">
          <h5 className="modal-title">Documents</h5>
          <button className="close-button" onClick={handleClose}>
            &times;
          </button>
        </div>
        <div className="modal-body">
          {loading ? (
            <p>Loading documents...</p>
          ) : errorMessage ? (
            <p>{errorMessage}</p>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Type</th>
                  <th>Files</th>
                </tr>
              </thead>
              <tbody>
                {documents.length > 0 ? (
                  documents.map((doc: any) => (
                    <tr key={doc.id}>
                      <td>{doc.documentName}</td>
                      <td>{doc.documentType}</td>
                      <td>
                        {doc.filePath ? (
                          <button
                            className="download-button"
                            onClick={() => downloadFile(doc.filePath)}
                          >
                            <img
                              src="/image/icons8-download-24.png"
                              alt="Download"
                              style={{ width: "24px" }}
                            />
                          </button>
                        ) : (
                          <span style={{ color: "#999", fontStyle: "italic" }}>No file</span>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={3} style={{ textAlign: "center" }}>
                      No documents available.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>
      <style jsx>{`
        .modal-backdrop {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .modal-container {
          background: #fff;
          border-radius: 8px;
          padding: 20px;
          width: 500px;
        }
        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #ddd;
          margin-bottom: 15px;
        }
        .close-button {
          border: none;
          background: none;
          font-size: 20px;
          cursor: pointer;
        }
        .modal-body {
          max-height: 400px;
          overflow-y: auto;
        }
        .download-button {
          border: none;
          background: none;
          cursor: pointer;
        }
      `}</style>
    </div>
  );
};

export default DocumentPopup;
