

.mastercard-popup {
  position: absolute;
  transform: translateY(-18%);
  z-index: 2000;
  border-radius: 8px; 
}


.custom-card-masters {
  position: relative;
  padding: 5px;
  border-radius: 8px;
  margin-bottom: 352%;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  background-color: #ccc;
}

/* Reduce space inside the card body */
.custom-card-masters .card-body {
  padding: 5px; /* Less padding inside the card */
  margin: 0; /* Remove unnecessary margins */
}

/* Fix spacing inside unstyled list */
.custom-card-masters .list-unstyled {
  padding: 0;
  margin: 0;
}

/* Reduce spacing between list items */
.custom-card-masters .list-unstyled li {
  padding: 1px 0 !important; /* Reduce padding inside each item */
  margin-bottom: 1px !important; /* Reduce gap between items */
  font-size: 14px; /* Optional: Adjust font size */
  line-height: 1 !important; /* Reduce line spacing */
}



.custom-card-masters.left::before {
  content: '';
  position: absolute;
  top: 46%;
  left: -9px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #ccc;
}


.custom-card-masters.up::before {
  content: '';
  position: absolute;
  top: -9px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #ccc;
}

.custom-card-masters.up .mastercard-popup {
  top: -70px;
  transform: translateY(0);
}