import React, { useState, useEffect } from 'react';
//import 'bootstrap/dist/css/bootstrap.min.css';
import 'react-datepicker/dist/react-datepicker.css';
import { Col, Form, InputGroup, Button, Row } from 'react-bootstrap';
import { BsCalendar, BsChevronDown, BsX } from 'react-icons/bs';
import DatePicker from 'react-datepicker';
import { getRequest } from '@/services/apiService'; 
import { API_URLS } from '@/constants/apiConstants'; 

const ScheduleForm = ({ schedule, onClose }) => {
  const [selectedStartDate, setSelectedStartDate] = useState(null);
  const [selectedEndDate, setSelectedEndDate] = useState(null);
  const [isDatePickerVisible, setDatePickerVisible] = useState(false); 
  const [isSelectingEndDate, setSelectingEndDate] = useState(false); 
  const [selectedSchedule, setSelectedSchedule] = useState(schedule?.shift || 'Select Schedule');
  const [isDropdownVisible, setDropdownVisible] = useState(false);
  const [departmentName, setDepartmentName] = useState(schedule?.department_id || '');
  const [timeSlots, setTimeSlots] = useState([]);

  const fetchTimeSlots = async () => {
    try {
      const response = await getRequest(API_URLS.SHIFTCHANGE_TIMESLOT_ROSTER(department_id));
      if (response) {
        setTimeSlots(response.time_slots);
      }
    } catch (error) {
    }
  };

  useEffect(() => {
    fetchTimeSlots();
  }, []);

  useEffect(() => {
    if (schedule?.department_name) {
      setDepartmentName(schedule.department_name);
    }
  }, [schedule]);

  const toggleDropdown = () => {
    setDropdownVisible(!isDropdownVisible);
  };

  const handleOptionSelect = (option) => {
    setSelectedSchedule(option);
    setDropdownVisible(false);
  };

  const handleDateChange = (date) => {
    if (!isSelectingEndDate) {
      setSelectedStartDate(date);
      setSelectingEndDate(true); 
    } else {
      setSelectedEndDate(date);
      setSelectingEndDate(false); 
      setDatePickerVisible(false); 
    }
  };

  const toggleDatePicker = () => {
    setDatePickerVisible(!isDatePickerVisible);
  };


  const handleUpdate = () => {

    
  };

  return (
    <div className="container mt-5 bg-white p-0" style={{ height: "auto", position: 'relative', border: "1px solid grey", width: "100%" }}>
      <BsX
        onClick={onClose}
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          cursor: 'pointer',
          fontSize: '24px',
          color: '#333'
        }}
      />
      <Row className="justify-content-center">
        <Col xs={12} md={10} lg={10}>
          <Form className="p-4">
            <Form.Group className="mb-4 position-relative">
              <Form.Label className="fw-bold" style={{ fontSize: '18px' }}>Date Range</Form.Label>
              <InputGroup className="d-flex align-items-center">
                <Form.Control
                  placeholder={
                    selectedStartDate && selectedEndDate
                      ? `${selectedStartDate.toLocaleDateString()} - ${selectedEndDate.toLocaleDateString()}`
                      : "Select Date Range"
                  }
                  style={{ border: '1px solid #CCC', height: '50px', fontWeight: 'bold', flexGrow: 1 }}
                  readOnly
                  onClick={toggleDatePicker} 
                />
                <InputGroup.Text
                  style={{ border: '1px solid #CCC', height: '50px', cursor: 'pointer', borderRadius: '0 5px 5px 0' }}
                  onClick={toggleDatePicker}
                >
                  <BsCalendar />
                </InputGroup.Text>
              </InputGroup>

              {isDatePickerVisible && (
                <div className="position-absolute" style={{ zIndex: 1000 }}>
                  <div className="mb-2 fw-bold">
                    {isSelectingEndDate ? 'Select End Date' : 'Select Start Date'}
                  </div>
                  <DatePicker
                    selected={isSelectingEndDate ? selectedEndDate : selectedStartDate}
                    onChange={handleDateChange}
                    startDate={selectedStartDate}
                    endDate={selectedEndDate}
                    selectsStart={!isSelectingEndDate}
                    selectsEnd={isSelectingEndDate}
                    inline
                    minDate={new Date()}
                  />
                </div>
              )}
            </Form.Group>


            <Form.Group className="mb-4 position-relative">
              <Form.Label className="fw-bold" style={{ fontSize: '18px' }}>Schedule</Form.Label>
              <InputGroup>
                <Form.Control
                  placeholder={selectedSchedule}
                  style={{ border: '1px solid #CCC', height: '50px', borderRadius: '5px 0 0 5px', fontWeight: 'bold', flexGrow: 1 }}
                  readOnly
                />
                <InputGroup.Text
                  style={{ border: '1px solid #CCC', height: '50px', borderRadius: '0 5px 5px 0', cursor: 'pointer' }}
                  onClick={toggleDropdown}
                >
                  <BsChevronDown />
                </InputGroup.Text>
              </InputGroup>

              {isDropdownVisible && (
                <div className="dropdown-menu show position-absolute w-100" style={{ top: '100%', left: 0, zIndex: 1000, maxHeight: '200px', overflowY: 'auto' }}>
                  <ul className="list-unstyled m-0 py-2" style={{ backgroundColor: '#F9F9FF' }}>
                    {timeSlots.map(slot => (
                      <li
                        key={slot.time_slot_id}
                        className="px-3 py-2 cursor-pointer"
                        onClick={() => handleOptionSelect(slot.timeRange)}
                      >
                        {slot.timeRange}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </Form.Group>
            <div className="d-flex justify-content-between">
              <Button onClick={handleUpdate} style={{ backgroundColor: '#6097B4', color: 'white', width: '40%', border: "0px" }}>Update</Button>
              <Button onClick={onClose} style={{ backgroundColor: '#6097B4', color: 'white', width: '45%', border: "0px" }}>Cancel</Button>
            </div>
          </Form>
        </Col>
      </Row>
    </div>
  );
};

export default ScheduleForm;