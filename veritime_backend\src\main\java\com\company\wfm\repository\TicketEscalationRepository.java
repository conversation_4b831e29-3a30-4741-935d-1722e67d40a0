package com.company.wfm.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.TicketEscalation;

@Repository
public interface TicketEscalationRepository extends JpaRepository<TicketEscalation, Long> {

    @Query("SELECT COUNT(te) FROM TicketEscalation te WHERE te.ticketId = :ticketId")
    int countByTicketId(@Param("ticketId") Long ticketId);

   /* @Query("SELECT te FROM TicketEscalation te WHERE te.ticketId = :ticketId ORDER BY te.createdAt DESC")
    Optional<TicketEscalation> findLatestEscalationByTicketId(@Param("ticketId") Long ticketId);*/

    @Query("SELECT te FROM TicketEscalation te WHERE te.ticketId = :ticketId ORDER BY te.createdAt DESC")
    Page<TicketEscalation> findLatestEscalationByTicketId(@Param("ticketId") Long ticketId, Pageable pageable);


    @Query("SELECT t FROM TicketEscalation t WHERE t.empId = :empId ORDER BY t.ticketId DESC")
    List<TicketEscalation> findByEmpId(@Param("empId") Long empId);

    @Query("SELECT t FROM TicketEscalation t WHERE t.empId = :empId ORDER BY t.ticketId DESC")
    Page<TicketEscalation> findByEmpIdOrderedByTicketIdDesc(@Param("empId") Long empId, Pageable pageable);

    List<TicketEscalation> findByTicketId(Long ticketId);




}
