package com.company.wfm.controller;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.company.wfm.dto.*;
import com.company.wfm.entity.*;
import com.company.wfm.repository.*;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.impl.DownloadReportServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.service.AmazonS3Service;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.service.impl.EmployeeService;
import com.company.wfm.util.EncryptionUtil;
import com.company.wfm.vo.EmployeeVO;
import com.google.gson.Gson;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = "*")
public class EmployeeController {

    private static final Logger logger = LoggerFactory.getLogger(EmployeeController.class);

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    AmazonS3Service s3Service;

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private WidgetsDashboardController controller;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeTerminationRepository employeeTerminationRepository;

    @Autowired
    private DesignationRepository designationRepository;

    @Autowired
    private ResignationStatusRepository resignationStatusRepository;

    @Autowired
    private CommonNotificationService notificationService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private DownloadReportServiceImpl downloadReportService;


    @Autowired
    private WidgetsDashboardController report;
    @Autowired
	private EncryptionUtil encryptionUtil;

    private static final List<String> ALLOWED_FILE_TYPES = Arrays.asList("image/jpeg");

    @PostMapping("/employee")
    public ResponseEntity<EncryptedResponse> createEmployee(
            @RequestPart("employeeDTO") EmployeeDTO employeeDTO,
            @RequestPart(value = "file", required = false) MultipartFile file) throws Exception {
        log.info("INSIDE CREATE CONTROLLER ");
        try {
            // Process the request and create the employee
            EmployeeDetailsDto employeeDetailsDto = employeeService.createEmployee(employeeDTO, file);

            Gson gson = encryptionUtil.createGson(); // Use the updated Gson
            String responseJson = gson.toJson(employeeDetailsDto);

            // Encrypt the serialized response
            String encryptedResponseData = encryptionUtil.encrypt(responseJson,encryptionUtil.generateKey());

            // Wrap the encrypted data into an EncryptedResponse object and return it
            return ResponseEntity.status(HttpStatus.CREATED).body(new EncryptedResponse(encryptedResponseData));
        } catch (IllegalArgumentException e) {
            // Handle specific exception
            String encryptedError = encryptionUtil.encrypt("Specific error occurred: " + e.getMessage(),encryptionUtil.generateKey());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new EncryptedResponse(encryptedError));
        } catch (Exception e) {
            // Custom exception handling for general errors
            String encryptedError = encryptionUtil.encrypt("Error creating employee: " + e.getMessage(),encryptionUtil.generateKey());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new EncryptedResponse(encryptedError));
        }
    }



    @GetMapping ("/employee")
    public ResponseEntity<List<EmployeeDetailsDto>> getAllEmployees(HttpServletRequest request) {
        List<EmployeeDetailsDto> employees = employeeService.getAllEmployeesAll(request);
        return ResponseEntity.ok(employees);
    }


    @PostMapping("/employee-old")
    public ResponseEntity<Page<EmployeeDetailsDto>> filterEmployees(
            @RequestBody EmployeeFilterRequest filterRequest, HttpServletRequest request) {

        // Validate limit (size)
        int validatedLimit = Math.max(1, Math.min(filterRequest.getLimit(), 100));  // Ensure limit is between 1 and 100

        // Create PageRequest with validated offset and limit
        PageRequest pageRequest = PageRequest.of(filterRequest.getOffset(), validatedLimit);

        Page<EmployeeDetailsDto> result = employeeService.filterEmployees(
                filterRequest.getBranchIds(),
                filterRequest.getDepartmentIds(),
                filterRequest.getDesignationIds(),
                filterRequest.getQuery(),
                pageRequest,  // Pass PageRequest here,
                request
        );

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping("/employee1")
    public ResponseEntity<?> searchEmployees(@RequestBody EncryptedRequest encryptedRequest) throws Exception {
        try {
            // Decrypt the incoming request data
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

            // Parse the decrypted data into the EmployeeFilterRequest object
            Gson gson = encryptionUtil.createGson();
            EmployeeFilterRequest filter = gson.fromJson(decryptedData, EmployeeFilterRequest.class);



            //new logic for download
            if (filter.isDownload()) {
                // Fetch all employees (with or without filters)
                List<EmployeeVO> employees = employeeService.searchAllEmployees(filter);

                // Log how many employees are included in the download
                log.info("Downloading {} employee records.", employees.size());

                // Generate Excel file
                byte[] excelContent = employeeService.generateExcelFile(employees);

                // Set headers for file download
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                headers.setContentDisposition(ContentDisposition.builder("attachment")
                        .filename("employees.xlsx")
                        .build());

                return new ResponseEntity<>(excelContent, headers, HttpStatus.OK);
            }



            // Call the service method with the filter
            Page<EmployeeVO> employees = employeeService.searchEmployees(filter);
            // Log total employees fetched for listing
                log.info("Total employees fetched for listing: {}", employees.getTotalElements());

           /* if (filter.isDownload()) {
                // Generate the Excel file content
                byte[] excelContent = employeeService.generateExcelFile(employees.getContent());

                // Set headers for file download
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                headers.setContentDisposition(ContentDisposition.builder("attachment")
                        .filename("employees.xlsx")
                        .build());

                return new ResponseEntity<>(excelContent, headers, HttpStatus.OK);
            }*/

            // Convert the list of employees to JSON and encrypt it
            String jsonResponse = gson.toJson(employees);
            String encryptedResponse = encryptionUtil.encrypt(jsonResponse,encryptionUtil.generateKey());

            // Return the encrypted response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));
        } catch (RuntimeException e) {
            // Handle application-specific exceptions and return encrypted error message
            String encryptedError = encryptionUtil.encrypt("An error occurred: " + e.getMessage(),encryptionUtil.generateKey());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse(encryptedError));
        } catch (Exception e) {
            // Handle generic exceptions
            String encryptedError = encryptionUtil.encrypt("A system error occurred: " + e.getMessage(),encryptionUtil.generateKey());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse(encryptedError));
        }
    }

    @PostMapping("/teams")
    public ResponseEntity<Page<EmployeeVO>> myEmployees(
            @RequestBody EmployeeFilterRequest filter) {

        // Call the service method with the calculated page and limit
        Page<EmployeeVO> employees = employeeService.myEmployees(filter);


        return ResponseEntity.ok(employees);
    }

    @PostMapping("/employee/leave-balance")
    public ResponseEntity<?> saveEmployeeLeaveBalances(@RequestBody List<LeaveCountSaveDTO> leaveCounts) {
        try {
            // Call the service method to save leave balances
            employeeService.saveEmployeeLeaveBalances(leaveCounts);
            return new ResponseEntity<>("Leave balances saved successfully.", HttpStatus.OK);
        } catch (Exception e) {
            // Log the exception for debugging
            log.error("Exception occured while saveEmployeeLeaveBalances", e);
            // Return a meaningful error response
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("An error occurred while saving leave balances: " + e.getMessage());
        }
    }

    @GetMapping("/employees/ids-names")
    public List<EmployeeIdNameDTO> getEmployeeIdsAndNames() {
        return employeeService.getAllActiveEmployeeIdsAndNames();
    }

  @GetMapping ("/current")
    public ResponseEntity<?> getCurrentEmployeeDetails() {
        try {
            EmployeeDetailsDto employeeDetailsDto = employeeService.getCurrentEmployeeDetails();
            Gson gson = encryptionUtil.createGson();
            String employeeJson = gson.toJson(employeeDetailsDto);
            String encryptedEmployeeData = encryptionUtil.encrypt(employeeJson,encryptionUtil.generateKey());
            // Decrypt the employee JSON data (for printing purposes)
            String decryptedEmployeeData = encryptionUtil.decrypt(encryptedEmployeeData,encryptionUtil.generateKey());
            // return ResponseEntity.ok(employeeDetailsDto);
            return ResponseEntity.ok(new EncryptedResponse(encryptedEmployeeData));
        } catch (RuntimeException e) {
           // return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            logger.error("Runtime exception occurred: {}", e.getMessage(), e);
            // return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ErrorResponse("Failed to retrieve employee details: " + e.getMessage()));
        } catch (Exception e) {
           // return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
            logger.error("exception occurred: {}", e.getMessage(), e);
            // return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ErrorResponse("An unexpected error occurred. Please try again later."));
        }

    }


    //update employee

  /*  // Update Employee by ID
    @PutMapping("/employee/update/{id}")
    public ResponseEntity<?> updateEmployee(@PathVariable Long id, @RequestBody EmployeeDTO employeeDTO) {
        try {
            Employee updatedEmployee = employeeService.updateEmployee(id, employeeDTO);
            return new ResponseEntity<>(updatedEmployee, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>("Employee not found", HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>("Failed to update employee: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }*/

    // Delete Employee by ID
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteEmployee(@PathVariable Long id) {
        try {
            employeeService.deleteEmployee(id);
            return new ResponseEntity<>("Employee deleted successfully", HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>("Employee not found", HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>("Error occurred: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    /*@PostMapping("/employee/upload-excel")
    public ResponseEntity<String> uploadExcelFile(@RequestParam("file") MultipartFile file,HttpServletRequest httpRequest) throws ParseException {
        try {
            logger.info("Received Excel file: {}", file.getOriginalFilename());
            String frontendUrl = httpRequest.getHeader("Referer");
            if (frontendUrl == null) {
                frontendUrl = httpRequest.getHeader("Origin");
            }

            if (frontendUrl != null) {
                logger.info("Request received from frontend URL: {}", frontendUrl);
            } else {
                logger.warn("Unable to detect frontend URL (No Referer or Origin header).");
            }

            httpRequest.setAttribute("frontendUrl", frontendUrl);
            employeeService.createEmployeesExcel(file.getInputStream(),httpRequest);
            return ResponseEntity.ok("Employees created successfully!");
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body("Error creating employees: " + e.getMessage());
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error reading the Excel file: " + e.getMessage());
        }
    }*/


    @PostMapping("/employee/upload-excel")
    public ResponseEntity<String> uploadExcelFile(@RequestParam("file") MultipartFile file, HttpServletRequest httpRequest) {
        try {
            logger.info("Received Excel file: {}", file.getOriginalFilename());
            String frontendUrl = httpRequest.getHeader("Referer");
            if (frontendUrl == null) {
                frontendUrl = httpRequest.getHeader("Origin");
            }

            if (frontendUrl != null) {
                logger.info("Request received from frontend URL: {}", frontendUrl);
            } else {
                logger.warn("Unable to detect frontend URL (No Referer or Origin header).");
            }

            httpRequest.setAttribute("frontendUrl", frontendUrl);
            employeeService.createEmployeesExcel(file.getInputStream(), httpRequest);
            return ResponseEntity.ok("Employees created successfully!");
        } catch (IllegalArgumentException e) {
            logger.error("Illegal argument error: {}", e.getMessage(), e); // Log the error message
            return ResponseEntity.badRequest().body("Error creating employees: " + e.getMessage());
        } catch (ParseException e) {
            logger.error("Parsing error: {}", e.getMessage(), e); // Log parsing exception
            return ResponseEntity.badRequest().body("Error parsing the Excel file: " + e.getMessage());
        } catch (IOException e) {
            logger.error("I/O error: {}", e.getMessage(), e); // Log I/O exception
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error reading the Excel file: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error: {}", e.getMessage(), e); // Log any other unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Unexpected error occurred: " + e.getMessage());
        }
    }


    //employee detail by id

    @GetMapping("/employee/{id}")
    public ResponseEntity<?> getEmployeeDetailsById(@PathVariable Long id, HttpServletRequest request) {
        try {
            EmployeeVO employeeVO = employeeService.getEmployeeByIdDetails(id, request);

            Gson gson = encryptionUtil.createGson();
            String employeeJson = gson.toJson(employeeVO);

            // Encrypt the employee JSON data
            String encryptedEmployeeData = encryptionUtil.encrypt(employeeJson,encryptionUtil.generateKey());
           // return ResponseEntity.ok(employeeVO);
           return ResponseEntity.ok(new EncryptedResponse(encryptedEmployeeData));

        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Employee not found");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error occurred: " + e.getMessage());
        }
    }



    /*@PutMapping("/employee/update/{empId}")
    public ResponseEntity<?> updateEmployee(@PathVariable Long empId, @RequestBody EmployeeDTO employeeDTO) {
        log.info("INSIDE UPDATE CONTROLLER for Employee ID: " + empId);
        try {
            EmployeeDetailsDto employeeDetailsDto = employeeService.updateEmployee(empId, employeeDTO);
            return new ResponseEntity<>(employeeDetailsDto, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Employee not found: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Specific error occurred: " + e.getMessage());
        } catch (Exception e) {
            return new ResponseEntity<>("Error updating employee: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }
*/

//    @PutMapping("/employee/update/{empId}")
//    public ResponseEntity<?> updateEmployee(@PathVariable Long empId, @RequestBody EncryptedRequest encryptedRequest) {
//        log.info("INSIDE UPDATE CONTROLLER for Employee ID: " + empId);
//        try {
//            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());
//            Gson gson = encryptionUtil.createGson(); // Use the custom Gson with LocalDateTime handling
//            EmployeeDTO employeeDTO = gson.fromJson(decryptedData, EmployeeDTO.class);
//            EmployeeDetailsDto employeeDetailsDto = employeeService.updateEmployee(empId, employeeDTO);
//            String encryptedResponse = encryptionUtil.encrypt(gson.toJson(employeeDetailsDto),encryptionUtil.generateKey());
//            return new ResponseEntity<>(new EncryptedResponse(encryptedResponse), HttpStatus.OK);
//        } catch (EntityNotFoundException e) {
//            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Employee not found: " + e.getMessage());
//        } catch (IllegalArgumentException e) {
//            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Specific error occurred: " + e.getMessage());
//        } catch (Exception e) {
//            return new ResponseEntity<>("Error updating employee: " + e.getMessage(), HttpStatus.BAD_REQUEST);
//        }
//    }

    @PostMapping("/employee/update/{empId}")
    public ResponseEntity<?> updateEmployee(@PathVariable Long empId, @RequestPart("employeeDTO") EmployeeDTO employeeDTO, @RequestPart(value = "file", required = false) MultipartFile image ) {
        log.info("INSIDE UPDATE CONTROLLER for Employee ID: " + empId);

        try {

            EmployeeDetailsDto employeeDetailsDto = employeeService.updateEmployee(empId, employeeDTO, image);

            return new ResponseEntity<>("The employee details updated successfully", HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Employee not found: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Specific error occurred: " + e.getMessage());
        } catch (Exception e) {
            return new ResponseEntity<>("Error updating employee: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }


    @PutMapping("/employee/update/self")
    public ResponseEntity<?> updateSelfEmployee( @RequestBody EncryptedRequest encryptedRequest) {
        //log.info("INSIDE UPDATE CONTROLLER for Employee ID: " + empId);
        try {
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());
            Gson gson = encryptionUtil.createGson(); // Use the custom Gson with LocalDateTime handling
            SelfEmployeeEditDTO employeeDTO = gson.fromJson(decryptedData, SelfEmployeeEditDTO.class);
            //EmployeeDetailsDto employeeDetailsDto = employeeService.updateEmployee(empId, employeeDTO);
            SelfEmployeeEditDTO employeeDetailsDto = employeeService.updateSelfEmployee(employeeDTO);
            String encryptedResponse = encryptionUtil.encrypt(gson.toJson(employeeDetailsDto),encryptionUtil.generateKey());
            return new ResponseEntity<>(new EncryptedResponse(encryptedResponse), HttpStatus.OK);
        }  catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Specific error occurred: " + e.getMessage());
        } catch (Exception e) {
            return new ResponseEntity<>("Error updating employee: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }


    // reset default password api

    @PostMapping("/resetDefault-password")
    public ResponseEntity<String> resetPassword(@RequestBody PasswordResetDto passwordResetDto) {
        System.out.println("username"+passwordResetDto.getUsername());
        System.out.println("defaultPassword"+passwordResetDto.getDefaultPassword());
        System.out.println("newPassword"+passwordResetDto.getNewPassword());
        System.out.println("confirmPassword"+passwordResetDto.getConfirmPassword());

        String response = employeeService.resetPassword(passwordResetDto);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/export")
    public ResponseEntity<String> exportEmployeesToExcel(HttpServletResponse response) {
        try {
            employeeService.exportEmployeesToExcel(response); // Pass the response directly
            return new ResponseEntity<>("Employees exported successfully.", HttpStatus.OK);
        } catch (IOException e) {
            return new ResponseEntity<>("Error exporting employees: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }



    @PostMapping("/employee/by-branch")
    public ResponseEntity<Page<EmployeeResponseDTO>> getEmployeesByBranch(
            @RequestBody BranchRequestDTO branchRequestDTO) {

        try {

            Long branchId = branchRequestDTO.getBranchId();
            int offset = branchRequestDTO.getOffset();
            int limit = branchRequestDTO.getLimit();
            String search = branchRequestDTO.getSearch_param();
            // Fetch paginated list of employees
            Page<EmployeeResponseDTO> employees = employeeService.getEmployeesByLoggedInUserBranch(branchId, search, offset, limit);
            return ResponseEntity.ok(employees);
        } catch (Exception e) {
            // Handle exception and return a 500 status with the error message
            return ResponseEntity.status(500).body(null);
        }
    }


    @PostMapping("/uploadBulkImages")
    @Transactional
    public ResponseEntity<String> uploadBulkImages(@RequestParam("files") List<MultipartFile> files) {

        Long companyId;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loggedInUsername = authentication.getName();
        User loggedInUser = userRepository.findByUsername(loggedInUsername);
        if (loggedInUser == null) {
            throw new EntityNotFoundException("Logged-in user not found");
        }
        companyId = loggedInUser.getEmployee().getCompanyId();
        StringBuilder errorMessages = new StringBuilder();

        // Loop through each file and process
        for (MultipartFile file : files) {
            try {
                String fileName = file.getOriginalFilename();


                long fileSize = file.getSize();
                String fileContentType = file.getContentType();

                // Validate file type (allow only image/jpeg)
                if (fileContentType == null || !ALLOWED_FILE_TYPES.contains(fileContentType)) {
                    errorMessages.append("Only .jpeg files are allowed for file: ").append(fileName).append("\n");
                    continue;
                }

                // Validate file size (15 KB to 250 KB)
                if (fileSize < 15 * 1024 || fileSize > 250 * 1024) {
                    errorMessages.append("File size must be between 15 KB and 250 KB for file: ").append(fileName).append("\n");
                    continue;
                }

                // Extract empCode from the file name
                String empCode = fileName.split("\\.")[0];

               /* // Retrieve the employee based on empCode
                Employee employee = employeeRepository.findByEmpCodeCustom(empCode)
                        .orElseThrow(() -> new EntityNotFoundException("Employee not found with empCode: " + empCode));*/

                // Retrieve the employee based on empCode
                List<Employee> employees = employeeRepository.findByEmpCodeCustom(empCode);
                if (employees.isEmpty()) {
                    errorMessages.append("No employee found with empCode: ").append(empCode).append("\n");
                    continue;
                }

                if (employees.size() > 1) {
                    errorMessages.append("Multiple employees found with empCode: ").append(empCode).append("\n");
                    continue;
                }

                // If exactly one employee exists, retrieve it
                // If exactly one employee exists, retrieve it
                Employee employee = employees.get(0);
                // Upload the file to S3
                String filePath = "employee/" + companyId + "/" + System.currentTimeMillis() + "_" + fileName;
                s3Service.uploadFile(file, filePath);

                // Update the employee's image URL in the database
                employee.setImgUre(filePath);
                employeeRepository.save(employee);

            } catch (EntityNotFoundException ex) {
                // Handle case when the employee is not found
                errorMessages.append("Error: ").append(ex.getMessage()).append("\n");
            } catch (Exception ex) {
                // Catch any unexpected errors
                errorMessages.append("Unexpected error for file: ").append(file.getOriginalFilename()).append(" - ").append(ex.getMessage()).append("\n");
            }
        }
        // Return a response
        if (errorMessages.length() > 0) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorMessages.toString());
        } else {
            return ResponseEntity.ok("Bulk images uploaded successfully");
        }
    }

@PostMapping("/employees/present")
public ResponseEntity<?> getPresentEmployeesDetails(@RequestBody Map<String, Integer> paginationParams) {
    try {
        // Retrieve pagination details with defaults
        int offset = paginationParams.getOrDefault("offset", 0);
        int limit = paginationParams.getOrDefault("limit", 10);
        logger.info("Offset value: {}", offset);
        logger.info("Limit value: {}", limit);

        // Call service to get employee details and pagination metadata
        Map<String, Object> response = controller.getPresentEmployeesDetails(offset, limit);

        // Check if there are no content
        if (response.isEmpty() || ((List<?>) response.get("content")).isEmpty()) {
            return ResponseEntity.noContent().build();
        } else {
            return ResponseEntity.ok(response);
        }
    } catch (IllegalArgumentException ex) {
        logger.error("Validation error: " + ex.getMessage(), ex);
        return ResponseEntity.badRequest().body(Map.of("error", "Invalid input", "details", ex.getMessage()));
    } catch (RuntimeException ex) {
        logger.error("Runtime error: " + ex.getMessage(), ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "An unexpected error occurred", "details", ex.getMessage()));
    } catch (Exception ex) {
        logger.error("Unexpected error: " + ex.getMessage(), ex);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Internal server error", "details", ex.getMessage()));
    }

}

    //total approve employee

    @PostMapping("/employees/onLeave")
    public ResponseEntity<?> getEmployeesOnLeaveDetails(@RequestBody Map<String, Integer> paginationParams) {
        try {
            // Retrieve pagination details with defaults
            int offset = paginationParams.getOrDefault("offset", 0);
            int limit = paginationParams.getOrDefault("limit", 10);

            logger.info("Offset value1: {}", offset);
            logger.info("Limit value1: {}", limit);

            // Call service to get employee details and pagination metadata
            Map<String, Object> response = controller.getEmployeesOnLeave(offset, limit);

            // Check if there are no content
            if (response.isEmpty() || ((List<?>) response.get("content")).isEmpty()) {
                return ResponseEntity.noContent().build();
            } else {
                return ResponseEntity.ok(response);
            }
        } catch (IllegalArgumentException ex) {
            logger.error("Validation error: " + ex.getMessage(), ex);
            return ResponseEntity.badRequest().body(Map.of("error", "Invalid input", "details", ex.getMessage()));
        } catch (RuntimeException ex) {
            logger.error("Runtime error: " + ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "An unexpected error occurred", "details", ex.getMessage()));
        } catch (Exception ex) {
            logger.error("Unexpected error: " + ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Internal server error", "details", ex.getMessage()));
        }
    }


    @PostMapping("/employees/onLeave1")
    public ResponseEntity<?> getEmployeesOnLeaveDetails1(@RequestBody Map<String, Integer> paginationParams) {
        try {
            // Retrieve pagination details with defaults
            int offset = paginationParams.getOrDefault("offset", 0);
            int limit = paginationParams.getOrDefault("limit", 10);

            logger.info("Offset value1: {}", offset);
            logger.info("Limit value1: {}", limit);

            // Call service to get employee details and pagination metadata
            List<Map<String, Object>> response = controller.getEmployeesOnLeave1();

            // Check if there are no content
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException ex) {
            logger.error("Validation error: " + ex.getMessage(), ex);
            return ResponseEntity.badRequest().body(Map.of("error", "Invalid input", "details", ex.getMessage()));
        } catch (RuntimeException ex) {
            logger.error("Runtime error: " + ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "An unexpected error occurred", "details", ex.getMessage()));
        } catch (Exception ex) {
            logger.error("Unexpected error: " + ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Internal server error", "details", ex.getMessage()));
        }
    }



    @PostMapping("/employees/onClockedIn")
    public ResponseEntity<?> getEmployeeOnClockedInDetails(@RequestBody Map<String, Integer> paginationParams)
    {
        try {
            // Retrieve pagination details with defaults
            int offset = paginationParams.getOrDefault("offset", 0);
            int limit = paginationParams.getOrDefault("limit", 10);

            logger.info("Offset value1: {}", offset);
            logger.info("Limit value1: {}", limit);

            // Call service to get employee details and pagination metadata
            List<Map<String, Object>> response = controller.getEmployeeOnClockedInDetails();

            // Check if there are no content
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException ex) {
            logger.error("Validation error: " + ex.getMessage(), ex);
            return ResponseEntity.badRequest().body(Map.of("error", "Invalid input", "details", ex.getMessage()));
        } catch (RuntimeException ex) {
            logger.error("Runtime error: " + ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "An unexpected error occurred", "details", ex.getMessage()));
        } catch (Exception ex) {
            logger.error("Unexpected error: " + ex.getMessage(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Internal server error", "details", ex.getMessage()));
        }
    }





    @PostMapping("/employee/approved")
    public ResponseEntity<Map<String, Object>> getApprovedLeaves(
            @RequestBody LeaveHistoryRequestDTO leaveHistoryRequestDTO) {

        // Set default values for offset and limit if not provided or invalid
        int offset = leaveHistoryRequestDTO.getOffset();
        if (offset < 0) {
            offset = 0; // Default offset to 0
        }

        int limit = leaveHistoryRequestDTO.getLimit();
        if (limit <= 0) {
            limit = 10; // Default limit to 10
        }

        // Set the updated values back to the DTO
        leaveHistoryRequestDTO.setOffset(offset);
        leaveHistoryRequestDTO.setLimit(limit);

        // Create a response map to hold the results
        Map<String, Object> response = new HashMap<>();

        try {
            // Call the service method to get the approved leaves page
            Page<LeaveHistoryResponseDTO> approvedLeavesPage = controller.getTotalApprovedList(leaveHistoryRequestDTO);

            // If no results were found, handle it gracefully
            if (approvedLeavesPage.getContent().isEmpty()) {
                response.put("message", "No approved leaves found.");
            } else {
                // Prepare the response map
                response.put("pagination", getPaginationDetails(approvedLeavesPage));
                response.put("content", approvedLeavesPage.getContent());
            }

        } catch (IllegalArgumentException e) {
            // Handle cases where invalid parameters are passed
            response.put("error", "Invalid request: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            // General exception handling (e.g., service errors, database issues)
            response.put("error", "An error occurred while retrieving approved leaves: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }

        // Return a successful response
        return ResponseEntity.ok(response);
    }


    private Map<String, Object> getPaginationDetails(Page<LeaveHistoryResponseDTO> page) {
        Map<String, Object> paginationDetails = new HashMap<>();
        paginationDetails.put("number", page.getNumber());
        paginationDetails.put("size", page.getSize());
        paginationDetails.put("numberOfElements", page.getNumberOfElements());
        paginationDetails.put("totalPages", page.getTotalPages());
        paginationDetails.put("sort", page.getSort());
        paginationDetails.put("totalElements", page.getTotalElements());
        return paginationDetails;
    }


    @PostMapping("/employee/approvals")
    public ResponseEntity<Map<String, Object>> getApprovedLeaves1(@RequestBody LeaveHistoryRequestDTO leaveHistoryRequestDTO) {
        // Set default values for offset and limit if not provided or invalid
        int offset = leaveHistoryRequestDTO.getOffset();
        if (offset < 0) {
            offset = 0; // Default offset to 0
        }

        int limit = leaveHistoryRequestDTO.getLimit();
        if (limit <= 0) {
            limit = 10; // Default limit to 10
        }

        // Set the updated values back to the DTO
        leaveHistoryRequestDTO.setOffset(offset);
        leaveHistoryRequestDTO.setLimit(limit);

        // Create a response map to hold the results
        Map<String, Object> response = new HashMap<>();

        try {
            // Call the service method to get the approved leaves page
            Page<LeaveHistoryResponseDTO> approvedLeavesPage = controller.getPendingApprovalsList(leaveHistoryRequestDTO);

            // If no results were found, handle it gracefully
            if (approvedLeavesPage.getContent().isEmpty()) {
                response.put("message", "No approved leaves found.");
            } else {
                // Prepare the response map
                response.put("pagination", getPaginationDetails(approvedLeavesPage));
                response.put("content", approvedLeavesPage.getContent());
            }

        } catch (RuntimeException e) {
            // General exception handling (e.g., service errors, database issues)
            response.put("error", "An error occurred while retrieving approved leaves: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }

        // Return a successful response
        return ResponseEntity.ok(response);
    }


    //in service or not

    @PutMapping("employee/toggle-status/{empId}")
    public ResponseEntity<String> toggleEmployeeStatus(@PathVariable Long empId) {
        try {
            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
            Optional<Employee> optionalEmployee = employeeRepository.findById(empId);

            if (optionalEmployee.isPresent()) {
                Employee employee = optionalEmployee.get();
                boolean currentStatus = employee.getInService() != null && employee.getInService();
                employee.setInService(!currentStatus); // Toggle status
                employeeRepository.save(employee);

                String statusMessage = employee.getInService() ? "activated" : "deactivated";
                return ResponseEntity.ok("Employee successfully " + statusMessage);
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Employee not found");
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error updating employee status: " + e.getMessage());
        }
    }

    //terminate direct

    @PostMapping("/employee/terminate")
    public ResponseEntity<String> terminateEmployee(@RequestBody TerminationRequest terminationRequest) {
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        // Find the employee
        Employee employee = employeeRepository.findById(terminationRequest.getEmpId()).orElse(null);

        if (employee == null || !employee.getInService()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Employee not found or already terminated");
        }

        // Set employee as inactive
        employee.setInService(false);
      //  employee.setLastWorkingDate(LocalDate.now());

        // Create termination record
        EmployeeTermination termination = new EmployeeTermination();
        termination.setEmpId(employee.getEmpId());
        termination.setTerminationType("Direct Terminated");
       // termination.setSupervisorEmpId(terminationRequest.getSupervisorEmpId());
        termination.setSupervisorEmpId(loggedInEmpId);
        termination.setTerminationReason(terminationRequest.getTerminationReason());
        termination.setLastWorkingDate(LocalDate.now());
        termination.setCreatedBy(loggedInEmpId);


        // Save the termination record and update employee
        employeeRepository.save(employee);
        employeeTerminationRepository.save(termination);

        return ResponseEntity.ok("Employee terminated successfully");
    }

    //resign

    @PostMapping("/employee/resign")
    public ResponseEntity<String> resignEmployee(@RequestBody ResignationRequest resignationRequest) {

        Long empId = tokenService.getEmployeeIdFromToken();

        logger.info("Logged-in Employee ID: {}", empId);
//            logger.info("Resignation Employee ID: {}", resignationStatus.getEmpId());



        // Find the employee
        Employee employee = employeeRepository.findById(empId).orElse(null);
        if (employee == null || !employee.getInService()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Employee not found or already terminated");
        }

        // Check if the employee has a previous resignation record
        List<ResignationStatus> resignationRecords = resignationStatusRepository.findLatestResignationByEmpId(empId);

        if (!resignationRecords.isEmpty()) {
            ResignationStatus latestResignation = resignationRecords.get(0); // Get the most recent record
            if (!"Rejected".equalsIgnoreCase(latestResignation.getStatus())) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body("Resignation already in process or completed. You cannot resign again.");
            }
        }


        Optional<Designation> designation = designationRepository.findById(employee.getDesignation().getId());


        // Ensure the designation is present before accessing its notice period
        LocalDate noticeStartDate;
        LocalDate lastWorkingDate;
        if (designation.isPresent()) {
            Designation empDesignation = designation.get();
            // Get the notice period (BigDecimal) from the designation
            BigDecimal noticePeriod = designation.get().getNoticePeriod();
            if (noticePeriod == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Notice period not found for the employee's designation: " + empDesignation.getName());
            }

            // Get the integer (months) and decimal (days) part separately
            int noticePeriodMonths = noticePeriod.intValue(); // Extracts the integer part (months)
            BigDecimal decimalPart = noticePeriod.remainder(BigDecimal.ONE); // Extracts the decimal part

            // Convert decimal part to days (assuming 0.1 means 1 day, 0.2 means 2 days, etc.)
            long extraDays = decimalPart.multiply(BigDecimal.valueOf(10)).longValue();

            // Set the notice start date as today
            noticeStartDate = LocalDate.now();

            // Calculate the last working date (months + extra days)
            lastWorkingDate = noticeStartDate.plusMonths(noticePeriodMonths).plusDays(extraDays);


        } else {
            throw new EntityNotFoundException("Designation not found for employee");
        }

        // Set resignation data in employee termination table
        EmployeeTermination termination = new EmployeeTermination();
        termination.setEmpId(employee.getEmpId());
        termination.setTerminationType("Resign");
        termination.setTerminationReason(resignationRequest.getTerminationReason());
        termination.setNoticeStartDate(noticeStartDate);
        termination.setLastWorkingDate(lastWorkingDate);
        termination.setCreatedBy(empId);
        termination.setCreatedTime(LocalDateTime.now());

        // Save the resignation record
        EmployeeTermination savedTermination =  employeeTerminationRepository.save(termination);

        // Save resignation request status in ResignationStatus table
        ResignationStatus resignationStatus = new ResignationStatus();
        resignationStatus.setEmpId(employee.getEmpId());
        resignationStatus.setStatus("Pending");  // Setting status as Pending
        resignationStatus.setTerminateId(savedTermination.getTerminationId());  // Save termination ID

        resignationStatus.setNoticeStartDate(noticeStartDate);
        resignationStatus.setLastWorkingDate(lastWorkingDate);
        resignationStatus.setCreatedBy(empId);
        resignationStatus.setCreatedTime(LocalDateTime.now());
        resignationStatusRepository.save(resignationStatus);

        // Notify the higher authority about the resignation
        notifyHigherAuthority(empId, employee.getEmpName());


        return ResponseEntity.ok("Resignation process initiated successfully");
    }

    @GetMapping("/empoyee/download")
    public ResponseEntity<?> downloadData() {

        downloadReportService.generateEmployeesOnLeaveTodayExcelFile(report.getEmployeesOnLeave1());

        return ResponseEntity.ok("The data downloaded successfully");
    }
    private void notifyHigherAuthority(Long empId, String empName) {
        // Finding the supervisor (upper authority) of the logged-in employee
        Long upperId = employeeRepository.findUpperIdByEmpId(empId);
        if (upperId == null) {
            logger.error("No supervisor found for employee ID: " + empId + " to send notification");
            return;
        }

        // Construct notification message
        String notificationTitle = "Employee Resignation Submitted";
        String notificationMessage = "Employee " + empName + " has submitted a resignation request.";

        // Send notification to higher authority
        NotificationDTO notificationDTO = new NotificationDTO(
                notificationTitle,
                notificationMessage,
                "Resignation Request",
                String.valueOf(upperId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(
                upperId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "Resign",
                String.valueOf(empId)
        );

        // Send email notification
        String emailId = employeeRepository.getReferenceById(upperId).getEmail();
        if (emailId != null) {
            EmailData data = new EmailData();
            data.setEmailId(emailId);
            data.setSubject("Resignation Request Submitted");
            data.setMessage("Dear Supervisor,\n\nEmployee " + empName + " has submitted a resignation request. Please review the request at your earliest convenience.");
            emailService.sendEmail(data);
        } else {
            logger.error("No email found for supervisor ID: " + upperId);
        }
    }

//    //employee leave details entry on excel file
//    @PostMapping(value = "/employee/uploads-leave-excel" )
//    public ResponseEntity<String> uploadLeaveExcel(@RequestParam(value = "file") MultipartFile file) {
//        try {
//            logger.info("Received Excel file for leave records: {}", file.getOriginalFilename());
//
//            employeeService.processLeaveExcel(file.getInputStream());
//            return ResponseEntity.ok("Employee leaves processed successfully!");
//        }
//        catch (ParseException e) {
//            logger.error("Parsing error: {}", e.getMessage(), e); // Log parsing exception
//            return ResponseEntity.badRequest().body("Error parsing the Excel file: " + e.getMessage());
//        } catch (IOException e) {
//            logger.error("Error processing leave Excel file: {}", e.getMessage(), e);
//            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Error: " + e.getMessage());
//        } catch (Exception e) {
//            logger.error("Unexpected error: {}", e.getMessage(), e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Unexpected error occurred.");
//        }
//    }
//
//
//
  @PostMapping("/upload-excel")
    public ResponseEntity<String> uploadExcel(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Please upload a valid Excel file.");
            }

            String message = employeeService.uploadExcelFile(file);
            return ResponseEntity.ok(message);

        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Error: " + e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An unexpected error occurred while processing the file.");
        }
    }











}


   /* @PostMapping("/employee/resign")
    public ResponseEntity<String> resignEmployee(@RequestBody ResignationRequest resignationRequest) {

        Long empId = tokenService.getEmployeeIdFromToken();

        // Find the employee
        Employee employee = employeeRepository.findById(empId).orElse(null);
        if (employee == null || !employee.getInService()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Employee not found or already terminated");
        }

        Optional<Designation> designation = designationRepository.findById(employee.getDesignation().getId());

        // Ensure the designation is present before accessing its notice period
        LocalDateTime noticeStartDate;
        LocalDateTime lastWorkingDate;
        if (designation.isPresent()) {
            // Get the notice period (BigDecimal) from the designation
            BigDecimal noticePeriod = designation.get().getNoticePeriod();

            // Convert notice period to minutes (assuming 3.00 means 3 minutes)
            long noticePeriodMinutes = noticePeriod.multiply(BigDecimal.valueOf(60)).longValue();

            // Set the notice start time as now
            noticeStartDate = LocalDateTime.now();

            // Calculate the last working time (add minutes instead of months)
            lastWorkingDate = noticeStartDate.plusMinutes(noticePeriodMinutes);

            System.out.println("Notice Period Minutes: " + noticePeriodMinutes);
            System.out.println("Last Working Date (Time): " + lastWorkingDate);

        } else {
            throw new EntityNotFoundException("Designation not found for employee");
        }

        // Set resignation data in employee termination table
        EmployeeTermination termination = new EmployeeTermination();
        termination.setEmpId(employee.getEmpId());
        termination.setTerminationType("Resign");
        termination.setTerminationReason(resignationRequest.getTerminationReason());
        termination.setNoticeStartDate(noticeStartDate.toLocalDate());  // Convert to LocalDate for storage
        termination.setLastWorkingDate(lastWorkingDate.toLocalDate());
        termination.setCreatedBy(empId);
        termination.setCreatedTime(LocalDateTime.now());

        // Save the resignation record
        employeeTerminationRepository.save(termination);


         // Save resignation request status in ResignationStatus table
        ResignationStatus resignationStatus = new ResignationStatus();
        resignationStatus.setEmpId(employee.getEmpId());
        resignationStatus.setStatus("Pending");  // Setting status as Pending
        resignationStatus.setNoticeStartDate(LocalDate.from(noticeStartDate));
        resignationStatus.setLastWorkingDate(LocalDate.from(lastWorkingDate));
        resignationStatus.setCreatedBy(empId);
        resignationStatus.setCreatedTime(LocalDateTime.now());
        resignationStatusRepository.save(resignationStatus);


        return ResponseEntity.ok("Resignation process initiated successfully");
    }*/







