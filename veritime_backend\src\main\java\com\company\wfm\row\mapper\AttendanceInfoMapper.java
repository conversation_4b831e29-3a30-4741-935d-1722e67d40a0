package com.company.wfm.row.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.jdbc.core.RowMapper;

import com.company.wfm.entity.AttendanceInfo;

public class AttendanceInfoMapper implements RowMapper<AttendanceInfo> {
	@Override
	public AttendanceInfo mapRow(ResultSet rs, int rowNum) throws SQLException {
		return AttendanceInfo
				.builder()
				.id(rs.getString(1))
				.datetime(rs.getObject(2, LocalDateTime.class))
				.date(rs.getObject(3, LocalDate.class))
				.time(rs.getObject(4, Time.class))
				.status(rs.getString(5))
				.device(rs.getString(6))
				.deviceno(rs.getString(7))
				.empname(rs.getString(8))
				.cardNo(rs.getString(9))
				.consumeStatus(rs.getInt(10))
				.build();
	}
}
