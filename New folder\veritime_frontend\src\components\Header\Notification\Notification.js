import React, { useEffect, useRef, useState } from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import "../Notification/Notification.css";
import moment from "moment";
import momentTimezone from "moment-timezone";
import { getRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";

const NotificationCard = ({ notificationList, onEndReached }) => {
  const cardRef = useRef(null);
  const [isVisible, setIsVisible] = useState(true);
  const [readNotifications, setReadNotifications] = useState({});

  const [role, setRole] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("accessToken");
      const userRole = localStorage.getItem("role");

      if (!token || !userRole) {
        localStorage.clear();
        localStorage.setItem("currentPath", window.location.href);
        console.log(userRole);

        window.location.href = "/login?sessionExpired=1";
      } else {
        setRole(userRole);
        console.log(userRole);
      }
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (cardRef.current && !cardRef.current.contains(event.target)) {
        setIsVisible(false);
      }
    };

    const handleScroll = () => {
      if (cardRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = cardRef.current;
        if (scrollTop + clientHeight >= scrollHeight - 10) {
          if (onEndReached) {
            onEndReached();
          }
        }
      }
    };

    if (cardRef.current) {
      cardRef.current.addEventListener("scroll", handleScroll);
    }

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      if (cardRef.current) {
        cardRef.current.removeEventListener("scroll", handleScroll);
      }
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onEndReached]);

  if (!isVisible) return null;

  const getIconByType = (type) => {
    switch (type) {
      case "leave":
        return "/image/leavenotify.png";
      case "shift":
        return "/image/shift.png";
      case "biometric":
        return "/image/biometricnotify.png";
      case "ticket":
        return "/image/ticket.png";

      case "normal":
      default:
        return "/image/notification.png";
    }
  };

  const handleNotificationClick = async (id, type, read_status) => {
    if (read_status === 0) {
      const response = await getRequest(`${API_URLS.READ_NOTIFIACTION}/${id}`);
      if (response) {
        setReadNotifications((prev) => ({ ...prev, [id]: true }));
        handleRedirection(type);
      }
    } else {
      handleRedirection(type);
    }
  };

  // const handleNotificationClick = async (id, type, read_status) => {
  //   // Ensure notificationList exists
  //   if (!notificationList || !notificationList.length) {
  //     console.error("Notifications data is missing or not loaded.");
  //     return;
  //   }

  //   // Find the notification by ID
  //   const notification = notificationList.find((item) => item.id === id);

  //   if (!notification) {
  //     console.error("Notification not found for ID:", id);
  //     return;
  //   }

  //   if (read_status === 0) {
  //     const response = await getRequest(`${API_URLS.READ_NOTIFIACTION}/${id}`);
  //     if (response) {
  //       setReadNotifications((prev) => ({ ...prev, [id]: true }));
  //       handleRedirection(type, notification);
  //     }
  //   } else {
  //     handleRedirection(type, notification);
  //   }
  // };

  // const handleRedirection = (type, notification, username) => {
  //   console.log("User Role:", role);
  //   console.log("Notification Data:", notification);
  //   console.log("Logged-in Username:", username);

  //   const loggedInEmpId = Number(localStorage.getItem("empId"));
  //   console.log("Logged-in Employee ID:", loggedInEmpId);

  //   const sentById = Number(notification?.sentById);
  //   console.log("Notification sentById:", sentById);

  //   if (sentById === loggedInEmpId) {
  //     console.log("Redirecting to View History (own request).");
  //     window.location.href = `/employeeDashboard/viewHistory`;
  //     return;
  //   }

  //   // **If an Admin/Supervisor receives a request from another user, go to Approvals**
  //   if (["supervisor", "admin", "superadmin", "ceo"].includes(role)) {
  //     console.log("Redirecting to Approvals (handling employee request).");
  //     window.location.href = `/approvals`;
  //     return;
  //   }

  //   // **Fallback redirections based on role**
  //   if (["admin", "superadmin", "ceo"].includes(role)) {
  //     switch (type) {
  //       case "Leave_Request":
  //       case "Resign":
  //       case "ATTENDANCE_REGULARIZATION_REQUEST":
  //         window.location.href = `/approvals`;
  //         break;
  //       case "employee":
  //         window.location.href = `/adminDashboard/employee`;
  //         break;
  //       default:
  //         window.location.href = `/approvals`;
  //     }
  //   } else {
  //     // Employees go to view history
  //     console.log("Redirecting to View History (default case).");
  //     window.location.href = `/employeeDashboard/viewHistory`;
  //   }
  // };

  const handleRedirection = (type) => {
    console.log(role, "role");
    if (type?.includes("ticket")) {
      const typeParts = type.split("/");
      if (typeParts.length > 2) {
        const ticketId = btoa(typeParts[2]);
        window.location.href = "/ticket/details/" + ticketId;
        return;
      }
    }

    if (
      role == "admin" ||
      role == "superadmin" ||
      role === "supervisor" ||
      role === "superadmin" ||
      role === "ceo"
    ) {
      // Admins & Supervisors go to approvals
      switch (type) {
        case "Leave_Request":
        case "Resign":
        case "ATTENDANCE_REGULARIZATION_REQUEST":
        case "Attendance Modification ":
          window.location.href = `/approvals`;
          break;
        case "employee":
          window.location.href = `/adminDashboard/employee`;
          break;
        default:
          break;
          //window.location.href = `/approvals`;
      }
    } else {
      //  Employees go to view history
      switch (type) {
        case "biometric":
        case "leave":
        case "shift":
        case "Leave_Request":
        case "Resign":
        case "ATTENDANCE_REGULARIZATION_REQUEST":
        case "Attendance Modification ":
          window.location.href = `/employeeDashboard/viewHistory`;
          break;
        default:
          window.location.href = `/employeeDashboard/viewHistory`;
      }
    }
  };

  // const handleRedirection = (type) => {
  //   console.log("Redirection Type:", type);

  //   if (type?.includes("ticket")) {
  //     const typeParts = type.split("/");
  //     if (typeParts.length > 2) {
  //       const ticketId = btoa(typeParts[2]);
  //       console.log("Extracted Ticket ID:", ticketId);
  //       window.location.href = `/ticket/details/${ticketId}`;
  //     } else {
  //       console.warn(" Ticket ID Not Found in Type");
  //     }
  //   } else {
  //     switch (type) {
  //       case "biometric":
  //       case "leave":
  //       case "shift":
  //       case "Leave_Request":
  //       case "Resign":
  //       case "ATTENDANCE_REGULARIZATION_REQUEST":
  //         console.log(` Redirecting to: /employeeDashboard/viewHistory`);
  //         window.location.href = `/employeeDashboard/viewHistory`;
  //         break;
  //       case "employee":
  //         console.log(" Redirecting to Employee Dashboard");
  //         window.location.href = `/adminDashboard/employee`;
  //         break;
  //       default:
  //         console.warn("Unhandled Notification Type:", type);
  //     }
  //   }
  // };

  const convertToIST = (timestamp) => {
    return momentTimezone
      .tz(timestamp * 1000, "Asia/Kolkata")
      .format("DD-MMM-YYYY hh:mm a");
  };

  const formatCreateTime = (timestamp) => {
    return convertToIST(timestamp);
  };

  return (
    <div className="notification-container">
      <div
        className="card notification-card rounded"
        ref={cardRef}
        style={{
          borderRadius: "20px",
          height: "400px",
          overflowY: "auto",
          marginLeft: "42%",
          marginTop: "12px",
        }}
      >
        <div className="card-body">
          <div className="list-group">
            {notificationList?.map((notification, index) => (
              <div key={index}>
                <a
                  href="#"
                  className="list-group-item list-group-item-action d-flex align-items-center"
                  onClick={() =>
                    handleNotificationClick(
                      notification.id,
                      notification.type,
                      notification.read_status
                    )
                  }
                >
                  <img
                    src={getIconByType(notification.type)}
                    alt="Icon"
                    className="me-3"
                    style={{ width: "24px", height: "24px" }}
                  />
                  <div
                    className="notification-text"
                    style={{
                      fontSize: "14px",
                      fontWeight: "bold",
                      color:
                        readNotifications[notification.id] ||
                        notification.read_status === 1
                          ? "grey"
                          : "black",
                    }}
                  >
                    {notification.title.length > 25
                      ? `${notification.title.substring(0, 25)}...`
                      : notification.title}
                    <br />
                    <small
                      className="notification-body"
                      style={{
                        color:
                          readNotifications[notification.id] ||
                          notification.read_status === 1
                            ? "grey"
                            : "black",
                      }}
                    >
                      {notification.body.length > 20
                        ? `${notification.body.substring(0, 40)}...`
                        : notification.body}
                    </small>
                    <br />
                    <small
                      className="notification-time"
                      style={{
                        color:
                          readNotifications[notification.id] ||
                          notification.read_status === 1
                            ? "grey"
                            : "black",
                      }}
                    >
                      {formatCreateTime(notification.createTime)}
                    </small>
                  </div>
                  <span className="ms-auto text-muted small">&gt;</span>
                </a>
                <hr className="divider" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationCard;
