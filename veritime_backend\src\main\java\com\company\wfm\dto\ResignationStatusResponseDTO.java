package com.company.wfm.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@NoArgsConstructor
@Data
public class ResignationStatusResponseDTO {

    private Long id;
    private Long empId;
    private String employeeName; // Employee Name
    private String imageUrl;
    private LocalDate noticeStartDate;
    private LocalDate lastWorkingDate;
    private String status;
    private Long createdBy;
    private LocalDateTime createdTime;
    private Long updatedBy;
    private LocalDateTime updatedTime;
    private String remark;
    private String createdByName; //
    private String updatedByName;
    private String terminationReason; // New field

    public ResignationStatusResponseDTO(Long id, Long empId, String employeeName, String imageUrl, LocalDate noticeStartDate,
                                        LocalDate lastWorkingDate, String status, Long createdBy, LocalDateTime createdTime, Long updatedBy,
                                        LocalDateTime updatedTime, String remark, String createdByName,String updatedByName, String terminationReason) {
        this.id = id;
        this.empId = empId;
        this.employeeName = employeeName;
        this.imageUrl = imageUrl;
        this.noticeStartDate = noticeStartDate;
        this.lastWorkingDate = lastWorkingDate;
        this.status = status;
        this.createdBy = createdBy;
        this.createdTime = createdTime;
        this.updatedBy = updatedBy;
        this.updatedTime = updatedTime;
        this.remark = remark;
        this.createdByName = createdByName;
        this.updatedByName = updatedByName;
        this.terminationReason = terminationReason; // Include termination reason
    }

}
