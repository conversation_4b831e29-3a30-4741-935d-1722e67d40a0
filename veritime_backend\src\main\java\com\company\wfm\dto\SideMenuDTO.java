package com.company.wfm.dto;

public class SideMenuDTO {
    private String label;
    private String path;
    private String icon;
    private boolean isActive;

    public SideMenuDTO(String label, String path, String icon, boolean isActive) {
        this.label = label;
        this.path = path;
        this.icon = icon;
        this.isActive = isActive;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }
}
