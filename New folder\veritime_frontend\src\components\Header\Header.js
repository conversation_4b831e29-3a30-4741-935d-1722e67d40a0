import React, { useEffect, useState } from "react";
import "./Header.css";
import NotificationCard from "./Notification/Notification";
import ProfileCard from "./ProfileCard/ProfileCard";
import { appConstants } from "../../constants/appConstants";
import { getRequest, postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import OneSignal from "react-onesignal";
import Avatar from "../Avatar";
import { showSuccessAlert, showSuccessAlert2 } from "@/services/alertService";

const Header = ({ toggleMenu }) => {
  const [profile, setProfile] = useState(false);
  const [profileImg, setProfileImg] = useState(false);
  const [username, setUsername] = useState("");
  const [notification, setNotification] = useState(false);
  const [notificationList, setNotificationList] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [notifCount, setTotalNotifCount] = useState(0);
  const [limit, setLimit] = useState(5);
  const [offset, setOffset] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setUsername(localStorage.getItem(appConstants.username));
    const storedProfileImg = localStorage.getItem("profileImg");
    //console.log("Stored Profile Image:", storedProfileImg);
    if (storedProfileImg) {
      setProfileImg(storedProfileImg);
    }

    // setProfileImg(localStorage.getItem("profileImg"));

    fetchNotifications();
    try {
      localStorage.setItem("currentPath", window.location.href);
    } catch (error) {}
  }, [offset]);

  const fetchNotifications = async () => {
    if (loading) return;

    try {
      setLoading(true);
      const notifications = await postRequest(API_URLS.NOTIFICATION_LIST, {
        limit: limit,
        offset: offset,
      });

      setNotificationList((prev) => {
        const newNotifications = notifications?.list.filter(
          (newNotif) => !prev.some((prevNotif) => prevNotif.id === newNotif.id)
        );
        return [...prev, ...newNotifications];
      });

      setUnreadCount(notifications.readcount);
      setTotalNotifCount(notifications?.count);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const onEndReachedNotification = () => {
    if (notificationList?.length < notifCount && !loading) {
      setOffset(offset + 1);
    }
  };
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  const baseURL = "https://wfm-upload-file.s3.ap-south-1.amazonaws.com/";
  //  Debugging - Check Final Image URL
  //console.log("Avatar Image URL:", `${baseURL}${profileImg}`);

  useEffect(() => {
    if (typeof window !== "undefined") {
      OneSignal.init({
        appId: "************************************",
        safari_web_id:
          "web.onesignal.auto.************************************",
        // notifyButton: {
        //   enable: true,
        // },
        // Uncomment the below line to run on localhost. See: https://documentation.onesignal.com/docs/local-testing
        allowLocalhostAsSecureOrigin: true,
      }).then(() => {});

      OneSignal.Notifications.requestPermission();
      OneSignal.Notifications.addEventListener("click", (event) => {});
      // OneSignal.Notifications.addEventListener(
      //   "foregroundWillDisplay",
      //   (event) => {
      //     showSuccessAlert(
      //       "New Notification" + ": " + event?.notification?.title
      //     );
      //     setTimeout(() => {
      //       fetchNotifications();
      //     }, 2000);

      //   }
      // );
      OneSignal.Notifications.addEventListener(
        "foregroundWillDisplay",
        (event) => {
          const newNotification = event?.notification?.title;

          // Check and prevent duplicate notifications
          if (!displayedNotifications.current.has(newNotification)) {
            displayedNotifications.current.add(newNotification);

            showSuccessAlert("New Notification: " + newNotification);
            setTimeout(() => {
              fetchNotifications();
            }, 2000);
          }
        }
      );

      OneSignal.Notifications.addEventListener(
        "permissionChange",
        (permission) => {}
      );

      //   OneSignal.Notifications.addEventListener("click", (event) => {
      //     console.log("Notification clicked:", event);
      //   });
      //   OneSignal.Notifications.addEventListener(
      //     "foregroundWillDisplay",
      //     (event) => {
      //       console.log("Notification will display in foreground:", event);
      //     }
      //   );
      //   OneSignal.Notifications.addEventListener(
      //     "permissionChange",
      //     (permission) => {
      //       console.log("Notification permission changed:", permission);
      //     }
      //   );
    }
  }, []);

  const handleNotification = async () => {
    if (notificationList.length > 0) {
      setNotification(!notification);
    }
  };

  return (
    <div className="header d-flex align-items-center justify-content-between py-2">
      <button className="menu-toggle d-lg-none" onClick={toggleMenu}>
        <img
          src="/image/sidemenu_center.png"
          style={{ width: "30px", height: "40px" }}
          alt="Menu Toggle"
        />
      </button>
      <div
        className="search-bar-container d-flex align-items-center position-relative"
        style={{ justifyContent: "space-between" }}
      >
        <p
          style={{
            textAlign: "center",
            margin: 0,
            padding: 0,
            fontWeight: "bold",
            fontSize: "20px",
          }}
        >
          {localStorage.getItem(appConstants.role)?.toUpperCase()} PORTAL
        </p>
      </div>
      <div
        className="notification mx-3"
        onClick={handleNotification}
        style={{ cursor: "pointer" }}
      >
        <img
          src="/image/notification.png"
          alt="Notifications"
          className="profile-pic-notification"
        />
        {notificationList?.length > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
        {notification && (
          <div className="Notification-card">
            <NotificationCard
              notificationList={notificationList}
              onEndReached={onEndReachedNotification}
            />
          </div>
        )}
      </div>
      <div
        className="profile d-flex align-items-center position-relative"
        onClick={() => setProfile(!profile)}
        style={{ cursor: "pointer" }}
      >
        <span className="username ch1" style={{ textTransform: "capitalize" }}>
          Hello{" "}
          {username
            ? ", " + username?.charAt(0).toUpperCase() + username.slice(1)
            : ""}
        </span>
        <Avatar
          // profileImg={profileImg ? `${baseURL}${profileImg}` : null} // Prevent broken image
          profileImg={`${S3URL}${profileImg}`}
          setProfile={setProfile}
          profile={profile}
          className={"profile-pic rounded-circle"}
          styleInital={{
            width: "40px",
            height: "40px",
            backgroundColor: "#AABFD5",
            color: "#000",
            fontSize: 20,
            pointer: "cursor",
          }}
          styleImg={{
            width: "50px",
            height: "50px",
            pointer: "cursor",
          }}
          intial={username?.charAt(0).toUpperCase()}
        />
        {profile && (
          <div className="profile-card-container">
            <ProfileCard />
          </div>
        )}
      </div>
    </div>
  );
};

export default Header;
