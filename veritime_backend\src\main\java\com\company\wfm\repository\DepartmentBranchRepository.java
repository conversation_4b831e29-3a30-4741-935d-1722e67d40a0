package com.company.wfm.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.DepartmentIdNameDTO;
import com.company.wfm.entity.DepartmentBranch;

import jakarta.transaction.Transactional;

@Repository
public interface DepartmentBranchRepository extends JpaRepository<DepartmentBranch, Long> {

    /*List<DepartmentBranch> findByDepartmentIdIn(List<Long> deptIds);
    List<DepartmentBranch> findByDepartmentId(Long deptId);
    List<DepartmentBranch> findByBranchId(Long deptId);

    void deleteByDepartmentId(Long departmentId);

   DepartmentBranch findByBranchIdAndDepartmentId(Long branchId, Long departmentId);*/

  //  Optional<DepartmentBranch> findByBranchIdAndDepartmentId(Long branchId, Long departmentId);

    Page<DepartmentBranch> findByDepartmentIdIn(List<Long> deptIds, Pageable pageable);
    Page<DepartmentBranch> findByDepartmentId(Long deptId,Pageable pageable);
    List<DepartmentBranch> findByDepartmentId(Long deptId);
    Page<DepartmentBranch> findByBranchId(Long deptId,Pageable pageable);

    Page<DepartmentBranch> findByBranchIdIn(List<Long> deptIds,Pageable pageable);

    void deleteByDepartmentId(Long departmentId);

    DepartmentBranch findByBranchIdAndDepartmentId(Long branchId, Long departmentId);


    @Modifying
    @Transactional
    @Query("DELETE FROM DepartmentBranch db WHERE db.branchId = :branchId")
    void deleteByBranchId(@Param("branchId") Long branchId);


    @Query("SELECT new com.company.wfm.dto.DepartmentIdNameDTO(db.departmentId, d.departmentName) " +
            "FROM DepartmentBranch db " +
            "JOIN Department d ON db.departmentId = d.departmentId " +
            "WHERE db.branchId = :branchId")
    List<DepartmentIdNameDTO> findDepartmentsByBranchIdWithName(@Param("branchId") Long branchId);


    @Query("SELECT new com.company.wfm.dto.DepartmentIdNameDTO(d.departmentId, d.departmentName) FROM Department d")
    List<DepartmentIdNameDTO> findAllDepartmentsWithName();





}
