package com.company.wfm.repository;

import java.sql.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.TicketFilterRequestDTO;
import com.company.wfm.entity.Ticket;

@Repository
public class TicketJdbcTemplateRepository {
    private final JdbcTemplate jdbcTemplate;
    public TicketJdbcTemplateRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }


    private final RowMapper<Ticket> ticketRowMapper = new RowMapper<>() {
        @Override
        public Ticket mapRow(ResultSet rs, int rowNum) throws SQLException {
            Ticket ticket = new Ticket();
            ticket.setTicketId(rs.getLong("TICKET_ID")); // Adjusted to match your entity column names
            ticket.setEmpId(rs.getLong("ticket_emp_id"));
            ticket.setTicketCode(rs.getString("ticket_code"));
            ticket.setTicketSubject(rs.getString("TICKET_SUBJECT"));
            ticket.setTicketMessage(rs.getString("TICKET_MESSAGE"));
            ticket.setStatus(rs.getString("STATUS"));
            ticket.setCategory(rs.getString("CATEGORY"));
            ticket.setFilePath(rs.getString("FILE_PATH"));
            ticket.setCreatedBy(rs.getLong("CREATED_BY")); // Changed to Long type
            ticket.setCreatedTime(rs.getTimestamp("CREATED_TIME").toLocalDateTime());
            ticket.setUpdatedBy(rs.getLong("UPDATED_BY"));
            ticket.setUpdatedTime(rs.getTimestamp("UPDATED_TIME") != null ? rs.getTimestamp("UPDATED_TIME").toLocalDateTime() : null); // Check for null before conversion
            return ticket;
        }
    };

    public Page<Ticket> findAllTickets(Pageable pageable, Long departmentId, Long loggedInEmpId) {
        // Count total tickets based on any of the three conditions
        String countSql = "SELECT COUNT(*) FROM t_ticket t " +
                "WHERE t.DEPARTMENT_ID = ? " +
                "OR t.CREATED_BY = ? " +
                "OR t.ASSIGNED_TO = ?";

        int totalRows = jdbcTemplate.queryForObject(countSql, new Object[]{departmentId, loggedInEmpId, loggedInEmpId}, Integer.class);

        // Fetch tickets with pagination
        String sql = "SELECT t.TICKET_ID, t.EMP_ID AS ticket_emp_id, t.ticket_code, t.TICKET_SUBJECT, t.TICKET_MESSAGE, t.STATUS, " +
                "t.CATEGORY, t.CREATED_BY, t.CREATED_TIME, t.FILE_PATH, t.UPDATED_BY, t.UPDATED_TIME " +
                "FROM t_ticket t " +
                "WHERE t.DEPARTMENT_ID = ? " +
                "OR t.CREATED_BY = ? " +
                "OR t.ASSIGNED_TO = ? " +
                "ORDER BY t.TICKET_ID DESC " +
                "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY";

        List<Ticket> tickets = jdbcTemplate.query(
                sql,
                new Object[]{departmentId, loggedInEmpId, loggedInEmpId, pageable.getOffset(), pageable.getPageSize()},
                ticketRowMapper
        );

        // Return the paginated response
        return new PageImpl<>(tickets, pageable, totalRows);
    }

    public Page<Ticket> findAllTickets1(Pageable pageable, Long departmentId, Long loggedInEmpId, TicketFilterRequestDTO ticketFilterRequestDTO) {
        // If the filter request is null or empty, use the default query
        //|| ticketFilterRequestDTO.getCreatedBy().isEmpty() || ticketFilterRequestDTO.getAssignedTo().isEmpty()
        if (ticketFilterRequestDTO == null ||
                (ticketFilterRequestDTO.getCreatedBy() == null ) &&
                        (ticketFilterRequestDTO.getAssignedTo() == null) &&
                        (ticketFilterRequestDTO.getStatus() == null || ticketFilterRequestDTO.getStatus().isEmpty()) &&
                        (ticketFilterRequestDTO.getCategoryId() == null || ticketFilterRequestDTO.getCategoryId().isEmpty()) &&
                        ticketFilterRequestDTO.getFromDate() == null &&
                        ticketFilterRequestDTO.getToDate() == null) {

            // Default behavior: no filter, just based on department and user-related conditions
            String countSql = "SELECT COUNT(*) FROM t_ticket t " +
                    "WHERE t.DEPARTMENT_ID = ? " +
                    "OR t.CREATED_BY = ? " +
                    "OR t.ASSIGNED_TO = ?";

            int totalRows = jdbcTemplate.queryForObject(countSql, new Object[]{departmentId, loggedInEmpId, loggedInEmpId}, Integer.class);

            String sql = "SELECT t.TICKET_ID, t.EMP_ID AS ticket_emp_id, t.ticket_code, t.TICKET_SUBJECT, t.TICKET_MESSAGE, t.STATUS, " +
                    "t.CATEGORY, t.CREATED_BY, t.CREATED_TIME, t.FILE_PATH, t.UPDATED_BY, t.UPDATED_TIME " +
                    "FROM t_ticket t " +
                    "WHERE t.DEPARTMENT_ID = ? " +
                    "OR t.CREATED_BY = ? " +
                    "OR t.ASSIGNED_TO = ? " +
                    "ORDER BY t.TICKET_ID DESC " +
                    "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY";

            List<Ticket> tickets = jdbcTemplate.query(
                    sql,
                    new Object[]{departmentId, loggedInEmpId, loggedInEmpId, pageable.getOffset(), pageable.getPageSize()},
                    ticketRowMapper
            );

            return new PageImpl<>(tickets, pageable, totalRows);
        } else {
            // Construct the base SQL query with filters
            StringBuilder countSql = new StringBuilder("SELECT COUNT(*) FROM t_ticket t WHERE t.DEPARTMENT_ID = ?");
            StringBuilder sql = new StringBuilder("SELECT t.TICKET_ID, t.EMP_ID AS ticket_emp_id, t.ticket_code, t.TICKET_SUBJECT, t.TICKET_MESSAGE, t.STATUS, " +
                    "t.CATEGORY, t.CREATED_BY, t.CREATED_TIME, t.FILE_PATH, t.UPDATED_BY, t.UPDATED_TIME FROM t_ticket t WHERE t.DEPARTMENT_ID = ?");

            // Parameters list for the query
            List<Object> params = new ArrayList<>();
            params.add(departmentId);

            // Apply filter for createdBy (me or other)
            if ("me".equals(String.valueOf(ticketFilterRequestDTO.getCreatedBy()))) {
                countSql.append(" AND t.CREATED_BY = ?");
                sql.append(" AND t.CREATED_BY = ?");
                params.add(loggedInEmpId);
            } else if ("other".equals(String.valueOf(ticketFilterRequestDTO.getCreatedBy()))) {
                countSql.append(" AND t.CREATED_BY != ?");
                sql.append(" AND t.CREATED_BY != ?");
                params.add(loggedInEmpId);
            }

            // Apply filter for assignedTo (me or other)
            if ("me".equals(String.valueOf(ticketFilterRequestDTO.getAssignedTo()))) {
                countSql.append(" AND t.ASSIGNED_TO = ?");
                sql.append(" AND t.ASSIGNED_TO = ?");
                params.add(loggedInEmpId);
            } else if ("other".equals(String.valueOf(ticketFilterRequestDTO.getAssignedTo()))) {
                countSql.append(" AND t.ASSIGNED_TO != ?");
                sql.append(" AND t.ASSIGNED_TO != ?");
                params.add(loggedInEmpId);
            }

            // Apply filter for status
            if (ticketFilterRequestDTO.getStatus() != null && !ticketFilterRequestDTO.getStatus().isEmpty()) {
                countSql.append(" AND t.STATUS = ?");
                sql.append(" AND t.STATUS = ?");
                params.add(ticketFilterRequestDTO.getStatus());
            }

            // Apply filter for categoryId
            if (ticketFilterRequestDTO.getCategoryId() != null && !ticketFilterRequestDTO.getCategoryId().isEmpty()) {
                countSql.append(" AND t.CATEGORY = ?");
                sql.append(" AND t.CATEGORY = ?");
                params.add(ticketFilterRequestDTO.getCategoryId());
            }

            // Apply filter for fromDate
            if (ticketFilterRequestDTO.getFromDate() != null) {
                LocalDate fromDate = ticketFilterRequestDTO.getFromDate(); // Directly use LocalDate
                countSql.append(" AND CAST(t.CREATED_TIME AS DATE) >= ?");
                sql.append(" AND CAST(t.CREATED_TIME AS DATE) >= ?");
                params.add(Date.valueOf(fromDate)); // Convert to java.sql.Date
            }

            if (ticketFilterRequestDTO.getToDate() != null) {
                LocalDate toDate = ticketFilterRequestDTO.getToDate(); // Directly use LocalDate
                countSql.append(" AND CAST(t.CREATED_TIME AS DATE) <= ?");
                sql.append(" AND CAST(t.CREATED_TIME AS DATE) <= ?");
                params.add(Date.valueOf(toDate)); // Convert to java.sql.Date
            }

            // Execute the count query
            int totalRows = Optional.ofNullable(jdbcTemplate.queryForObject(countSql.toString(), params.toArray(), Integer.class)).orElse(0);

            // Execute the main query for fetching tickets with pagination
            sql.append(" ORDER BY t.TICKET_ID DESC OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
            params.add(pageable.getOffset());
            params.add(pageable.getPageSize());

            List<Ticket> tickets = jdbcTemplate.query(
                    sql.toString(),
                    params.toArray(),
                    ticketRowMapper
            );

            // Return the paginated response
            return new PageImpl<>(tickets, pageable, totalRows);
        }
    }




}
