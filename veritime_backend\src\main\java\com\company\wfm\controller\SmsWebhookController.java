package com.company.wfm.controller;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.entity.SmsDeliveryStatus;
import com.company.wfm.repository.SmsDeliveryStatusRepository;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/sms/response/webhook")
@CrossOrigin(origins = "*")
@Slf4j
public class SmsWebhookController {

    private final SmsDeliveryStatusRepository smsDeliveryStatusRepository;

    @Autowired
    public SmsWebhookController(SmsDeliveryStatusRepository smsDeliveryStatusRepository) {
        this.smsDeliveryStatusRepository = smsDeliveryStatusRepository;
    }

	@GetMapping("/sms-delivery-report")
	public ResponseEntity<String> receiveSmsDeliveryReport(@RequestParam("FN") int fromNumber,
			@RequestParam("TN") int toNumber, @RequestParam("SC") int success, @RequestParam("ST") String smscStatus,
			@RequestParam("RF") int referenceNumber, @RequestParam("TS") LocalDateTime timestamp) {

		try {
			// Log the received parameters
			log.info("Received SMS Delivery Report: From={}, To={}, Success={}, Status={}, Reference={}, Timestamp={}",
					fromNumber, toNumber, success, smscStatus, referenceNumber, timestamp);

			// Create and save entity
			SmsDeliveryStatus smsDeliveryStatus = new SmsDeliveryStatus();
			smsDeliveryStatus.setFromNumber(fromNumber);
			smsDeliveryStatus.setToNumber(toNumber);
			smsDeliveryStatus.setSuccess(success);
			smsDeliveryStatus.setSmscStatus(smscStatus);
			smsDeliveryStatus.setReferenceNumber(referenceNumber);
			smsDeliveryStatus.setTimestamp(timestamp);

			smsDeliveryStatusRepository.save(smsDeliveryStatus);

			// Return 200 OK response
			return ResponseEntity.ok("Delivery report received and stored successfully");
		} catch (Exception e) {
			log.error("Error processing SMS delivery report", e);
			return ResponseEntity.status(500).body("Failed to process delivery report");
		}
	}
}
