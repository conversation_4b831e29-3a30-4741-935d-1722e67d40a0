package com.company.wfm.repository;


import java.util.List;
import java.util.Optional;

import com.company.wfm.entity.Employee;
import com.company.wfm.entity.LeaveMaster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.EmployeeLeaveBalance;

@Repository
public interface EmployeeLeaveBalanceRepository extends JpaRepository<EmployeeLeaveBalance, Long> {
    Optional<EmployeeLeaveBalance> findByEmpIdAndLeaveId(Long empId, Long leaveId);

    List<EmployeeLeaveBalance> findByEmpId(Long empId);

 /// ////////

// Optional<EmployeeLeaveBalance> findByEmployeeAndLeaveType(Employee employee, LeaveMaster leaveMaster);

//    Optional<EmployeeLeaveBalance> findByEmployeeAndLeaveType(Long empId, Long leaveTypeId);


//    List<EmployeeLeaveBalance> findByEmpCode(String empCode);







}
/////
