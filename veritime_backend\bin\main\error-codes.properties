CCF000001=Parameter error.
CCF000004=Database operation failed.
CCF000005=No permission to operate this resource.
CCF000007=Internal calling failed.
CCF038002=Name length exceeded limit.
CCF038005=Name cannot contain special characters.
CCF038007=User is not assigned with the access level.
CCF038009=Person group does not exist.
CCF038014=Invalid first name of person.
CCF038015=Invalid family name of person.
CCF038016=Invalid person ID.
CCF038017=Invalid person's full name.
CCF038019=Invalid person email.
CCF038020=Invalid person's phone No.
CCF038021=Invalid person's description.
CCF038022=Invalid person's validity.
CCF038023=Person does not exist.
CCF038024=Person ID already exists.
CCF038025=Person ID does not exist.
CCF038026=Person's picture does not exist.
CCF038028=Saving person's picture failed.
CCF038029=No more persons are allowed.
CCF038031=Adding person failed.
CCF038032=No more cards for a person are allowed.
CCF038034=Duplicate card No.
CCF038035=Card does not exist.
CCF038036=Card No. length exceeded limit.
CCF038037=Card No. is empty
CCF038038=Invalid card No.
CCF038039=No more fingerprints for a person are allowed.
CCF038042=Fingerprint does not exist.
CCF038052=Email address already exist (remote calling error code).
CCF038055=Duplicate person's PIN code.
CCF038059=Updating card failed.
CCF038060=Updating fingerprint failed.
CCF038061=Updating additional attribute failed.
CCF038062=Updating picture failed.
CCF038064=No matched department by search condition.
CCF038065=Device does not exist.
CCF038066=Fingerprint data length exceeded limit.
CCF038067=Duplicate fingerprint.
CCF038068=Duplicate profile picture.
CCF038084=Collecting card No. by device failed.
CCF038085=Collecting fingerprint by device failed.
CCF038089=Phone No. has been occupied.
CCF021103=Area name already exists.
CCF021307=Device does not exist.
CCF021008=Not supported by current device version.
EVZ20002=Device does not exist.
EVZ20007=Device offline.
EVZ20008=Device response timed out.
EVZ20010=Incorrect device verification code.
EVZ20013=The device has been added by another account.
EVZ20014=Incorrect device serial No.
EVZ20017=The device has been added by yourself.
LAP000001=Parameter error.
LAP000004=Service calling blow exception.
VMS000000=System error.
VMS000001=Parameter error.
VMS000003=No resource found or resource does not exist.
VMS000004=Database operation failed.
VMS000005=No permission to operate this resource.
VMS000007=Internal calling failed.
VMS000008=Redis operation failed.
VMS000009=Concurrent requests (distributed lock is enabled).
VMS001000=Gateway error.
VMS001001=Service instance unavailable. Service is overloaded.
VMS003001=This email has been registered on the platform.
VMS003002=The user does not exist.
VMS004004=Invalid role ID.
VMS004005=No permission.
VMS020007=Get information from Hik- Connect.
VMS021008=The device version is not supported.
VMS021102=The area already exists.
VMS021103=The area name already exits.
VMS021104=No more areas can be added.
VMS021105=Maximum area levels reached.
VMS021106=No more logical resources can be added to the VMS system.
VMS021107=No more logical resources can be added to the area.
VMS021108=Duplicate license plate No.
VMS021109=Device already linked with another vehicle.
VMS021301=Adding the device...
VMS021302=The device has been added.
VMS021303=The device already exits in the system.
VMS021305=The device name already exists.
VMS021306=Adding the device timed out.
VMS021307=The device does not exist.
VMS021308=Refreshing the device...
VMS021309=Incorrect device type.
VMS021311=No more devices can be added.
VMS021314=Device offline.
VMS021315=Invalid serial No.
VMS021316=The device belongs to another user.
VMS021337=Importing resource failed.
VMS023001=Sending the email failed.
VMS023002=Duplicate color.
VMS023003=The priority name already exits.
VMS023004=The alarm category name already exists.
VMS023005=The email template name already exists.
VMS023006=The system email is not configured.
VMS023007=Repeatedly acknowledging the alarm.
VMS023008=The alarm configuration already exists.
VMS038005=Number of persons in the room exceeds limit.
VMS038009=Adding person failed.
VMS038011=Wrong person type added to the room.
VMS038012=No permission for adding persons to the room.
VMS038014=Resident already in the room.
VMS038019=The calling record does not exist.
VMS038026=Email address already exists.
VMS038027=Call answered.
VMS038028=Resident does not exist.
VMS038029=Call from device canceled.
VMS040002=Duplicate name.
VMS040003=Name length exceeded limit.
VMS040004=Name is empty
VMS040005=Name contains special characters.
VMS040011=No more access schedule templates are allowed.
VMS040012=The default schedule template cannot be modified
VMS040013=No more access levels are allowed on the platform.
VMS040014=No more holidays are allowed.
VMS040015=Select at least one access point.
VMS040016=Access level does not exist.
VMS040017=No more than 4 access schedules can be linked with one person for an access point.
VMS040018=No valid credential for the person.
VMS040020=The current device is in applying process... Please try again later.
VMS040101=No more schedule templates are allowed on device.
VMS040102=No more holiday groups are allowed on device.
VMS040103=No more holidays are allowed on device.
VMS040104=No more weekly schedules are allowed on device.
VMS040105=Access group settings are not applied to device.
VMS040106=Fingerprint length mismatch.
VMS040107=No valid card reader for the fingerprint currently.
VMS040108=Applying fingerprint failed.
VMS040109=Fingerprint module offline
VMS040110=Quality of fingerprint is low.
VMS040111=No more fingerprints are allowed on card reader.
VMS040112=Duplicate fingerprint.
VMS040113=Duplicate fingerprint ID.
VMS040114=Invalid fingerprint ID.
VMS040115=Fingerprint module does not require configuration.
VMS040116=Device does not support applying fingerprints
VMS040117=Fingerprint card reader version too old (employee No. not supported).
VMS040118=Fingerprint type not supported.
VMS040119=No valid card reader for the face picture currently.
VMS040120=Visit schedule template does not exist.
VMS040121=Password length mismatch.
VMS040122=The current device only supports 6-digit password.
THD_ISAPI_0x20000004=Device busy.
THD_ISAPI_0x60000003=XML message error (e.g., incorrect value of node).
THD_ISAPI_0x60000017=JSON message error.
THD_ISAPI_0x60000019=Missing parameters in the message.
OPEN000000=System internal errors.
OPEN000001=The AK does not exist.
OPEN000002=Incorrect SK.
OPEN000003=The user does not exists.
OPEN000004=The system does not exist.
OPEN000005=Linking the HCC account with the AK and SK failed.
OPEN000006=Token expired.
OPEN000007=Token exception.
OPEN000008=Parameter conversion exception.
OPEN000009=Network exception.
OPEN000010=Parameter verification exception.
OPEN000011=The requested resources are not available yet.
OPEN000012=The message submission ID (batchId) does not exist.
OPEN000013=The alarm offset AK does not exist.
OPEN000014=Incorrect format of alarm picture URL.
OPEN000015=Alarm message conversion failed.
OPEN000016=The alarm information is not subscribed.
OPEN000017=Adding resource failed.
OPEN000018=Hik-Connect server error.
OPEN000019=Hik-Connect server response error.
OPEN000020=Saving file failed.
OPEN000021=No permission for viewing the resource
OPEN000022=No permission for managing the source.
OPEN000023=Reading Dynamo failed.
OPEN000024=Number of resources for request exceeded limit.
OPEN000025=Number of records on each page exceeded limit.
OPEN000026=Incorrect time format.
OPEN000027=The time range set for search exceeded limit.
OPEN000028=Area ID is empty.
OPEN000029=Incorrect request volume.
OPEN000030=Start time of search is later than end time of search.
OPEN000031=Area does not exist.
OPEN000501=Device does not exist.
OPEN000502=Cloud storage configuration information not found.
OPEN000503=Camera resource does not exist.
OPEN000504=Adding area failed.
OPEN000505=Adding resource failed.
OPEN000510=Area already exists.
OPEN000511=Resource already exists.
OPEN000550=No response to request.
OPEN000552=Error returned from request.
OPEN000554=Device offline.
OPEN000555=Device response timed out.
OPEN000556=Device capturing failed.
THD_ISAPI_0x6000001C=Containing invalid characters.
THD_ISAPI_0x6000001D=Name too long.
THD_ISAPI_0x60000025=The parameter value exceeded limit.
THD_ISAPI_0x6000002D=Number of face picture libraries exceeded limit.
THD_ISAPI_0x6000002E=Number of face pictures exceeded limit.
THD_ISAPI_0x6000002F=Picture data detection failed.
THD_ISAPI_0x60000030=Invalid face picture data PID.
THD_ISAPI_0x60000031=Invalid face picture library ID.
THD_ISAPI_0x60000032=Data library version of face picture library mismatch.
THD_ISAPI_0x60000033=Incorrect verification code.
THD_ISAPI_0x60000034=Face picture library data error.
THD_ISAPI_0x60000035=Multi-face target error (multiple valid targets exist among pictures imported to face picture library, and are not differentiated by coordinates).
THD_ISAPI_0x60000036=No target detected in the picture.
THD_ISAPI_0x60000037=Sub-picture modeling failed.
THD_ISAPI_0x60000038=Incorrect name.
THD_ISAPI_0x60000039=Name already exists.
THD_ISAPI_0x6000003A=Importing pictures to face picture library...
THD_ISAPI_0x6000003B=Invalid picture format.
THD_ISAPI_0x6000003C=Invalid picture resolution.
THD_ISAPI_0x6000003D=Picture size too large.
THD_ISAPI_0x6000003E=Picture size too small.
THD_ISAPI_0x6000005F=The maximum length of password is 16.
THD_ISAPI_0x60000060=Password mismatch.
THD_ISAPI_0x60000063=Deleting failed.
THD_ISAPI_0x60000068=No data returned from face picture comparison search.
THD_ISAPI_0x60000069=No data returned from face picture library search.
THD_ISAPI_0x6000006A=Concurrent processing is not supported. The device is performing operation of picture modeling.
THD_ISAPI_0x6000006B=No data to be modeled in face picture library.
THD_ISAPI_0x6000006C=The upper layer applied duplicate custom face picture library ID.
THD_ISAPI_0x6000006D=The upper layer applied duplicate custom person ID.
THD_ISAPI_0x6000006E=Dowloading URL failed.
THD_ISAPI_0x6000006F=Dowloading URL not started yet.
THD_ISAPI_0x60000071=Internal device modeling error.
THD_ISAPI_0x60000072=Face picture modeling error.
THD_ISAPI_0x60000073=Face picture categorization error.
THD_ISAPI_0x60000074=Face feature modeling error.
THD_ISAPI_0x60000075=Face picture analysis modeling error.
THD_ISAPI_0x60000082=Pupil distance parameter error (the min. value is larger than the max. value, or the configured max. value is smaller than the min value).
THD_ISAPI_0x60000083=Duplicate alarm linkage name.
THD_ISAPI_0x60006000=Overlapped time periods.
THD_ISAPI_0x60006001=Overlapped holiday schedules.
THD_ISAPI_0x60006002=Card No. is not sorted in order.
THD_ISAPI_0x60006003=Card No. does not exist.
THD_ISAPI_0x60006004=Incorrect card No.
THD_ISAPI_0x60006008=No more cards are allowed.
THD_ISAPI_0x60006009=Downloading holiday group failed.
THD_ISAPI_0x60006014=Multiple cards for a single person is not supported.
THD_ISAPI_0x60006015=Face picture to be deleted does not exist.
THD_ISAPI_0x60006019=Incorrect finger No.
THD_ISAPI_0x6000601a=Incorrect fingerprint type.
THD_ISAPI_0x6000601b=Fingerprint is not linked with employee No. or card
THD_ISAPI_0x6000601c=Fingerprint already exists.
THD_ISAPI_0x6000601d=Fingerprint module does not require configuration.
THD_ISAPI_0x6000601e=Card reader does not support deleting fingerprint by pressing fingerprint ID.
THD_ISAPI_0x6000601f=Employee No. does not exist.
THD_ISAPI_0x60006020=Employee No. already exists.
THD_ISAPI_0x60006033=Card No. already exists.
THD_ISAPI_0x60006034=The length of fingerprint data is 0.
THD_ISAPI_0x60006035=Incorrect card reader No.
THD_ISAPI_0x60006036=Incorrect employee No.
THD_ISAPI_0x60006039=Admin password not configured yet (for prompting on configurating activation code on device).
THD_ISAPI_0x6000603a=Picture does not exist.
THD_ISAPI_0x6000603b=Ocular distance too narrow.
THD_ISAPI_0x6000603c=Face picture data less than 1 KB.
THD_ISAPI_0x6000603d=Picture information verification failed.
THD_ISAPI_0x6000603e=Face rating failed.
THD_ISAPI_0x6000603f=Converting QR code picture failed.