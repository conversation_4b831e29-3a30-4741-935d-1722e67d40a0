package com.company.wfm.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/widget-sequence")
@CrossOrigin(origins = "*")
public class WidgetSequenceController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @GetMapping("/roles")
    public List<Map<String, Object>> getRoles() {
        return jdbcTemplate.queryForList("SELECT DISTINCT(role_name) FROM role_widgets WHERE is_active = 1");
    }

    @PostMapping("/updateSequence")
    public String updateWidgetSequence(@RequestBody Map<String, Object> payload) {
        String roleName = (String) payload.get("roleName");
        List<Map<String, Object>> widgets = (List<Map<String, Object>>) payload.get("widgets");

        for (Map<String, Object> widget : widgets) {
            String widgetName = (String) widget.get("widget_name");
            Integer sequence = (Integer) widget.get("sequence");

            // Use a parameterized query to get the widgetId safely
            Long widgetId = jdbcTemplate.queryForObject("SELECT widget_id FROM t_widgets WHERE widget_name = ?",
                    Long.class, widgetName);

            // Update query using placeholders for parameters to prevent SQL injection
            String updateQuery = "UPDATE role_widgets SET sequence = ? WHERE widget_id = ? AND role_name = ?";

            // Execute the update with parameters
            jdbcTemplate.update(updateQuery, sequence, widgetId, roleName);
        }
        return "Widget sequence updated successfully!";
    }

    @GetMapping("/widgets")
    public List<Map<String, Object>> getWidgets(@RequestParam String role) {
        String query =
                "SELECT w.widget_name, w.description, w.icon, w.backgroundColor, w.onClickPath, rw.sequence, w.isCommon " +
                        "FROM t_widgets w " +
                        "JOIN role_widgets rw ON w.widget_id = rw.widget_id " +
                        "WHERE rw.role_name = ? AND w.is_active = 1 AND rw.is_active = 1 " +
                        "ORDER BY rw.sequence ASC";

        return jdbcTemplate.query(query, new Object[]{role}, (rs, rowNum) -> {
            Map<String, Object> widget = new HashMap<>();
            widget.put("widget_name", rs.getString("widget_name"));
            widget.put("description", rs.getString("description"));
            widget.put("icon", rs.getString("icon"));
            widget.put("backgroundColor", rs.getString("backgroundColor"));
            widget.put("onClickPath", rs.getString("onClickPath"));
            widget.put("sequence", rs.getInt("sequence"));
            widget.put("componentType", rs.getString("isCommon"));
            return widget;
        });
    }
}
