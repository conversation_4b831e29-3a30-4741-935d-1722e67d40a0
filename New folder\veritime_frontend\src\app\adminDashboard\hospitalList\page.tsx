"use client";
import { useEffect, useState } from "react";
import { Container, <PERSON>, Col, Button } from "react-bootstrap";
import { styled } from "@mui/system";
import TableFilter from "../../../common-components/TableFilter2.js";
//import CreateDesignationModal from '../modals/CreateDesignationModal';
//import CreateScheduleModal from '../modals/CreateScheduleModal';
import Layout from "@/components/Layout";
import { API_URLS } from "@/constants/apiConstants.js";
import { getRequest, postRequest } from "@/services/apiService.js";
import { appConstants } from "@/constants/appConstants.js";
// import ProvinceDropdown from '@/components/Dropdowns/ProvinceDropdown';
// import DistrictDropdown from '@/components/Dropdowns/DistrictDropdown';
// import SubDistrictDropdown from '@/components/Dropdowns/SubDistrictDropdown';
import CreateExcelModal from "../modals/CreateExcelModal";
import CreateExcelModalHospital from "../modals/CreateExcelModalHospital";
import CreateHospitalModal2 from "../modals/CreateHospitalModal2";
import CreateHospitalModal from "../modals/CreateHospitalModal2";
import VisibilityIcon from "@mui/icons-material/Visibility";
import HospitalDetailModal from "../modals/HospitalDetailModal";
import EditIcon from "@mui/icons-material/Edit";

const Hospital = ({ toggleMenu, expanded }: any) => {
  const [username, setUsername] = useState("");
  const [role, setRole] = useState("");
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);
  const [showHospitalModal, setShowHospitalModal] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem(appConstants?.username);
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
  }, []);
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedRole = localStorage.getItem(appConstants?.role);
      if (storedRole) {
        setRole(storedRole);
      }
    }
  }, []);

  const [openHospitalDetailModal, setOpenHospitalDetailModal] = useState(false);
  const [selectedHospital, setSelectedHospital] = useState<any>("");

  const handleEyeIconClick = (hospital: any) => {
    setSelectedHospital(hospital); // Set the selected hospital details
    setOpenHospitalDetailModal(true); // Open the modal
  };

  const handleHospitalDetailModalClose = () => {
    setOpenHospitalDetailModal(false);
    setSelectedHospital("");
  };

  const [province, setProvince] = useState([]);
  const [district, setDistrict] = useState([]);
  const [subdistrict, setSubDistrict] = useState([]);
  const [selectedProvince, setSelectedProvince] = useState([]);
  const [selectedDistrict, setSelectedDistrict] = useState([]);
  const [selectedSubDistrict, setSelectedSubDistrict] = useState([]);

  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const provincesData = await getRequest(API_URLS.GET_PROVIENCE);
        if (provincesData) setProvince(provincesData);
        // const districtsData = await getRequest(API_URLS.GET_DISTRICT);
        // if (districtsData)
        //   setDistrict(districtsData);
        // const subdistrictData = await getRequest(API_URLS.GET_SUB_DISTRICT);
        // if (subdistrictData)
        //   setSubDistrict(subdistrictData);
        // console.log('subdistrictData::: ', subdistrictData)
      } catch (error) {}
    };

    fetchFilterOptions();
  }, []);

  useEffect(() => {
    const fetchFilteredData = async () => {
      try {
        const filterParams = {
          province: selectedProvince,
          district: selectedDistrict,
          subDistrict: selectedSubDistrict,
        };
        // const response = await postdata('employee/filter', filterParams);
        // setData(response.data);
      } catch (error) {}
    };

    fetchFilteredData();
  }, [selectedProvince, selectedDistrict, selectedSubDistrict]);

  const handleProvinceChange = (selected: any) => setSelectedProvince(selected);
  const handleDistrictChange = (selected: any) => setSelectedDistrict(selected);
  const handleSubDistrictChange = (selected: any) =>
    setSelectedSubDistrict(selected);

  const [anchorEl, setAnchorEl] = useState(null);
  const [data, setData] = useState<any[]>([]);
  const [totalItems, setTotalItems] = useState(0);

  const [rowToEdit, setRowToEdit] = useState<any>({});

  const handleHospitalModalOpen = () => {
    setRowToEdit(null);
    setShowHospitalModal(true);
  };

  const handleHospitalModalClose = () => {
    setRowToEdit(null);
    setShowHospitalModal(false);
  };

  const handleEdit = (row: any) => {
    //setShowModal(true);
    setShowHospitalModal(true);
    setRowToEdit(row);
  };
  const handleCloseModal = () => {
    setShowHospitalModal(false);
    //setShowModal(false);
    setRowToEdit(null);
  };

  useEffect(() => {
    fetchItems();
  }, [offset, limit]);

  // const totalCount = Math.ceil(totalItems / pageSize);

  const fetchItems = async () => {
    try {
      const request = {
        provinceIds: [],
        districtIds: [],
        subDistrictIds: [],
        query: "",
        offset: offset,
        limit: limit,
      };
      const data = await postRequest(API_URLS.GET_HOSPITAL_LIST, request);
      console.log("API Response HOSPITALData: ", data); // ✅ Log the response

      if (data) {
        setData(data.content);

        setTotalItems(data.totalElements);
      }
    } catch (error) {}
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const handleClick = (event: any) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleModalClose = () => {
    setShowExcelUploadModal(false);
  };

  // const [openDesigntionModal, setDesigntionModal] = useState(false);
  // const handleDesigntionModalOpen = () => setDesigntionModal(true);
  // const handleDesigntionModalClose = () => setDesigntionModal(false);

  // const [openSchedulenModal, setScheduleModal] = useState(false);
  // const handleScheduleModalOpen = () => setScheduleModal(true);
  // const handleScheduleModalClose = () => setScheduleModal(false);
  const [showExcelUploadModal, setShowExcelUploadModal] = useState(false);

  const columns = [
    {
      field: "srno",
      headerName: "Sr. No",
      width: 150,
      renderCell: (params: any) => {
        return offset * limit + params.row.id;
      },
    },
    {
      field: "name",
      headerName: "Hospital Name",
      width: 200,
    },
    { field: "province", headerName: "Province", width: 150 },
    { field: "district", headerName: "District", width: 200 },
    { field: "subDistrict", headerName: "Sub District", width: 150 },

    {
      field: "action",
      headerName: "Action",
      width: 200,
      renderCell: (params: any) => {
        return (
          <>
            <button
              style={{
                backgroundColor: "transparent",
                border: "none",
                color: "black",
              }}
            >
              <VisibilityIcon
                style={{ cursor: "pointer", marginRight: "10px" }}
                onClick={() => handleEyeIconClick(params.row)}
              />
            </button>

            <button
              onClick={() => handleEdit(params.row)}
              style={{
                backgroundColor: "transparent",
                border: "none",
                color: "black",
              }}
            >
              <EditIcon />
            </button>

            {/* 
            //require branchid also to create employee which is not available here. kindly do not add this button
            <button
              className="btn btn-small btn-primary"
              style={{ marginRight: "8px" }}
              onClick={() =>
                (window.location.href =
                  "/adminDashboard/employee/empDetails?hospitalId=" +
                  btoa(params.row.id))
              }
            >
              Create Emp
            </button> */}

            {/* Report Button */}
            {/* <button className="btn btn-small btn-secondary">Report</button> */}
          </>
        );
      },
    },
  ];
  const rows = data.map((hospital, index) => ({
    id: index + 1,
    hospital_id: hospital.id,
    name: hospital.name,
    type: hospital.type,
    province: hospital.province,
    provinceId: hospital.provinceId,
    district: hospital.district,
    districtId: hospital.districtId,
    subDistrict: hospital.subDistrict,
    subDistrictId: hospital.subDistrictId,
    address: hospital.address,
    facility: hospital.facility || "Not Available",
    createdAt: hospital.createdAt || "Not Available",
    updatedBy: hospital.updatedBy || "Not Available",
  }));

  const handlePageChange = (newPage: any) => {
    // const newOffset = newPage * limit;
    // setOffset(newOffset);
    // fetchItems();
    setOffset(newPage?.page);
    setLimit(newPage?.pageSize);
  };

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  // function setShowModal(arg0: boolean): void {
  //   throw new Error("Function not implemented.");
  // }

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <div className="d-flex justify-content-between">
          <div style={{ marginLeft: "20px" }}>
            <h4>{"Hospital List"}</h4>
            {/* <span style={{ fontSize: "12px", color: "grey" }}>
              Hi,{" "}
              {username
                ? username?.charAt(0).toUpperCase() + username.slice(1)
                : ""}
              . Your organization's Hospitals are listed here
            </span> */}
          </div>
          <div>
            <Button
              style={{
                background: "green",
                marginBottom: "10px",
                marginRight: "10px",
              }}
              onClick={() => setShowExcelUploadModal(true)}
            >
              Upload Hospitals
            </Button>
          </div>
        </div>

        {/*<Row>
              <Button
                style={{
                  width: '100px', margin: 'auto',
                  marginRight: '0px'
                }}
                aria-describedby={id}
                variant="contained"
                className={"carousal-container"}
                onClick={handleClick}
              >
                <FilterAltIcon />
              </Button>
              <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
              >
                <div>
                  <select className="filter-select">
                    <option disabled selected>Select Branch</option>
                    <option>Account</option>
                    <option>IT</option>
                    <option>Pharmacy</option>
                    <option>Nursing</option>
                  </select>
                </div>
                <div>
                  <select className="filter-select">
                    <option disabled selected>Select Department</option>
                    <option>Account</option>
                    <option>IT</option>
                    <option>Pharmacy</option>
                    <option>Nursing</option>
                  </select>
                </div>
                <hr />
                <div>
                  <select className="filter-select">
                    <option disabled selected>Select Designation</option>
                    <option>Junior HR</option>
                    <option>Pharmacist</option>
                    <option>Doctor</option>
                    <option>Nurse</option>
                  </select>
                </div>
              </Popover>
            </Row> */}

        {/* <Col lg={12} md={12} sm={12}>
            <Row className='filter-container' style={{ marginLeft: "100px" }}>
              <Col lg={3} md={3} sm={6}>
                <ProvinceDropdown provinces={provinces} onChange={handleProvinceChange} isMulti={true} />
              </Col>
              <Col lg={3} md={3} sm={6}>
                <DistrictDropdown districts={districts} isMulti={true} onChange={handleDistrictChange} />
              </Col>
              <Col lg={3} md={3} sm={6}>
                <SubDistrictDropdown subDistricts={subDistricts} isMulti={true} onChange={handleSubDistrictChange} />
              </Col>
              <Col lg={2} md={3} sm={6}>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search..."
                  className="form-control"
                  style={{ marginBottom: '10px' }}
                />
              </Col>
              <Col lg={1} md={3} sm={12} className="d-flex justify-content-start">
                <button onClick={fetchItems} className="btn btn-primary" style={{ width: "60px", maxHeight: "80%" }}>Go</button>
              </Col>
            </Row>
          </Col> */}

        <Row className="my-3" style={{ marginLeft: "10px" }}>
          <TableFilter
            columns={columns}
            rows={rows}
            onPageChange={handlePageChange}
            totalCount={totalItems}
            pageSize={limit}
          />
        </Row>
        {/* {openDesigntionModal && <CreateDesignationModal show={openDesigntionModal} handleClose={handleDesigntionModalClose} />}
        {openSchedulenModal && <CreateScheduleModal show={openSchedulenModal} handleClose={handleScheduleModalClose} />} */}
        {showExcelUploadModal && (
          <CreateExcelModalHospital
            handleClose={handleModalClose}
            show={showExcelUploadModal}
            fetchItems={fetchItems} // ✅ Pass this function
            downloadType="hospital"
            // holiday={selectedHoliday}
            // fetchHolidays={fetchHolidays}
          />
        )}

        {showHospitalModal && (
          <CreateHospitalModal
            show={showHospitalModal}
            handleClose={handleHospitalModalClose}
            fetchItems={fetchItems}
            rowToEdit={rowToEdit}
          />
        )}
        {openHospitalDetailModal && (
          <HospitalDetailModal
            show={openHospitalDetailModal}
            //handleClose={handleHospitalDetailModalClose}
            onHide={handleHospitalDetailModalClose}
            hospital={selectedHospital}
          />
        )}
      </Container>
    </Layout>
  );
};

export default Hospital;

const PopupBody = styled("div")(
  ({ theme }) => `
    width: max-content;
    padding: 12px 16px;
    margin: 8px;
    border-radius: 8px;
    border: 1px solid ${theme.palette.mode === "dark" ? grey[700] : grey[200]};
    background-color: ${theme.palette.mode === "dark" ? grey[900] : "#fff"};
    box-shadow: ${
      theme.palette.mode === "dark"
        ? `0px 4px 8px rgb(0 0 0 / 0.7)`
        : `0px 4px 8px rgb(0 0 0 / 0.1)`
    };
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    z-index: 1;
  `
);

const FilterButton = styled("button")(
  ({ theme }) => `
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: ${blue[500]};
    padding: 8px 16px;
    border-radius: 8px;
    color: white;
    transition: all 150ms ease;
    cursor: pointer;
    border: 1px solid ${blue[500]};
    box-shadow: 0 2px 4px ${
      theme.palette.mode === "dark"
        ? "rgba(0, 0, 0, 0.5)"
        : "rgba(0, 127, 255, 0.5)"
    }, inset 0 1.5px 1px ${blue[400]}, inset 0 -2px 1px ${blue[600]};
  
    &:hover {
      background-color: ${blue[600]};
    }
  
    &:active {
      background-color: ${blue[700]};
      box-shadow: none;
    }
  
    &:focus-visible {
      box-shadow: 0 0 0 4px ${
        theme.palette.mode === "dark" ? blue[300] : blue[200]
      };
      outline: none;
    }
  
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      box-shadow: none;
      &:hover {
        background-color: ${blue[500]};
      }
    }
  `
);

const grey = {
  50: "#F3F6F9",
  100: "#E5EAF2",
  200: "#DAE2ED",
  300: "#C7D0DD",
  400: "#B0B8C4",
  500: "#9DA8B7",
  600: "#6B7A90",
  700: "#434D5B",
  800: "#303740",
  900: "#1C2025",
};

const blue = {
  200: "#99CCFF",
  300: "#66B2FF",
  400: "#3399FF",
  500: "#007FFF",
  600: "#0072E5",
  700: "#0066CC",
};
// function setTotalItems(totalElements: any) {
//   throw new Error('Function not implemented.');
// }
