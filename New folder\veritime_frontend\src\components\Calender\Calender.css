
.calendar-container {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1050;
    background-color: white;
    border: 1px solid var(--red7);
    box-shadow: 0 4px 8px var(--neutral1);
    min-width: 300px;
    width: 100%;
    margin-top: 10px;
}

.filter-down-icon {
    cursor: pointer;
}

.input-group {
    display: flex;
    align-items: center;
}

.input-group-text {
    background-color: transparent;
    border: none;
}

.form-label {
    display: block;
    margin-bottom: 5px;
}

.text-container {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    display: inline-block;
}

.text-container span {
    display: inline-block;
    vertical-align: middle;
}
.grid-layout{
    /* width: fit-content; */
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background-color: white; */
    border-radius: 10px;

}

@media (max-width: 768px) {
    .calendar-container {
        width: calc(100% - 40px);
        left: 20px;
    }
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .input-group {
        flex-direction: column;
        align-items: flex-start;
    }
    .input-group img {
        margin-bottom: 5px;
    }
    .grid-layout{
        width: 315px;
        height: 50px;
       
    
    }
    
}
