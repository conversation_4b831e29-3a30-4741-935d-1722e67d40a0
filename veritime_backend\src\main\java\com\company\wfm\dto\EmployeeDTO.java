package com.company.wfm.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeDTO {

    private Long empId;
    private String firstName;
    private String middleName;
    private String lastName;
    private Long companyId;
    private Long departmentId;
    private Long designationId;
    private String empCode;
    private String empName;
   // private String uid;
   private String biometricID;
    private String idNo;
    private Long orgId;
    private Long upperId;
   // private Long regionId;
   private Long suburb;
    private Long countryId;
    private Long provinceId;
    private String city;
    private String upperName;
    private Date hireDate;
    private String gender;
    private Date birthday;
    private String nation;
    private Boolean married;
    private String phoneNo;
    private String mobileNo;
    private String email;
    private String nativePlace;
    private String zipCode;
    private Boolean isHistory;
    private Boolean inService;
    private String remark;
    private String createdBy;
    private LocalDateTime createdTime;
    private String updatedBy;
    private LocalDateTime updatedTime;
    private Long version;
    private String nativeLanguage;
   //private String foreignLanguages;
  private List<String> foreignLanguages;
    private Integer workYears;
    private String graduateSchool;
    private Date graduateTime;
    private String highestDegree;
    private String imgUre;
    private Long defaultTimeSlotId;
    private List<String> leaveOnDays;
    private String street;
    private String unitNumber;
    private Long reportingPerson;
    private String workZone;
    private Long branchId;
    private String alternateNumber;
    private String alternateEmail;
    private String emergencyContact1;
    private String emergencyContact2;
    private String emergencyContactname1;
    private String emergencyContactname2;
    private String base64EncodeFile;
    private String fileName;
    private long fileSize;
    private String ethnicity;
    private String nationalId;
    private BigDecimal probationPeriod;


    // Getters and Setters


}
