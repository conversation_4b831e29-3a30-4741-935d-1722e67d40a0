import React from 'react';
import { Doughnut } from 'react-chartjs-2';
//import 'bootstrap/dist/css/bootstrap.min.css';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

ChartJS.register(ArcElement, Tooltip, Legend);

const DashboardCard = ({ percentage, totalTickets, title }) => {
  const data = {
    labels: ['Closed', 'Open'],
    datasets: [
      {
        label: 'Ticket Status',
        data: [100 - percentage, percentage],
        backgroundColor: ['#c8e6c9', '#ef9a9a'],
        borderWidth: 0,
      },
    ],
  };

  const options = {
    cutout: '70%',
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
  };

  return (
    <div className="card p-4 shadow-sm m-4" style={{ borderRadius: '10px', width: '100%', maxWidth: '360px', height: 'auto' }}>
      <div className="d-flex justify-content-between align-items-center">
        <div>
          <h5 className="mb-1 text-muted">{title}</h5>
          <h2 className="mt-2 p-2 font-weight-bold">{totalTickets}</h2>
        </div>
        <div style={{ width: '100px', height: '100px', position: 'relative' }}>
          <Doughnut data={data} options={options} />
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              fontSize: '16px',
              fontWeight: 'bold',
              color: '#333',
            }}
          >
            {percentage}%
          </div>
        </div>
      </div>
    </div>
  );
};

const Cards = () => {
  return (
    <div className="container">
      <div className="row">
        <div className="col-12 col-md-4 d-flex justify-content-center">
          <DashboardCard title={"Total Tickets"} percentage={25} totalTickets={100} />
        </div>
        <div className="col-12 col-md-4 d-flex justify-content-center">
          <DashboardCard title={"Assigned To Me"} percentage={10} totalTickets={50} />
        </div>
        <div className="col-12 col-md-4 d-flex justify-content-center">
          <DashboardCard title={"Created by me"} percentage={2} totalTickets={5} />
        </div>
      </div>
    </div>
  );
};

export default Cards;
