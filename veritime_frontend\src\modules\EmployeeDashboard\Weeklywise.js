import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import 'chart.js/auto';
import { postRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import { Button, ButtonGroup, Card } from 'react-bootstrap';
import { colors } from '@constants/colors';

const WeeklyAttendance = () => {
  const [view, setView] = useState('week');
  const [chartData, setChartData] = useState({ labels: [], datasets: [] });
  const [response, setResponse] = useState(null);
  const [message, setMessage] = useState('');  // State to hold the message

  useEffect(() => {
    fetchCalendarData(view);
  }, [view]);

  const fetchCalendarData = async (period) => {
    try {
      const res = await postRequest(API_URLS.WEEKLY_ATTENDANCE, { period: period });
      setResponse(res);

      if (period === 'day') {
        setMessage('This is displaying data of previous day');
        const dayData = res.day;
        const totalHoursWorked = parseFloat(dayData.totalHoursWorked.split(' ')[0]);
        const activityDetails = dayData.activityDetails;

        const hoursData = activityDetails.map((entry) => {
          return parseFloat(entry.hoursWorked.split(' ')[0]);
        });

        const labels = Array.from({ length: totalHoursWorked + 1 }, (_, index) => index);

        const filledHoursData = Array.from({ length: totalHoursWorked + 1 }, (_, index) => {
          return hoursData[index] || 0;
        });

        setChartData({
          labels: labels,
          datasets: [
            {
              label: 'Hours Worked',
              data: filledHoursData,
              borderColor: colors.blue11,
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              pointBackgroundColor: colors.blue11,
              pointBorderColor: colors.red14,
              pointHoverBackgroundColor: colors.red14,
              pointHoverBorderColor: colors.blue11,
              tension: 0.4,
              fill: true,
            },
          ],
        });
      } else if (period === 'week') {
        setMessage('');  // Clear the message for the week view
        const periodData = res[period];

        // Combine dates and day labels for the week view
        const labels = periodData.labels.map((day, index) => {
          return `${day} (${periodData.dates[index]})`;
        });

        setChartData({
          labels: labels,
          datasets: [
            {
              label: 'Hours Worked',
              data: periodData.datasets[0].data,
              borderColor: colors.blue11,
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              pointBackgroundColor: colors.blue11,
              pointBorderColor: colors.red14,
              pointHoverBackgroundColor: colors.red14,
              pointHoverBorderColor: colors.blue11,
              tension: 0.4,
              fill: true,
            },
          ],
        });
      } else {
        setMessage('');  // Keep message cleared for month view
        const periodData = res[period];

        // Month view remains unchanged
        setChartData({
          labels: periodData.labels,
          datasets: [
            {
              label: 'Hours Worked',
              data: periodData.datasets[0].data,
              borderColor: colors.blue11,
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              pointBackgroundColor: colors.blue11,
              pointBorderColor: colors.red14,
              pointHoverBackgroundColor: colors.red14,
              pointHoverBorderColor: colors.blue11,
              tension: 0.4,
              fill: true,
            },
          ],
        });
      }
    } catch (error) {
    }
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (tooltipItem) => {
            if (view === 'day') {
              const dataIndex = tooltipItem.dataIndex;
              const entry = response.day.activityDetails[dataIndex]; // Access punchInTime and hoursWorked data
              return `${entry.punchInTime} - ${entry.hoursWorked}`;
            } else if (view === 'week') {
              // For the week view, display both day label and date
              const label = tooltipItem.label;
              const hours = tooltipItem.raw;
              return `${label}: ${hours} hours`;
            }
            return `${tooltipItem.raw} hours`; // For month view
          },
          afterLabel: (tooltipItem) => {
            if (view === 'day') {
              return `Total Worked: ${response.day.activityDetails[tooltipItem.dataIndex]?.hoursWorked}`;
            }
            return ''; // No additional info for week/month view
          },
        },
        backgroundColor: colors.red14,
        titleColor: colors.purple2,
        bodyColor: colors.purple2,
        borderColor: colors.blue11,
        borderWidth: 1,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        title: {
          display: false,
        },
      },
      y: {
        display: false,
        title: {
          display: false,
        },
        beginAtZero: true,
      },
    },
    elements: {
      line: {
        borderWidth: 2,
      },
    },
  };

  return (
    <div className="col col-md-9 col-lg-85 mb-4">
      <Card className="shadow-sm border-0" style={{ height: "280px" }}>
        <Card.Body>
          <div className="d-flex justify-content-between align-items-center">
            <p className="cardHeaderTxt">Weekly Attendance</p>
            {view === 'day' && message && (
              <div className="alert alert-info mt-2" style={{ height: "46px", width: "342px" }}>
                {message}
              </div>
            )}
            <ButtonGroup>
              <Button
                className="btnTxt1 px-2"
                variant={view === 'day' ? 'primary' : 'outline-primary'}
                onClick={() => setView('day')}
              >
                Day
              </Button>
              <Button
                className="btnTxt1 px-2"
                variant={view === 'week' ? 'primary' : 'outline-primary'}
                onClick={() => setView('week')}
              >
                Week
              </Button>
              <Button
                className="btnTxt1 px-2"
                variant={view === 'month' ? 'primary' : 'outline-primary'}
                onClick={() => setView('month')}
              >
                Month
              </Button>
            </ButtonGroup>
          </div>

          <div style={{ height: '145px' }}>
            <Line data={chartData} options={options} />
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default WeeklyAttendance;
