import { useEffect, useState, useRef } from "react";
import { Form, Button, Table, Alert } from "react-bootstrap";
import {
  deleteRequest,
  fileDownload,
  getRequest,
  postRequest,
} from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import {
  showErrorAlert,
  showSuccessAlert,
  showSuccessAlert2,
} from "@/services/alertService";
import {
  FaFilePdf,
  FaFileWord,
  FaFileExcel,
  FaFileImage,
  FaFileAlt,
  FaFile,
} from "react-icons/fa";
import { FaUpload, FaTimes } from "react-icons/fa";
import { MdUpload } from "react-icons/md";

// const getFileIcon = (filePath) => {
//   const extension = filePath.split(".").pop().toLowerCase();

//   switch (extension) {
//     case "pdf":
//       return <FaFilePdf style={{ color: "red" }} />;
//     case "doc":
//     case "docx":
//       return <FaFileWord style={{ color: "blue" }} />;
//     case "xls":
//     case "xlsx":
//       return <FaFileExcel style={{ color: "green" }} />;
//     case "png":
//     case "jpg":
//     case "jpeg":
//     case "gif":
//       return <FaFileImage style={{ color: "orange" }} />;
//     case "txt":
//       return <FaFileAlt style={{ color: "gray" }} />;
//     default:
//       return <FaFile style={{ color: "black" }} />;
//   }
// };

const DocumentDetailsForm = ({ data = "", onSubmit, empId, refresh }) => {
  const [documentTypes, setDocumentTypes] = useState([]);
  const [fileInputs, setFileInputs] = useState({});
  const [uploadErrors, setUploadErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [formError, setFormError] = useState("");
  const [offset, setOffset] = useState(0);
  const limit = 99;
  const [hasMore, setHasMore] = useState(true);
  const [documentsUploaded, setDocumentsUploaded] = useState([]);
  const [uploadStatus, setUploadStatus] = useState({});

  const isFetching = useRef(false);
  useEffect(() => {
    const fetchDocumentTypes = async () => {
      if (loading || !hasMore || isFetching.current) return;
      isFetching.current = true;

      setLoading(true);
      try {
        const response = await postRequest(API_URLS.DOCUMENT_LIST, {
          offset,
          limit,
        });
        if (response?.content?.length > 0) {
          if (data) {
            setDocumentTypes((prev) => [...prev, ...response.content]);
            let dummyFileInputs = [];
            data.map((item) => {
              dummyFileInputs[item.documentId] = {};
              dummyFileInputs[item.documentId].name =
                item.filePath?.split("/")[2];
            });
            setFileInputs(dummyFileInputs);
          } else {
            setDocumentTypes((prev) => [...prev, ...response.content]);
            if (response.content.length < limit) {
              setHasMore(false);
            }
          }
        } else {
          setHasMore(false);
        }
      } catch (error) {
        // console.error("Error fetching document types:", error);
        setErrorMessage("Failed to fetch documents. Please try again later.");
      } finally {
        setLoading(false);
        isFetching.current = false;
      }
    };

    fetchDocumentTypes();
  }, [offset]);
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    if (scrollHeight - scrollTop <= clientHeight + 50 && hasMore && !loading) {
      setOffset((prevOffset) => prevOffset + limit);
    }
  };

  const handleFileChangee = (id, file) => {
    setFileInputs((prevInputs) => ({
      ...prevInputs,
      [id]: file,
    }));
    if (uploadErrors[id]) {
      setUploadErrors((prevErrors) => ({
        ...prevErrors,
        [id]: "",
      }));
    }
  };

  const handleFileChange = (id, file, docType) => {
    if (!file) return; // Prevent calling upload with empty file

    // Prevent duplicate uploads by checking if the file is already uploaded
    if (fileInputs[id]?.name === file.name) {
      // console.log("File already uploaded, skipping duplicate upload.");
      return;
    }

    setFileInputs((prev) => ({ ...prev, [id]: file }));

    // Ensure the upload happens only once
    setTimeout(() => {
      handleUploadd(id, file, docType);
    }, 500); // Small delay to prevent quick double calls
  };

  const handleUploadd = async (id, file, docType) => {
    if (!empId) {
      showErrorAlert("No employee found");
      return;
    }

    if (!file) {
      setUploadErrors((prevErrors) => ({
        ...prevErrors,
        [docType.documentId]: "Please select a file to upload!",
      }));
      return;
    }

    const maxFileSize = 2 * 1024 * 1024; // 2 MB limit
    if (file.size > maxFileSize) {
      setUploadErrors((prevErrors) => ({
        ...prevErrors,
        [docType.documentId]: "File size exceeds the 2 MB limit!",
      }));
      return;
    }

    // Check if document is already uploaded before making API call
    const isAlreadyUploaded = documentsUploaded.some(
      (doc) => doc.documentId === docType.documentId
    );

    if (isAlreadyUploaded) {
      // console.log(
      //   `Skipping upload: Document ${docType.documentId} already uploaded`
      // );
      return; // Prevent double upload
    }

    const formData = new FormData();
    formData.append("file", file);
    formData.append("documentId", docType.documentId);
    formData.append("empId", empId);

    try {
      await postRequest(API_URLS.EMPLOYEE_DOC_UPLOAD, formData, true);
      // console.log(`Upload successful for document ID: ${docType.documentId}`);

      // Update state safely (Using Functional Update to ensure proper sync)
      setDocumentsUploaded((prev) => {
        const updated = [...prev, { documentId: docType.documentId }];
        // console.log("Updated documentsUploaded:", updated);
        return updated;
      });
    } catch (error) {
      console.error(
        `Upload failed for document ID: ${docType.documentId}`,
        error
      );
      setUploadErrors((prevErrors) => ({
        ...prevErrors,
        [docType.documentId]: "Error uploading file. Please try again.",
      }));
    }
  };

  // const handleSave = async (event) => {
  //   if (event) event.preventDefault(); // Prevent unintended form submission
  //   // console.log("handleSave triggered!");

  //   if (loading) {
  //     // console.log("Save already in progress, skipping...");
  //     return;
  //   }

  //   setLoading(true);
  //   let uploadedDocs = [];

  //   try {
  //     for (const [id, file] of Object.entries(fileInputs)) {
  //       const docType = documentTypes.find(
  //         (doc) => String(doc.documentId) === String(id)
  //       );

  //       if (!docType) {
  //         //console.error(`Document type not found for ID: ${id}`);
  //         continue;
  //       }

  //       await handleUploadd(id, file, docType); // Upload file
  //       uploadedDocs.push({ documentId: docType.documentId });
  //     }

  //     // console.log("Final Uploaded Docs:", uploadedDocs);

  //     const uploadedDocumentsMap = uploadedDocs.reduce((acc, doc) => {
  //       acc[doc.documentId] = true;
  //       return acc;
  //     }, {});

  //     // console.log("Uploaded Documents Map:", uploadedDocumentsMap);

  //     const missingMandatoryDocs = documentTypes.filter(
  //       (docType) =>
  //         docType.isMandatory && !uploadedDocumentsMap[docType.documentId]
  //     );

  //     if (missingMandatoryDocs.length > 0) {
  //       const newUploadErrors = missingMandatoryDocs.reduce((errors, doc) => {
  //         errors[doc.documentId] = "Mandatory document is missing!";
  //         return errors;
  //       }, {});

  //       console.log(
  //         "Missing Mandatory Documents:",
  //         missingMandatoryDocs.map((doc) => doc.documentId)
  //       );

  //       setUploadErrors(newUploadErrors);
  //       setFormError(
  //         `Please upload the mandatory documents: ${missingMandatoryDocs
  //           .map((doc) => doc.name)
  //           .join(", ")}`
  //       );

  //       setLoading(false); // Ensure loading is reset before returning
  //       return;
  //     }

  //     setFormError("");
  //     // console.log("All files uploaded! Showing success alert...");
  //     showSuccessAlert2("All documents uploaded successfully");

  //     // console.log("Alert displayed! Calling onSubmit()...");

  //     onSubmit();
  //   } catch (error) {
  //     console.error("Error in handleSave:", error);
  //   } finally {
  //     setLoading(false); // Reset loading in all cases
  //   }
  // };

  const handleSave = async (event) => {
    if (event) event.preventDefault();

    if (loading) return;
    setLoading(true);

    try {
      let uploadedDocs = [];

      for (const [id, file] of Object.entries(fileInputs)) {
        const docType = documentTypes.find(
          (doc) => String(doc.documentId) === String(id)
        );

        if (file && docType) {
          await handleUploadd(id, file, docType);
          uploadedDocs.push({ documentId: docType.documentId });
        }
      }

      const uploadedDocumentsMap = uploadedDocs.reduce((acc, doc) => {
        acc[doc.documentId] = true;
        return acc;
      }, {});

      const missingMandatoryDocs = documentTypes.filter(
        (docType) =>
          docType.isMandatory && !uploadedDocumentsMap[docType.documentId]
      );

      if (missingMandatoryDocs.length > 0) {
        const newUploadErrors = missingMandatoryDocs.reduce((errors, doc) => {
          errors[doc.documentId] = "Mandatory document is missing!";
          return errors;
        }, {});

        setUploadErrors(newUploadErrors);
        setFormError(
          `Please upload mandatory documents: ${missingMandatoryDocs
            .map((doc) => doc.name)
            .join(", ")}`
        );

        showErrorAlert("Mandatory documents are missing!");
        return;
      }

      setFormError("");
      showSuccessAlert2("All documents uploaded successfully");
      onSubmit();
    } catch (error) {
      console.error("Error in handleSave:", error);
      showErrorAlert("An error occurred while saving. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const downloadFile = async (file) => {
    try {
      const fileName = file.split("/").pop();
      const response = await fileDownload(
        `${API_URLS.DOWNLOAD_EMPLOYEE_FILE}${file}`,
        true
      );

      if (!response.ok) {
        throw new Error("Failed to download file");
      }

      const blob = await response.blob();
      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.target = "_blank";
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();

      showSuccessAlert(`File "${fileName}" downloaded successfully.`);
    } catch (error) {
      // console.error("Error downloading file:", error);
      showErrorAlert("Error downloading file: " + error.message);
    }
  };

  // const getDocumentNameAndIcon = (documentName) => {
  //   const document = documentsUploaded.find((doc) => doc.documentId === documentName);

  //   if (!document) {
  //     return (
  //       <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
  //         <span>-</span>
  //       </div>
  //     );
  //   }

  //   const icon = getFileIcon(document.filePath);
  //   return (
  //     <div
  //       onClick={() => downloadFile(document.filePath)}
  //       style={{ textDecoration: "none", color: "inherit", display: "flex", alignItems: "center", gap: "10px", cursor: "pointer" }}
  //     >
  //       {icon}
  //       <span>{document.filePath.split(".").pop().toLowerCase()}</span>
  //     </div>
  //   );
  // };
  const handleRemoveFile = async (id) => {
    setFileInputs((prev) => {
      const updatedInputs = { ...prev };
      delete updatedInputs[id];
      return updatedInputs;
    });

    setDocumentsUploaded((prev) =>
      prev.filter((doc) => String(doc.documentId) !== String(id))
    );

    setUploadErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors };
      delete updatedErrors[id];
      return updatedErrors;
    });

    if (!empId) {
      showErrorAlert("No employee found");
      return;
    }

    const url = `${API_URLS.DELETE_DOCUMENT}empId=${empId}&documentId=${id}`;

    try {
      const response = await deleteRequest(url);
      console.log("Delete API Response:", response);

      showSuccessAlert("Document removed successfully!");
    } catch (error) {
      console.error("Delete Request Failed:", error);
      showErrorAlert("Error removing document. Please try again.");
    }
  };

  return (
    <div style={{ width: "100%" }}>
      <h3 style={{ textAlign: "left", marginBottom: "20px" }}>
        Document Upload
      </h3>
      <Table bordered>
        <thead>
          <tr>
            <th style={{ width: "20%" }}>Name</th>
            <th style={{ width: "10%" }}>Type</th>
            <th style={{ width: "10%" }}>Upload</th>
          </tr>
        </thead>
        <tbody>
          {documentTypes.map((docType) => (
            <tr key={docType.documentId}>
              <td>
                {/* {docType.isMandatory ? <p>{docType.name} *</p> : docType.name} */}
                {docType.isMandatory ? (
                  <p>
                    {docType.name} <span style={{ color: "red" }}>*</span>
                  </p>
                ) : (
                  docType.name
                )}
              </td>

              <td>{docType.type}</td>
              <td>
                {fileInputs[docType.documentId] ? (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "10px",
                    }}
                  >
                    <span>{fileInputs?.[docType.documentId]?.name}</span>
                    <FaTimes
                      style={{ color: "red", cursor: "pointer" }}
                      onClick={() => handleRemoveFile(docType.documentId)}
                    />
                  </div>
                ) : (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "10px",
                    }}
                  >
                    <Form.Control
                      type="file"
                      style={{ display: "none" }}
                      id={`fileInput-${docType.documentId}`}
                      onChange={(e) =>
                        handleFileChange(
                          docType.documentId,
                          e.target.files[0],
                          docType
                        )
                      }
                    />
                    <label
                      htmlFor={`fileInput-${docType.documentId}`}
                      style={{ cursor: "pointer" }}
                    >
                      <MdUpload
                        style={{
                          color: "Black",
                          textAlign: "center",
                          alignItems: "center",
                          display: "flex",
                          fontSize: 22,
                        }}
                      />
                    </label>
                  </div>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </Table>

      <div style={{ textAlign: "center", marginTop: "20px" }}>
        <Button variant="primary" onClick={handleSave} disabled={loading}>
          Save
        </Button>
      </div>
    </div>
  );
};

const formRowStyle = {
  width: "100%",
  display: "flex",
  marginBottom: "20px",
};
const headingStyle = {
  fontSize: "24px",
  fontWeight: "bold",
  color: "#333",
  marginTop: "-20px",
};

const submitButtonStyle = {
  marginTop: "20px",
  width: "150px",
  padding: "10px 20px",
  borderRadius: "4px",
  border: "none",
  backgroundColor: "#007bff",
  color: "#fff",
  fontSize: "16px",
  cursor: "pointer",
  textAlign: "center",
};

export default DocumentDetailsForm;
