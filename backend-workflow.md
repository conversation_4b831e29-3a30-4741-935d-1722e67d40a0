# Veritime Backend API Documentation

## Table of Contents

1. [Authentication Flow](#authentication-flow)
2. [Complete API Inventory](#complete-api-inventory)
3. [Role-Based API Access Matrix](#role-based-api-access-matrix)
4. [Data Models and Relationships](#data-models-and-relationships)
5. [Error Handling Patterns](#error-handling-patterns)

## Authentication Flow

### Login Endpoint

**Endpoint:** `POST /api/auth/login`

**Request Structure:**

```json
{
  "encryptedData": "encrypted_payload_containing_username_and_password"
}
```

**Decrypted Payload:**

```json
{
  "username": "<EMAIL>",
  "password": "user_password"
}
```

**Response Structure (Success):**

```json
{
  "encryptedData": "encrypted_response_containing_jwt_and_user_details"
}
```

**Decrypted Response:**

```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "empId": 12345,
  "role": "admin",
  "logHistoryId": 67890,
  "employeeName": "<PERSON>e"
}
```

### Token Validation Process

- JWT tokens are validated on each API request
- Token contains employee ID, role, and expiration
- `UserTokenService.getEmployeeIdFromToken()` extracts user context
- Invalid/expired tokens return 401 Unauthorized

### Role Hierarchy

```
CEO (Highest)
├── SuperAdmin (Company-wide access)
├── Admin (Branch-level access)
├── Supervisor (Team-level access)
└── Employee (Self-access only)
```

## Complete API Inventory

### Employee Management APIs

#### Get All Employees

**Endpoint:** `GET /api/v1/employee`
**Access:** Admin, CEO, SuperAdmin
**Response:** List of EmployeeDetailsDto with role-based filtering

#### Get Employee by ID

**Endpoint:** `GET /api/v1/employee/{empId}`
**Access:** All roles (filtered by hierarchy)
**Response:** Single EmployeeDetailsDto

#### Create Employee

**Endpoint:** `POST /api/v1/employee`
**Access:** Admin, CEO, SuperAdmin
**Request:**

```json
{
  "empName": "John Doe",
  "empCode": "EMP001",
  "email": "<EMAIL>",
  "departmentId": 1,
  "designationId": 2,
  "branchId": 3,
  "upperId": 456
}
```

#### Update Employee

**Endpoint:** `PUT /api/v1/employee/update/{empId}`
**Access:** Admin, CEO, SuperAdmin
**Request:** Same as create with updated fields

#### Get Present Employees

**Endpoint:** `POST /api/v1/employees/present`
**Access:** All roles (filtered by hierarchy)
**Request:**

```json
{
  "offset": 0,
  "limit": 10
}
```

### Attendance Management APIs

#### Get Attendance List

**Endpoint:** `GET /api/v1/attendance/listByEmployee`
**Parameters:** `fromDate`, `toDate`
**Access:** All roles (filtered by hierarchy)
**Response:** List of AttendanceResponseDTO

#### Get Multiple Employee Attendance

**Endpoint:** `POST /api/v1/attendance/list`
**Access:** Supervisor, Admin, CEO, SuperAdmin
**Request:**

```json
{
  "empIds": [123, 456, 789],
  "fromDate": "2024-01-01",
  "toDate": "2024-01-31"
}
```

#### Team Attendance

**Endpoint:** `POST /api/v1/attendance/teamAttendance`
**Access:** Supervisor, Admin, CEO, SuperAdmin
**Request:**

```json
{
  "userid": 123,
  "fromDate": "2024-01-01",
  "toDate": "2024-01-31"
}
```

#### Approve/Deny Attendance

**Endpoint:** `POST /api/v1/attendance/update-status`
**Access:** Supervisor, Admin, CEO, SuperAdmin
**Request:**

```json
{
  "attendanceId": 123,
  "status": "APPROVED",
  "remarks": "Approved by supervisor"
}
```

### Leave Management APIs

#### Apply Leave

**Endpoint:** `POST /api/v1/employees/leave/applications`
**Access:** All roles
**Request:**

```json
{
  "leaveId": 1,
  "startDate": "2024-01-15",
  "endDate": "2024-01-17",
  "appliedLeaveCount": 3,
  "reason": "Personal work",
  "uploadedFileName": "medical_certificate.pdf"
}
```

#### Get Leave History

**Endpoint:** `GET /api/v1/leave-applications/history`
**Access:** All roles (filtered by hierarchy)
**Response:** List of leave applications with status

#### Approve Leave

**Endpoint:** `PUT /api/v1/leave-applications/{leaveHistoryId}/approve`
**Access:** Supervisor, Admin, CEO, SuperAdmin
**Request:**

```json
{
  "remarks": "Approved for personal reasons"
}
```

#### Deny Leave

**Endpoint:** `PUT /api/v1/leave-applications/{leaveHistoryId}/deny`
**Access:** Supervisor, Admin, CEO, SuperAdmin
**Request:**

```json
{
  "remarks": "Insufficient leave balance"
}
```

### Dashboard & Reports APIs

#### Get Dashboard Widgets

**Endpoint:** `POST /api/v1/widgets`
**Parameters:** `report` (query param), `download` (boolean)
**Access:** Role-based filtering applied
**Available Reports:**

- `EmployeesOnClockedInToday`
- `EmployeePresent`
- `LeaveApprovalsPending`
- `LeaveApproved`
- `FacilityCount`

#### Get Side Menu

**Endpoint:** `GET /api/v1/sidemenu`
**Access:** All authenticated users
**Response:** Role-based menu structure

```json
[
  {
    "id": 1,
    "menu_label": "Dashboard",
    "menu_path": "/dashboards",
    "icon_path": "/image/dashboard.png",
    "is_active": true,
    "sequence": 1,
    "subMenuItems": []
  }
]
```

### Department & Branch APIs

#### Get Departments

**Endpoint:** `GET /api/v1/departments/names`
**Access:** All roles (filtered by hierarchy)

#### Create Department

**Endpoint:** `POST /api/v1/departments/create-dept-branch`
**Access:** Admin, CEO, SuperAdmin

#### Get Branches

**Endpoint:** `GET /api/v1/departments/branches`
**Access:** All roles (filtered by hierarchy)

#### Create Branch

**Endpoint:** `POST /api/v1/branches/create-branch-dept`
**Access:** CEO, SuperAdmin

### User Profile APIs

#### Get Current User Profile

**Endpoint:** `GET /api/v1/current`
**Access:** All authenticated users
**Response:** Current user's profile details

#### Update Profile

**Endpoint:** `PUT /api/v1/employee/update/{empId}`
**Access:** Self or higher hierarchy

### Notification APIs

#### Update Notification Token

**Endpoint:** `POST /api/v1/employees/updateToken`
**Access:** All authenticated users
**Request:**

```json
{
  "firebaseToken": "firebase_device_token"
}
```

#### Read Notifications

**Endpoint:** `POST /api/v1/employees/notifications/read`
**Access:** All authenticated users

### File Management APIs

#### Upload Employee Documents

**Endpoint:** `POST /api/v1/employee-documents/upload`
**Access:** All roles
**Content-Type:** `multipart/form-data`

#### Download Employee Files

**Endpoint:** `GET /api/v1/employee-documents/download/employeefile`
**Parameters:** `name` (filename)
**Access:** Role-based access to files

### Ticket Management APIs

#### Create Ticket

**Endpoint:** `POST /api/v1/tickets/create`
**Access:** All roles

#### Get Ticket Details

**Endpoint:** `GET /api/v1/tickets/getById`
**Parameters:** `ticketId`
**Access:** Creator or assigned personnel

#### Assign Ticket

**Endpoint:** `POST /api/v1/tickets/assign`
**Access:** Admin, CEO, SuperAdmin

#### Update Ticket Status

**Endpoint:** `POST /api/v1/tickets/respond`
**Access:** Assigned personnel or higher hierarchy

## Role-Based API Access Matrix

### CEO Access

- **Full Access:** All APIs across all branches and companies
- **Data Scope:** Organization-wide data
- **Special Permissions:**
  - Create/modify any employee
  - Access all reports and analytics
  - Manage company-wide settings

### SuperAdmin Access

- **Scope:** Company-level access (filtered by company_id)
- **Restrictions:** Cannot access other companies' data
- **Permissions:**
  - Manage employees within company
  - Access company-wide reports
  - Create/modify branches and departments

### Admin Access

- **Scope:** Branch-level access (filtered by branch_id)
- **Restrictions:** Cannot access other branches' data
- **Permissions:**
  - Manage employees within branch
  - Approve leave requests for branch
  - Access branch-level reports

### Supervisor Access

- **Scope:** Team-level access (filtered by UPPER_ID = supervisor's empId)
- **Restrictions:** Only direct reports and their data
- **Permissions:**
  - View team attendance
  - Approve team leave requests
  - Access team reports

### Employee Access

- **Scope:** Self-access only (filtered by empId)
- **Restrictions:** Only own data
- **Permissions:**
  - View own profile and attendance
  - Apply for leave
  - Create tickets

## Data Models and Relationships

### Core Entities

#### Employee (t_employee)

```sql
- EMP_ID (Primary Key)
- EMP_CODE (Unique)
- EMP_NAME
- EMAIL
- DEPARTMENT_ID (FK to t_department)
- DESIGNATION_ID (FK to t_designation)
- BRANCH_ID (FK to t_branch)
- UPPER_ID (FK to t_employee - Self-referencing for hierarchy)
- COMPANY_ID (FK to feeder_hospitals)
- IN_SERVICE (Boolean)
- IMG_URE (Profile image path)
```

#### Attendance (t_attendance)

```sql
- ATTENDANCE_ID (Primary Key)
- EMP_ID (FK to t_employee)
- DATE
- CHECK_IN_TIME
- CHECK_OUT_TIME
- OVERTIME
- MODE_TYPE (Entry mode)
- STATUS (PENDING, APPROVED, DENIED)
```

#### Leave Applications (t_employee_leave_history)

```sql
- LEAVE_HISTORY_ID (Primary Key)
- EMP_ID (FK to t_employee)
- LEAVE_ID (FK to t_leave_master)
- START_DATE
- END_DATE
- APPLIED_LEAVE_COUNT
- STATUS (PENDING, APPROVED, DENIED)
- REASON
- UPLOADED_FILE_NAME
```

#### Department (t_department)

```sql
- DEPARTMENT_ID (Primary Key)
- DEPARTMENT_NAME
- BRANCH_ID (FK to t_branch)
```

#### Branch (t_branch)

```sql
- BRANCH_ID (Primary Key)
- BRANCH_NAME
- COMPANY_ID (FK to feeder_hospitals)
```

### Key Relationships

- Employee → Department (Many-to-One)
- Employee → Branch (Many-to-One)
- Employee → Employee (Self-referencing hierarchy)
- Attendance → Employee (Many-to-One)
- Leave Applications → Employee (Many-to-One)
- Department → Branch (Many-to-One)

## Error Handling Patterns

### Standard Error Response Format

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed for field 'email'",
  "path": "/api/v1/employee"
}
```

### Common HTTP Status Codes

#### 200 OK

- Successful GET, PUT requests
- Data retrieved or updated successfully

#### 201 Created

- Successful POST requests
- New resource created

#### 400 Bad Request

- Invalid request payload
- Validation errors
- Missing required fields

#### 401 Unauthorized

- Invalid or expired JWT token
- Missing authentication header

#### 403 Forbidden

- User lacks permission for requested resource
- Role-based access denied

#### 404 Not Found

- Requested resource doesn't exist
- Invalid endpoint

#### 500 Internal Server Error

- Database connection issues
- Unexpected server errors
- Encryption/decryption failures

### Encrypted Response Pattern

Most APIs use encryption for sensitive data:

**Request:**

```json
{
  "encryptedData": "base64_encrypted_payload"
}
```

**Response:**

```json
{
  "encryptedData": "base64_encrypted_response"
}
```

### Validation Error Examples

```json
{
  "status": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    },
    {
      "field": "empCode",
      "message": "Employee code already exists"
    }
  ]
}
```

### Role-Based Access Error

```json
{
  "status": 403,
  "message": "Access denied: Insufficient permissions to view this resource",
  "requiredRole": "admin",
  "userRole": "employee"
}
```

## Additional API Endpoints

### Schedule Management APIs

#### Get Schedule List

**Endpoint:** `GET /api/v1/schedule/list`
**Access:** All roles (filtered by hierarchy)
**Response:** List of employee schedules

#### Update Schedule

**Endpoint:** `PUT /api/v1/schedule/update`
**Access:** Admin, CEO, SuperAdmin
**Request:**

```json
{
  "empId": 123,
  "timeSlotId": 456,
  "effectiveDate": "2024-01-15"
}
```

### Shift Management APIs

#### Get Time Slots

**Endpoint:** `GET /api/v1/timeslots`
**Parameters:** `deptId` (optional)
**Access:** All roles

#### Request Shift Change

**Endpoint:** `POST /api/v1/shift-change/request`
**Access:** All roles
**Request:**

```json
{
  "fromDate": "2024-01-15",
  "toDate": "2024-01-17",
  "newTimeSlotId": 456,
  "reason": "Personal emergency"
}
```

#### Approve Shift Change

**Endpoint:** `PUT /api/v1/shift-change/{swapRequestId}/approve`
**Access:** Supervisor, Admin, CEO, SuperAdmin

### Biometric Integration APIs

#### Get Biometric Data

**Endpoint:** `GET /api/v1/attendance/audit/listByEmployee`
**Parameters:** `fromDate`, `toDate`
**Access:** Admin, CEO, SuperAdmin
**Response:** Raw biometric punch data

#### Sync HikVision Data

**Endpoint:** `GET /api/v1/hikvision/fetchAndInsertAttendance`
**Access:** System Admin only
**Purpose:** Manual trigger for biometric data sync

### Working Hours APIs

#### Get Working Hours

**Endpoint:** `GET /api/v1/working-hours`
**Access:** All authenticated users
**Response:** Weekly working hours summary

#### Get Attendance Activity

**Endpoint:** `POST /api/v1/attendance/activity`
**Access:** All authenticated users
**Request:**

```json
{
  "period": "week" // "day", "week", "month"
}
```

### Document Management APIs

#### Get Document Types

**Endpoint:** `GET /api/v1/documentMaster/list`
**Access:** All roles

#### Create Document Type

**Endpoint:** `POST /api/v1/documentMaster/save`
**Access:** Admin, CEO, SuperAdmin

#### Update Document Type

**Endpoint:** `PUT /api/v1/documentMaster/update/{docId}`
**Access:** Admin, CEO, SuperAdmin

### Team Management APIs

#### Get My Teams

**Endpoint:** `GET /api/v1/teams`
**Access:** Supervisor, Admin, CEO, SuperAdmin
**Response:** Teams under user's management

#### Get Team Hierarchy

**Endpoint:** `GET /api/v1/teams/get-hierarchy-data`
**Access:** Supervisor, Admin, CEO, SuperAdmin
**Response:** Complete team hierarchy structure

### Resignation Management APIs

#### Submit Resignation

**Endpoint:** `POST /api/v1/employee/resign`
**Access:** All roles
**Request:**

```json
{
  "resignationDate": "2024-02-15",
  "lastWorkingDay": "2024-03-15",
  "reason": "Career growth"
}
```

#### Get Resignation List

**Endpoint:** `GET /api/v1/Resignation/status/list`
**Access:** Admin, CEO, SuperAdmin

#### Approve Resignation

**Endpoint:** `PUT /api/v1/Resignation/status/approve/{resignationId}`
**Access:** Admin, CEO, SuperAdmin

### Calendar APIs

#### Get Employee Calendar

**Endpoint:** `POST /api/v1/employee/calendar-month`
**Access:** All authenticated users
**Request:**

```json
{
  "year": 2024,
  "month": 1
}
```

**Response:** Monthly calendar with attendance, leaves, and holidays
