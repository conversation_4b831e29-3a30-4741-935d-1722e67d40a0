import React, { useState } from "react";

const TopFacilities = ({ data = [] }) => {
  return (
    <div className="col-12 col-lg-6 col-xl-6 col-md-12 mb-3">
      <div className="card shadow-sm">
        <div className="card-body">
          <h2 className="h5">Top 3 Facilities</h2>
          <div className="d-flex justify-content-around">
            {Array.isArray(data) && data.length > 0 ? (
              data.map((item, index) => (
                <div key={index} className="text-center">
                  <div
                    className="pie-chart"
                    style={{
                      "--percentage": `${item.value.toFixed(2)}%`,
                    }}
                    data-percentage={`${item.value.toFixed(2)}%`}
                  >
                    <div className="pie-chart-center">
                      {item.value.toFixed(2)}%
                    </div>
                  </div>
                  <p className="small">{item.name}</p>
                </div>
              ))
            ) : (
              <p className="text-muted">No facilities available</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopFacilities;
