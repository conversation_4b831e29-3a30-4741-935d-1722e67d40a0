package com.company.wfm.controller;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.EmployeeDocumentVO;
import com.company.wfm.dto.EmployeeLeaveBalanceDto;
import com.company.wfm.dto.LeaveHistoryRequestDTO;
import com.company.wfm.dto.LeaveHistoryResponseDTO;
import com.company.wfm.entity.User;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.util.CommonConstant;
import com.company.wfm.vo.EmployeeVO;

@RestController
@RequestMapping("/api/v1/dashboard/widgets")
@CrossOrigin(origins = "*")

public class WidgetsDashboardController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private UserTokenService userTokenService;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;


    // Initialize the logger for this class
    private static final Logger logger = LoggerFactory.getLogger(WidgetsDashboardController.class);

    @GetMapping
    public List<Map<String, Object>> getWidgets(@RequestParam(required = false) String type) {
        Long empId = null;
        try {
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee ID from token", e);
            throw new RuntimeException("Unable to retrieve employee ID from the token. Please try again later.");
        }

        String role = null;
        try {
            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee role from token", e);
            throw new RuntimeException("Unable to retrieve role from the token. Please try again later.");
        }

        List<Map<String, Object>> responseList = new ArrayList<>();
        String query;
        List<Object> params = new ArrayList<>();

        try {
            if (type != null && !type.isEmpty()) {
                List<String> typeList = Arrays.asList(type.split(","));
                String inClause = String.join(",", Collections.nCopies(typeList.size(), "?"));
                query = "SELECT w.widget_name AS widgetName, w.description, w.icon, w.onClickPath, w.isCommon, w.type, w.color, w.backgroundColor, w.downloadParms " +
                        "FROM t_widgets w " +
                        "WHERE w.type IN (" + inClause + ") AND w.is_active = 1 " +
                        "ORDER BY w.widget_name";
                params.addAll(typeList);
            } else {
                query = "SELECT w.widget_name AS widgetName, w.description, w.icon, w.onClickPath, w.isCommon, rw.sequence, w.color, w.backgroundColor, w.downloadParms " +
                        "FROM t_widgets w " +
                        "JOIN role_widgets rw ON w.widget_id = rw.widget_id " +
                        "WHERE w.type LIKE '%overview%' AND rw.role_name = ? AND rw.is_active = 1 AND w.is_active = 1 " +
                        "ORDER BY rw.sequence";
                params.add(role);
            }

            List<Map<String, Object>> widgets = jdbcTemplate.queryForList(query, params.toArray());

            if (widgets == null || widgets.isEmpty()) {
                return responseList;
            }

            for (Map<String, Object> widget : widgets) {
                Map<String, Object> widgetResponse = new HashMap<>();

                String widgetType = (widget.get("isCommon") != null && !widget.get("isCommon").toString().isEmpty())
                        ? widget.get("isCommon").toString()
                        : widget.get("widgetName").toString();

                widgetResponse.put("type", widgetType);
                widgetResponse.put("widgetName", widget.get("description"));
                widgetResponse.put("downloadParameter", widget.get("downloadParms") );

                Integer totalRequestCount = getTotalLeaveRequestCount(empId);
                Integer totalModificationsCount = getTotalModificationsRequestCount(empId);
                try {
                    switch (widget.get("widgetName").toString()) {

                        case "totalEmployeesPresent":
                            Integer totalEmployees = getTotalEmployeesPresent(empId, role);
                            logger.info("Total Employees Present: " + totalEmployees);
                            widgetResponse.put("value", totalEmployees);
                            break;
                        case "totalApproved":
                            widgetResponse.put("value", getTotalApproved());
                            break;

                        case "devicesCount":
                            widgetResponse.put("value", getDevicesCount(empId, role));
                            break;
                        case "activeDevicesCount":
                            widgetResponse.put("value", getActiveDevicesCount(empId, role));
                            break;
                        case "branchTicketList":
                            widgetResponse.put("value", getBranchTicketList(empId, role));
                            break;
                        case "empCount":
                            widgetResponse.put("value", getEmployeeCount(empId, role));
                            break;
                        case "top3branches":
                            widgetResponse.put("value", getTop3Branches(empId, role));
                            break;
                        case "totalEnrollmentsCount":
                            widgetResponse.put("value", getTotalEnrollmentsCount(empId, role));
                            break;
                        case "totalApprovals":
                            widgetResponse.put("value", getTotalApprovals(empId, role));
                            break;
                        case "branchCount":
                            widgetResponse.put("value", getBranchCount(empId, role));
                            break;
                        case "deptCount":
                            widgetResponse.put("value", getDeptCount(empId, role));
                            break;
                        case "teamsCount":
                            widgetResponse.put("value", getEmployeeCount(empId, role));
                            break;
                        case "departmentWiseReport":
                            widgetResponse.put("value", getDepartmentWiseReport(empId, role));
                            break;
                        case "totalDepartments":
                            widgetResponse.put("value", getTotalDepartments(empId, role));
                            break;
                        case "totalEmployeesOnLeave":
                            widgetResponse.put("value", getTotalEmployeesOnLeave(empId, role));
                            break;
                        case "scheduleWiseReport":
                            widgetResponse.put("value", getScheduleWiseReport(empId, role));
                            break;
                        case "ticketCreated":
                            widgetResponse.put("value", getTicketsCreatedByMe(empId));
                            break;
                        case "ticketAssigned":
                            widgetResponse.put("value", getTicketsAssignedToMe(empId));
                            break;
                        case "totalTickets":
                            widgetResponse.put("value", getTotalTicketsOfMyEmployees(empId));
                            break;
                        case "totalPendingRequests":
                            Map<String, Object> pendingData = getLeaveStatusData(getPendingLeaveRequests(empId), totalRequestCount);
                            widgetResponse.put("value", pendingData.get("value"));
                            widgetResponse.put("percentage", pendingData.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;
                        case "totalLeavesApproved":
                          Map<String, Object> pendingData2 = getLeaveStatusData(getTotalLeavesApproved(empId), totalRequestCount);
                            widgetResponse.put("value", pendingData2.get("value"));
                            widgetResponse.put("percentage", pendingData2.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;
                        case "totalLeavesRejected":
                            Map<String, Object> pendingData3 = getLeaveStatusData(getTotalLeavesRejected(empId), totalRequestCount);
                            widgetResponse.put("value", pendingData3.get("value"));
                            widgetResponse.put("percentage", pendingData3.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;

                        //modifcations
                        case "totalPendingModifications":
                            Map<String, Object> pendingData4 = getLeaveStatusData(getPendingModificationsRequests(empId), totalModificationsCount);
                            widgetResponse.put("value", pendingData4.get("value"));
                            widgetResponse.put("percentage", pendingData4.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;
                        case "totalApprovedModifications":
                            Map<String, Object> pendingData5 = getLeaveStatusData(getTotalModificationsApproved(empId), totalModificationsCount);
                            widgetResponse.put("value", pendingData5.get("value"));
                            widgetResponse.put("percentage", pendingData5.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;
                        case "totalRejectedModifications":
                            Map<String, Object> pendingData6 = getLeaveStatusData(getTotalModificationsRejected(empId), totalModificationsCount);
                            widgetResponse.put("value", pendingData6.get("value"));
                            widgetResponse.put("percentage", pendingData6.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;
                         //shftchange
                        case "totalPendingShiftchange":
                            Map<String, Object> pendingData7 = getLeaveStatusData(getPendingShiftChangeRequests(empId), totalModificationsCount);
                            widgetResponse.put("value", pendingData7.get("value"));
                            widgetResponse.put("percentage", pendingData7.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;
                        case "totalApprovedShiftchange":
                            Map<String, Object> pendingData8 = getLeaveStatusData(getTotalShiftChangeApproved(empId), totalModificationsCount);
                            widgetResponse.put("value", pendingData8.get("value"));
                            widgetResponse.put("percentage", pendingData8.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;
                        case "totalRejectedShiftchange":
                            Map<String, Object> pendingData9 = getLeaveStatusData(getTotalShiftChangeRejected(empId), totalModificationsCount);
                            widgetResponse.put("value", pendingData9.get("value"));
                            widgetResponse.put("percentage", pendingData9.get("percentage"));
                            widgetResponse.put("title", widget.get("description"));
                            widgetResponse.put("value", widgetResponse.get("value"));
                            widgetResponse.put("color", widget.get("color"));
                            widgetResponse.put("pathcolor", widget.get("backgroundColor"));
                            break;
                        case "ticketWiseReport":
                        case "leaveBalanceChart":
                        case "weeklyAttendance":
                        case "myTicketsChart":
                        case "employeeCalender":
                        case "weeklyWorkingHours":
                            widgetResponse.put("value", 1);
                            break;
                        case "totalEmployeesClockedIn":
                            widgetResponse.put("value", getTotalEmployeesClockedInToday(empId, role));
                            break;
                        default:
                            logger.warn("No handler for widget: " + widget.get("widgetName"));
                            break;
                    }
                } catch (Exception e) {
                    logger.error("Error processing widget: " + widget.get("widgetName"), e);
                    widgetResponse.put("value", "-");
                }

                // Handling common widgets and custom response formatting
                if (widget.get("isCommon") != null) {
                    try {
                        Map<String, Object> valueMap = new HashMap<>();
                        valueMap.put("name", widget.get("description"));
                        valueMap.put("count", widgetResponse.get("value"));
                        valueMap.put("icon", widget.get("icon"));
                        valueMap.put("onClickPath", widget.get("onClickPath"));
                        widgetResponse.put("value", valueMap);
                    } catch (Exception e) {
                        logger.error("Error formatting common widget value for " + widget.get("widgetName"), e);
                    }
                }

                responseList.add(widgetResponse);
            }

        } catch (Exception e) {
            logger.error("Error retrieving widgets or processing query", e);
            return responseList;
        }

        return responseList;
    }
    private Map<String, Object> getLeaveStatusData(Integer statusCount, Integer totalRequestCount) {
        Map<String, Object> result = new HashMap<>();
        result.put("value", statusCount);
        result.put("percentage", totalRequestCount > 0 ? (statusCount * 100 / totalRequestCount) : 0);
        return result;
    }

    private Integer getEmployeeCount(Long empId, String role) {
        switch (role.toLowerCase()) {
            case "ceo":
                return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_employee where IN_SERVICE = 1", Integer.class);
            case "superadmin":
                Long hospitalId = getHospitalId(empId);
                return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_employee WHERE IN_SERVICE = 1 AND company_id = ?", Integer.class, hospitalId);
            case "admin":
                Long branchId = getBranchId(empId);
                return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_employee WHERE IN_SERVICE = 1 AND branch_id = ?", Integer.class, branchId);
            case "supervisor":
                return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_employee WHERE IN_SERVICE = 1 AND UPPER_ID = ?", Integer.class, empId);
            case "employee":
                return 1;
            default:
                return 0;
        }
    }

    private Long getHospitalId(Long empId) {
        return jdbcTemplate.queryForObject("SELECT id FROM feeder_hospitals WHERE id = (SELECT company_id FROM t_employee WHERE EMP_ID = ?)", Long.class, empId);
    }

    private Long getBranchId(Long empId) {
        return jdbcTemplate.queryForObject("SELECT BRANCH_ID FROM t_employee WHERE EMP_ID = ?", Long.class, empId);
    }

    private Integer getTotalApproved() {
        Long hospitalId = getHospitalId(tokenService.getEmployeeIdFromToken());
        return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_employee_leave_history WHERE approval_status = 'APPROVED' AND EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE company_id = ?)  AND updated_time >= DATEADD(DAY, -30, GETDATE()) ", Integer.class, hospitalId);
    }

    private Integer getDevicesCount(Long empId, String role) {
        return 0;
        //Long hospitalId = getHospitalId(empId);
        //return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_user WHERE EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE company_id = ?)", Integer.class, hospitalId);
    }

    private Integer getActiveDevicesCount(Long empId, String role) {
        return 0;
        //Long hospitalId = getHospitalId(empId);
        //return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_user WHERE IS_ONLINE = 1 AND EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE company_id = ?)", Integer.class, hospitalId);
    }

    private Map<String, Object> getBranchTicketList(Long empId, String role) {
        Map<String, Object> branchTicketList = new HashMap<>();
        List<String> axisLbl = new ArrayList<>();
        List<Double> axis = new ArrayList<>();
        List<String> colors = new ArrayList<>();
        Long hospitalId = getHospitalId(empId);

        List<Map<String, Object>> ticketsPerBranch = jdbcTemplate.queryForList(
                "SELECT b.branch_name AS branchName, COUNT(t.TICKET_ID) AS totalTickets FROM t_branch b " +
                        "LEFT JOIN t_employee e ON b.branch_id = e.BRANCH_ID " +
                        "LEFT JOIN t_ticket t ON e.EMP_ID = t.EMP_ID WHERE e.company_id = ? " +
                        "GROUP BY b.branch_name ORDER BY COUNT(t.TICKET_ID) DESC", hospitalId);

        for (Map<String, Object> row : ticketsPerBranch) {
            axisLbl.add((String) row.get("branchName"));
            Integer totalTickets = (Integer) row.get("totalTickets");

            // added 0.1 becuase pie chart was not rendering
            axis.add(totalTickets != null && totalTickets > 0 ? totalTickets.doubleValue() : 0.1);

            colors.add(getRandomBlueShade());
        }

        branchTicketList.put("axis", axis);
        branchTicketList.put("axisLbl", axisLbl);
        branchTicketList.put("colors", colors);
        return branchTicketList;
    }

    private Integer getTotalApprovals(Long empId, String role) {
        LocalDate today = LocalDate.now();
        Date sqlToday = Date.valueOf(today);

        String baseQuery = "SELECT COUNT(*) " +
                "FROM t_employee_leave_history l " +
                "JOIN t_employee e ON l.emp_id = e.EMP_ID " +
                "WHERE l.approval_status = 'PENDING' ";
//                "AND l.start_date <= ? AND l.end_date >= ? "

        List<Object> params = new ArrayList<>();
//        params.add(sqlToday);
//        params.add(sqlToday);

//        Commented due to logic provided by Sam from Xpertlyte Date: 11-03-2025
//        This widget shows the number of pending leave requests of employees reporting to the logged In person that require approval. It ensures timely decision-making and prevents unnecessary delays in the leave approval process.
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery += "AND e.company_id = ? ";
//            params.add(companyId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery += "AND e.company_id = ? AND e.branch_id = ? ";
//            params.add(companyId);
//            params.add(branchId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
            baseQuery += "AND e.UPPER_ID = ? ";
            params.add(empId);
       // }

        return jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
    }

    private Integer getBranchCount(Long empId, String role) {
        if ("ceo".equalsIgnoreCase(role)) {
            return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_branch", Integer.class);
        } else if ("superadmin".equalsIgnoreCase(role)) {
            Long hospitalId = getHospitalId(empId);
            return jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM t_branch WHERE company_id = ?", Integer.class, hospitalId);
        }else if ("admin".equalsIgnoreCase(role)) {

            return 1;
        }else {
            return 0;
        }
    }

    private Integer getDeptCount(Long empId, String role) {
        User employee = userTokenService.getEmployeeFromToken();

        // Base query, avoiding dynamic concatenation
        StringBuilder queryBuilder = new StringBuilder(CommonConstant.DepartmentQuery.DEPARTMENT_EXTRACT_COUNT_QUERY);

        // MapSqlParameterSource to hold query parameters
        MapSqlParameterSource params = new MapSqlParameterSource();

        // Apply role-specific conditions
        if ("superadmin".equalsIgnoreCase(employee.getRole())) {
            queryBuilder.append(" AND branch.company_id = :companyId");
            params.addValue("companyId", employee.getEmployee().getCompanyId());
        } else if ("admin".equalsIgnoreCase(employee.getRole())) {
            queryBuilder.append(" AND branch.branch_id = :branchId");
            params.addValue("branchId", employee.getEmployee().getBranch().getId());
        }

        // For "ceo", no additional condition, so no changes needed to query
     // Build the complete query string
        String countQuery = queryBuilder.toString();

        // Execute the query safely using named parameters
        return namedParameterJdbcTemplate.queryForObject(countQuery, params, (rs, rowNum) -> rs.getInt(1));
    }


    private Integer getTotalEmployeesPresent(Long empId, String role) {
        String baseQuery = "SELECT COUNT(DISTINCT a.EMP_ID) " +
                "FROM t_attendance a " +
                "JOIN t_employee e ON a.EMP_ID = e.EMP_ID " +
                "WHERE a.DATE = CAST(GETDATE()-1 AS DATE) ";



        List<Object> params = new ArrayList<>();

        // Role-based query modification
        // Commented due to updated logic by Sam from Xpertlyte
        // This widget provides the number of employees reporting to the logged-in user who were present on the previous day. It offers insights into attendance trends and helps track workforce consistency.
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery += "AND e.company_id = ? ";
//            params.add(companyId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery += "AND e.company_id = ? AND e.branch_id = ? ";
//            params.add(companyId);
//            params.add(branchId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
            baseQuery += "AND e.UPPER_ID = ? ";
            params.add(empId);
//        } else if ("employee".equalsIgnoreCase(role)) {
//            baseQuery += "AND e.EMP_ID = ? ";
//            params.add(empId);
//        }

        return jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
    }

    private List<Map<String, Object>> getTotalEmployeesPresentList(Long empId, String role) {
        String baseQuery = "SELECT a.EMP_ID AS empId, e.EMP_CODE AS EmployeeCode, e.EMP_NAME AS EmployeeName, e.IMG_URE AS EmployeeImage, d.DEPARTMENT_NAME AS DepartmentName, dg.designation_name AS DesignationName, a.DATE AS Date, a.CHECK_IN_TIME AS PunchInTime, a.CHECK_OUT_TIME AS PunchOutTime, a.OVERTIME AS Overtime, a.MODE_TYPE AS EntryMode  " +
                "FROM t_attendance a " +
                "JOIN t_employee e ON a.EMP_ID = e.EMP_ID " +
                "JOIN t_department d ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                "JOIN t_designation dg ON dg.designation_id = e.DESIGNATION_ID " +
                "WHERE a.DATE = CAST(GETDATE()-1 AS DATE) ";



        List<Object> params = new ArrayList<>();

        // Role-based query modification
        // Commented due to updated logic by Sam from Xpertlyte
        // This widget provides the number of employees reporting to the logged-in user who were present on the previous day. It offers insights into attendance trends and helps track workforce consistency.
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery += "AND e.company_id = ? ";
//            params.add(companyId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery += "AND e.company_id = ? AND e.branch_id = ? ";
//            params.add(companyId);
//            params.add(branchId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
        baseQuery += "AND e.UPPER_ID = ? ";
        params.add(empId);
//        } else if ("employee".equalsIgnoreCase(role)) {
//            baseQuery += "AND e.EMP_ID = ? ";
//            params.add(empId);
//        }

        return jdbcTemplate.queryForList(baseQuery, params.toArray());
    }

    private Integer getTotalEmployeesClockedInToday(Long empId, String role) {
        LocalDate today = LocalDate.now();
        Date sqlToday = Date.valueOf(today);

        String baseQuery = "SELECT COUNT(DISTINCT a.EMP_ID) " +
                "FROM t_attendance_audit a " +
                "JOIN t_employee e ON a.EMP_ID = e.EMP_ID " +
                "WHERE CONVERT(DATE, a.DATE) = ? ";

        List<Object> params = new ArrayList<>();
        params.add(sqlToday);
//      Comment due to updated logic by Sam from Xpertlyte
        // This widget shows the number of employees reporting to the logged-in user who have clocked in on time for the present day. It helps monitor employee punctuality.
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            System.out.println(companyId);
//            baseQuery += "AND e.company_id = ? ";
//            params.add(companyId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery += "AND e.company_id = ? AND e.branch_id = ? ";
//            params.add(companyId);
//            params.add(branchId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
            baseQuery += "AND e.UPPER_ID = ? ";
            params.add(empId);
//        }

        return jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
    }
    // changed logic as per frontend pie chart
    private Map<String, Object> getDepartmentWiseReport(Long empId, String role) {
        Map<String, Object> departmentWiseReport = new HashMap<>();
        List<String> axisLbl = new ArrayList<>();
        List<Double> axis = new ArrayList<>();
        List<String> colors = new ArrayList<>();

        String queryCondition = getQueryCondition(role);
        Long totalEmployees = Long.valueOf(getEmployeeCount(empId, role));

        String query = "SELECT d.department_name AS name, h.hospital_name AS hospitalName, " +
                "ROUND(CAST(COUNT(e.EMP_ID) AS FLOAT) / ? * 100, 2) AS value " +
                "FROM t_department d " +
                "JOIN t_employee e ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                "JOIN feeder_hospitals h ON e.company_id = h.id " +
                queryCondition +
                " GROUP BY d.department_name, h.hospital_name";

        List<Map<String, Object>> departmentData;

        if ("superadmin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            departmentData = jdbcTemplate.queryForList(query, totalEmployees, companyId);
        } else if ("admin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            Long branchId = getBranchId(empId);
            departmentData = jdbcTemplate.queryForList(query, totalEmployees, companyId, branchId);
        } else if ("supervisor".equalsIgnoreCase(role)) {
            departmentData = jdbcTemplate.queryForList(query, totalEmployees, empId);
        } else if ("employee".equalsIgnoreCase(role)) {
            departmentData = jdbcTemplate.queryForList(query, totalEmployees, empId);
        } else {
            departmentData = jdbcTemplate.queryForList(query, totalEmployees);
        }

        for (Map<String, Object> row : departmentData) {
            axisLbl.add((String) row.get("name") + " - " + (String) row.get("hospitalName"));
            Double percentageValue = (Double) row.get("value");

            axis.add(percentageValue != null && percentageValue > 0 ? percentageValue : 0.1);

            colors.add(getRandomBlueShade());
        }

        departmentWiseReport.put("axis", axis);
        departmentWiseReport.put("axisLbl", axisLbl);
        departmentWiseReport.put("colors", colors);

        return departmentWiseReport;
    }

    private List<Map<String, Object>> getTotalDepartments(Long empId, String role) {
        String queryCondition = getQueryCondition(role);
        Long totalEmployees = Long.valueOf(getEmployeeCount(empId, role));

        String query = "SELECT d.DEPARTMENT_NAME AS name, h.hospital_name AS hospitalName, " +
                "ROUND(CAST(COUNT(e.EMP_ID) AS FLOAT) / ? * 100, 2) AS value " +
                "FROM t_department d " +
                "JOIN t_employee e ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                "JOIN feeder_hospitals h ON e.company_id = h.id " +
                queryCondition +
                " GROUP BY d.DEPARTMENT_NAME, h.hospital_name";

        if ("superadmin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            return jdbcTemplate.queryForList(query, totalEmployees, companyId);
        } else if ("admin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            Long branchId = getBranchId(empId);
            return jdbcTemplate.queryForList(query, totalEmployees, companyId, branchId);
        } else if ("supervisor".equalsIgnoreCase(role)) {
            return jdbcTemplate.queryForList(query, totalEmployees, empId);
        } else if ("employee".equalsIgnoreCase(role)) {
            return jdbcTemplate.queryForList(query, totalEmployees, empId);
        }

        return jdbcTemplate.queryForList(query, totalEmployees);
    }

    private Integer getTotalEmployeesOnLeave(Long empId, String role) {
        LocalDate today = LocalDate.now();
        Date sqlToday = Date.valueOf(today);

        String baseQuery = "SELECT COUNT(DISTINCT l.emp_id) " +
                "FROM t_employee_leave_history l " +
                "JOIN t_employee e ON l.emp_id = e.EMP_ID " +
                "WHERE l.approval_status = 'APPROVED' " +
                "AND CAST(GETDATE() AS DATE) BETWEEN l.START_DATE AND l.END_DATE ";

        List<Object> params = new ArrayList<>();
        //params.add(empId);
//        params.add(sqlToday);
//        params.add(sqlToday);

        // Commented due to updated logic by Sam from Xpertlyte Date: 11-03-2025
        //This widget displays the number of employees currently on leave who report to the logged-in user. It assists in assessing workforce availability and making necessary adjustments for operational efficiency
//       if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery += "AND e.company_id = ? ";
//            params.add(companyId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery += "AND e.company_id = ? AND e.branch_id = ? ";
//            params.add(companyId);
//            params.add(branchId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
            baseQuery += "AND e.UPPER_ID = ? ";
            params.add(empId);
        //}

        return jdbcTemplate.queryForObject(baseQuery, Integer.class, params.toArray());
    }

    private List<Map<String, Object>> getScheduleWiseReport(Long empId, String role) {
        String baseQuery = "SELECT TOP 3 CONCAT(CONVERT(VARCHAR(8), ts.start_time, 108), '-', CONVERT(VARCHAR(8), ts.end_time, 108)) AS name, " +
                "ROUND(CAST(COUNT(DISTINCT es.EMP_ID) AS FLOAT) / ? * 100, 2) AS value " +
                "FROM t_time_slot ts " +
                "LEFT JOIN t_employee_schedule es ON ts.time_slot_id = COALESCE(es.modified_shift, es.actual_shift) ";

        List<Object> params = new ArrayList<>();

        Long totalEmployees = Long.valueOf(getEmployeeCount(empId, role));
        params.add(totalEmployees);

        if ("ceo".equalsIgnoreCase(role)) {
            baseQuery += "GROUP BY ts.start_time, ts.end_time ";
        } else if ("superadmin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            baseQuery += "WHERE es.EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE company_id = ?) ";
            baseQuery += "GROUP BY ts.start_time, ts.end_time ";
            params.add(companyId);
        } else if ("admin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            Long branchId = getBranchId(empId);
            baseQuery += "WHERE es.EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE company_id = ? AND branch_id = ?) ";
            baseQuery += "GROUP BY ts.start_time, ts.end_time ";
            params.add(companyId);
            params.add(branchId);
        } else if ("supervisor".equalsIgnoreCase(role)) {
            baseQuery += "WHERE es.EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) ";
            baseQuery += "GROUP BY ts.start_time, ts.end_time ";
            params.add(empId);
        } else if ("employee".equalsIgnoreCase(role)) {
            baseQuery += "WHERE es.EMP_ID = ? ";
            baseQuery += "GROUP BY ts.start_time, ts.end_time ";
            params.add(empId);
        }

        baseQuery += "ORDER BY value DESC";

        return jdbcTemplate.queryForList(baseQuery, params.toArray());
    }

    private List<Map<String, Object>> getTop3Branches(Long empId, String role) {
        String baseQuery = "SELECT TOP 3 b.branch_name AS name, COUNT(e.EMP_ID) AS employeeCount, h.hospital_name AS hospitalName " +
                "FROM t_branch b " +
                "JOIN t_employee e ON b.branch_id = e.branch_id " +
                "JOIN feeder_hospitals h ON b.company_id = h.id " +
                "WHERE e.company_id = ? ";

        List<Object> params = new ArrayList<>();
        params.add(getCompanyId(empId));

        String queryCondition = getQueryCondition2(role, empId, params);

        String finalQuery = baseQuery + queryCondition + " GROUP BY b.branch_name, h.hospital_name ORDER BY COUNT(e.EMP_ID) DESC";

        System.out.println("qurey" + finalQuery);
        List<Map<String, Object>> top3branches = jdbcTemplate.queryForList(finalQuery, params.toArray());

        for (Map<String, Object> branch : top3branches) {
            Integer employeeCount = (Integer) branch.get("employeeCount");
            branch.put("value", employeeCount);
            branch.remove("employeeCount");
        }

        return top3branches;
    }

    private String getQueryCondition2(String role, Long empId, List<Object> params) {
        if ("admin".equalsIgnoreCase(role)) {
            params.add(getBranchId(empId));
            return "AND e.branch_id = ? ";
        } else if ("supervisor".equalsIgnoreCase(role)) {
            params.add(empId);
            return "AND e.UPPER_ID = ? ";
        }
        return "";
    }

    //show to deepak
    private String getQueryCondition(String role) {
        if ("superadmin".equalsIgnoreCase(role)) {
            return "WHERE company_id = ?";
        } else if ("admin".equalsIgnoreCase(role)) {
            return "WHERE company_id = ? AND branch_id = ?";
        } else if ("supervisor".equalsIgnoreCase(role)) {
            return "WHERE UPPER_ID = ?";
        }
        return "";
    }

    private Long getCompanyId(Long empId) {
        return jdbcTemplate.queryForObject("SELECT COMPANY_ID FROM t_employee WHERE EMP_ID = ?", Long.class, empId);
    }

    private Integer getTotalEnrollmentsCount(Long empId, String role) {
        String queryCondition = getQueryCondition(role);
        String query = "SELECT COUNT(*) FROM t_employee " + queryCondition;

        if ("ceo".equalsIgnoreCase(role)) {
            return jdbcTemplate.queryForObject("SELECT COUNT(*) FROM t_employee", Integer.class);
        } else if ("superadmin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            return jdbcTemplate.queryForObject(query, Integer.class, companyId);
        } else if ("admin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            Long branchId = getBranchId(empId);
            return jdbcTemplate.queryForObject(query, Integer.class, companyId, branchId);
        } else if ("supervisor".equalsIgnoreCase(role)) {
            return jdbcTemplate.queryForObject(query, Integer.class, empId);
        } else if ("employee".equalsIgnoreCase(role)) {
            return jdbcTemplate.queryForObject(query, Integer.class, empId);
        }

        return 0;
    }

    private String getRandomBlueShade() {
        Random rand = new Random();
        String shade = "";
        int colorChoice = rand.nextInt(2);

        if (colorChoice == 0) {
            int blue = rand.nextInt(155) + 100;
            shade = String.format("#0000%02x", blue);
        } else {
            int red = rand.nextInt(56) + 200;
            int green = rand.nextInt(101) + 100;
            shade = String.format("#%02x%02x00", red, green);
        }
        return shade;
    }

    private Integer getTotalLeaveRequestCount(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee_leave_history WHERE emp_id IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?)", Integer.class, empId);
    }

    private Integer getPendingLeaveRequests(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee_leave_history WHERE emp_id IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND approval_status = 'PENDING'", Integer.class, empId);
    }

    private Integer getTotalLeavesApproved(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee_leave_history WHERE emp_id IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND approval_status = 'APPROVED'", Integer.class, empId);
    }

    private Integer getTotalLeavesRejected(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee_leave_history WHERE emp_id IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND approval_status = 'DENIED'", Integer.class, empId);
    }

    private Integer getTotalModificationsRequestCount(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_attendance_regularization WHERE employee_id IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?)", Integer.class, empId);
    }

    private Integer getPendingModificationsRequests(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_attendance_regularization WHERE employee_id IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND approval_status = 'PENDING'", Integer.class, empId);
    }

    private Integer getTotalModificationsApproved(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_attendance_regularization WHERE employee_id IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND approval_status = 'APPROVED'", Integer.class, empId);
    }

    private Integer getTotalModificationsRejected(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_attendance_regularization WHERE employee_id IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND approval_status = 'REJECTED'", Integer.class, empId);
    }

    private Integer getPendingShiftChangeRequests(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_shift_swap WHERE SWAP_REQUESTED_BY_EMPLOYEE_ID IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND STATUS = 'PENDING'", Integer.class, empId);
    }

    private Integer getTotalShiftChangeApproved(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_shift_swap WHERE SWAP_REQUESTED_BY_EMPLOYEE_ID IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND STATUS = 'APPROVED'", Integer.class, empId);
    }

    private Integer getTotalShiftChangeRejected(Long empId) {
        return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_shift_swap WHERE SWAP_REQUESTED_BY_EMPLOYEE_ID IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) AND STATUS = 'DENIED'", Integer.class, empId);
    }

    private Integer getTotalTicketsOfMyEmployees(Long empId) {
        String query = "SELECT COUNT(*) FROM t_ticket " +
                "WHERE assigned_to IN (SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?) " +
                "OR assigned_to = ?" +
                "OR created_by = ?";
        return jdbcTemplate.queryForObject(query, Integer.class, empId, empId,empId);
    }

    private Integer getTicketsAssignedToMe(Long empId) {
        String query = "SELECT COUNT(*) FROM t_ticket WHERE assigned_to = ?";
        return jdbcTemplate.queryForObject(query, Integer.class, empId);
    }

    private Integer getTicketsCreatedByMe(Long empId) {
        String query = "SELECT COUNT(*) FROM t_ticket WHERE created_by = ?";
        return jdbcTemplate.queryForObject(query, Integer.class, empId);
    }

//present employee list
    public Map<String, Object> getPresentEmployeesDetails(int offset, int limit) {
        Long empId;
        String role;
        try {
            // Retrieve employee ID and role from token
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }
            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee details from token", e);
            throw new RuntimeException("Unable to retrieve employee details from the token. Please try again later.");
        }

        // Construct query based on role
        StringBuilder baseQuery = new StringBuilder(
                "SELECT e.EMP_ID " +
                        "FROM t_attendance a " +
                        "JOIN t_employee e ON a.EMP_ID = e.EMP_ID " +
                        "WHERE a.DATE = CAST(GETDATE() AS DATE) "
        );
        List<Object> params = new ArrayList<>();

        // Add role-based conditions dynamically
        if ("superadmin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            baseQuery.append("AND e.company_id = ? ");
            params.add(companyId);
        } else if ("admin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            Long branchId = getBranchId(empId);
            baseQuery.append("AND e.company_id = ? AND e.branch_id = ? ");
            params.add(companyId);
            params.add(branchId);
        } else if ("supervisor".equalsIgnoreCase(role)) {
            baseQuery.append("AND e.UPPER_ID = ? ");
            params.add(empId);
        } else if ("employee".equalsIgnoreCase(role)) {
            baseQuery.append("AND e.EMP_ID = ? ");
            params.add(empId);
        }

        // Add pagination
        baseQuery.append("GROUP BY e.EMP_ID ");
        baseQuery.append("ORDER BY e.EMP_ID OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
        params.add(offset);
        params.add(limit);

        try {
            List<Long> empIds = jdbcTemplate.query(baseQuery.toString(), (rs, rowNum) -> rs.getLong("EMP_ID"), params.toArray());
            if (empIds.isEmpty()) {
                logger.info("No present employees found for today's date.");
                return Collections.emptyMap();
            }

            List<EmployeeVO> employeeDetails = fetchEmployeeDetails(empIds);

            // Fetch total elements (total count of employees)
            String countQuery = baseQuery.toString().replace("SELECT e.EMP_ID", "SELECT COUNT(DISTINCT e.EMP_ID)");
            int totalElements = jdbcTemplate.queryForObject(countQuery, Integer.class, params.toArray());

            // Calculate total pages
            int totalPages = (int) Math.ceil((double) totalElements / limit);
         // Prepare response with pagination details at the bottom
            Map<String, Object> response = new HashMap<>();
            response.put("content", employeeDetails);  // Add employee details

            Map<String, Object> paginationDetails = new HashMap<>();
            paginationDetails.put("number", offset / limit);
            paginationDetails.put("size", limit);
            paginationDetails.put("numberOfElements", employeeDetails.size());
            paginationDetails.put("totalPages", totalPages);
            paginationDetails.put("totalElements", totalElements);
            paginationDetails.put("sort", Map.of("sorted", true, "unsorted", false, "empty", false));

           // Add pagination details separately in the response
            response.put("pagination", paginationDetails);

            return response;
        } catch (DataAccessException e) {
            logger.error("Database error while fetching present employees", e);
            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
        } catch (Exception e) {
            logger.error("Unexpected error", e);
            throw new RuntimeException("An unexpected error occurred. Please contact support.");
        }
    }




    private List<EmployeeVO> fetchEmployeeDetails(List<Long> empIds) {
        String employeeDetailsQuery = """
        SELECT e.*,
               d.department_name AS department_name,
               b.branch_name AS branch_name,
               ds.designation_name AS designation_name
        FROM t_employee e
        JOIN t_department d ON e.department_id = d.department_id
        JOIN t_branch b ON e.branch_id = b.branch_id
        JOIN t_designation ds ON e.designation_id = ds.designation_id
        WHERE e.EMP_ID = ?
    """;

        String documentQuery = """
        SELECT ed.*, dm.name AS document_name, dm.type AS document_type
        FROM t_employee_document ed
        JOIN t_document_master dm ON ed.document_id = dm.DOCUMENT_ID
        WHERE ed.emp_id = ? AND ed.is_active = 1
    """;

        String leaveBalanceQuery = """
        SELECT leb.emp_id, leb.leave_id, leb.assigned_leave, leb.balance_leave
        FROM t_employee_leave_balance leb
        WHERE leb.emp_id = ?
    """;

        List<EmployeeVO> employeeDetails = new ArrayList<>();
        for (Long id : empIds) {
            try {
                // Fetch employee basic details
                EmployeeVO employee = jdbcTemplate.queryForObject(employeeDetailsQuery, (rs, rowNum) ->
                        EmployeeVO.builder()
                                .empId(rs.getLong("emp_id"))
                                .companyId(rs.getLong("company_id"))
                                .departmentId(rs.getLong("department_id"))
                                .department(rs.getString("department_name"))
                                .empCode(rs.getString("emp_code"))
                                .empName(rs.getString("emp_name"))
                                .uid(rs.getString("uid"))
                                .biometricID(rs.getString("uid"))
                                .idNo(rs.getString("id_no"))
                                .upperId(rs.getLong("upper_id"))
                                .regionId(rs.getLong("region_id"))
                                .countryId(rs.getLong("country_id"))
                                .provinceId(rs.getLong("province_id"))
                                .city(rs.getString("city"))
                                .upperName(rs.getString("upper_name"))
                                .hireDate(rs.getDate("hire_date") != null ? rs.getDate("hire_date") : null)
                                .gender(rs.getString("gender"))
                                .birthday(rs.getDate("birthday") != null ? rs.getDate("birthday") : null)
                                .nation(rs.getString("nation"))
                                .married(rs.getBoolean("married"))
                                .phoneNo(rs.getString("phone_no"))
                                .mobileNo(rs.getString("mobile_no"))
                                .email(rs.getString("email"))
                                .nativePlace(rs.getString("native_place"))
                                .zipCode(rs.getString("zip_code"))
                                .isHistory(rs.getBoolean("is_history"))
                                .inService(rs.getBoolean("in_service"))
                                .remark(rs.getString("remark"))
                                .createdBy(rs.getLong("created_by"))
                                .createdTime(rs.getTimestamp("created_time") != null ? rs.getTimestamp("created_time").toLocalDateTime() : null)
                                .updatedBy(rs.getLong("updated_by"))
                                .updatedTime(rs.getTimestamp("updated_time") != null ? rs.getTimestamp("updated_time").toLocalDateTime() : null)
                                .version(rs.getLong("version"))
                                .nativeLanguage(rs.getString("native_language"))
                                .foreignLanguages(rs.getString("foreign_languages"))
                                .workYears(rs.getInt("work_years"))
                                .graduateSchool(rs.getString("graduate_school"))
                                .graduateTime(rs.getDate("graduate_time") != null ? rs.getDate("graduate_time") : null)
                                .highestDegree(rs.getString("highest_degree"))
                                .imgUre(rs.getString("img_ure"))
                                .branchId(rs.getLong("branch_id"))
                                .branchName(rs.getString("branch_name"))
                                .workingDayCountInWeek(rs.getString("working_day_count_in_week"))
                                .leaveOnDays(rs.getString("leave_on_days"))
                                .designationId(rs.getLong("designation_id"))
                                .designation(rs.getString("designation_name"))
                                .defaultTimeSlotId(rs.getLong("default_time_slot_id"))
                                .unitNumber(rs.getString("unit_no"))
                                .street(rs.getString("street"))
                                .alternateNumber(rs.getString("alternateNumber"))
                                .emergencyContact1(rs.getString("emergencyContact1"))
                                .emergencyContact2(rs.getString("emergencyContact2"))
                                .emergencyContactname1(rs.getString("emergencyContactname1"))
                                .emergencyContactname2(rs.getString("emergencyContactname2"))
                                .alternateEmail(rs.getString("alternateEmail"))
                                .build(), id);

                if (employee != null) {
                    // Fetch documents
                    List<EmployeeDocumentVO> documents = jdbcTemplate.query(documentQuery, (docRs, docRowNum) ->
                            EmployeeDocumentVO.builder()
                                    .id(docRs.getLong("id"))
                                    .documentName(docRs.getString("document_name"))
                                    .documentType(docRs.getString("document_type"))
                                    .build(), id);
                    employee.setDocuments(documents);

                    // Fetch leave balances
                    List<EmployeeLeaveBalanceDto> leaveBalances = jdbcTemplate.query(leaveBalanceQuery, (rs, rowNum) ->
                            EmployeeLeaveBalanceDto.builder()
                                    .empId(rs.getLong("emp_id"))
                                    .leaveId(rs.getLong("leave_id"))
                                    .assignedLeave(rs.getInt("assigned_leave"))
                                    .balanceLeave(rs.getInt("balance_leave"))
                                    .build(), id);
                    employee.setLeaveBalances(leaveBalances);

                    employeeDetails.add(employee);
                }
            } catch (DataAccessException e) {
                logger.error("Error fetching details for employee ID: " + id, e);
            }
        }

        return employeeDetails;
    }



    public Map<String, Object> getEmployeesOnLeave(int offset, int limit) {
        Long empId;
        String role;

        // Step 1: Get employee ID and role from token
        try {
            // Retrieve employee ID and role from token
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }
            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee details from token", e);
            throw new RuntimeException("Unable to retrieve employee details from the token. Please try again later.");
        }

        // Step 2: Get today's date
        LocalDate today = LocalDate.now();
        Date sqlToday = Date.valueOf(today);

        // Step 3: Build the base query dynamically based on the role
        StringBuilder baseQuery = new StringBuilder(
                "SELECT e.EMP_ID " +
                        "FROM t_employee_leave_history l " +
                        "JOIN t_employee e ON l.emp_id = e.EMP_ID " +
                        "WHERE l.approval_status = 'APPROVED' " +
                        "AND CAST(GETDATE() AS DATE) BETWEEN l.START_DATE AND l.END_DATE " +
                        "AND e.UPPER_ID = ?"

        );

        List<Object> params = new ArrayList<>();
          params.add(empId);
//        params.add(sqlToday);
//        params.add(sqlToday);

        // Step 4: Adjust the query based on role
        if ("superadmin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            baseQuery.append("AND e.company_id = ? ");
            params.add(companyId);
        } else if ("admin".equalsIgnoreCase(role)) {
            Long companyId = getCompanyId(empId);
            Long branchId = getBranchId(empId);
            baseQuery.append("AND e.company_id = ? AND e.branch_id = ? ");
            params.add(companyId);
            params.add(branchId);
        } else if ("supervisor".equalsIgnoreCase(role)) {
            baseQuery.append("AND e.UPPER_ID = ? ");
            params.add(empId);
        }

        // Step 5: Add GROUP BY and pagination to the query
        baseQuery.append("GROUP BY e.EMP_ID ");
        baseQuery.append("ORDER BY e.EMP_ID OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
        params.add(offset);
        params.add(limit);

        try {
            // Step 6: Fetch employee IDs
            List<Long> empIds = jdbcTemplate.query(baseQuery.toString(),
                    (rs, rowNum) -> rs.getLong("EMP_ID"),
                    params.toArray());

            if (empIds.isEmpty()) {
                logger.info("No employees on leave found for today's date.");
                return Collections.emptyMap();  // No employees on leave
            }

            // Step 7: Fetch employee details
            List<EmployeeVO> employeeDetails = fetchEmployeeDetails(empIds);
            logger.info("Fetched {} employee details.", employeeDetails.size());

            // Step 8: Fetch total elements (count of employees on leave)
            String countQuery = baseQuery.toString().replace("SELECT e.EMP_ID", "SELECT COUNT(DISTINCT e.EMP_ID)");

            // Use queryForObject and handle the case where there might be multiple rows
            List<Integer> totalElementsList = jdbcTemplate.query(countQuery, (rs, rowNum) -> rs.getInt(1), params.toArray());


            int totalElements = totalElementsList.isEmpty() ? 0 : totalElementsList.get(0);
            // Step 9: Calculate total pages
            int totalPages = (int) Math.ceil((double) totalElements / limit);

            // Step 10: Prepare the response with pagination details
            Map<String, Object> response = new HashMap<>();
            response.put("content", employeeDetails);  // Add employee details

            Map<String, Object> paginationDetails = new HashMap<>();
            paginationDetails.put("number", offset / limit);  // current page number
            paginationDetails.put("size", limit);  // number of records per page
            paginationDetails.put("numberOfElements", employeeDetails.size());  // elements on current page
            paginationDetails.put("totalPages", totalPages);  // total number of pages
            paginationDetails.put("totalElements", totalElements);  // total number of employees on leave
            paginationDetails.put("sort", Map.of("sorted", true, "unsorted", false, "empty", false));

            // Step 11: Add pagination details to the response
            response.put("pagination", paginationDetails);

            return response;
        } catch (DataAccessException e) {
            logger.error("Database error while fetching employees on leave", e);
            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
        } catch (Exception e) {
            logger.error("Unexpected error", e);
            throw new RuntimeException("An unexpected error occurred. Please contact support.");
        }
    }

    public List<Map<String, Object>> getEmployeesOnLeave1() {
        Long empId;
        String role;

        // Step 1: Get employee ID and role from token
        try {
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }
            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee details from token", e);
            throw new RuntimeException("Unable to retrieve employee details from the token. Please try again later.");
        }

        // Step 2: Get today's date
        LocalDate today = LocalDate.now();
        Date sqlToday = Date.valueOf(today);

        // Step 3: Build the base query dynamically
        StringBuilder baseQuery = new StringBuilder(
                "SELECT e.EMP_CODE AS EmployeeCode, e.IMG_URE AS EmployeeImage, e.EMP_NAME AS EmployeeName,  d.DEPARTMENT_NAME AS DepartmentName, dg.designation_name AS DesignationName, lm.type AS LeaveType, " +
                        "CONVERT(VARCHAR(10), l.START_DATE, 120) AS StartDate, " +
                        "CONVERT(VARCHAR(10), l.END_DATE, 120) AS EndDate, " +
                        "l.applied_leave_count AS LeaveDays " +
                        "FROM t_employee e " +
                        "JOIN t_employee_leave_history l ON e.EMP_ID = l.EMP_ID " +
                        "JOIN t_leave_master lm ON l.leave_id = lm.leave_id " +
                        "JOIN t_department d ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                        "JOIN t_designation dg ON dg.designation_id = e.DESIGNATION_ID " +
                        "WHERE CAST(GETDATE() AS DATE) BETWEEN l.START_DATE AND l.END_DATE " +
                        "AND l.APPROVAL_STATUS = 'APPROVED' " +
                        "AND e.UPPER_ID = ? "
        );


        List<Object> params = new ArrayList<>();
        params.add(empId);
        logger.info("The upper id will be :"+empId);
//        params.add(sqlToday); // Add today's date
//
//        // Step 4: Adjust the query based on role
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery.append("e.UPPER_ID = ? ");
//            params.add(empId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery.append("e.UPPER_ID = ? ");
//            params.add(empId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
//            baseQuery.append("AND e.UPPER_ID = ? ");
//            params.add(empId);
//        }

        // Step 5: Add pagination
//        baseQuery.append("ORDER BY e.EMP_ID OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
//        params.add(offset);
//        params.add(limit);

        try {
            // Step 6: Fetch employees on leave
            List<Map<String, Object>> empIds = jdbcTemplate.queryForList(baseQuery.toString(), params.toArray());

            if (empIds.isEmpty()) {
                logger.info("No employees on leave found for today's date.");
                return Collections.emptyList();  // ✅ FIX: Return an empty list instead of an empty map
            }
            return empIds;
        } catch (DataAccessException e) {
            logger.error("Database error while fetching employees on leave", e);
            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
        }
    }
    public List<Map<String, Object>> getLeaveRequestApprovedPastDays() {
        Long empId;
        String role;

        Long hospitalId = getHospitalId(tokenService.getEmployeeIdFromToken());
        // Step 1: Get employee ID and role from token
        try {
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }
            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee details from token", e);
            throw new RuntimeException("Unable to retrieve employee details from the token. Please try again later.");
        }

//        // Step 2: Get today's date
//        LocalDate today = LocalDate.now();
//        Date sqlToday = Date.valueOf(today);

        // Step 3: Build the base query dynamically
        StringBuilder baseQuery = new StringBuilder(
                "SELECT " +
                        "e.EMP_CODE AS EmployeeCode, " +
                        "e.IMG_URE AS EmployeeImage, " +
                        "e.EMP_NAME AS EmployeeName, " +
                        "d.DEPARTMENT_NAME AS DepartmentName, " +
                        "dg.designation_name AS DesignationName, " +
                        "lm.type AS leavetype, " +
                        "CONVERT(VARCHAR(10), l.START_DATE, 120) AS StartDate, " +
                        "CONVERT(VARCHAR(10), l.END_DATE, 120) AS EndDate, " +
                        "l.applied_leave_count AS LeaveDays " +
                        "FROM t_employee e " +
                        "JOIN t_employee_leave_history l ON e.EMP_ID = l.EMP_ID " +
                        "JOIN t_leave_master lm ON l.leave_id = lm.leave_id " +
                        "JOIN t_department d ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                        "JOIN t_designation dg ON dg.designation_id = e.DESIGNATION_ID " +
                        "WHERE l.approval_status = 'APPROVED' " +
                        "AND e.EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE company_id = ?) " +
                        "AND l.updated_time >= DATEADD(DAY, -30, GETDATE())"

        );


        List<Object> params = new ArrayList<>();
        params.add(hospitalId);
        logger.info("The hospital id will be :"+hospitalId);
//        params.add(sqlToday); // Add today's date
//
//        // Step 4: Adjust the query based on role
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery.append("e.UPPER_ID = ? ");
//            params.add(empId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery.append("e.UPPER_ID = ? ");
//            params.add(empId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
//            baseQuery.append("AND e.UPPER_ID = ? ");
//            params.add(empId);
//        }

        // Step 5: Add pagination
//        baseQuery.append("ORDER BY e.EMP_ID OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
//        params.add(offset);
//        params.add(limit);

        try {
            // Step 6: Fetch employees on leave
            List<Map<String, Object>> empIds = jdbcTemplate.queryForList(baseQuery.toString(), params.toArray());

            if (empIds.isEmpty()) {
                logger.info("No data found for past 30 days.");
                return Collections.emptyList();  // ✅ FIX: Return an empty list instead of an empty map
            }
            return empIds;
        } catch (DataAccessException e) {
            logger.error("Database error while fetching approved leave requests within past 30 days", e);
            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
        }
    }
    public List<Map<String, Object>> getEmployeeOnClockedInDetails(){
        Long empId;
        String role;

        // Step 1: Get employee ID and role from token
        try {
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }
            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee details from token", e);
            throw new RuntimeException("Unable to retrieve employee details from the token. Please try again later.");
        }

        // Step 2: Get today's date
        LocalDate today = LocalDate.now();
        Date sqlToday = Date.valueOf(today);

        // Step 3: Build the base query dynamically
        StringBuilder baseQuery = new StringBuilder(
                "SELECT  e.EMP_CODE AS EmployeeCode, e.IMG_URE AS EmployeeImage, e.EMP_NAME AS EmployeeName,  d.DEPARTMENT_NAME AS DepartmentName, dg.designation_name AS DesignationName, " +
                        "MIN(a.PUNCH_IN_TIME) AS FirstPunchInTime, " +
                        "COUNT(a.PUNCH_IN_TIME) AS PunchInCount " +
                        "FROM t_attendance_audit a " +
                        "JOIN t_employee e ON a.EMP_ID = e.EMP_ID " +
                        "JOIN t_department d ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                        "JOIN t_designation dg ON dg.designation_id = e.DESIGNATION_ID " +
                        "WHERE a.[DATE] = CAST(GETDATE() AS DATE) AND e.UPPER_ID = ? " +
                        "GROUP BY e.EMP_ID, e.EMP_NAME, e.EMP_CODE, d.DEPARTMENT_NAME, e.IMG_URE, dg.designation_name  "
        );

        List<Object> params = new ArrayList<>();
        params.add(empId);
        logger.info("The upper id will be :"+empId);

        // Step 5: Add pagination
//        baseQuery.append("ORDER BY e.EMP_ID OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
//        params.add(offset);
//        params.add(limit);

        try {
            // Step 6: Fetch employees on leave
            List<Map<String, Object>> empIds = jdbcTemplate.queryForList(baseQuery.toString(), params.toArray());

            if (empIds.isEmpty()) {
                logger.info("No data found for today's date.");
                return Collections.emptyList();  // ✅ FIX: Return an empty list instead of an empty map
            }
            return empIds;
        } catch (DataAccessException e) {
            logger.error("Database error while fetching employees present today", e);
            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
        }
    }
    public List<Map<String, Object>> getTotalEmployeesPresentList() {


        String baseQuery = "SELECT e.EMP_CODE AS EmployeeCode, e.IMG_URE AS EmployeeImage, e.EMP_NAME AS EmployeeName,  d.DEPARTMENT_NAME AS DepartmentName, dg.designation_name AS DesignationName, " +
                "CONVERT(VARCHAR(10), a.DATE, 120) AS StartDate, " +
                "a.CHECK_IN_TIME AS PunchInTime, a.CHECK_OUT_TIME AS PunchOutTime, a.OVERTIME AS Overtime, a.MODE_TYPE AS EntryMode  " +
                "FROM t_attendance a " +
                "JOIN t_employee e ON a.EMP_ID = e.EMP_ID " +
                "JOIN t_department d ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                "JOIN t_designation dg ON dg.designation_id = e.DESIGNATION_ID " +
                "WHERE a.DATE = CAST(GETDATE()-1 AS DATE) ";



        List<Object> params = new ArrayList<>();

        // Role-based query modification
        // Commented due to updated logic by Sam from Xpertlyte
        // This widget provides the number of employees reporting to the logged-in user who were present on the previous day. It offers insights into attendance trends and helps track workforce consistency.
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery += "AND e.company_id = ? ";
//            params.add(companyId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery += "AND e.company_id = ? AND e.branch_id = ? ";
//            params.add(companyId);
//            params.add(branchId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
        baseQuery += "AND e.UPPER_ID = ? ";
        params.add(tokenService.getEmployeeIdFromToken());
//        } else if ("employee".equalsIgnoreCase(role)) {
//            baseQuery += "AND e.EMP_ID = ? ";
//            params.add(empId);
//        }
        try {
            List<Map<String, Object>> presentList = jdbcTemplate.queryForList(baseQuery, params.toArray());

            if (presentList.isEmpty()) {
                logger.info("No employees found for yesterday's date.");
                return Collections.emptyList();
            }
            return presentList;
        }  catch (DataAccessException e) {
            logger.error("Database error while fetching employees on leave", e);
            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
        }


    }
    public List<Map<String, Object>> getPendingLeaveRequest() {
        Long empId;
        String role;

        // Step 1: Get employee ID and role from token
        try {
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }
            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee details from token", e);
            throw new RuntimeException("Unable to retrieve employee details from the token. Please try again later.");
        }

//        // Step 2: Get today's date
//        LocalDate today = LocalDate.now();
//        Date sqlToday = Date.valueOf(today);

        // Step 3: Build the base query dynamically
        StringBuilder baseQuery = new StringBuilder(
                "SELECT  e.EMP_CODE AS empCode, e.IMG_URE AS EmployeeImage, e.EMP_NAME AS EmployeeName,  d.DEPARTMENT_NAME AS DepartmentName, dg.designation_name AS DesignationName, lm.type AS LeaveType, " +
                        "CONVERT(VARCHAR(10), l.START_DATE, 120) AS startDate, " +
                        "CONVERT(VARCHAR(10), l.END_DATE, 120) AS endDate, " +
                        "l.applied_leave_count " +
                        "FROM t_employee e " +
                        "JOIN t_employee_leave_history l ON e.EMP_ID = l.EMP_ID " +
                        "JOIN t_leave_master lm ON l.leave_id = lm.leave_id " +
                        "JOIN t_department d ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                        "JOIN t_designation dg ON dg.designation_id = e.DESIGNATION_ID " +
                        "WHERE l.APPROVAL_STATUS = 'PENDING' " +
                        "AND e.UPPER_ID = ? "
        );


        List<Object> params = new ArrayList<>();
        params.add(empId);
        logger.info("The upper id will be :"+empId);
//        params.add(sqlToday); // Add today's date
//
//        // Step 4: Adjust the query based on role
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery.append("e.UPPER_ID = ? ");
//            params.add(empId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery.append("e.UPPER_ID = ? ");
//            params.add(empId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
//            baseQuery.append("AND e.UPPER_ID = ? ");
//            params.add(empId);
//        }

        // Step 5: Add pagination
//        baseQuery.append("ORDER BY e.EMP_ID OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
//        params.add(offset);
//        params.add(limit);

        try {
            // Step 6: Fetch employees on leave
            List<Map<String, Object>> empIds = jdbcTemplate.queryForList(baseQuery.toString(), params.toArray());

            if (empIds.isEmpty()) {
                logger.info("No pending leave requests were found.");
                return Collections.emptyList();  // ✅ FIX: Return an empty list instead of an empty map
            }
            return empIds;
        } catch (DataAccessException e) {
            logger.error("Database error while fetching pending leave request", e);
            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
        }
    }
    public List<Map<String, Object>> getFacilityDetails() {
        Long empId;
        String role;
        String joinQuery = "";


        // Step 1: Get employee ID and role from token
        try {
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }
            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }
        } catch (Exception e) {
            logger.error("Error retrieving employee details from token", e);
            throw new RuntimeException("Unable to retrieve employee details from the token. Please try again later.");
        }
        if("admin".equalsIgnoreCase(role)) {
            joinQuery = "LEFT JOIN t_employee e ON e.BRANCH_ID = b.branch_id ";
        }
        List<Object> params = new ArrayList<>();

//        logger.info("The hospital id will be :"+hospitalId);

//        // Step 2: Get today's date
//        LocalDate today = LocalDate.now();
//        Date sqlToday = Date.valueOf(today);

        // Step 3: Build the base query dynamically
        StringBuilder baseQueryOfCEO = new StringBuilder(
                "SELECT " +
                        "b.branch_code AS BranchCode, " +
                        "b.branch_name AS BranchName, " +
                        "b.BRANCH_HEAD_NAME AS BranchHeadName, " +
                        "b.leave_credit_day AS LeaveCreditDay, " +
                        "b.timezone AS TimeZone " +
                        "FROM t_branch b " +
                        "WHERE 1=1 "
        );
        StringBuilder baseQuery = new StringBuilder(
                "SELECT " +
                        "b.branch_code AS BranchCode, " +
                        "b.branch_name AS BranchName, " +
                        "b.BRANCH_HEAD_NAME AS BranchHeadName, " +
                        "b.leave_credit_day AS LeaveCreditDay, " +
                        "b.timezone AS TimeZone " +
                        "FROM t_branch b " +
                        joinQuery +
                        "WHERE 1=1 "
        );
        if("ceo".equalsIgnoreCase(role)) {
            baseQuery = baseQueryOfCEO;
        } else if ("superadmin".equalsIgnoreCase(role)) {
            Long hospitalId = getHospitalId(empId);
            joinQuery = "LEFT JOIN t_employee e ON e.BRANCH_ID = b.branch_id ";
            baseQuery.append("AND b.company_id = ?");
            params.add(hospitalId);
            logger.info("The hospital id will be :"+hospitalId);
        }else if ("admin".equalsIgnoreCase(role)) {
            baseQuery.append("AND e.emp_id = ?");
            params.add(empId);

        } else  {
            throw new RuntimeException("The user is unauthorised to view the list");
        }


//        params.add(sqlToday); // Add today's date
//
//        // Step 4: Adjust the query based on role
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery.append("e.UPPER_ID = ? ");
//            params.add(empId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery.append("e.UPPER_ID = ? ");
//            params.add(empId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
//            baseQuery.append("AND e.UPPER_ID = ? ");
//            params.add(empId);
//        }

        // Step 5: Add pagination
//        baseQuery.append("ORDER BY e.EMP_ID OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
//        params.add(offset);
//        params.add(limit);

        try {
            // Step 6: Fetch employees on leave
            List<Map<String, Object>> empIds = jdbcTemplate.queryForList(baseQuery.toString(), params.toArray());

            if (empIds.isEmpty()) {
                logger.info("No facility found in application");
                return Collections.emptyList();  // ✅ FIX: Return an empty list instead of an empty map
            }
            return empIds;
        } catch (DataAccessException e) {
            logger.error("Database error while fetching facility", e);
            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
        }
    }
//    public List<Map<String, Object>> getEmployeesOnLeave1(int offset, int limit) {
//        Long empId;
//        String role;
//
//        // Step 1: Get employee ID and role from token
//        try {
//            // Retrieve employee ID and role from token
//            empId = tokenService.getEmployeeIdFromToken();
//            if (empId == null) {
//                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
//            }
//            role = tokenService.getEmployeeFromToken().getRole();
//            if (role == null || role.isEmpty()) {
//                throw new IllegalArgumentException("Role could not be retrieved from token.");
//            }
//        } catch (Exception e) {
//            logger.error("Error retrieving employee details from token", e);
//            throw new RuntimeException("Unable to retrieve employee details from the token. Please try again later.");
//        }
////        // Step 2: Get today's date
////        LocalDate today = LocalDate.now();
////        Date sqlToday = Date.valueOf(today);
//
//        // Step 3: Build the base query dynamically based on the role
//        StringBuilder baseQuery = new StringBuilder(
//                "SELECT new com.company.wfm.dto.EmployeeDTO(e.empId, e.empName, l.startDate, l.endDate, l.approvalStatus) " +
//                        "FROM Employee e " +
//                        "JOIN e.EmployeeLeaveHistory l " +
//                        "WHERE :specificDate BETWEEN l.startDate AND l.endDate " +
//                        "AND l.approvalStatus = 'APPROVED' " +
//                        "AND e.reportingManagerId = ?"
//        );
//
//       List<Object> params = new ArrayList<>();
////        params.add(sqlToday);
//       params.add(empId);
//
//
//        // Step 4: Adjust the query based on role
//        if ("superadmin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            baseQuery.append("AND e.company_id = ? ");
//            params.add(companyId);
//        } else if ("admin".equalsIgnoreCase(role)) {
//            Long companyId = getCompanyId(empId);
//            Long branchId = getBranchId(empId);
//            baseQuery.append("AND e.company_id = ? AND e.branch_id = ? ");
//            params.add(companyId);
//            params.add(branchId);
//        } else if ("supervisor".equalsIgnoreCase(role)) {
//            baseQuery.append("AND e.UPPER_ID = ? ");
//            params.add(empId);
//        }
//
//        // Step 5: Add GROUP BY and pagination to the query
//       // baseQuery.append("GROUP BY e.EMP_ID ");
////        baseQuery.append(" OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");
////        params.add(offset);
////        params.add(limit);
//
//        try {
//            // Step 6: Fetch employee IDs
//            List<Map<String, Object>> empIds = jdbcTemplate.queryForList(String.valueOf(baseQuery), params);
//
//            if (empIds.isEmpty()) {
//                logger.info("No employees on leave found for today's date.");
//                return (List<Map<String, Object>>) Collections.emptyMap();  // No employees on leave
//            }
//            return empIds;
//        } catch (DataAccessException e) {
//            throw new RuntimeException(e);
//        }
//
//        // Step 7: Fetch employee details
//            //<EmployeeVO> employeeDetails = fetchEmployeeDetails(empIds);
//            // logger.info("Fetched {} employee details.", employeeDetails.size());
//
//            // Step 8: Fetch total elements (count of employees on leave)
//            //String countQuery = baseQuery.toString().replace("SELECT e.EMP_ID", "SELECT COUNT(DISTINCT e.EMP_ID)");
//
////            // Use queryForObject and handle the case where there might be multiple rows
////            List<Integer> totalElementsList = jdbcTemplate.query(countQuery, (rs, rowNum) -> rs.getInt(1), params.toArray());
////
////
////            int totalElements = totalElementsList.isEmpty() ? 0 : totalElementsList.get(0);
////            // Step 9: Calculate total pages
////            int totalPages = (int) Math.ceil((double) totalElements / limit);
////
////            // Step 10: Prepare the response with pagination details
////            Map<String, Object> response = new HashMap<>();
////            response.put("content", employeeDetails);  // Add employee details
////
////            Map<String, Object> paginationDetails = new HashMap<>();
////            paginationDetails.put("number", offset / limit);  // current page number
////            paginationDetails.put("size", limit);  // number of records per page
////            paginationDetails.put("numberOfElements", employeeDetails.size());  // elements on current page
////            paginationDetails.put("totalPages", totalPages);  // total number of pages
////            paginationDetails.put("totalElements", totalElements);  // total number of employees on leave
////            paginationDetails.put("sort", Map.of("sorted", true, "unsorted", false, "empty", false));
////
////            // Step 11: Add pagination details to the response
////            response.put("pagination", paginationDetails);
////
////            return response;
////        } catch (DataAccessException e) {
////            logger.error("Database error while fetching employees on leave", e);
////            throw new RuntimeException("An error occurred while accessing the database. Please try again later.");
////        } catch (Exception e) {
////            logger.error("Unexpected error", e);
////            throw new RuntimeException("An unexpected error occurred. Please contact support.");
////        }
//
//    }


  //approve list
    public Page<LeaveHistoryResponseDTO> getTotalApprovedList(LeaveHistoryRequestDTO requestDTO) {
        try {
            Long empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }

            Long hospitalId = getHospitalId(empId);
            Long branchId = requestDTO.getBranchId(); // Get the branch ID from the request

            // Create pagination request
            Pageable pageable = PageRequest.of(requestDTO.getOffset(), requestDTO.getLimit(), Sort.by("startDate").descending());

            // Build dynamic query with search filter (branch id) and pagination for SQL Server
            StringBuilder query = new StringBuilder("SELECT e.EMP_ID, e.EMP_NAME, e.EMP_CODE, d.DESIGNATION_NAME, e.IMG_URE, " +
                    "lh.ID AS LEAVE_HISTORY_ID, lh.EMP_LEAVE_ID, lh.LEAVE_ID, lm.TYPE AS LEAVE_TYPE_NAME, " +
                    "lh.APPLIED_LEAVE_COUNT, lh.START_DATE, lh.END_DATE, lh.APPROVAL_STATUS, lh.REASON, " +
                    "lh.CREATED_BY, lh.CREATED_TIME, lh.UPDATED_BY, lh.UPDATED_TIME, lh.COMMENT, lh.FILE_PATH " +
                    "FROM t_employee_leave_history lh " +
                    "JOIN t_employee e ON e.EMP_ID = lh.EMP_ID " +
                    "JOIN t_designation d ON d.DESIGNATION_ID = e.DESIGNATION_ID " +
                    "JOIN t_leave_master lm ON lm.LEAVE_ID = lh.LEAVE_ID " +
                    "WHERE lh.APPROVAL_STATUS = 'APPROVED' " +
                    "AND e.COMPANY_ID = ?");

            // Add branch ID filter if provided
            if (branchId != null) {
                query.append(" AND e.BRANCH_ID = ?");
            }

            // Add pagination using OFFSET and FETCH NEXT (SQL Server syntax)
            query.append(" ORDER BY lh.START_DATE DESC ")
                    .append("OFFSET ? ROWS FETCH NEXT ? ROWS ONLY");

            // Prepare parameters for the main query
            List<Object> params = new ArrayList<>();
            params.add(hospitalId);  // Add the hospital ID to the query parameters

            // If branchId is provided, add it to the query parameters
            if (branchId != null) {
                params.add(branchId);
            }

            // Add pagination details (OFFSET and FETCH NEXT)
            params.add(requestDTO.getOffset() * requestDTO.getLimit());  // OFFSET: Start from this row
            params.add(requestDTO.getLimit());                          // FETCH NEXT: Number of rows to fetch

            // Execute the query and fetch results
            List<Map<String, Object>> result = jdbcTemplate.queryForList(query.toString(), params.toArray());

            // Get the total count of records for pagination
            String countQuery = "SELECT COUNT(*) FROM t_employee_leave_history lh " +
                    "JOIN t_employee e ON e.EMP_ID = lh.EMP_ID " +
                    "WHERE lh.APPROVAL_STATUS = 'APPROVED' AND e.COMPANY_ID = ?";

            // If branchId is provided, add it to the count query parameters
            if (branchId != null) {
                countQuery += " AND e.BRANCH_ID = ?";
            }

            // Prepare parameters for the count query
            List<Object> countParams = new ArrayList<>();
            countParams.add(hospitalId);  // Add the hospital ID to the count query parameters
            if (branchId != null) {
                countParams.add(branchId);  // Add branch ID to the count query parameters if provided
            }

            // Execute the count query for pagination
            Integer total = jdbcTemplate.queryForObject(countQuery, Integer.class, countParams.toArray());

            // Convert query results to DTO
            List<LeaveHistoryResponseDTO> leaveHistoryList = mapToLeaveHistoryResponseDTO(result);

            // Create and return a Page object from the result list
            return new PageImpl<>(leaveHistoryList, pageable, total);

        } catch (IllegalArgumentException e) {
            // Handle argument exceptions (e.g., missing or invalid employee ID)
            throw new RuntimeException("Invalid input: " + e.getMessage(), e);
        } catch (DataAccessException e) {
            // Handle database related exceptions (e.g., query issues, connection problems)
            throw new RuntimeException("Database error: " + e.getMessage(), e);
        } catch (Exception e) {
            // Handle general exceptions
            throw new RuntimeException("An unexpected error occurred: " + e.getMessage(), e);
        }
    }





    private List<LeaveHistoryResponseDTO> mapToLeaveHistoryResponseDTO(List<Map<String, Object>> result) {
        List<LeaveHistoryResponseDTO> leaveHistoryList = new ArrayList<>();

        for (Map<String, Object> row : result) {
            LeaveHistoryResponseDTO dto = new LeaveHistoryResponseDTO(
                    (Long) row.get("LEAVE_HISTORY_ID"),
                    (Long) row.get("EMP_LEAVE_ID"),
                    (String) row.get("EMP_NAME"),
                    (Long) row.get("LEAVE_ID"),
                    (String) row.get("LEAVE_TYPE_NAME"),
                    (Long) row.get("EMP_ID"),
                    (String) row.get("EMP_NAME"),
                    (String) row.get("EMP_CODE"),
                    (String) row.get("DESIGNATION_NAME"),
                    (String) row.get("IMG_URE"),
                    (Integer) row.get("APPLIED_LEAVE_COUNT"),
                    ((java.sql.Date) row.get("START_DATE")) != null ? ((java.sql.Date) row.get("START_DATE")).toLocalDate() : null,
                    ((java.sql.Date) row.get("END_DATE")) != null ? ((java.sql.Date) row.get("END_DATE")).toLocalDate() : null,
                    (String) row.get("APPROVAL_STATUS"),
                    (String) row.get("REASON"),
                    (String) row.get("CREATED_BY"),
                    (String) row.get("CREATED_BY") != null ? getEmployeeNameById((String) row.get("CREATED_BY")) : null,
                    ((java.sql.Timestamp) row.get("CREATED_TIME")) != null ? ((java.sql.Timestamp) row.get("CREATED_TIME")).toLocalDateTime() : null,
                    (String) row.get("UPDATED_BY"),
                    (String) row.get("UPDATED_BY") != null ? getEmployeeNameById((String) row.get("UPDATED_BY")) : null,
                    ((java.sql.Timestamp) row.get("UPDATED_TIME")) != null ? ((java.sql.Timestamp) row.get("UPDATED_TIME")).toLocalDateTime() : null,
                    (String) row.get("COMMENT"),
                    (String) row.get("FILE_PATH")
            );

            // Debugging DTO mapping
            System.out.println("✅ Mapped DTO: " + dto);
            leaveHistoryList.add(dto);
        }

        return leaveHistoryList;
    }



    private String getEmployeeNameById(String empId) {
        if (empId == null) {
            return null;
        }
        String query = "SELECT EMP_NAME FROM t_employee WHERE EMP_ID = ?";
        return jdbcTemplate.queryForObject(query, new Object[]{Long.parseLong(empId)}, String.class);
    }

 //approvals listing

    public Page<LeaveHistoryResponseDTO> getPendingApprovalsList(LeaveHistoryRequestDTO leaveHistoryRequestDTO) {
        Long empId;
        String role;

        try {
            // Retrieve employee ID and role from token
            empId = tokenService.getEmployeeIdFromToken();
            if (empId == null) {
                throw new IllegalArgumentException("Employee ID could not be retrieved from token.");
            }

            role = tokenService.getEmployeeFromToken().getRole();
            if (role == null || role.isEmpty()) {
                throw new IllegalArgumentException("Role could not be retrieved from token.");
            }

            // Set pagination parameters (offset and limit)
            int offset = (leaveHistoryRequestDTO.getOffset() > 0) ? leaveHistoryRequestDTO.getOffset() : 0;
            int limit = (leaveHistoryRequestDTO.getLimit() > 0) ? leaveHistoryRequestDTO.getLimit() : 10;
            Pageable pageable = PageRequest.of(offset, limit);

            LocalDate today = LocalDate.now();
            Date sqlToday = Date.valueOf(today);

            // Build the base query for leave history
            String baseQuery = "SELECT e.EMP_ID, e.EMP_NAME, e.EMP_CODE, d.DESIGNATION_NAME, e.IMG_URE, " +
                    "lh.ID AS LEAVE_HISTORY_ID, lh.EMP_LEAVE_ID, lh.LEAVE_ID, lm.TYPE AS LEAVE_TYPE_NAME, " +
                    "lh.APPLIED_LEAVE_COUNT, lh.START_DATE, lh.END_DATE, lh.APPROVAL_STATUS, lh.REASON, " +
                    "lh.CREATED_BY, lh.CREATED_TIME, lh.UPDATED_BY, lh.UPDATED_TIME, lh.COMMENT, lh.FILE_PATH " +
                    "FROM t_employee_leave_history lh " +
                    "JOIN t_employee e ON e.EMP_ID = lh.EMP_ID " +
                    "JOIN t_designation d ON d.DESIGNATION_ID = e.DESIGNATION_ID " +
                    "JOIN t_leave_master lm ON lm.LEAVE_ID = lh.LEAVE_ID " +
                    "WHERE lh.APPROVAL_STATUS = 'PENDING' " +
                    "AND lh.START_DATE <= ? AND lh.END_DATE >= ? ";

            List<Object> params = new ArrayList<>();
            params.add(sqlToday);
            params.add(sqlToday);

            // Modify query based on role
            if ("superadmin".equalsIgnoreCase(role)) {
                Long companyId = getCompanyId(empId);
                baseQuery += "AND e.company_id = ? ";
                params.add(companyId);
            } else if ("admin".equalsIgnoreCase(role)) {
                Long companyId = getCompanyId(empId);
                Long branchId = getBranchId(empId);
                baseQuery += "AND e.company_id = ? AND e.branch_id = ? ";
                params.add(companyId);
                params.add(branchId);
            } else if ("supervisor".equalsIgnoreCase(role)) {
                baseQuery += "AND e.UPPER_ID = ? ";
                params.add(empId);
            }

            // Apply pagination to query (using OFFSET-FETCH for MS SQL Server)
            baseQuery += "ORDER BY lh.START_DATE " +
                    "OFFSET ? ROWS FETCH NEXT ? ROWS ONLY";
            params.add(offset);
            params.add(limit);

            // Execute the query to get the list of pending leave history records
            List<Map<String, Object>> result = jdbcTemplate.queryForList(baseQuery, params.toArray());

            // Map the result to LeaveHistoryResponseDTO
            List<LeaveHistoryResponseDTO> leaveHistoryResponseDTOList = mapToLeaveHistoryResponseDTO(result);

            // Return the result wrapped in a Page
            return new PageImpl<>(leaveHistoryResponseDTOList, pageable, leaveHistoryResponseDTOList.size());

        } catch (Exception e) {
            // Handle any exception (e.g., SQL errors, invalid arguments)
            throw new RuntimeException("An error occurred while fetching leave history data: " + e.getMessage());
        }
    }










}