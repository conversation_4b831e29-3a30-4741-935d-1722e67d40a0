import React, { useState, useEffect } from "react";
import "./Masters.css";
import useLocalStorage from "@/services/localstorage";
import Link from "next/link";

const MastersCard = ({ arrowDirection }: any) => {
  const [role] = useLocalStorage("role", "");
  const [menuData, setMenuData] = useState<any[]>([]);

  useEffect(() => {
    const cachedMenu = localStorage.getItem("menuData");

    if (cachedMenu) {
      setMenuData(JSON.parse(cachedMenu));
    }
  }, []);

  const renderMastersMenuItems = () => {
    const mastersMenu = menuData.find((item) => item.menu_label === "Masters");
    if (!mastersMenu) return null;

    return mastersMenu.subMenuItems
      .filter((subItem: any) => subItem.is_active)
      .sort((a: any, b: any) => a.sequence - b.sequence)
      .map((subItem: any) => (
        <li key={subItem.id} className="d-flex align-items-center mb-2">
          <Link href={subItem.menu_path} className="d-flex align-items-center">
            <div className="icon-placeholder">
              <img src={subItem.icon_path}  style={{ width: "24px", height: "24px" }} />
            </div>
            <span className="link-text ms-2">{subItem.menu_label}</span>
          </Link>
        </li>
      ));
  };

  return (
    <div className={`custom-card-masters ${arrowDirection}`}>
      <div className="card-body">
        <ul className="list-unstyled">
        {/* {role === "superadmin" && (
        <li className="d-flex align-items-center mb-2">
            <Link href="adminDashboard/employee_present" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/icons8-danger-48.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Employee Present</span>
            </Link>
          </li>
        )}

{role === "superadmin" && (
        <li className="d-flex align-items-center mb-2">
            <Link href="employeeLeave_present" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/icons8-danger-48.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Employee Present</span>
            </Link>
          </li>
        )} */}


          {renderMastersMenuItems()}
         
        </ul>
      </div>
    </div>
  );
};

export default MastersCard;
