package com.company.wfm.dto;

import java.time.LocalDate;
import java.time.LocalTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShiftSwapResponseDTO {

    private Long swapRequestId;
    private Long originalShiftAssignmentId;
    private Long requestByEmployeeId;
    private Long swapWithEmployeeId;

    private String swapRequestedByEmployeeName;
    private String swapRequestedByEmployeeImage;
    private String swapRequestedToEmployeeName;
    private String swapRequestedToEmployeeImage;
    private LocalDate swapRequestDate;
    private String status;
    private LocalDate startDate;
    private LocalDate endDate;
    private String requestedSchedule;
    private LocalTime startTime;
    private LocalTime endTime;
    private String reason;
    private String actionReason;
}
