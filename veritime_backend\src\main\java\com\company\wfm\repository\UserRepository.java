package com.company.wfm.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.UserBranchDepartmentDTO;
import com.company.wfm.entity.User;


@Repository
public interface UserRepository extends JpaRepository<User, Long>{
	@EntityGraph(attributePaths = {"employee"})
	User findByUsername(String username);

	Optional<User> findByEmployee_EmpId(Long empId);

	boolean existsByUsername(String username);

	Optional<User> findByResetToken(String token);

	@Query("SELECT u FROM User u JOIN u.employee e WHERE e.email = :email")
	Optional<User> findByEmployeeEmail(@Param("email") String email);


	//boolean existsByResetToken(String resetToken);

	@Query("SELECT CASE WHEN COUNT(u) > 0 THEN true ELSE false END FROM User u WHERE u.resetToken = :resetToken")
	boolean existsByResetToken(@Param("resetToken") String resetToken);





/*	@Query("SELECT new com.company.wfm.dto.UserBranchDepartmentDTO(u.id, u.username, b.branchName, d.departmentName) " +
			"FROM User u " +
			"JOIN DepartmentBranch db ON u.departmentBranch.id = db.id " +
			"JOIN Branch b ON db.branchId = b.id " +
			"JOIN Department d ON db.departmentId = d.id")
	List<UserBranchDepartmentDTO> findAllUsersWithBranchAndDepartment();*/

	@Query("SELECT new com.company.wfm.dto.UserBranchDepartmentDTO(u.id, u.username, b.branchName, d.departmentName) " +
			"FROM User u " +
			"JOIN u.employee e " +
			"JOIN e.branch b " +
			"JOIN e.department d")
	List<UserBranchDepartmentDTO> findAllUsersWithBranchAndDepartment();


	@Query("SELECT u FROM User u WHERE u.employee.id = :employeeId")
	Optional<User> findUserIdByEmployeeId(@Param("employeeId") Long employeeId);

	@Query("SELECT u.id FROM User u WHERE u.employee.id = :employeeId")
	Optional<Long> findIdByEmployeeId(@Param("employeeId") Long employeeId);


	@Query(value = "SELECT * FROM t_user WHERE EMP_ID = :empId", nativeQuery = true)
	Optional<User> findByEmpId(@Param("empId") Long empId);

}
