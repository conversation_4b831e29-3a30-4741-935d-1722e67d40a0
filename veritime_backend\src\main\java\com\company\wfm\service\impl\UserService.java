package com.company.wfm.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.dto.EmailData;
import com.company.wfm.dto.EmailTemplateDto;
import com.company.wfm.dto.EmployeeDetailsDto;
import com.company.wfm.dto.ForgotPasswordResponseDTO;
import com.company.wfm.dto.NotificationDTO;
import com.company.wfm.dto.UserCreationDTO;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.DepartmentBranch;
import com.company.wfm.entity.Designation;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.FeederHospital;
import com.company.wfm.entity.User;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentBranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.DesignationRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.FeederHospitalRepository;
import com.company.wfm.repository.UserRepository;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.EmailTemplateService;

import jakarta.servlet.http.HttpServletRequest;

@Service
@DependsOn("appConfigurations")
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Value("${account.lock.time.duration}")
    public String lock_time_duration; // 30 minutes

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

	@Autowired
	private EmailTemplateService template;

    @Autowired
    private CommonNotificationService notificationService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private DepartmentBranchRepository departmentBranchRepository;

    @Autowired
    private DesignationRepository designationRepository;

    @Autowired
    private FeederHospitalRepository feederHospitalRepository;




    @Transactional
    public User createUser1(UserCreationDTO userDTO) {
        User user = new User();
        user.setUserCode(userDTO.getUserCode());
        user.setUsername(userDTO.getUsername());
        user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        user.setRole(userDTO.getRole());



        FeederHospital feederHospital =new FeederHospital();
        feederHospital.setName("Xpertlyte");
        feederHospital.setType("Hospital");
        feederHospital.setAddress("Turbe");
        feederHospital.setShortCode("T1");
        feederHospital= feederHospitalRepository.save(feederHospital);



        Branch branch=new Branch();
        branch.setBranchName(" Mumbai");
        branch.setBranchCode("Mumbai01");
        branch.setCompany(feederHospital);
        branch=  branchRepository.save(branch);

        Department department=new Department();
        department.setDepartmentName("Support");
        department.setDepartmentCode("S1");
        department.setCategory("Administration");
        departmentRepository.save(department);

        DepartmentBranch departmentBranch=new DepartmentBranch();
        departmentBranch.setBranchId(branch.getId());
        departmentBranch.setDepartmentId(department.getDepartmentId());
        departmentBranchRepository.save(departmentBranch);


        Designation designation =new Designation();
        designation.setName("masteruser");
        designation.setDepartment(department);
        designation.setCode("MUO01");
        designation.setCreatedAt(LocalDateTime.now());
        designation= designationRepository.save(designation);

        Employee employee1=new Employee();
        employee1.setEmpName("Support User");
        employee1.setDesignation(designation);
        employee1.setDepartment(department);
        employee1.setBranch(branch);
        employee1.setCompanyId(feederHospital.getId());
        employee1.setProvinceId(14L);
        employee1.setRegionId(9L);
        employee1.setCity("21");
        employee1=employeeRepository.save(employee1);

        // if (userDTO.getEmployeeId() != null) {
        Employee employee = employeeRepository.findById(employee1.getEmpId())
                .orElseThrow(() -> new IllegalArgumentException("Employee not found"));
        user.setEmployee(employee);
        // }

        return userRepository.saveAndFlush(user);

    }
   /* @Transactional
    public User createUser(UserCreationDTO userDTO) {
        User user = new User();
        user.setUserCode(userDTO.getUserCode());
        user.setUsername(userDTO.getUsername());
        user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        user.setRole(userDTO.getRole());

        if (userDTO.getEmployeeId() != null) {
            Employee employee = employeeRepository.findById(userDTO.getEmployeeId())
                    .orElseThrow(() -> new IllegalArgumentException("Employee not found"));
            user.setEmployee(employee);
        }

        return userRepository.saveAndFlush(user);
    }*/

   /* @Transactional
    public EmployeeDetailsDto createUser(UserCreationDTO userDTO) {
        try {
            // Create a new User entity from the UserCreationDTO
            User user = new User();
            user.setUserCode(userDTO.getUserCode());
            user.setUsername(userDTO.getUsername());
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
            user.setRole(userDTO.getRole());

            Employee employee = null;
            if (userDTO.getEmployeeId() != null) {
                employee = employeeRepository.findById(userDTO.getEmployeeId())
                        .orElseThrow(() -> new IllegalArgumentException("Employee not found"));
                user.setEmployee(employee);
            }

            // Save User entity to the database
            userRepository.saveAndFlush(user);

            // Construct and return UserWithEmployeeResponseDTO using builder
            return EmployeeDetailsDto.builder()
                    .role(user.getRole())
                    .empId(employee != null ? employee.getEmpId() : null)
                    .companyId(employee != null ? employee.getCompanyId() : null)
                    .departmentId(employee != null && employee.getDepartment() != null ? employee.getDepartment().getDepartmentId() : null)
                    .deptName(employee != null && employee.getDepartment().getDepartmentName() != null ? employee.getDepartment().getDepartmentName() : null)
                    .branchId(employee != null && employee.getBranch() != null ? employee.getBranch().getId() : null)
                    .branchName(employee != null ? employee.getBranch().getBranchName() : null)
                    .designationId(employee != null && employee.getDesignation() != null ? employee.getDesignation().getId() : null)
                    .designationName(employee != null && employee.getDesignation().getName() != null ? employee.getDesignation().getName() : null)
                    .empCode(employee != null ? employee.getEmpCode() : null)
                    .empName(employee != null ? employee.getEmpName() : null)
                    .uid(employee != null ? employee.getBiometricID() : null)
                    .idNo(employee != null ? employee.getIdNo() : null)
                    .orgId(employee != null ? employee.getOrgId() : null)
                    .upperId(employee != null ? employee.getUpperId() : null)
                    .countryId(employee != null ? employee.getCountryId() : null)
                    .provinceId(employee != null ? employee.getProvinceId() : null)
                    // .cityId(employee != null ? employee.getCityId() : null) // Use getCityId instead of getCity if cityId is the correct field
                    .upperName(employee != null ? employee.getUpperName() : null)
                    .hireDate(employee != null ? employee.getHireDate() : null)
                    .gender(employee != null ? employee.getGender() : null)
                    .birthday(employee != null ? employee.getBirthday() : null)
                    .nation(employee != null ? employee.getNation() : null)
                    .married(employee != null ? employee.getMarried() : null)
                    .phoneNo(employee != null ? employee.getPhoneNo() : null)
                    .mobileNo(employee != null ? employee.getMobileNo() : null)
                    .email(employee != null ? employee.getEmail() : null)
                    .nativePlace(employee != null ? employee.getNativePlace() : null)
                    .zipCode(employee != null ? employee.getZipCode() : null)
                    .isHistory(employee != null ? employee.getIsHistory() : null)
                    .inService(employee != null ? employee.getInService() : null)
                    .remark(employee != null ? employee.getRemark() : null)
                    .createdBy(employee != null ? employee.getCreatedBy() : null)
                    .createdTime(employee != null ? employee.getCreatedTime() : null)
                    .updatedBy(employee != null ? employee.getUpdatedBy() : null)
                    .updatedTime(employee != null ? employee.getUpdatedTime() : null)
                    .version(employee != null ? employee.getVersion() : null)
                    .nativeLanguage(employee != null ? employee.getNativeLanguage() : null)
                    .foreignLanguages(employee != null ? employee.getForeignLanguages() : null)
                    .workYears(employee != null ? employee.getWorkYears() : null)
                    .graduateSchool(employee != null ? employee.getGraduateSchool() : null)
                    .graduateTime(employee != null ? employee.getGraduateTime() : null)
                    .highestDegree(employee != null ? employee.getHighestDegree() : null)
                    .imgUre(employee != null ? employee.getImgUre() : null)
                    // Removed defaultTimeSlotId field
                    .departmentId(employee != null && employee.getDepartment() != null ? employee.getDepartment().getDepartmentId() : null)
                    .defaultTimeSlot(employee != null && employee.getDefaultTimeSlotId() != null ? employee.getDefaultTimeSlotId() : null )
                    .street(employee != null ? employee.getStreet() : null)
                    .unitNumber(employee != null ? employee.getUnitNumber() : null)
                    .build();

        } catch (IllegalArgumentException e) {
            // Handle specific exception for employee not found
            throw new RuntimeException("Error creating user: " + e.getMessage());
        } catch (Exception e) {
            // Handle general exceptions
            throw new RuntimeException("An unexpected error occurred while creating the user: " + e.getMessage());
        }
    }
*/

    @Transactional
    public EmployeeDetailsDto createUser(UserCreationDTO userDTO) {
        try {
            // Check if email already exists
            if (userRepository.existsByUsername(userDTO.getUsername())) {
                throw new EmailAlreadyExistsException("Email is already in use");
            }

            // Create a new User entity from the UserCreationDTO
            User user = new User();
            user.setUserCode(userDTO.getUserCode());
            user.setUsername(userDTO.getUsername());
            user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
            user.setRole(userDTO.getRole());

            Employee employee = null;
            if (userDTO.getEmployeeId() != null) {
                employee = employeeRepository.findById(userDTO.getEmployeeId())
                        .orElseThrow(() -> new IllegalArgumentException("Employee not found"));
                user.setEmployee(employee);
            }

            // Save User entity to the database
            userRepository.saveAndFlush(user);

            // Construct and return EmployeeDetailsDto
            return EmployeeDetailsDto.builder()
                    .role(user.getRole())
                    .empId(employee != null ? employee.getEmpId() : null)
                    .companyId(employee != null ? employee.getCompanyId() : null)
                    .departmentId(employee != null && employee.getDepartment() != null ? employee.getDepartment().getDepartmentId() : null)
                    .deptName(employee != null && employee.getDepartment().getDepartmentName() != null ? employee.getDepartment().getDepartmentName() : null)
                    .branchId(employee != null && employee.getBranch() != null ? employee.getBranch().getId() : null)
                    .branchName(employee != null ? employee.getBranch().getBranchName() : null)
                    .designationId(employee != null && employee.getDesignation() != null ? employee.getDesignation().getId() : null)
                    .designationName(employee != null && employee.getDesignation().getName() != null ? employee.getDesignation().getName() : null)
                    .empCode(employee != null ? employee.getEmpCode() : null)
                    .empName(employee != null ? employee.getEmpName() : null)
                    .uid(employee != null ? employee.getBiometricID() : null)
                    .idNo(employee != null ? employee.getIdNo() : null)
                    .orgId(employee != null ? employee.getOrgId() : null)
                    .upperId(employee != null ? employee.getUpperId() : null)
                    .countryId(employee != null ? employee.getCountryId() : null)
                    .provinceId(employee != null ? employee.getProvinceId() : null)
                    .upperName(employee != null ? employee.getUpperName() : null)
                    .hireDate(employee != null ? employee.getHireDate() : null)
                    .gender(employee != null ? employee.getGender() : null)
                    .birthday(employee != null ? employee.getBirthday() : null)
                    .nation(employee != null ? employee.getNation() : null)
                    .married(employee != null ? employee.getMarried() : null)
                    .phoneNo(employee != null ? employee.getPhoneNo() : null)
                    .mobileNo(employee != null ? employee.getMobileNo() : null)
                    .email(employee != null ? employee.getEmail() : null)
                    .nativePlace(employee != null ? employee.getNativePlace() : null)
                    .zipCode(employee != null ? employee.getZipCode() : null)
                    .isHistory(employee != null ? employee.getIsHistory() : null)
                    .inService(employee != null ? employee.getInService() : null)
                    .remark(employee != null ? employee.getRemark() : null)
                    .createdBy(employee != null ? employee.getCreatedBy() : null)
                    .createdTime(employee != null ? employee.getCreatedTime() : null)
                    .updatedBy(employee != null ? employee.getUpdatedBy() : null)
                    .updatedTime(employee != null ? employee.getUpdatedTime() : null)
                    .version(employee != null ? employee.getVersion() : null)
                    .nativeLanguage(employee != null ? employee.getNativeLanguage() : null)
                    .foreignLanguages(employee != null ? employee.getForeignLanguages() : null)
                    .workYears(employee != null ? employee.getWorkYears() : null)
                    .graduateSchool(employee != null ? employee.getGraduateSchool() : null)
                    .graduateTime(employee != null ? employee.getGraduateTime() : null)
                    .highestDegree(employee != null ? employee.getHighestDegree() : null)
                    .imgUre(employee != null ? employee.getImgUre() : null)
                    .defaultTimeSlot(employee != null && employee.getDefaultTimeSlotId() != null ? employee.getDefaultTimeSlotId() : null)
                    .street(employee != null ? employee.getStreet() : null)
                    .unitNumber(employee != null ? employee.getUnitNumber() : null)
                    .build();

        } catch (EmailAlreadyExistsException e) {
            // Handle specific email already exists exception
            throw new RuntimeException("Error creating user: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            // Handle specific exception for employee not found
            throw new RuntimeException("Error creating user: " + e.getMessage());
        } catch (Exception e) {
            // Handle general exceptions
            throw new RuntimeException("An unexpected error occurred while creating the user: " + e.getMessage());
        }
    }





    public String getUserNameById(Long userId) {
        // Fetch user by ID and get the associated employee name
        return userRepository.findById(userId)
                .map(user -> user.getEmployee().getEmpName()) // Assuming Employee has a 'name' field
                .orElse(null);
    }

    public String getUserImageById(Long userId) {
        // Fetch user by ID and get the associated employee image
        return userRepository.findById(userId)
                .map(user -> user.getEmployee().getImgUre()) // Assuming Employee has an 'image' field
                .orElse(null);
    }

    public class EmailAlreadyExistsException extends RuntimeException {
        public EmailAlreadyExistsException(String message) {
            super(message);
        }
    }


   /* @Transactional
    public ForgotPasswordResponseDTO forgotPassword(String email) {
      //  Optional<User> userOptional = userRepository.findByUsername(email);
        Optional<User> userOptional = userRepository.findByEmployeeEmail(email);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("User with email " + email + " not found");
        }

        User user = userOptional.get();
        String token = UUID.randomUUID().toString();

        // Save the token in the database (you need a 'resetToken' field in User or another storage for tokens)
        user.setResetToken(token);
        userRepository.save(user);

        // Send the email
        sendResetEmail(email, token);

       // return "Reset password email has been sent to: " + email;
       return new ForgotPasswordResponseDTO(email, token);
    }*/
 //exception handle

    @Transactional
    public ForgotPasswordResponseDTO forgotPassword(String email, HttpServletRequest request) {
        try {
            Optional<User> userOptional = userRepository.findByEmployeeEmail(email);

            if (!userOptional.isPresent()) {
                logger.error("User with email {} not found", email);
                throw new RuntimeException("User with email " + email + " not found");
            }

            User user = userOptional.get();
            String token = UUID.randomUUID().toString();

            // Save the reset token in the database
            user.setResetToken(token);
            userRepository.save(user);

           // String resetUrl = request.getRequestURL().toString().replace(request.getRequestURI(), "") + "/login/reset-password/" + token;
            // Retrieve frontend URL from the request attribute
            String frontendUrl = (String) request.getAttribute("frontendUrl");

            // Construct the reset URL with frontend URL, falling back to server URL if needed
            String resetUrl;
            if (frontendUrl != null) {
                resetUrl = frontendUrl + "/login/reset-password/" + token;
            } else {
                resetUrl = request.getRequestURL().toString().replace(request.getRequestURI(), "") + "/login/reset-password/" + token;
            }

            // Send the email
            sendResetEmail(user.getUsername(), email, resetUrl);

            // Send the notification
            sendPasswordResetNotification(user);

            // Return response with email and token
            return ForgotPasswordResponseDTO.builder().mesaage("Password Reset link has been shared on your Email").build();

        } catch (Exception e) {
            logger.error("An error occurred during forgot password process for email {}: {}", email, e.getMessage(), e);
            throw new RuntimeException("An unexpected error occurred while processing your request. Please try again later.");
        }
    }


    private void sendPasswordResetNotification(User user) {
        // Retrieve Employee ID from User entity and send the notification
        Long employeeId = user.getEmployee().getEmpId();

        // Construct notification message content
        String messageContent = "A password reset request has been generated. Please check your email to reset your password.";

        // Create and configure NotificationDTO
        NotificationDTO notificationDTO = new NotificationDTO(
                "Password Reset Request",
                messageContent,
                "PASSWORD_RESET",
                String.valueOf(employeeId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );


        notificationService.sendNotificationToEmployee(employeeId, notificationDTO.getTitle(), notificationDTO.getBody(), "password", String.valueOf(employeeId));
    }






    private void sendResetEmail(String name, String email, String resetUrl) {
    	//String resetUrl = "http://www.aitechiez.com:3001/reset-password/" + token;
        String subject = "Password Reset Request";

        String messageBody = template.getPasswordResetTemplate(
				EmailTemplateDto.builder().name(name).resetLink(resetUrl).build());

		EmailData data = new EmailData();
		data.setEmailId(email);
		data.setSubject(subject);
		data.setMessage(messageBody);
        data.setHtml(true);
        emailService.sendEmail(data);
    }

  /*  @Transactional
    public String resetPassword(String token, String newPassword) {
        Optional<User> userOptional = userRepository.findByResetToken(token);
        if (!userOptional.isPresent()) {
            throw new RuntimeException("Invalid or expired password reset token");
        }

        User user = userOptional.get();
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setResetToken(null); // clear the token
        userRepository.save(user);

        return "Password successfully reset!";
    }*/


    @Transactional
    public String resetPassword(String token, String newPassword) {
        try {
            Optional<User> userOptional = userRepository.findByResetToken(token);

            if (!userOptional.isPresent()) {
                logger.error("Invalid or expired password reset token for token: {}", token);
                throw new RuntimeException("Invalid or expired password reset token");
            }

            User user = userOptional.get();
            user.setPassword(passwordEncoder.encode(newPassword));
            user.setResetToken(null); // Clear the token after successful reset
            userRepository.save(user);

            sendPasswordResetSuccessNotificationAndEmail(user);

            return "Password successfully reset!";

        } catch (Exception e) {
            logger.error("An error occurred while resetting the password: {}", e.getMessage(), e);
            throw new RuntimeException("An unexpected error occurred while resetting the password. Please try again later.");
        }
    }

    //email and notification
    private void sendPasswordResetSuccessNotificationAndEmail(User user) {
        // Notification message content
        Long EmpId=user.getEmployee().getEmpId();

        String notificationMessage = "Your password has been successfully reset.";

        // Create Notification DTO
        NotificationDTO notificationDTO = new NotificationDTO(
                "Password Reset Successful",
                notificationMessage,
                "PASSWORD_RESET_SUCCESS",
                String.valueOf(user.getId()),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        // Send Notification
        notificationService.sendNotificationToEmployee(EmpId, notificationDTO.getTitle(), notificationDTO.getBody(),"password", String.valueOf(user.getId()));

        // Email content
        String emailMessage = "Your password has been successfully reset.";

        String emailId = employeeRepository.getReferenceById(EmpId).getEmail();

        // Create EmailData object
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Password Reset Confirmation");
        emailData.setMessage(emailMessage);

        // Send Email
        emailService.sendEmail(emailData);

        logger.info("Password reset notification and email sent to user: {}",emailId );
    }



   /* public boolean validateResetToken(String resetToken) {
        return userRepository.existsByResetToken(resetToken);
    }*/

    public boolean validateResetToken(String resetToken) {
        try {
            return userRepository.existsByResetToken(resetToken);
        } catch (Exception e) {
            // Log the error message along with the exception stack trace
            logger.error("An error occurred while validating the reset token: {}", e.getMessage(), e);
            return false;
        }
    }

    public void increaseFailedAttempts(User user) {

		int newFailAttempts = user.getFailedAttempts() == null ? 1 : user.getFailedAttempts().intValue() + 1;
        user.setFailedAttempts(newFailAttempts);
        userRepository.save(user);
    }

    public void resetFailedAttempts(User user) {
        user.setFailedAttempts(0);
        userRepository.save(user);
    }

    public void lockUser(User user) {
        user.setLockTime(LocalDateTime.now());
        userRepository.save(user);
    }

    public boolean isUserLocked(User user) {
        if (user.getLockTime() == null) {
            return false;
        }

        long minutesPassed = ChronoUnit.MINUTES.between(user.getLockTime(), LocalDateTime.now());
        if (minutesPassed > Integer.parseInt(lock_time_duration)) {
            user.setLockTime(null);
            user.setFailedAttempts(0);
            userRepository.save(user);
            return false;
        }

        return true;
    }


}