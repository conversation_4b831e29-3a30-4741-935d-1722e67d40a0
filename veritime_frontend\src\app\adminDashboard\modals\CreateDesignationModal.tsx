import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Form } from "react-bootstrap";
import Select from "react-select"; // Import react-select
import "../../../css/style.css";
import { getRequest, postRequest, putRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showSuccessAlert2 } from "@/services/alertService";

type Department = {
  departmentId: number;
  departmentName: string;
};

const CreateDesignationModal = ({
  show,
  handleClose,
  rowToEdit,
  setRows,
}: any) => {
  const [errors, setErrors] = useState<any>({});
  const [deptNames, setDeptNames] = useState<Department[]>([]);
  const [data, setData] = useState({
    name: "",
    code: "",
    departmentId: "",
    level: "1", // Set default level to 1
    role: "",
    noticePeriod: "",
  });

  useEffect(() => {
    if (rowToEdit) {
      setData({
        name: rowToEdit.name || "",
        code: rowToEdit.code || "",
        departmentId: rowToEdit.departmentId || "",
        level: "1", // Ensure level is 1 when editing as well
        role: rowToEdit.role || "",
        noticePeriod: rowToEdit.noticePeriod || "",
      });
    }
  }, [rowToEdit]);

  const handleChange = (e: any) => {
    setData({
      ...data,
      [e.target.name]: e.target.value,
    });
    setErrors({
      ...errors,
      [e.target.name]: "",
    });
  };

  const handleSelectChange = (selectedOption: any) => {
    setData({
      ...data,
      departmentId: selectedOption ? selectedOption.value.toString() : "",
    });
    setErrors({
      ...errors,
      departmentId: "",
    });
  };

  const validateForm = () => {
    let formErrors: any = {};
    if (!data.departmentId)
      formErrors.departmentId = "Department Name is required";
    if (!data.code) formErrors.code = "Designation Code is required";
    if (!data.name) formErrors.name = "Designation Name is required";
    if (!data.role) formErrors.role = "Role is required";
    if (!data.noticePeriod)
      formErrors.noticePeriod = "Notice Period is required";

    return formErrors;
  };

  const handleSubmit = async () => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const requestData = {
        ...data,
        departmentId: parseInt(data.departmentId),
        level: parseInt(data.level),
      };

      const departmentName =
        deptNames.find((dept) => dept.departmentId === requestData.departmentId)
          ?.departmentName || "";

      if (rowToEdit) {
        handleClose();
        const payload = {
          ...requestData,
          departmentName,
        };

        const response = await putRequest(
          `${API_URLS.DESIGNATION_UPDATE}/${rowToEdit.id}`,
          payload
        );

        if (response) {
          setRows((prevRows: any) =>
            prevRows.map((row: any) =>
              row.id === rowToEdit.id ? { ...row, ...payload } : row
            )
          );

          showSuccessAlert2("Designation updated successfully!");
          handleClose(response);
        }
      } else {
        const response = await postRequest(
          API_URLS.CREATE_DESIGNATION,
          requestData
        );

        if (response) {
          // setRows((prevRows: any) => [...prevRows, response]);

          setRows((prevRows: any) => [
            ...prevRows,
            { ...response, departmentName },
          ]);

          showSuccessAlert2("Designation has been created successfully");
          // Fetch updated list after successful creation
          fetchUpdatedDesignations();
          handleClose(response);
        }
      }
    } catch (error) {}
  };

  // Function to refresh designation list
  const fetchUpdatedDesignations = async () => {
    try {
      const updatedDesignations = await getRequest(API_URLS.DESIGNATION_LIST);
      if (updatedDesignations) {
        setRows(updatedDesignations); // Update state with new data
      }
    } catch (error) {
      console.error("Error fetching updated designations:", error);
    }
  };

  const fetchDeptNames = async () => {
    const data = await getRequest(API_URLS.DEPT_LOOKUP);
    if (data) setDeptNames(data);
  };

  useEffect(() => {
    fetchDeptNames();
  }, []);

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>
          {rowToEdit ? "Edit Designation" : "Create Designation"}
        </Modal.Title>
      </Modal.Header>
      <label
        htmlFor="ticketSubject"
        className="ticket-text-primary"
        style={{ marginTop: "10px", marginLeft: "14px" }}
      >
        <span className="text-danger">
          The fields with * marks are mandatory
        </span>
      </label>
      <Modal.Body>
        <Form>
          <Form.Group className="mb-3" controlId="departmentSelect">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Select Department <span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Select
              options={deptNames.map((dept) => ({
                value: dept.departmentId,
                label: dept.departmentName,
              }))}
              value={
                deptNames.find(
                  (dept) => dept.departmentId === parseInt(data.departmentId)
                )
                  ? {
                      value: deptNames.find(
                        (dept) =>
                          dept.departmentId === parseInt(data.departmentId)
                      )?.departmentId,
                      label: deptNames.find(
                        (dept) =>
                          dept.departmentId === parseInt(data.departmentId)
                      )?.departmentName,
                    }
                  : null
              }
              onChange={handleSelectChange}
              placeholder="Select Department"
              isClearable
              classNamePrefix="react-select"
              className={errors.departmentId ? "is-invalid" : ""}
            />
            {errors.departmentId && (
              <div className="invalid-feedback d-block">
                {errors.departmentId}
              </div>
            )}
          </Form.Group>

          {/* Other form fields remain the same */}
          <Form.Group className="mb-3" controlId="designationCode">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Designation Code<span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Form.Control
              isInvalid={!!errors.code}
              type="text"
              placeholder="Designation Code"
              value={data.code}
              name="code"
              onChange={handleChange}
            />
            <Form.Control.Feedback type="invalid">
              {errors.code}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3" controlId="noticePeriod">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Notice Period (in days) <span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Form.Control
              isInvalid={!!errors.noticePeriod}
              type="text"
              placeholder="Enter Notice Period"
              value={data.noticePeriod}
              name="noticePeriod"
              onChange={handleChange}
            />
            <Form.Control.Feedback type="invalid">
              {errors.noticePeriod}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3" controlId="designationName">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Designation Name<span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Form.Control
              isInvalid={!!errors.name}
              type="text"
              placeholder="Designation Name"
              value={data.name}
              name="name"
              onChange={handleChange}
            />
            <Form.Control.Feedback type="invalid">
              {errors.name}
            </Form.Control.Feedback>
          </Form.Group>
          {/* <Form.Group className="mb-3" controlId="designationLevel">
            <Form.Label>Designation Level</Form.Label>
            <Form.Select
              isInvalid={!!errors.level}
              className="modal-button"
              aria-label="Designation Level"
              value={data.level}
              name="level"
              onChange={handleChange}
            >
              <option value="">Select Level</option>
              {Array.from({ length: 10 }, (_, i) => i + 1).map((level) => (
                <option key={level} value={level}>
                  Level {level}
                </option>
              ))}
            </Form.Select>
            <Form.Control.Feedback type="invalid">
              {errors.level}
            </Form.Control.Feedback>
          </Form.Group> */}

          <Form.Group className="mb-3" controlId="roleSelect">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Role <span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Form.Select
              className="modal-button"
              aria-label="Select Role"
              value={data.role}
              name="role"
              onChange={handleChange}
              isInvalid={!!errors.role}
            >
              <option value="">Select Role</option>
              <option value="ceo">Veritime CEO</option>
              <option value="superadmin">SuperAdmin</option>
              <option value="admin">Admin</option>
              <option value="supervisor">Supervisor</option>
              <option value="employee">Employee</option>
            </Form.Select>
            <Form.Control.Feedback type="invalid">
              {errors.role}
            </Form.Control.Feedback>
          </Form.Group>
        </Form>
      </Modal.Body>

      <Modal.Footer>
        <Button className="modal-button" onClick={handleSubmit}>
          {rowToEdit ? "Update" : "Create"}
        </Button>
        <Button className="modal-button" onClick={handleClose}>
          Cancel
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateDesignationModal;
