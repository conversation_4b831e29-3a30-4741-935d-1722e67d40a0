"use client";
import { useEffect, useState } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, Button } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter2.js";
import { styled } from "@mui/system";
import Switch from "@mui/material/Switch";
import EmailIcon from "@mui/icons-material/Email";
import CallIcon from "@mui/icons-material/Call";
import CreateDesignationModal from "../modals/CreateDesignationModal";
import CreateScheduleModal from "../modals/CreateScheduleModal";
import Layout from "@/components/Layout";
import { API_URLS } from "@/constants/apiConstants.js";
import {
  getRequest,
  postRequest,
  postRequestWithSecurity,
} from "@/services/apiService.js";
import { appConstants } from "@/constants/appConstants.js";
import BranchDropdown from "@/components/Dropdowns/BranchDropdown";
import DepartmentDropdown from "@/components/Dropdowns/DeptDropdown";
import DesignationDropdown from "@/components/Dropdowns/DesignationDropdown";
import CreateExcelModal from "../modals/CreateEmployeeModal";
import UploadLeaveExcel from "../modals/UploadLeaveExcel";

import EditIcon from "@mui/icons-material/Edit";
import Link from "next/link.js";
import PauseCircleIcon from "@mui/icons-material/PauseCircle";

import DocumentPopup from "../modals/DocumentPopUP";
import "../../../css/style.css";
import VisibilityIcon from "@mui/icons-material/Visibility";
import axios from "axios";
import BulkUploadModal from "../modals/Pngupload";
import { encryptData } from "@/utils/encryption";
import TerminateEmployeeModal from "../modals/TerminateEmployeeModal";
import DeleteIcon from "@mui/icons-material/Delete";
import { showErrorAlert, showSuccessAlert2 } from "@/services/alertService.js";

const Employee = ({ toggleMenu, expanded }: any) => {
  const [username, setUsername] = useState("");
  const [role, setRole] = useState("");
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem(appConstants?.username);
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
  }, []);
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedRole = localStorage.getItem(appConstants?.role);

      if (storedRole) {
        setRole(storedRole);
      }
    }
  }, []);

  const [branches, setBranches] = useState([]);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);
  const [departments, setDepartments] = useState([]);
  const [designations, setDesignations] = useState([]);
  const [selectedBranch, setSelectedBranch] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState([]);
  const [selectedDesignation, setSelectedDesignation] = useState([]);
  const [showExcelUploadModal, setShowExcelUploadModal] = useState(false);
  const [showLeaveUploadModal, setShowLeaveUploadModal] = useState(false);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [employeeId, setEmployeeId] = useState(123);
  const [documentData, setDocumentData] = useState([]);
  const [refreshFlag, setRefreshFlag] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [uploadErrors, setUploadErrors] = useState({});
  const [file, setFile] = useState(null);
  const [docType, setDocType] = useState({ documentId: 1 });

  // const [data, setData] = useState<any[]>([]);
  const toggleModal = () => {
    setIsModalOpen(!isModalOpen);
  };

  const handleRefresh = () => {
    setRefreshFlag(!refreshFlag);
    // Add additional logic if needed
  };
  const handleBulkUploadClose = () => setShowBulkUploadModal(false);
  const handleBulkUploadOpen = () => setShowBulkUploadModal(true);
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const payload = {
          // deptId:'',
          // branchId:'',
          // isActive:1,
          // offset:0,
          // limit:10
          type: "select",
        };

        const branchesData = await postRequest(API_URLS.GET_BRANCH, payload);
        // console.log("branchesDataconsole.log('::: ', );", branchesData);
        if (branchesData) setBranches(branchesData);
        const departmentsData = await getRequest(API_URLS.DEPT_LOOKUP);
        if (departmentsData) setDepartments(departmentsData);
        const designationsData = await getRequest(API_URLS.DESIGNATION_LOOKUP);
        if (designationsData) setDesignations(designationsData);
      } catch (error) {
        // console.error("Error fetching filter options:", error);
      }
    };

    fetchFilterOptions();
  }, []);

  useEffect(() => {
    const fetchFilteredData = async () => {
      try {
        const filterParams = {
          branch: selectedBranch,
          department: selectedDepartment,
          designation: selectedDesignation,
        };
        // const response = await postdata('employee/filter', filterParams);
        // setData(response.data);
      } catch (error) {
        console.error("Error fetching filtered data:", error);
      }
    };

    fetchFilteredData();
  }, [selectedBranch, selectedDepartment, selectedDesignation]);

  const handleBranchChange = (selected: any) => setSelectedBranch(selected);
  const handleDepartmentChange = (selected: any) =>
    setSelectedDepartment(selected);
  const handleDesignationChange = (selected: any) =>
    setSelectedDesignation(selected);

  const [anchorEl, setAnchorEl] = useState(null);
  const [data, setData] = useState<any[]>([]);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");

  const handleSearchChange = (e: any) => {
    setSearchTerm(e.target.value);
  };

  useEffect(() => {
    fetchItems();
  }, [offset, limit]);

  const fetchItems = async () => {
    try {
      const request = {
        branchIds: selectedBranch,
        departmentIds: selectedDepartment,
        designationIds: selectedDesignation,
        query: searchTerm,
        offset: offset,
        limit: limit,
        download: false,
      };
      const data = await postRequestWithSecurity(
        API_URLS.EMPLOYEE_LIST2,
        request,
        true
      );

      console.log("API Response from employee: ", data);

      if (data) {
        setData(data.content);
        setTotalItems(data.total);
      }
    } catch (error) {
      console.error("Error fetching items:", error);
    }
  };

  const handleCreateSchedule = async (params: any) => {
    try {
      console.log("Full params object: ", params);

      const empCode = params?.row?.empCode;

      // const payload = { data: [String(empCode)] };
      const payload = [empCode];

      console.log(" Final Payload:", payload);

      const response = await postRequest(
        API_URLS.SCHEDULE_WITH_EMP_CODE,
        payload
      );

      if (response) {
        showSuccessAlert2(
          ` Schedule created successfully for EmpCode ${empCode}`
        );
      } else {
        showErrorAlert(" Failed to create schedule!");
      }
    } catch (error) {
      console.error(" Error creating schedule:", error);
    }
  };

  // const handleCreateSchedule = async (params: any) => {
  //   try {
  //     console.log(" Full params object: ", params);

  //     const empCode = params?.row?.empCode;

  //     const payload = { empCodes: [String(empCode)] };
  //     console.log(" Final Payload:", payload);

  //     const token = localStorage.getItem("accessToken");

  //     const response = await axios.post(
  //       API_URLS.SCHEDULE_WITH_EMP_CODE,
  //       payload,
  //       {
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //           "Content-Type": "application/json",
  //         },
  //       }
  //     );

  //     if (response.status === 200) {
  //       alert(`Schedule created successfully for ${empCode}`);
  //     } else {
  //       alert("Failed to create schedule!");
  //     }
  //   } catch (error) {
  //     console.error("Error creating schedule:", error);
  //     alert(" An error occurred while creating the schedule.");
  //   }
  // };
  const handleExport = async () => {
    try {
      const token = localStorage.getItem("accessToken");
      const request = {
        branchIds: selectedBranch,
        departmentIds: selectedDepartment,
        designationIds: selectedDesignation,
        query: searchTerm,
        offset: offset,
        limit: limit,
        download: true,
      };

      // const response = await postRequest(API_URLS.EMPLOYEE_LIST2, request, true);
      const response = await axios.post(
        API_URLS.EMPLOYEE_LIST2,
        { encryptedData: await encryptData(request) },
        {
          responseType: "blob",
          headers: { Authorization: `Bearer ${token}` },
          // Important for handling binary data
        }
      );

      const blob = new Blob([response.data], {
        type: "application/octet-stream",
      });

      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.download = "employee_list.xlsx";
      a.click();
      window.URL.revokeObjectURL(urlBlob);
    } catch (error) {
      // console.error("Error exporting employee data:", error);
    }
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const handleClick = (event: any) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const [openDesigntionModal, setDesigntionModal] = useState(false);
  const handleDesigntionModalOpen = () => setDesigntionModal(true);
  const handleDesigntionModalClose = () => setDesigntionModal(false);
  const [selectedEmpId, setSelectedEmpId] = useState<any>(null);
  const [terminateModalOpen, setTerminateModalOpen] = useState(false);
  const [selectedEmpForTermination, setSelectedEmpForTermination] =
    useState(null);

  const [openSchedulenModal, setScheduleModal] = useState(false);
  const handleScheduleModalOpen = () => setScheduleModal(true);
  const handleScheduleModalClose = () => setScheduleModal(false);
  const [isLoading, setIsLoading] = useState(false);
  const [empId, setEmpId] = useState("");

  const handleResetFilters = () => {
    setSelectedBranch([]);
    setSelectedDepartment([]);
    setSelectedDesignation([]);
    setSearchTerm("");
  };

  const handleEditEmp = (row: any) => {
    localStorage.setItem("empDetailsToEdit", JSON.stringify(row?.allData));
    window.location.href = "./employee/empDetails";
  };
  const handleOpenPopup = (row: any) => {
    setShowPopup(true);
    setSelectedEmpId(row);
  };

  const handleUploadSuccess = () => {
    //console.log("Bulk upload successful!");
  };

  const handleWorkOff = (empId: any) => {
    setSelectedEmpForTermination(empId);
    setTerminateModalOpen(true);
  };

  const handleTerminateModalClose = () => {
    setTerminateModalOpen(false);
    setSelectedEmpForTermination(null);
  };

  const handleTerminate = (empId: any) => {
    // console.log(`Employee with ID: ${empId} terminated.`);
    setTerminateModalOpen(false);
  };

  const handleClosePopup = () => setShowPopup(false);

  const columns = [
    {
      field: "srNo",
      headerName: "Sr.no",
      width: 61,
      renderCell: (params: any) => {
        return <span>{params.row.srNo}</span>; // Show the srNo value
      },
    },
    {
      field: "empName",
      headerName: "Name",
      width: 150,
      // renderCell: (params:any) => (
      //   <div style={{ display: 'flex', alignItems: 'center' }}>
      //     <img
      //       src={params.row.imgUre}
      //       alt="Employee"
      //       style={{ width: 30, height: 30, borderRadius: '50%', marginRight: 10 }}
      //     />
      //     {params.value}
      //   </div>
      // ),
    },

    {
      field: "contact",
      headerName: "Contact",
      width: 80,
      renderCell: (params: any) => {
        return (
          <>
            {/* <span>{params.row.empName}</span> &nbsp; */}
            <a
              style={{ textDecoration: "none" }}
              href={`mailto:${params.row.email}`}
            >
              &nbsp;
              <EmailIcon sx={{ color: "blue" }} />
            </a>
            <a
              style={{ textDecoration: "none" }}
              href={`tel:${params.row.mobileNo}`}
            >
              &nbsp;
              <CallIcon sx={{ color: "blue" }} />
            </a>
          </>
        );
      },
    },
    { field: "branch", headerName: "Facility", width: 150 },
    { field: "department", headerName: "Department", width: 150 },
    { field: "designation", headerName: "Designation", width: 150 },
    { field: "upperName", headerName: "Reporting To", width: 150 },
    { field: "empCode", headerName: "Employee Code", width: 120 },

    {
      field: "action",
      headerName: "Action",
      width: 180,
      renderCell: (params: any) => {
        console.log("Row Data: ", params.row); // ✅ Check if empCode exists

        return (


          <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
            {/* New "Create Schedule" Button */}

            {/* Document Image */}
            <button
              className="btn btn-primary"
              onClick={() => handleOpenPopup(params.row.empId)}
              style={{ border: "none", background: "none", cursor: "pointer" }}
            >
              <img
                src="/image/icons8-document-48.png"
                alt="Document"
                style={{
                  width: "24px",
                  height: "24px",
                  marginBottom: "9px",
                  marginRight: "-9px",
                }}
              />
            </button>

            {/* Edit Icon */}
            <Link
              href={{
                pathname: `./employee/empDetails/${btoa(params.row.empId)}`,
              }}
            >
              <EditIcon
                style={{ cursor: "pointer", fontSize: "24px", color: "black" }}
              />
            </Link>

            <button
              className="btn btn-warning"
              onClick={() => handleWorkOff(params.row.empId)}
              style={{ border: "none", background: "none", cursor: "pointer" }}
            >
              <DeleteIcon
                style={{
                  fontSize: "24px",
                  color: "black",
                  marginBottom: "7px",
                }}
              />
            </button>

            <Link
              href={{
                pathname: `./employee/empViewDetails`,
                query: { empId: params.row.empId },
              }}
            >
              <VisibilityIcon
                style={{ cursor: "pointer", marginRight: "10px" }}
              />
            </Link>

            {/* <button
              className="btn btn-success"
              onClick={() => handleCreateSchedule(params)}
            >
              Create Schedule
            </button> */}

<button
  className="btn btn-success"
  onClick={() => handleCreateSchedule(params)}
  style={{ border: "none", background: "none", cursor: "pointer" }}
>
  <img
    src="/image/schedule1_black.png"
    alt="Create Schedule"
    className="icon-black"
    style={{
      width: "28px",
      height: "28px",
    }}
  />
</button>


          </div>
        );


      },
    },
  ];

  const rows = data.map((employee, index) => ({
    id: index,
    srNo: offset * limit + (index + 1),
    empName: employee.designationName
      ? `${employee.empName} (${employee.designationName})`
      : employee.empName,
    ethnicity: employee.ethnicity ?? "N/A", // ✅ Set default value

    branch: employee.branchName,
    department: employee.department,
    designation: employee.designation,
    upperName: employee.upperName,
    attendance: employee.attendance ? employee.attendance : "75%",
    email: employee.email,
    mobileNo: employee.mobileNo,
    allData: employee,
    empCode: employee?.empCode, // ✅ Add empCode for Create Schedule

    empId: employee?.empId,
    overTimeHours: employee.overTimeHours,
  }));

  const handlePageChange = (newPage: any) => {
    // const newOffset = newPage.page * limit;  // Calculate offset based on page number
    setOffset(newPage.page);
    setLimit(newPage.pageSize); // Handle changing page size
  };

  const handleModalClose = () => {
    setShowExcelUploadModal(false);
    setShowLeaveUploadModal(false);
  };

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <Row className="my-3">
          <Col md={12} sm={12}>
            <Row>
              <Col
                style={{
                  width: "30%",
                  marginLeft: "20px",
                  marginBottom: "20px",
                }}
              >
                <h4>{"Employee List"}</h4>
                {/* <span style={{ fontSize: "12px", color: "grey" }}>
                  Hi,{" "}
                  {username
                    ? username?.charAt(0).toUpperCase() + username.slice(1)
                    : ""}
                  . Your organization's Employees are listed here
                </span> */}
              </Col>
              {(role === "admin" ||
                role === "ceo" ||
                role === "superadmin") && (
                <Col
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                    marginBottom: "5px",
                    height: "30%",
                  }}
                >
                  {role !== "ceo" && (
                    <Button
                      style={{
                        width: "150px",
                        background: "green",
                        marginRight: "16px",
                        height: "37px",
                      }}
                      onClick={() => setShowBulkUploadModal(true)} // Open modal on click
                    >
                      Profile Upload
                    </Button>
                  )}
                  {role !== "ceo" && (
                    <Button
                      style={{
                        width: "150px",
                        background: "green",
                        height: "39px",
                        marginRight: "10px",
                      }}
                      onClick={handleExport}
                    >
                      Export
                    </Button>
                  )}

                  {role !== "ceo" && (
                    <Button
                      style={{
                        width: "150px",
                        background: "green",
                        marginRight: "15px",
                        height: "37px",
                      }}
                      onClick={() => setShowExcelUploadModal(true)}
                    >
                      Upload Excel
                    </Button>
                  )}

                  {role !== "ceo" && (
                    <Button
                      style={{
                        width: "180px",
                        background: "green",
                        marginRight: "10px",
                        height: "37px",
                      }}
                      onClick={() => setShowLeaveUploadModal(true)}
                    >
                      Upload Leave
                    </Button>
                  )}

                  <Link
                    href={`/adminDashboard/employee/empDetails/${btoa("new")}`}
                  >
                    {role !== "ceo" && (
                      <Button
                        style={{
                          width: "150px",
                          background: "green",
                          marginRight: "16px",
                        }}
                      >
                        + New User
                      </Button>
                    )}
                  </Link>
                </Col>
              )}
            </Row>
            {/* <Row>
              <Button
                style={{
                  width: '100px', margin: 'auto',
                  marginRight: '0px'
                }}
                aria-describedby={id}
                variant="contained"
                className={"carousal-container"}
                onClick={handleClick}
              >
                <FilterAltIcon />
              </Button>
              <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
              >
                <div>
                  <select className="filter-select">
                    <option disabled selected>Select Branch</option>
                    <option>Account</option>
                    <option>IT</option>
                    <option>Pharmacy</option>
                    <option>Nursing</option>
                  </select>
                </div>
                <div>
                  <select className="filter-select">
                    <option disabled selected>Select Department</option>
                    <option>Account</option>
                    <option>IT</option>
                    <option>Pharmacy</option>
                    <option>Nursing</option>
                  </select>
                </div>
                <hr />
                <div>
                  <select className="filter-select">
                    <option disabled selected>Select Designation</option>
                    <option>Junior HR</option>
                    <option>Pharmacist</option>
                    <option>Doctor</option>
                    <option>Nurse</option>
                  </select>
                </div>
              </Popover>
            </Row> */}
          </Col>
          <Col lg={12}>
            <Row className="employee-dropdowns" style={{ marginLeft: "100px" }}>
              {(role === "admin" ||
                role === "ceo" ||
                role === "superadmin") && (
                <>
                  <Col lg={3} style={{ marginLeft: "-80px" }}>
                    <BranchDropdown
                      branches={branches}
                      onChange={handleBranchChange}
                      isMulti={true}
                      value={selectedBranch}
                    />
                  </Col>
                  <Col lg={3}>
                    <DepartmentDropdown
                      departments={departments}
                      isMulti={true}
                      onChange={handleDepartmentChange}
                      value={selectedDepartment}
                    />
                  </Col>
                  <Col lg={3}>
                    <DesignationDropdown
                      designations={designations}
                      isMulti={true}
                      onChange={handleDesignationChange}
                      value={selectedDesignation}
                    />
                  </Col>
                </>
              )}
              <Col lg={2} md={1}>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search..."
                  className="form-control"
                  //className="cusm-form-control-searchterm "
                  style={{
                    borderRadius: "5px",
                    marginBottom: "10px",
                    marginLeft: "0px",
                    height: "40px",
                  }}
                />
              </Col>
              <Col
                md={1}
                className=" go-button-employee d-flex justify-content-start"
              >
                <button
                  onClick={fetchItems}
                  className="btn btn-primary"
                  style={{
                    width: "60px",
                    maxHeight: "80%",
                    marginLeft: "26px",
                  }}
                >
                  Apply
                </button>
                <Button
                  onClick={handleResetFilters}
                  className="btn-hanger"
                  style={{
                    width: "66px",
                    maxHeight: "80%",
                    backgroundColor: "grey",
                    marginLeft: "3px",
                  }}
                >
                  Reset
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>

        <Row className="my-3" style={{ marginLeft: "10px" }}>
          <TableFilter
            columns={columns}
            rows={rows}
            onPageChange={handlePageChange}
            totalCount={totalItems}
            pageSize={limit}
          />
        </Row>
        {openDesigntionModal && (
          <CreateDesignationModal
            show={openDesigntionModal}
            handleClose={handleDesigntionModalClose}
          />
        )}
        {openSchedulenModal && (
          <CreateScheduleModal
            show={openSchedulenModal}
            handleClose={handleScheduleModalClose}
          />
        )}
        {showExcelUploadModal && (
          <CreateExcelModal
            show={showExcelUploadModal}
            handleClose={handleModalClose}
            fetchItems={fetchItems} // Pass  function
            downloadType="Employee"
          />
        )}

        {/* {showLeaveUploadModal && (
          <UploadLeaveExcel
            show={showExcelUploadModal}
            handleClose={handleModalClose}
            //fetchItems={fetchItems} // Pass  function
            downloadType="Employee"
          />
        )} */}

        {showLeaveUploadModal && (
          <UploadLeaveExcel
            show={showLeaveUploadModal}
            handleClose={handleModalClose}
            fetchItems={fetchItems} // Pass  function
            downloadType="leave"
          />
        )}

        <DocumentPopup
          show={showPopup}
          handleClose={handleClosePopup}
          empId={selectedEmpId}
        />

        <TerminateEmployeeModal
          show={terminateModalOpen}
          onHide={handleTerminateModalClose}
          empId={selectedEmpForTermination}
          onTerminate={handleTerminate}
        />

        <BulkUploadModal
          show={showBulkUploadModal}
          handleClose={handleBulkUploadClose}
          onUploadSuccess={handleUploadSuccess}
        />
      </Container>
    </Layout>
  );
};

export default Employee;

const PopupBody = styled("div")(
  ({ theme }) => `
    width: max-content;
    padding: 12px 16px;
    margin: 8px;
    border-radius: 8px;
    border: 1px solid ${theme.palette.mode === "dark" ? grey[700] : grey[200]};
    background-color: ${theme.palette.mode === "dark" ? grey[900] : "#fff"};
    box-shadow: ${
      theme.palette.mode === "dark"
        ? `0px 4px 8px rgb(0 0 0 / 0.7)`
        : `0px 4px 8px rgb(0 0 0 / 0.1)`
    };
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    z-index: 1;
  `
);

const FilterButton = styled("button")(
  ({ theme }) => `
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: ${blue[500]};
    padding: 8px 16px;
    border-radius: 8px;
    color: white;
    transition: all 150ms ease;
    cursor: pointer;
    border: 1px solid ${blue[500]};
    box-shadow: 0 2px 4px ${
      theme.palette.mode === "dark"
        ? "rgba(0, 0, 0, 0.5)"
        : "rgba(0, 127, 255, 0.5)"
    }, inset 0 1.5px 1px ${blue[400]}, inset 0 -2px 1px ${blue[600]};
  
    &:hover {
      background-color: ${blue[600]};
    }
  
    &:active {
      background-color: ${blue[700]};
      box-shadow: none;
    }
  
    &:focus-visible {
      box-shadow: 0 0 0 4px ${
        theme.palette.mode === "dark" ? blue[300] : blue[200]
      };
      outline: none;
    }
  
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      box-shadow: none;
      &:hover {
        background-color: ${blue[500]};
      }
    }
  `
);

const grey = {
  50: "#F3F6F9",
  100: "#E5EAF2",
  200: "#DAE2ED",
  300: "#C7D0DD",
  400: "#B0B8C4",
  500: "#9DA8B7",
  600: "#6B7A90",
  700: "#434D5B",
  800: "#303740",
  900: "#1C2025",
};

const blue = {
  200: "#99CCFF",
  300: "#66B2FF",
  400: "#3399FF",
  500: "#007FFF",
  600: "#0072E5",
  700: "#0066CC",
};
