import React, { useState } from "react";
import "./CustomCalendar.css"; 
import moment from "moment";

const CustomCalendar = ({ setSelectedDates }) => {
  const today = new Date();
  const [currentMonth, setCurrentMonth] = useState(today.getMonth());
  const [currentYear, setCurrentYear] = useState(today.getFullYear());
  const [internalSelectedDates, setInternalSelectedDates] = useState([]); 

  const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const getDaysInMonth = (year, month) => {
    let date = new Date(year, month, 1);
    const days = [];


    const firstDayIndex = new Date(year, month, 1).getDay();
    let prevMonthDays = [];
    if (firstDayIndex > 0) {
      const lastDayPrevMonth = new Date(year, month - 1, 0).getDate();
      for (let i = firstDayIndex - 1; i >= 0; i--) {
        prevMonthDays.push(new Date(year, month - 1, lastDayPrevMonth - i));
      }
    }
    days.push(...prevMonthDays);

 
    while (date.getMonth() === month) {
      days.push(new Date(date));
      date.setDate(date.getDate() + 1);
    }


    const nextMonthDays = [];
    const daysToAdd = 7 - (days.length % 7);
    for (let i = 1; i <= daysToAdd && days.length < 42; i++) {
      nextMonthDays.push(new Date(year, month + 1, i));
    }
    days.push(...nextMonthDays);

    return days;
  };

  const handleDateClick = (day) => {
    const formattedDate = moment(day).format("YYYY-MM-DD")
    let updatedDates;
    if (internalSelectedDates.includes(formattedDate)) {
      updatedDates = internalSelectedDates.filter((d) => d !== formattedDate);
    } else {
      updatedDates = [...internalSelectedDates, formattedDate];
    }

    setInternalSelectedDates(updatedDates);
    setSelectedDates(updatedDates);
  };

  const handlePrevMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  const handleNextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  const days = getDaysInMonth(currentYear, currentMonth);

  return (
    <div className="custom-calendar">
      <div className="calendar-header">
        <button className="nav-button" onClick={handlePrevMonth}>
          &lt;
        </button>
        <p>
          {new Date(currentYear, currentMonth).toLocaleString("default", { month: "long" })}, {currentYear}
        </p>
        <button className="nav-button" onClick={handleNextMonth}>
          &gt;
        </button>
      </div>

      <div className="calendar-grid">
        {daysOfWeek.map((day, index) => (
          <div key={index} className="day-header">
            {day}
          </div>
        ))}
        {days.map((day, index) => {
          const isCurrentMonth = day.getMonth() === currentMonth;
          const isSelected = internalSelectedDates.includes(moment(day).format("YYYY-MM-DD"));
          const isSaturday = day.getDay() === 6;
          const isSunday = day.getDay() === 0;
          const isDisabled = day > today; 

          return (
            <div
              key={index}
              className={`day-cell ${isSelected ? "selected" : ""} 
              ${isSunday ? "sunday" : ""} ${isSaturday ? "saturday" : ""} 
              ${isCurrentMonth ? "" : "disabled"} ${isDisabled ? "disabled" : ""}`}
              onClick={() => isCurrentMonth && !isDisabled && handleDateClick(day)}
            >
              {day.getDate()}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CustomCalendar;
