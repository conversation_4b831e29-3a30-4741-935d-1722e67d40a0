{"id": "veritime-env-001", "name": "Veritime API Environment", "values": [{"key": "baseUrl", "value": "https://www.xpertlyte.tech/api/v1", "type": "default", "enabled": true, "description": "Base URL for API v1 endpoints"}, {"key": "baseUrl2", "value": "https://www.xpertlyte.tech/api", "type": "default", "enabled": true, "description": "Base URL for API endpoints without version"}, {"key": "accessToken", "value": "", "type": "secret", "enabled": true, "description": "JWT access token for authentication - set after login"}, {"key": "userRole", "value": "", "type": "default", "enabled": true, "description": "Current user role (ceo, superadmin, admin, supervisor, employee)"}, {"key": "empId", "value": "", "type": "default", "enabled": true, "description": "Current user employee ID"}, {"key": "employeeName", "value": "", "type": "default", "enabled": true, "description": "Current user employee name"}, {"key": "logHistoryId", "value": "", "type": "default", "enabled": true, "description": "Login session history ID"}, {"key": "createdEmpId", "value": "", "type": "default", "enabled": true, "description": "ID of newly created employee for testing"}, {"key": "leaveHistoryId", "value": "", "type": "default", "enabled": true, "description": "ID of leave application for approval/denial testing"}, {"key": "attendanceId", "value": "", "type": "default", "enabled": true, "description": "ID of attendance record for approval testing"}, {"key": "departmentId", "value": "1", "type": "default", "enabled": true, "description": "Default department ID for testing"}, {"key": "branchId", "value": "1", "type": "default", "enabled": true, "description": "Default branch ID for testing"}, {"key": "designationId", "value": "1", "type": "default", "enabled": true, "description": "Default designation ID for testing"}, {"key": "testUsername", "value": "<EMAIL>", "type": "default", "enabled": true, "description": "Test username for login"}, {"key": "testPassword", "value": "raj456123", "type": "secret", "enabled": true, "description": "Test password for login"}, {"key": "aesPassword", "value": "uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabqAU=", "type": "secret", "enabled": true, "description": "AES password for payload encryption (from NEXT_PUBLIC_AES_PASSWORD)"}, {"key": "aesSalt", "value": "uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabBA==", "type": "secret", "enabled": true, "description": "AES salt for payload encryption (from NEXT_PUBLIC_AES_SALT)"}, {"key": "currentDate", "value": "2024-01-15", "type": "default", "enabled": true, "description": "Current date for testing (YYYY-MM-DD format)"}, {"key": "fromDate", "value": "2024-01-01", "type": "default", "enabled": true, "description": "Start date for date range queries"}, {"key": "toDate", "value": "2024-01-31", "type": "default", "enabled": true, "description": "End date for date range queries"}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-15T10:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}