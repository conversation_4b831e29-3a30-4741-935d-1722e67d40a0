package com.company.wfm.entity;


import java.time.LocalDate;
import java.time.LocalDateTime;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "t_attendance_regularization")
public class AttendanceRegularization {


        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Long id;

        @Column(name = "employee_id")
        private String employeeId;

        @Column(name = "name")
        private String employeeName;

        @Column(nullable = false)
        private LocalDate date;

        @Column(name = "time_range", nullable = false)
        private String timeRange;

        @Column(nullable = false)
        private String reason;
        @Enumerated(EnumType.STRING)
        @Column(name = "approval_status", nullable = false)
        private ApprovalStatus approvalStatus = ApprovalStatus.PENDING;
        @Column(name = "action_reason")
        private String actionReason;

        @CreationTimestamp
        @Column(name = "created_at", nullable = false, updatable = false)
        private LocalDateTime createdAt;

        @UpdateTimestamp
        @Column(name = "updated_at", nullable = false)
        private LocalDateTime updatedAt;

        @Column(name = "multi_date", nullable = true)
        private String multiDate;

    }


