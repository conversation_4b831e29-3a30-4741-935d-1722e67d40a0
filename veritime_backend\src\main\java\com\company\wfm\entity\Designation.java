package com.company.wfm.entity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "t_designation")
public class Designation implements Serializable{

 	private static final long serialVersionUID = 1L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DESIGNATION_ID")
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "DEPARTMENT_ID", nullable = false)
    private Department department;

    @Column(name = "DESIGNATION_NAME", length = 100, nullable = false)
    private String name;

    @Column(name = "DESIGNATION_CODE", length = 50, nullable = false)
    private String code;

    @Column(name = "DESIGNATION_LEVEL", length = 50, nullable = false)
    private Long level;

    @Column(name = "CREATED_AT", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_AT", nullable = false)
    private LocalDateTime updatedAt;
//    @Enumerated(EnumType.STRING)
//    @Column(name = "ROLE", length = 20)
//    private DesignationRole role;

    @Column(name = "ROLE", length = 20)
    private String role;

    @Column(name = "is_active")
    private boolean isActive = true;

    @Column(name = "CREATED_BY")
    private Long createdBy;

    @Column(name = "UPDATED_BY")
    private Long updatedBy;

    @Column(name = "NOTICE_PERIOD")
    private BigDecimal noticePeriod;

}

