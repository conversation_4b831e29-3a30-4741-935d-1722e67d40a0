
.calendar-wrapper {
    max-width: 1000px;
    margin: 0 auto;
  }
  
  .my-custom-calendar .react-calendar__tile--active {
    background-color: var(--blue13) !important;
    color: white !important;
  }
  
  .cldr-hurs2 {
    font-size: 10px;
    margin-top: -3.5px;
    color: inherit;
    margin-left: 29px;
}
.Modified-Time {
  width: 89px;
 
  display: inline-block;
}
  .dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-left: 5px;
  }
  
  .legend-container {
    display: flex;
    justify-content: space-around;
    padding: 10px;
  }
  
  .legend-item {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 8px;
    border-radius: 50%;
  }
  
  .text-primary {
    color: var(--blue13) !important;
  }
  
  .bg-success {
    background-color: #00ff00 !important;
  }
  
  .bg-danger {
    background-color: #ff0000 !important;
  }
  
  .bg-warning {
    background-color: #f7c11c !important;
  }
  
  .bg-info {
    background-color: #17a2b8 !important;
  }
  
  .btn-block {
    width: 100%;
  }
  
  button.btn-outline-primary {
    color: var(--blue13);
    border-color: var(--blue13);
  }
  
  button.btn-outline-primary:hover {
    background-color: var(--blue13);
    color: white;
  }
  
  button.btn-outline-secondary {
    color: var(--blue3);
    border-color: var(--blue3);
  }
  
  button.btn-outline-secondary:hover {
    background-color: var(--blue3);
    color: white;
  }
  


.cldr-calendar {
    padding: 20px;
    background-color: var(--red14);
  }
  
  .cldr-calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .cldr-calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
  }
  
  .cldr-day {
    padding: 10px;
    font-size: 14px;
    color: var(--grey1);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    position: relative;
    min-height: 80px;
  }
  
  .cldr-hours {
    text-align: left;
    font-size: 10px;
    color: gray;
    margin-bottom: 5px;
  }
  
  .cldr-hours2 {
    text-align: center;
    font-size: 10px;
    color: green;
    margin-top: -10px;
    margin-right: -5px;
  }
  
  .cldr-date {
    text-align: right;
    font-weight: bold;
    color: var(--grey1);
    font-size: 16px;
  }
  
  .cldr-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-bottom: -6px;
  }
  
  .cldr-sunday .cldr-date {
    color: red;
  }
  
  .cldr-non-month-day {
    color: lightgray;
  }
  
  .cldr-non-month-day .cldr-date {
    color: lightgray;
  }
  .cldr-day-label {
    text-align: center;
    font-weight: bold;
    color: var(--grey1);
    padding-bottom: 10px;
  }
  
  @media (max-width: 968px) {
    .cldr-hours, .cldr-hours2 {
        /* display: none; */
    }
    .cldr-calendar {
        padding: 1px;
        background-color: var(--red14);
        /* width: 350px; */
      }
      .text{
        padding-right: 100px;
      }
      .btn-text{
        padding-right: 100px;
      }
  }



 
 
 
  
  