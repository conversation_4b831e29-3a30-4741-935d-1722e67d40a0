package com.company.wfm.util;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.KeySpec;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.company.wfm.service.impl.LocalDateTimeAdapter;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@Component
public class EncryptionUtil {

	private static final Logger logger = LoggerFactory.getLogger(EncryptionUtil.class);

	@Value("${aes.secret.password}")
	private  String password;
	@Value("${aes.secret.salt}")
	private  String salt;
	private static final String ALGORITHM = "AES/GCM/NoPadding";
	private static final String KEY_GENERATOR_ALGORITHM = "PBKDF2WithHmacSHA256";
	private static final int GCM_TAG_LENGTH = 128;
	private static final int KEY_SIZE = 256; // AES-256
	private static final int ITERATIONS = 100000; // Strong security

	/**
	 *
	 * @return SecretKey
	 * @throws NoSuchAlgorithmException
	 * @throws Exception
	 */
	public  SecretKey generateKey() throws Exception {
		SecretKeyFactory factory = SecretKeyFactory.getInstance(KEY_GENERATOR_ALGORITHM);
		KeySpec spec = new PBEKeySpec(password.toCharArray(), getSalt(), ITERATIONS, KEY_SIZE);
		byte[] keyBytes = factory.generateSecret(spec).getEncoded();
		return new SecretKeySpec(keyBytes, "AES");
	}

	private  byte[] getSalt() {
		return Base64.getDecoder().decode(salt);
	}
	/**
	 *
	 * @return byte[]
	 */
	public   byte[] generateIv() {
		byte[] iv = new byte[12]; // Recommended IV size for GCM
		new SecureRandom().nextBytes(iv);
		return iv;
	}

	/**
	 *
	 * @param plainText
	 * @param secretKey
	 * @return
	 * @throws Exception
	 */
	public   String encrypt(String plainText, SecretKey secretKey) throws Exception {

		logger.info("Reached inside encrypt function: plaintext=" + plainText);
		logger.info("SecretKey (Base64 Encoded): " + Base64.getEncoder().encodeToString(secretKey.getEncoded()));

		byte[] iv = generateIv();
		logger.info("Generated IV: " + Arrays.toString(iv));
		Cipher cipher = Cipher.getInstance(ALGORITHM);
		logger.info("Cipher Algorithm: " + cipher.getAlgorithm());
		GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
		cipher.init(Cipher.ENCRYPT_MODE, secretKey, parameterSpec);

		byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
		logger.info("Encrypted Bytes: " + Arrays.toString(encryptedBytes));
		logger.info("EncryptedBytes length=" + encryptedBytes.length + " | IV length=" + iv.length);

		// Prepend IV to the encrypted data
		byte[] combined = new byte[iv.length + encryptedBytes.length];
		logger.info("combined="+combined);
		System.arraycopy(iv, 0, combined, 0, iv.length);
		System.arraycopy(encryptedBytes, 0, combined, iv.length, encryptedBytes.length);
		logger.info("Combined IV + Ciphertext: " + Arrays.toString(combined));
		String encryptedText=Base64.getEncoder().encodeToString(combined);

		logger.info("Final Encrypted Text (Base64 Encoded): " + encryptedText);

		return encryptedText;
	}

	/**
	 *
	 * @param encryptedText
	 * @param secretKey
	 * @return
	 * @throws Exception
	 */
	public   String decrypt(String encryptedText, SecretKey secretKey) throws Exception {
		logger.info("Reached inside decrypt function: encryptedText=" + encryptedText);
		logger.info("SecretKey=" + Base64.getEncoder().encodeToString(secretKey.getEncoded()));
		byte[] decodedBytes = Base64.getDecoder().decode(encryptedText);
		logger.info("Decoded Bytes (Base64 Decoded): " + Arrays.toString(decodedBytes));

		// Extract IV
		byte[] iv = new byte[12];

		System.arraycopy(decodedBytes, 0, iv, 0, iv.length);
		logger.info("IV (Initialization Vector): " + Arrays.toString(iv));
		logger.info("decodedBytes.length=" + decodedBytes.length + " | iv length=" + iv.length);

		// Extract ciphertext

		byte[] encryptedBytes = new byte[decodedBytes.length - iv.length];
		System.arraycopy(decodedBytes, iv.length, encryptedBytes, 0, encryptedBytes.length);
		logger.info("Extracted Encrypted Bytes: " + Arrays.toString(encryptedBytes));
		Cipher cipher = Cipher.getInstance(ALGORITHM);
		logger.info("Inside the decrypt cipher=" + cipher);
		GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
		cipher.init(Cipher.DECRYPT_MODE, secretKey, parameterSpec);

		byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
		logger.info("Decrypted Bytes: " + Arrays.toString(decryptedBytes));
		logger.info("Decrypted Text: " + new String(decryptedBytes));
		return new String(decryptedBytes);
	}

	/**
	 *
	 * @return Gson
	 */
	public  Gson createGson() {
		return new GsonBuilder().registerTypeAdapter(LocalDateTime.class, new LocalDateTimeAdapter())
				.registerTypeAdapter(LocalTime.class, new LocalDateTimeAdapter())
				.registerTypeAdapter(LocalDate.class, new LocalDateTimeAdapter()).serializeNulls().setPrettyPrinting().create();
	}
}
