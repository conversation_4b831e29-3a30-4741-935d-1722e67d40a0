package com.company.wfm.controller;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.TeamHierarchyService;

@RestController
@RequestMapping("/api/v1/teams")
@CrossOrigin(origins = "*")
public class TeamHierarchyController {
    private static final Logger logger = LoggerFactory.getLogger(TeamHierarchyController.class);


    @Autowired
    private TeamHierarchyService teamHierarchyService;

    @GetMapping("/get-all-employees-list")
    public List<Map<String, Object>> getAllEmployeesList() {
        return teamHierarchyService.getAllEmployeesList();
    }

    @GetMapping("/get-my-teams/{empId}")
    public List<Map<String, Object>> getMyTeams(@PathVariable Long empId) {
        return teamHierarchyService.getMyTeams(empId);
    }

   /* @GetMapping("/get-hierarchy-data/{empId}")
    public Map<String, List<Map<String, Object>>> getHierarchyData(@PathVariable Long empId) {
        return teamHierarchyService.getHierarchyData(empId);
    }*/

    @GetMapping("/get-hierarchy-data/{empId}")
    public ResponseEntity<?> getHierarchyData(@PathVariable Long empId) {
        try {
            logger.info("Received request to get hierarchy data for empId: {}", empId);

            // Fetch hierarchy data from the service
            Map<String, List<Map<String, Object>>> hierarchyData = teamHierarchyService.getHierarchyData(empId);

            // Return successful response with the hierarchy data
            return ResponseEntity.ok(hierarchyData);
        } catch (IllegalArgumentException ex) {
            // Handle specific exceptions with meaningful messages
            logger.error("Invalid input for empId: {}", empId, ex);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("error", "Invalid employee ID. Please check the input and try again."));
        } catch (Exception ex) {
            // Handle general exceptions
            logger.error("Error occurred while processing request for empId: {}", empId, ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unable to fetch hierarchy data. Please try again later."));
        }
    }

}