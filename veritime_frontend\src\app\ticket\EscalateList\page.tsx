"use client";
import Layout from "../../../components/Layout";
import React, { useEffect, useState } from "react";
import { getRequest, postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import Table from "../../../modules/Ticket/ViewTicket/ExcalateLate";
import { Container, Row, Col, Button } from 'react-bootstrap';
import Select from 'react-select';
import DepartmentDropdown from "@/components/Dropdowns/DeptDropdown";

interface Employee {
  value: string; // empId
  label: string; // empName
}

const ViewTicket = () => {
  const [departmentId, setDepartmentId] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [assignedTo, setAssignedTo] = useState<string | null>(null);
  const [createdBy, setCreatedBy] = useState<string | null>(null);
  const [myDepartmentTicket, setMyDepartmentTicket] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  // Function to handle ticket details view
  const handleEyeClick = (ticketId: string) => {
  };

  // Function to handle the fetch of data
  const fetchData = async () => {
    setLoading(true);
    try {
      // If needed, make any API calls to fetch required data here
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  // Effect hook to fetch data when the component mounts or dependencies change
  useEffect(() => {
    fetchData();
  }, [departmentId, status, assignedTo, createdBy, myDepartmentTicket]);

  return (
    <Layout>
      <div className="container-fluid bg-custom p-4 pt-5">
        <h1 className="h3" style={{marginLeft: '34px'}}>Escalation List</h1>
        <Container fluid style={{ width: '100%' }}>
          <Row>
            <Col>
              {loading ? (
                <p>Loading tickets...</p>
              ) : (
                <div className="container-fluid bg-custom p-4 pt-5">
                  <div className="container-fluid bg-white p-4">
                    <Table
                      onEyeClick={handleEyeClick}
                      departmentId={departmentId}
                      status={status}
                      assignedTo={assignedTo}
                      createdBy={createdBy}
                      myDepartmentTicket={myDepartmentTicket}
                    />
                  </div>
                </div>
              )}
            </Col>
          </Row>
        </Container>
      </div>
    </Layout>
  );
};

export default ViewTicket;
