package com.company.wfm.dto;


import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor

public class DepartmentDTO {

    private Long departmentId;

    private String departmentName;

    private String departmentCode;

    private String category;

    private Boolean isActiveDepartment;

    private List<BranchDTO> branches;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long createdBy;
    private Long updatedBy;
}
