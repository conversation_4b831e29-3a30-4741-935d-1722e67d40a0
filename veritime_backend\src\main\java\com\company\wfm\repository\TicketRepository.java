package com.company.wfm.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.Ticket;

@Repository
public interface TicketRepository extends JpaRepository<Ticket, Long> {
/*
    @Query("SELECT t FROM Ticket t ORDER BY t.ticketId DESC")
    Page<Ticket> findAllTickets(Pageable pageable);

    //admin
    @Query("SELECT t FROM Ticket t " +
            "WHERE (t.branchId = :branchId OR t.departmentId = :departmentId) " +
            "AND (t.assignedTo = :userId OR t.departmentId = :departmentId)")
    Page<Ticket> findTicketsForAdmin(@Param("userId") Long userId,
                                     @Param("branchId") Long branchId,
                                     @Param("departmentId") Long departmentId,
                                     Pageable pageable);

    //Supervisor Role:
    @Query("SELECT t FROM Ticket t " +
            "WHERE (t.createdBy = :userId OR t.assignedTo = :userId OR t.departmentId = :departmentId) " +
            "AND (t.createdBy = :userId OR t.assignedTo = :userId)")
    Page<Ticket> findTicketsForSupervisor(@Param("userId") Long userId,
                                          @Param("departmentId") Long departmentId,
                                          Pageable pageable);


    //Employee Role:
    @Query("SELECT t FROM Ticket t " +
            "WHERE (t.createdBy = :userId OR t.assignedTo = :userId OR t.departmentId = :departmentId) " +
            "AND (t.createdBy = :userId OR t.assignedTo = :userId)")
    Page<Ticket> findTicketsForEmployee(@Param("userId") Long userId,
                                        @Param("departmentId") Long departmentId,
                                        Pageable pageable);

    // Superadmin Role:
    @Query("SELECT t FROM Ticket t " +
            "WHERE (t.companyId = :companyId OR t.departmentId = :departmentId) " +
            "AND (t.assignedTo = :userId OR t.departmentId = :departmentId)")
    Page<Ticket> findTicketsForSuperAdmin(@Param("userId") Long userId,
                                          @Param("companyId") Long companyId,
                                          @Param("departmentId") Long departmentId,
                                          Pageable pageable);*/

    Optional<Ticket> findByTicketId(Long ticketId);

}
