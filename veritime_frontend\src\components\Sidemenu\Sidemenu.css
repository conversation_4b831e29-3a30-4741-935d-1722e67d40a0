
.sidemenu-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 1000;
  background-color: var(--red14);
  transition: left 0.3s;
  overflow: hidden;
}

.sidemenu-container.expanded {
  left: 0;
  top:0px;
}

.logo {
  height: 80px;
  display: none;
}

.sidemenu-container.expanded .logo {
  display: block;
}

.menu-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}


.menu-item {
  background-color: transparent;
  width: 100%;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: width 0.3s, justify-content 0.3s;
}

.menu-item.active {
  background-color: var(--blue9);
}

.sidemenu-container.expanded .menu-item {
  justify-content: flex-start;
}

.menu-item-image {
  height: auto;
  width: 25px;
  margin-right: 0;
  transition: height 0.3s, margin-right 0.3s;
}

.menu-item-image.active {
  height: 20px;
}

.sidemenu-container.expanded .menu-item-image {
  margin-right: 20px;
  /* margin-bottom: 50px; */
}

.menu-item-text {
  display: none;
  transition: display 0.3s;
  cursor: pointer;
  margin: 0;
}

.sidemenu-container.expanded .menu-item-text {
  display: inline;
}

.center-image {
  position: absolute;
  height: 70px;
  width: 40px;
  top: 13%;
  left: 65px;
  cursor: pointer;
  transition: left 0.3s;
  z-index: 2000;
}

.center-image.expanded {
  left: 210px;
}

.logo_sm{
  height: 100px;
}

.sidemenu-container {
  position: relative;
}

@media (max-width: 768px) {
  .sidemenu-container {
    left: -250px;
  }

  .sidemenu-container.expanded {
    left: 0;
    width: 100%;
  }

  .menu-container {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  .menu-item {
    width: 100%;
    justify-content: center;
  }

  .menu-item-text {
    display: inline;
  }

  .sidemenu-container.expanded .menu-item-text {
    display: inline;
  }

  .center-image {
    display: none;
  }
}




.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.popup-content {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  position: relative;
  z-index: 1000;
}

.custom-card {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

a {
  color: #000;
  text-decoration: none;
  padding-bottom: 10px;

  :hover {
    color: #999;
  }
}