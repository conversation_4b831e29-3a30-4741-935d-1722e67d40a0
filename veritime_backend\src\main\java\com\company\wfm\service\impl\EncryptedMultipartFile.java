package com.company.wfm.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import org.springframework.web.multipart.MultipartFile;

public class EncryptedMultipartFile implements MultipartFile {

    private byte[] fileData;
    private String originalFilename;

    public EncryptedMultipartFile(byte[] fileData, String originalFilename) {
        this.fileData = fileData;
        this.originalFilename = originalFilename;
    }

    @Override
    public String getName() {
        return originalFilename;
    }

    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }

    @Override
    public String getContentType() {
        // Return the content type if needed, or infer it based on file extension
        return "application/octet-stream"; // You may change this to a more specific content type if needed
    }

    @Override
    public boolean isEmpty() {
        return fileData == null || fileData.length == 0;
    }

    @Override
    public long getSize() {
        return fileData.length;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return fileData;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(fileData);
    }

    @Override
    public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
        // If you need to save the file to disk, you can implement it here
    }
}
