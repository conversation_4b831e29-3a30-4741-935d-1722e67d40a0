package com.company.wfm.repository;


import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.ProvinceDTO;
import com.company.wfm.entity.Province;

@Repository
public interface ProvinceRepository extends JpaRepository<Province, Long> {
    Optional<Province> findByName(String name);

    @Query("select new com.company.wfm.dto.ProvinceDTO(d.id, d.name)  from Province d")
    List<ProvinceDTO> findAllProvincesOnly();
}