package com.company.wfm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "t_shift")
public class Shift {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SHIFT_ID")
    private Long shiftId;

    @Column(name = "SHIFT_DATE")
    private LocalDateTime shiftDate;

    @Column(name = "EMP_ID")
    private Long empId;

    @Column(name = "START_TIME")
    private LocalDateTime startTime;

    @Column(name = "END_TIME")
    private LocalDateTime endTime;

    @Column(name = "DUTY_ID")
    private Long dutyId;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "CREATED_TIME", nullable = false)
    private LocalDate createdTime;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Column(name = "UPDATED_TIME")
    private LocalDate updatedTime;


}