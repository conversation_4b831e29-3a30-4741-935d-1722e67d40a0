import { <PERSON>dal, <PERSON><PERSON>, Form } from "react-bootstrap";
import React, { useState } from "react";
import { postRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import { showErrorAlert, showSuccessAlert2 } from "@/services/alertService.js";

const TerminateEmployeeModal = ({ show, onHide, empId, onTerminate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [reason, setReason] = useState(""); // New state for reason

  const handleReasonChange = (e) => {
    setReason(e.target.value);
  };

  const handleTerminate = async () => {
    if (!reason.trim()) {
      alert("Please provide a reason for termination.");
      return;
    }

    setIsLoading(true);
    try {
      const response = await postRequest(API_URLS.DEACTIVATION, {
        empId,
        terminationReason: reason, // Use 'terminationReason' in the API body
      });

      
      onTerminate(empId, reason);
      showSuccessAlert2(response);
      onHide(); // Close the modal
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>Terminate Employee</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p style={{ marginBottom: '10px' }}>Are you sure you want to terminate this employee?</p>

        <Form.Group controlId="terminationReason">
          <Form.Label style={{marginTop: "10px"}}>Reason for Termination</Form.Label>
          <Form.Control
            as="textarea"
            rows={3}
            value={reason}
            onChange={handleReasonChange}
            placeholder="Please provide a reason for termination."
          />
        </Form.Group>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button
          variant="danger"
          onClick={handleTerminate}
          disabled={isLoading}
        >
          {isLoading ? "Terminating..." : "Terminate"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default TerminateEmployeeModal;
