import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Form } from "react-bootstrap";
import { postRequest, putRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import { showSuccessAlert2, showErrorAlert } from "@/services/alertService.js";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Container, Row, Col } from "react-bootstrap";

const CreateLeaveModal = ({
  show,
  handleClose,
  handleSave,
  rowToEdit,
}: any) => {
  const [data, setData] = useState({
    leaveName: "",
    allocatedDays: 0,
    daysApplied: "",
    leaveDescription: "",
    isActive: true,
    leaveCreditMethod: "",
    leaveExclusion: {
      holiday: false,
      weekOff: false,
      leave: false,
    },
    carryForward: false,
    leaveCreditBasis: "",
    leaveCreditInterval: 0,
    leaveCredited: 0,
    minimumCount: [],
    effectiveLeaveDate: "",
    deefectiveLeaveDate: "",
    allowFileUpload: false,
  });

  const [allowFileUpload, setAllowFileUpload] = useState(false);
  const [allowInterval, setallowInterval] = useState(false);
  useEffect(() => {
    console.log("Row to Edit:", rowToEdit); // Log rowToEdit data

    if (rowToEdit) {
      setData({
        leaveName: rowToEdit.leaveName,
        allocatedDays: rowToEdit.allocatedDays || 0,
        daysApplied: rowToEdit.daysApplied || 0,
        allowFileUpload: rowToEdit.allowFileUpload || false,

        // daysApplied: rowToEdit.daysApplied,
        leaveDescription: rowToEdit.leaveDescription,
        isActive: rowToEdit.isActive || false,
        leaveCreditMethod: rowToEdit.leaveCreditMethod ,
        leaveExclusion: {
          holiday: rowToEdit.leaveExclusion?.includes("Holiday") || false,
          weekOff: rowToEdit.leaveExclusion?.includes("Weekoff") || false,
          leave: rowToEdit.leaveExclusion?.includes("Leave") || false,
        },
        carryForward: rowToEdit.carryForward || false,
        leaveCreditBasis: rowToEdit.leaveCreditBasis ,
        leaveCreditInterval: rowToEdit.leaveCreditInterval || 0,

        leaveCredited: rowToEdit.leaveCredited || 0,

        minimumCount: rowToEdit.minimumCount || 1,
        effectiveLeaveDate: rowToEdit.effectiveLeaveDate || "",
        deefectiveLeaveDate: data.deefectiveLeaveDate,
      });

      setAllowFileUpload(rowToEdit.allowFileUpload || false);
    } else {
      setData({
        leaveName: "",
        allocatedDays: 0,
        daysApplied: "",
        leaveDescription: "",
        isActive: true,
        leaveCreditMethod: "",
        leaveExclusion: { holiday: false, weekOff: false, leave: false },
        carryForward: false,
        leaveCreditBasis: "",
        leaveCreditInterval: 0,
        leaveCredited: 0,
        minimumCount: [],
        effectiveLeaveDate: "",
        deefectiveLeaveDate: "",
        allowFileUpload: false,
      });
      setAllowFileUpload(false);
    }
  }, [rowToEdit]);

  const handleChange = (e: any) => {
    setData({ ...data, [e.target.name]: e.target.value });
    if(e.target.name==="allocatedDays"){
      setData({ ...data, leaveCredited: e.target.value,allocatedDays:e.target.value });
    }
      
  };
  // useEffect(()=>{{console.log("leaveCredited",data)}},[data.leaveCredited,data.allocatedDays])

  const handleCheckboxChange = (e: any) => {
    setData({ ...data, isActive: e.target.checked });
  };

  const handleRadioChange = (e: any) => {
    const newValue = e.target.value;
    if (newValue == "lumpsum") {
      setData({
        ...data,
        leaveCreditInterval: 0,
      });
      setallowInterval(newValue === "lumpsum");
    }
    setData({
      ...data,
      leaveCreditMethod: newValue,
    });
    setallowInterval(newValue === "attendance");
  };

  const handleLeaveExclusionChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, checked } = e.target;

    setData((prevData) => ({
      ...prevData,
      leaveExclusion: {
        ...prevData.leaveExclusion,
        [name]: checked, // Update the boolean value for that exclusion type
      },
    }));
  };

  const handleDocumentUploadChange = (e: any) => {
    setAllowFileUpload(e.target.checked);
  };

  const handleCarryForwardChange = (e: any) => {
    setData({ ...data, carryForward: e.target.checked });
  };

  const handleSaveClick = async () => {
    if (
      !data.leaveName ||
      !data.leaveCredited ||
      !data.daysApplied ||
      !data.leaveDescription
    ) {
      // console.log("leave name",!data.leaveName);
      // console.log("allocatedDays",!data.allocatedDays);
      // console.log("daysApplied",!data.daysApplied);
      // console.log("leaveDescription",!data.leaveDescription);
      return;
    }

    try {
      const requestBody = {
        type: data.leaveName,
        description: data.leaveDescription,
        daysAppliedBefore: Number(data.daysApplied),
        allowFileUpload: allowFileUpload,
        minimumCount: Number(data.minimumCount) || 0,
        leaveCreditMethod:
          data.leaveCreditMethod === "lumpsum"
            ? "lumpsum"
            : data.leaveCreditMethod,
        leaveCreditBasis: data.leaveCreditBasis,
        carryForward: data.carryForward,
        leaveCreditInterval: String(data.leaveCreditInterval),
        leaveCredited: data.allocatedDays,
        leaveExclusion: (
          Object.keys(data.leaveExclusion) as Array<
            keyof typeof data.leaveExclusion
          >
        )
          .filter((key) => data.leaveExclusion[key])
          .map((key) => {
            if (key === "holiday") return "Holiday";
            if (key === "weekOff") return "Weekoff";
            if (key === "leave") return "Leave";
            return key;
          }),
        effectiveLeaveDate: data.effectiveLeaveDate,
        deefectiveLeaveDate: data.deefectiveLeaveDate,
      };

      console.log("Sending payload:", requestBody);

      let response, updatedRow;

      if (rowToEdit) {
        response = await putRequest(
          `${API_URLS.UPDATE_LEAVE_LIST(rowToEdit.id)}?isActive=${
            data.isActive
          }`,
          requestBody
        );
        updatedRow = { ...rowToEdit, ...data };
        showSuccessAlert2("Leave Updated");
      } else {
        response = await postRequest(API_URLS.MASTER_LEAVE_CREATE, requestBody);
        showSuccessAlert2("Leave created successfully!");
        handleClose();
      }

      if (response) {
        handleSave(updatedRow);
      } else {
        showErrorAlert("Failed to save the leave.");
      }
    } catch (error) {
      showErrorAlert("An error occurred while saving the leave.");
    }
  };

  const handleLeaveCreditBasisChange = (e: any) => {
    console.log("Selected leave credit basis:", e.target.value);
    setData({ ...data, leaveCreditBasis: e.target.value });
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      backdrop="static"
      keyboard={false}
      size="xl" // Changed from "lg" to "xl" for extra width
      centered
      style={{ margin: "auto", alignItems: "center" }}
    >
      <Modal.Header closeButton>
        <Modal.Title>{rowToEdit ? "Edit Leave" : "Add Leave"}</Modal.Title>
      </Modal.Header>
      <Modal.Body
        style={{
          overflowY: "auto",
          maxHeight: "90vh",
          width: "100%",
        }}
      >
        <Form>
          {/* Top Section - Leave Details */}
          <div className="leave-details-section mb-4 p-3 border rounded">
            <h5 className="mb-3">Leave Details</h5>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Leave Type<span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Control
                    type="text"
                    name="leaveName"
                    value={data.leaveName}
                    onChange={handleChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Leave Description<span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Control
                    type="text"
                    name="leaveDescription"
                    value={data.leaveDescription}
                    onChange={handleChange}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label style={{marginRight:"15px"}}>
                    Effective Leave Date<span className="text-danger">*</span>
                  </Form.Label>
                  <DatePicker
                    selected={data.effectiveLeaveDate ? new Date(data.effectiveLeaveDate) : null}
                    onChange={(date: Date | null) => {
                      if (date) {
                        const localDate = new Date(
                          date.getTime() - date.getTimezoneOffset() * 60000
                        )
                          .toISOString()
                          .split("T")[0];
                        setData(prevData => ({
                          ...prevData,
                          effectiveLeaveDate: localDate,
                        }));
                      }
                    }}
                    className="form-control"
                    dateFormat="yyyy-MM-dd"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label style={{marginRight:"15px"}}>Deactive Leave Date</Form.Label>
                  <DatePicker
                    selected={data.deefectiveLeaveDate ? new Date(data.deefectiveLeaveDate) : null}
                    onChange={(date: Date | null) => {
                      if (date) {
                        const localDate = new Date(
                          date.getTime() - date.getTimezoneOffset() * 60000
                        )
                          .toISOString()
                          .split("T")[0];
                        setData(prevData => ({
                          ...prevData,
                          deefectiveLeaveDate: localDate,
                        }));
                      }
                    }}
                    className="form-control"
                    dateFormat="yyyy-MM-dd"
                  />
                </Form.Group>
              </Col>
            </Row>
          </div>
  
          {/* Bottom Section - Split into two columns */}
          <Row>
            {/* Left Column - Rule Engine */}
            <Col md={6} className="pe-3">
              <div className="rule-engine-section p-3 border rounded h-100">
                <h5 className="mb-3">Rule Engine</h5>
                
                <Form.Group className="mb-3">
                  <Form.Label>Leave Credit Method<span className="text-danger">*</span></Form.Label>
                  <div className="d-flex flex-wrap gap-3">
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                     type="radio"
                      label="Lumpsum-Based"
                      value="lumpsum"
                      checked={data.leaveCreditMethod === "lumpsum"}
                      onChange={handleRadioChange}
                    />
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                      type="radio"
                      label="Attendance"
                      value="attendance"
                      checked={data.leaveCreditMethod === "attendance"}
                      onChange={handleRadioChange}
                    />
                  </div>
                </Form.Group>
                
  
                <Form.Group className="mb-3">
                  <Form.Label>Leave Credit Basis<span className="text-danger">*</span></Form.Label>
                  <div className="d-flex flex-column gap-2">
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                      type="radio"
                      label="Joining Date"
                      name="leaveCreditBasis"
                      value="joiningDate"
                      checked={data.leaveCreditBasis === "joiningDate"}
                      onChange={handleLeaveCreditBasisChange}
                    />
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                      type="radio"
                      label="Effective Date"
                      name="leaveCreditBasis"
                      value="effectiveDate"
                      checked={data.leaveCreditBasis === "effectiveDate"}
                      onChange={handleLeaveCreditBasisChange}
                    />
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                      type="radio"
                      label="Immediate After Probation Period"
                      value="ImmediateAfterProbationPeriod"
                      checked={data.leaveCreditBasis === "ImmediateAfterProbationPeriod"}
                      onChange={handleChange}
                      name="leaveCreditBasis"
                    />
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                      type="radio"
                      label="Worked After Probation Period"
                      value="WorkedAfterProbationPeriod"
                      checked={data.leaveCreditBasis === "WorkedAfterProbationPeriod"}
                      onChange={handleChange}
                      name="leaveCreditBasis"
                    />
                  </div>
                </Form.Group>
  
                {allowInterval && (
                  <Form.Group className="mb-3">
                    <Form.Label>Leave Credit Interval<span className="text-danger">*</span></Form.Label>
                    <Form.Control
                      type="number"
                      name="leaveCreditInterval"
                      value={data.leaveCreditInterval}
                      onChange={(e) => {
                        const value = parseInt(e.target.value, 10);
                        if (value >= 0 || e.target.value === "") {
                          handleChange(e);
                        }
                      }}
                      min="0"
                    />
                  </Form.Group>
                )}
  
                <Form.Group className="mb-3">
                  <Form.Label>Leave Credited (in days)<span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    type="number"
                    name="allocatedDays"
                    value={data.leaveCredited}
                    onChange={(e) => {
                      const value = parseInt(e.target.value, 10);
                      if (value >= 0 || e.target.value === "") {
                        handleChange(e);
                      }
                    }}
                    min="0"
                  />
                </Form.Group>
  
                <Form.Group className="mb-3">
                  <Form.Label>Leave Exclusion<span className="text-danger">*</span></Form.Label>
                  <div className="d-flex flex-wrap gap-3">
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                      type="checkbox"
                      label="Holiday"
                      name="holiday"
                      checked={data.leaveExclusion.holiday}
                      onChange={handleLeaveExclusionChange}
                    />
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                      type="checkbox"
                      label="Week Off"
                      name="weekOff"
                      checked={data.leaveExclusion.weekOff}
                      onChange={handleLeaveExclusionChange}
                    />
                    <Form.Check
                    style={{display:"flex", gap:"10px"}}
                      type="checkbox"
                      label="Leave"
                      name="leave"
                      checked={data.leaveExclusion.leave}
                      onChange={handleLeaveExclusionChange}
                    />
                  </div>
                </Form.Group>
  
                <Form.Group className="mb-3">
                  <Form.Check
                  style={{display:"flex", gap:"10px"}}
                    type="checkbox"
                    label="Carry Leave Forward"
                    checked={data.carryForward}
                    onChange={handleCarryForwardChange}
                  />
                </Form.Group>
              </div>
            </Col>
  
            {/* Right Column - Validation */}
            <Col md={6} className="ps-3">
              <div className="validation-section p-3 border rounded h-100">
                <h5 className="mb-3">Validation</h5>
                
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    label="Document Upload"
                    checked={allowFileUpload}
                    onChange={handleDocumentUploadChange}
                    style={{display:"flex", gap:"10px"}}
                  />
                </Form.Group>
  
                {allowFileUpload && (
                  <Form.Group className="mb-3">
                    <Form.Label>Document Lead time<span className="text-danger">*</span></Form.Label>
                    <Form.Control
                      type="number"
                      name="minimumCount"
                      value={data.minimumCount}
                      onChange={(e) => {
                        const value = parseInt(e.target.value, 10);
                        if (value >= 0 || e.target.value === "") {
                          handleChange(e);
                        }
                      }}
                      min="0"
                    />
                  </Form.Group>
                )}
  
                <Form.Group className="mb-3">
                  <Form.Label>Days Applied<span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    type="number"
                    name="daysApplied"
                    value={data.daysApplied}
                    onChange={(e) => {
                      const value = parseInt(e.target.value, 10);
                      if (value >= 0 || e.target.value === "") {
                        handleChange(e);
                      }
                    }}
                    min="0"
                  />
                </Form.Group>
              </div>
            </Col>
          </Row>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSaveClick}>
          {rowToEdit ? "Update Leave" : "Create Leave"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateLeaveModal;
