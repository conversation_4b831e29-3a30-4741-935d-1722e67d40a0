import './login.css';
import { useState } from 'react';
import SendLink from './SendLink';

const ForgetPassword = () => {
    const [isLinkend, setLinkSend] = useState(false);
    return (
    <div className="login-container">
        <div className='left-pannel'><img className="loginImg" src={"/image/landing-page-forgetpassword.png"} /></div>
        <div className='right-pannel'><img className="logoImg" src={"/image//logo_full.png"} />
            <SendLink setLinkSend={setLinkSend}/>
        </div>
    </div>
    )
}
export default ForgetPassword;