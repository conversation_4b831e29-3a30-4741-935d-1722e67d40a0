package com.company.wfm.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
public class SideMenuService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public List<Map<String, Object>> getSideMenuByRole(String role) {
        String query = "SELECT id, menu_label, menu_path, icon_path, is_active, sequence, parent_menu_id " +
                "FROM side_menu WHERE role_name = ? AND is_active = 1 " +
                "ORDER BY sequence ASC";

        List<Map<String, Object>> menuItems = jdbcTemplate.queryForList(query, role);

        if (menuItems.isEmpty()) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> mainMenuItems = new ArrayList<>();
        List<Map<String, Object>> subMenuItems = new ArrayList<>();
        Map<Long, Map<String, Object>> itemMap = new HashMap<>();

        for (Map<String, Object> item : menuItems) {
            Long id = ((Number) item.get("id")).longValue();
            Long parentMenuId = item.get("parent_menu_id") != null ? ((Number) item.get("parent_menu_id")).longValue() : null;

            item.put("subMenuItems", new ArrayList<>());
            itemMap.put(id, item);

            if (parentMenuId == null) {
                mainMenuItems.add(item);
            } else {
                subMenuItems.add(item);
            }
        }

        for (Map<String, Object> subItem : subMenuItems) {
            Long parentMenuId = ((Number) subItem.get("parent_menu_id")).longValue();
            Map<String, Object> parentItem = itemMap.get(parentMenuId);

            if (parentItem != null) {
                List<Map<String, Object>> subMenuList = (List<Map<String, Object>>) parentItem.get("subMenuItems");
                subMenuList.add(subItem);
            }
        }

        return mainMenuItems;
    }

    public String getRoleByEmployeeId(Long empId) {
        String roleQuery = "SELECT role FROM t_user WHERE emp_id = ?";
        try {
            return jdbcTemplate.queryForObject(roleQuery, new Object[]{empId}, String.class);
        } catch (Exception e) {
            return null;
        }
    }
}
