package com.company.wfm.controller;

import java.util.Map;
import java.util.NoSuchElementException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.PaginationRequestDTO;
import com.company.wfm.dto.ResignationStatusDTO;
import com.company.wfm.dto.ResignationStatusResponseDTO;
import com.company.wfm.service.ResignationService;

import software.amazon.awssdk.services.amplify.model.UnauthorizedException;

@RestController
@RequestMapping("/api/v1/Resignation")
@CrossOrigin(origins = "*")
public class ResignationController {
	private static final Logger logger = LoggerFactory.getLogger(ResignationController.class);

	@Autowired
	private ResignationService resignationService;

	@PostMapping("/status/list")
	public ResponseEntity<?> getResignationStatusList(@RequestBody PaginationRequestDTO filter) {
		try {
			String type = filter.getType(); // Get the type from the filter

			// Depending on the value of "type", you can pass additional filters to the
			// service
			Page<ResignationStatusResponseDTO> resignationStatusPage;

			if ("history".equalsIgnoreCase(type)) {
				// Call the service method with custom logic for "history"
				resignationStatusPage = resignationService.getSelfHistory(filter);
			} else {
				// Call the default service method
				resignationStatusPage = resignationService.getAllStatusList(filter);
			}

			return ResponseEntity.ok(resignationStatusPage);
		} catch (Exception e) {
			logger.error("Error while fetching resignation status list: ", e);
			// Return a generic error response for any exception
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("An unexpected error occurred while fetching resignation status. Please try again.");
		}
	}

	// approve and reject api

	@PostMapping("/status/approve/{resignationId}")
	public ResponseEntity<?> approveResignation(@PathVariable Long resignationId, @RequestBody Map<String, String> requestBody) {
		try {
			String remark = requestBody.get("remark");

			resignationService.approveResignation(resignationId, remark);

			return ResponseEntity.ok("Resignation approved successfully");
		} catch (NoSuchElementException e) {
			logger.error("Resignation not found: ", e);
			return ResponseEntity.status(HttpStatus.NOT_FOUND)
					.body("Resignation not found: " + e.getMessage());
		} catch (IllegalStateException e) {
			logger.error("Invalid resignation status: ", e);
			return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					.body("Invalid resignation status: " + e.getMessage());
		} catch (RuntimeException e) {
			logger.error("Runtime error while approving resignation: ", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("Failed to approve resignation: " + e.getMessage());
		} catch (Exception e) {
			logger.error("Unexpected error while approving resignation: ", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("An unexpected error occurred while approving the resignation.");
		}
	}

	@PostMapping("/status/reject/{resignationId}")
	public ResponseEntity<?> rejectResignation(@PathVariable Long resignationId, @RequestBody Map<String, String> requestBody) {
		try {
			String remark = requestBody.get("remark");

			resignationService.rejectResignation(resignationId, remark);
			return ResponseEntity.ok("Resignation rejected successfully");
		} catch (NoSuchElementException e) {
			logger.error("Resignation not found: ", e);
			return ResponseEntity.status(HttpStatus.NOT_FOUND)
					.body("Resignation not found: " + e.getMessage());
		} catch (IllegalStateException e) {
			logger.error("Invalid resignation status: ", e);
			return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					.body("Invalid resignation status: " + e.getMessage());
		} catch (RuntimeException e) {
			logger.error("Runtime error while rejecting resignation: ", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("Failed to reject resignation: " + e.getMessage());
		} catch (Exception e) {
			logger.error("Unexpected error while rejecting resignation: ", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("An unexpected error occurred while rejecting the resignation.");
		}
	}

	@PostMapping("/status/cancel/{resignationId}")
	public ResponseEntity<?> cancelResignation(@PathVariable Long resignationId, @RequestBody ResignationStatusDTO resignationStatusDTO ) {
		try {


			resignationService.cancelResignation(resignationId, resignationStatusDTO, resignationStatusDTO.getRemark());
			return ResponseEntity.ok("Resignation cancelled successfully");
		} catch (AccessDeniedException e) {
			logger.error("Unauthorized cancel request1: ", e);
			return ResponseEntity.status(HttpStatus.FORBIDDEN)
					.body("You are not authorized to cancel this resignation request.");
		} catch (NoSuchElementException e) {
			logger.error("Resignation not found: ", e);
			return ResponseEntity.status(HttpStatus.NOT_FOUND)
					.body("Resignation not found: " + e.getMessage());
		} catch (IllegalStateException e) {
			logger.error("Invalid resignation status: ", e);
			return ResponseEntity.status(HttpStatus.BAD_REQUEST)
					.body("Invalid resignation status: " + e.getMessage());
		} catch (RuntimeException e) {
			logger.error("Runtime error while canceling resignation: ", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("Failed to cancel resignation: " + e.getMessage());
		} catch (Exception e) {
			logger.error("Unexpected error while canceling resignation: ", e);
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("An unexpected error occurred while canceling the resignation.");
		}
	}

}

