.custom-calendar {
    width: 350px;
    margin: 0 auto;
    /* font-family: Arial, sans-serif; */
  }
  
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .nav-button {
    background-color: transparent;
    border: none;
    font-size: 18px;
    cursor: pointer;
  }
  
  h3 {
    margin: 0;
  }
  
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
  }
  
  .day-header {
    text-align: center;
    font-weight: bold;
    padding: 8px;
    background-color: #f5f5f5;
    font-size: 15px;
    border-radius: 50%;

  }
  
  .day-cell {
    text-align: center;
    /* padding: 10px; */
    border: 1px solid #ddd;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 29px;
    font-size: 15px;
    border-radius: 50%;
}
  
  .day-cell:hover {
    background-color: #e0e0e0;
  }
  
  .saturday {
    background-color: #55a1f3;
    color: white;
  }
  
  .sunday {
    background-color: #55a1f3;
    color: white;
  }
  
  .selected {
    background-color: #28a745;
    color: white;
  }
  
  .disabled {
    color: #aaa;
  }
  
  .selected-dates {
    margin-top: 20px;
    text-align: center;
  }
  