package com.company.wfm.repository;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.Department;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {

    boolean existsByDepartmentCode(String departmentCode);
    Optional<Department> findByDepartmentCode(String departmentCode);
    @Query("SELECT d.departmentName FROM Department d")
    List<String> findAllDepartmentNames();

//    @Query("SELECT d FROM Department d LEFT JOIN FETCH d.branches WHERE d.id = :id")
//    Optional<Department> findByIdWithBranches(Long id);
//
//    @Query("SELECT d FROM Department d LEFT JOIN FETCH d.branches")
//    List<Department> findAllWithBranches();

    @Query("SELECT d.departmentId, d.departmentName FROM Department d WHERE d.isActiveDepartment = true")
    List<Object[]> findAllActiveDepartmentIdsAndNames();

    List<Department> findByDepartmentIdIn(List<Long> deptIds);

    @Override
	Page<Department> findAll(Pageable pageable);

    @Query("SELECT d FROM Department d WHERE d.departmentName LIKE %:query% AND d.isActiveDepartment = true")
    Page<Department> searchDepartments(@Param("query") String query, Pageable pageable);

    @Query("SELECT d.category FROM Department d")
    List<String> findAllCategories();

    Department findByDepartmentName(String departmentName);

  //Optional<Department> findByDepartmentName(String departmentName);

    @Query("SELECT d FROM Department d WHERE d.departmentName = :name")
    Optional<Department> findByName(@Param("name") String name); // Return type is Optional

    @Override
	Optional<Department> findById(Long id);



}