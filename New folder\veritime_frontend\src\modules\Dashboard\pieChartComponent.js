import React from 'react';
import { Pie } from 'react-chartjs-2';
import 'chart.js/auto';

const PieChartComponent = ({data}) => {
  const chartData = {
    labels: data?.count.axisLbl,
    datasets: [
      {
        data: data?.count.axis,
        backgroundColor: data?.count.colors,
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
  };

  return (
    <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-3">
      <div className="card shadow-sm">
        <div className="card-body">
          <p className="h6" style={{
    fontSize: '13px'
}}>{data?.name}</p>
          <div style={{ position: 'relative', width: '100%', height: "210px" , marginTop:"10px" }}>
            <Pie data={chartData} options={options} />
          </div>
        </div>
      </div>
    </div>

  );
};

export default PieChartComponent;