package com.company.wfm.repository;


import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.FeederHospitalDTO;
import com.company.wfm.entity.FeederHospital;

@Repository
public interface FeederHospitalRepository extends JpaRepository<FeederHospital, Long> {

    @Query("SELECT f FROM FeederHospital f " +
            "WHERE (:province is null or f.province = :province) " +
            "AND (:district is null or f.district = :district) " +
            "AND (:subDistrict is null or f.subDistrict = :subDistrict) " +
            "AND (:name is null or f.name LIKE %:name%)")
    List<FeederHospital> searchHospitals(
            @Param("province") String province,
            @Param("district") String district,
            @Param("subDistrict") String subDistrict,
            @Param("name") String name
    );


   @Query("SELECT new com.company.wfm.dto.FeederHospitalDTO(f.id, f.name) FROM FeederHospital f WHERE f.id = :hospitalId")
   FeederHospitalDTO findFeederHospitalById(@Param("hospitalId") Long hospitalId);

    @Query("SELECT f.name FROM FeederHospital f WHERE f.id = :companyId")
    String findCompanyNameById(@Param("companyId") Long companyId);

}
