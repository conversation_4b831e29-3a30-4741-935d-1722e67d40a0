package com.company.wfm.dto;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeInfoDTO {
    private String hospitalName;
    private String facility;
    private String empCode;
    private String empName;
    private Date dateOfBirth;
    private String gender;
    private String married;
    private String phoneNo;
    private String mobileNo;
    private String nationalId;
    private String departmentName;
    private String designationName;
    private String reportingPersonCode;
    private String biometricId;
    private String addressLine1;
    private String addressLine2;
    private String province;
    private String district;
    private String subDistrict;
    private String nation;
    private String zipcode;
    private String nativePlace;
    private Date joiningDate;
    private String email;
    private String inService;
    private String remark;
    private String nativeLanguage;
    private String foreignLanguages;
    private int experienceYears;
    private String defaultTimeSlot;
    private String leaveOnDays;

    private String ethnicity;
    private String workZone;
    private String emergencyContactName;  // New field
    private String emergencyContactNumber;  // New field
    private String emergencyContactName2;  // New field
    private String emergencyContactNumber2;  // New field
    private String probationPeriod;

   // private String leaveType;
}
