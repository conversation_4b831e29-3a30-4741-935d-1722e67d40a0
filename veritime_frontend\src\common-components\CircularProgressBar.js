import {
    CircularProgressbar,
    CircularProgressbarWithChildren,
    buildStyles
  } from "react-circular-progressbar";
  import "react-circular-progressbar/dist/styles.css";
  
  const CircularProgressBar = ({percentage,pathColor,trailColor}) =>{
    return(<CircularProgressbar
        value={percentage}
        text={`${percentage}%`}
        strokeWidth={20}
        styles={buildStyles({
          textColor: "black",
          pathColor: pathColor,
          trailColor: trailColor
        })}
        
      />)
}
export default CircularProgressBar;