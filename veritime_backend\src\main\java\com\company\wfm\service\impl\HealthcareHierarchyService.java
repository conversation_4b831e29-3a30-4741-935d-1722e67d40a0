package com.company.wfm.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.dto.DistrictDTO;
import com.company.wfm.dto.HospitalDTO;
import com.company.wfm.dto.HospitalUpdateDTO;
import com.company.wfm.dto.ProvinceDTO;
import com.company.wfm.dto.SubDistrictDTO;
import com.company.wfm.entity.District;
import com.company.wfm.entity.FeederHospital;
import com.company.wfm.entity.Province;
import com.company.wfm.entity.SubDistrict;
import com.company.wfm.repository.DistrictRepository;
import com.company.wfm.repository.FeederHospitalRepository;
import com.company.wfm.repository.ProvinceRepository;
import com.company.wfm.repository.SubDistrictRepository;


@Service
@Configuration
@EntityScan("com.company.wfm.entity")
@EnableJpaRepositories("com.company.wfm.repository")
@EnableTransactionManagement
public class HealthcareHierarchyService {
    private static final Logger logger = LoggerFactory.getLogger(HealthcareHierarchyService.class);

    @Autowired
    private ProvinceRepository provinceRepository;
    @Autowired
    private DistrictRepository districtRepository;
    @Autowired
    private SubDistrictRepository subDistrictRepository;
    @Autowired
    private FeederHospitalRepository feederHospitalRepository;

    @Transactional
    public FeederHospital createHospital(HospitalDTO hospitalDTO) {
        logger.info("Starting creation of new hospital: {}", hospitalDTO.getName());

        Province province = provinceRepository.findByName(hospitalDTO.getProvince())
                .orElseGet(() -> {
                    logger.debug("Creating new province: {}", hospitalDTO.getProvince());
                    Province newProvince = new Province();
                    newProvince.setName(hospitalDTO.getProvince());
                    return provinceRepository.saveAndFlush(newProvince);
                });

        District district = districtRepository.findByNameAndProvince(hospitalDTO.getDistrict(), province)
                .orElseGet(() -> {
                    District newDistrict = new District();
                    newDistrict.setName(hospitalDTO.getDistrict());
                    newDistrict.setProvince(province);
                    return districtRepository.saveAndFlush(newDistrict);
                });

        SubDistrict subDistrict = subDistrictRepository.findByNameAndDistrict(hospitalDTO.getSubDistrict(), district)
                .orElseGet(() -> {
                    SubDistrict newSubDistrict = new SubDistrict();
                    newSubDistrict.setName(hospitalDTO.getSubDistrict());
                    return subDistrictRepository.saveAndFlush(newSubDistrict);
                });

        FeederHospital hospital = new FeederHospital();
        hospital.setName(hospitalDTO.getName());
        hospital.setAddress(hospitalDTO.getAddress());
        hospital.setType(hospitalDTO.getType());
        hospital.setFacility(hospitalDTO.getFacility());
        hospital.setProvince(province.getName());
        hospital.setDistrict(district.getName());

        hospital.setSubDistrict(subDistrict.getName());
        hospital.setSubDistrictEntity(subDistrict);
        hospital.setCreatedAt(LocalDateTime.now());
        hospital.setUpdatedAt(LocalDateTime.now());

        return feederHospitalRepository.saveAndFlush(hospital);
    }
    private HospitalDTO convertToHospitalDTO(FeederHospital hospital) {
        HospitalDTO dto = new HospitalDTO();
        dto.setId(hospital.getId());
        dto.setName(hospital.getName());
        dto.setAddress(hospital.getAddress());
        dto.setType(hospital.getType());
        dto.setFacility(hospital.getFacility());
        dto.setProvince(hospital.getProvince());
        dto.setDistrict(hospital.getDistrict());
        dto.setSubDistrict(hospital.getSubDistrict());
        dto.setCreatedAt(hospital.getCreatedAt());
        dto.setUpdatedAt(hospital.getUpdatedAt());
        // Don't set adminId and password in the DTO for security reasons
        return dto;
    }

    public List<HospitalDTO> getAllHospitals() {
        List<FeederHospital> hospitals = feederHospitalRepository.findAll();
        return hospitals.stream().map(this::convertToHospitalDTO).collect(Collectors.toList());
    }

    public List<HospitalDTO> searchHospitals(String province, String district, String subDistrict, String name) {
        List<FeederHospital> hospitals = feederHospitalRepository.searchHospitals(province, district, subDistrict, name);
        return hospitals.stream().map(this::convertToHospitalDTO).collect(Collectors.toList());
    }


    @Transactional
    public HospitalDTO updateHospital(Long id, HospitalUpdateDTO updateDTO) {
        FeederHospital hospital = feederHospitalRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Hospital not found"));

        hospital.setName(updateDTO.getName());
        hospital.setAddress(updateDTO.getAddress());
        hospital.setType(updateDTO.getType());
        hospital.setFacility(updateDTO.getFacility());
        hospital.setUpdatedAt(LocalDateTime.now());

        // Update hierarchical relationships if changed
        if (updateDTO.getProvince() != null && updateDTO.getDistrict() != null &&
                updateDTO.getSubDistrict() != null) {

            Province province = provinceRepository.findByName(updateDTO.getProvince())
                    .orElseGet(() -> {
                        Province newProvince = new Province();
                        newProvince.setName(updateDTO.getProvince());
                        return provinceRepository.saveAndFlush(newProvince);
                    });

            District district = districtRepository.findByNameAndProvince(updateDTO.getDistrict(), province)
                    .orElseGet(() -> {
                        District newDistrict = new District();
                        newDistrict.setName(updateDTO.getDistrict());
                        newDistrict.setProvince(province);
                        return districtRepository.saveAndFlush(newDistrict);
                    });

            SubDistrict subDistrict = subDistrictRepository.findByNameAndDistrict(updateDTO.getSubDistrict(), district)
                    .orElseGet(() -> {
                        SubDistrict newSubDistrict = new SubDistrict();
                        newSubDistrict.setName(updateDTO.getSubDistrict());
                        newSubDistrict.setDistrict(district);
                        return subDistrictRepository.saveAndFlush(newSubDistrict);
                    });

            // Update both the String fields and the entity relationship
            hospital.setProvince(province.getName());
            hospital.setDistrict(district.getName());

            hospital.setSubDistrict(subDistrict.getName());
            hospital.setSubDistrictEntity(subDistrict);
        }

        FeederHospital updatedHospital = feederHospitalRepository.saveAndFlush(hospital);
        return convertToHospitalDTO(updatedHospital);
    }

    @Transactional
    public void deleteHospital(Long id) {
        if (!feederHospitalRepository.existsById(id)) {
            throw new RuntimeException("Resource Not Found");
        }
        feederHospitalRepository.deleteById(id);
    }

    public List<ProvinceDTO> getProvinces() {
        return provinceRepository.findAllProvincesOnly();
    }

	public List<DistrictDTO> getDistrictsByProvince(Long provinceName) {
		Province province = provinceRepository.findById(provinceName)
				.orElseThrow(() -> new RuntimeException("Province not found"));
		return province.getDistricts().stream().map(data -> {
			DistrictDTO std = new DistrictDTO();
			std.setId(data.getId());
			std.setName(data.getName());
			return std;
		}).collect(Collectors.toList());
	}

	public List<SubDistrictDTO> getSubDistrictsByDistrict(Long provinceId, Long districtId) {
		Province province = provinceRepository.findById(provinceId)
				.orElseThrow(() -> new RuntimeException("Province not found"));
		District district = districtRepository.findByIdAndProvince(districtId, province)
				.orElseThrow(() -> new RuntimeException("District not found"));
		return district.getSubDistricts().stream().map(data -> {
			SubDistrictDTO std = new SubDistrictDTO();
			std.setId(data.getId());
			std.setName(data.getName());
			return std;
		}).collect(Collectors.toList());
	}

	/*
	 * @Bean public EntityManagerFactory entityManagerFactory() {
	 * LocalContainerEntityManagerFactoryBean factory = new
	 * LocalContainerEntityManagerFactoryBean(); factory.setJpaVendorAdapter(new
	 * HibernateJpaVendorAdapter());
	 * factory.setPackagesToScan("com.company.wfm.service.entity");
	 * factory.setDataSource(dataSource()); factory.afterPropertiesSet();
	 *
	 * return factory.getObject(); }
	 *
	 * @Bean public DataSource dataSource() { DriverManagerDataSource dataSource =
	 * new DriverManagerDataSource();
	 * dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver")
	 * ; dataSource.setUrl(
	 * "***************************************************************************************************"
	 * ); dataSource.setUsername("admin"); dataSource.setPassword("8976436304");
	 *
	 * return dataSource; }
	 *
	 * @Bean public TransactionManager transactionManager() {
	 * DataSourceTransactionManager transactionManager = new
	 * DataSourceTransactionManager();
	 * transactionManager.setDataSource(dataSource());
	 *
	 * return transactionManager; }
	 */
	}


