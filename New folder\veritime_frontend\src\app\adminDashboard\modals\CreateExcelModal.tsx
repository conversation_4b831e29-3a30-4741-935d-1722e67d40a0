import React, { useState, useCallback } from "react";
import { <PERSON><PERSON>, Button, Form, Col, Alert } from "react-bootstrap";
import { postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showErrorAlert, showSuccessAlert2 } from "@/services/alertService";
import { FaDownload } from "react-icons/fa";
import "./CreateExcelModals.css";
import axios from "axios";

const CreateExcelModal = ({
  show,
  handleClose,
  fetchHolidays,
  downloadType,
}: any) => {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState("");

  const handleFileUpload = (event: any) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.type == "csv") {
      setError("Please upload a valid CSV file!");
      setFile(null);
    } else {
      setError("");
      setFile(selectedFile);
    }
  };

  const uploadExcelFile = async () => {
    console.log("uploadExcelFile function triggered!"); // Debugging
    if (!file) {
      setError("Please select a file first!");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    setIsUploading(true);
    try {
      console.log("Sending file to API..."); // Debugging
      const response = await postRequest(API_URLS.UPLOAD_EXCEL, formData);
      console.log("API Response:", response); // Debugging
      if (response) {
        showSuccessAlert2("File uploaded successfully");

        //  Fetch the latest holidays
        // const updatedHolidays = await fetchHolidays();
        // setHolidays(updatedHolidays); //  Immediately update the list
        handleClose();
        setFile(null);
        fetchHolidays();
      } else {
        // If the response has a message, show it; otherwise, show a generic message
        const errorMessage = response?.message || "File upload failed!";
        showErrorAlert(errorMessage);
      }
    } catch (error) {
      // Safely handle Axios errors
      if (axios.isAxiosError(error)) {
        console.error("Upload error details:", error.response?.data);

        // Check if the API returned a detailed error message
        const errorMsg =
          error.response?.data || "An error occurred while uploading.";

        // Display the exact error message returned from the API
        showErrorAlert(errorMsg);
      } else {
        // Handle unexpected (non-Axios) errors
        showErrorAlert("An unexpected error occurred.");
      }
    } finally {
      setIsUploading(false);
    }
  };
  const handleDownload = useCallback(() => {
    const fileMap: { [key: string]: string } = {
      holiday: "/Holiday_Format.xlsx",
      hospital: "/Hospital_Format.xlsx",
      Employee: "/Employee-upload-format.xlsx",
    };
    const fileUrl = fileMap[downloadType];
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = `${
      downloadType.charAt(0).toUpperCase() + downloadType.slice(1)
    }_Format.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [downloadType]);

  return (
    <Modal show={show} onHide={handleClose} className="create-excel-modal">
      <Modal.Header closeButton>
        <Modal.Title> Holiday Upload </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}
        <Col md={3}>
          <Form.Group controlId="uploadExcel" className="mb-3">
            <p className="upload-label">Upload file</p>
            <Form.Control
              type="file"
              onChange={handleFileUpload}
              className="upload-input"
            />
            <div style={{ marginTop: "15px" }}>
              <Button
                variant="primary"
                onClick={uploadExcelFile}
                disabled={isUploading}
                className="upload-button"
              >
                {isUploading ? "Uploading..." : "Upload excel"}
              </Button>
            </div>
          </Form.Group>
        </Col>

        <div className="note-container">
          <h1 className="note-title">Note: Download the sample file.</h1>

          <div onClick={handleDownload} className="download-container">
            <FaDownload size={30} className="download-icon" />
            <span className="download-text">
              Download{" "}
              {downloadType.charAt(0).toUpperCase() + downloadType.slice(1)}{" "}
              Format
            </span>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default CreateExcelModal;
