import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

// Define the type for the designation details
interface DesignationDetails {
  departmentName?: string;
  name?: string;
  level?: string;
  role?: string;
  code?: string;
  createdByName?: string; // Added field
  updatedByName?: string; // Added field
  createdAt?: string; // Added field
  updatedAt?: string; // Added field
  noticePeriod?: string;
}

// Define the props for the DesignationDetailModal
interface DesignationDetailModalProps {
  show: boolean;
  onClose: () => void;
  designationDetails: DesignationDetails;
}
// Function to format date from an array [year, month, day]
const formatDateFromArray = (dateArray?: number[]): string => {
  if (!dateArray || dateArray.length < 3) return "N/A";
  const [year, month, day] = dateArray;
  return `${year}-${String(month).padStart(2, "0")}-${String(day).padStart(
    2,
    "0"
  )}`;
};

function DesignationDetailModal({
  show,
  onClose,
  designationDetails,
}: DesignationDetailModalProps) {
  return (
    <Modal show={show} onHide={onClose}>
      <Modal.Header closeButton>
        <Modal.Title>Designation Details</Modal.Title>
      </Modal.Header>

      <Modal.Body style={{ overflowY: "auto", maxHeight: "400px" }}>
        <div style={{ padding: "10px" }}>
          {/* Department Name */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Department Name
            </div>
            <div>{designationDetails?.departmentName || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Designation Name */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Designation
            </div>
            <div>{designationDetails?.name || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Level */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Level
            </div>
            <div>{designationDetails?.level || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Role */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>Role</div>
            <div>{designationDetails?.role || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Notify Period
            </div>
            <div>{designationDetails?.noticePeriod || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Code */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>Code</div>
            <div>{designationDetails?.code || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Created By and Updated By structured similarly */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Created By
              </div>
              <div>{designationDetails?.createdByName || "N/A"}</div>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Updated By
              </div>
              <div>{designationDetails?.updatedByName || "N/A"}</div>
            </div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Created At and Updated At structured similarly */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Created At
              </div>
              <div>
                {formatDateFromArray(
                  designationDetails?.createdAt as unknown as number[]
                )}
              </div>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Updated At
              </div>
              <div>
                {formatDateFromArray(
                  designationDetails?.updatedAt as unknown as number[]
                )}
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="primary" onClick={onClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default DesignationDetailModal;
