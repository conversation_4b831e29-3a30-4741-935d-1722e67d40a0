package com.company.wfm.util;

public interface CommonConstant {

	public static final String SCHEDULER= "Biometric Device";
	
	public static final String ATTENDANCE= "attendance";
	
	
	
	
	interface ScheduleQuery {
		static String SCHEDULE_EXTRACT_QUERY = "select hosp.SCHEDULE_ID, branch.branch_id ,branch.branch_name ,dept.DEPARTMENT_ID ,dept.DEPARTMENT_NAME"
				+ "  from  t_schedule hosp inner join  t_branch branch on hosp.BRANCH_ID = branch.branch_id "
				+ " inner join t_department dept on hosp.DEPARTMENT_ID = dept.department_id where 1=1 ";
	}

	interface BranchQuery {
		static String BRANCH_EXTRACT_COUNT_QUERY = "SELECT COUNT(1) AS row_count FROM "
				+ "t_branch branch inner join feeder_hospitals hosp on branch.company_id = hosp.id where 1=1";

		/*static String BRANCH_EXTRACT_QUERY = "SELECT branch.branch_id ,branch.branch_name ,hosp.id ,hosp.hospital_name ,branch.is_active , branch.clustor_no, branch.BR<PERSON>CH_HEAD_ID, branch.BRANCH_HEAD_NAME, branch.branch_code,branch.updated_at ,branch.created_at , branch.branch_type FROM "
				+ "t_branch branch inner join feeder_hospitals hosp on branch.company_id = hosp.id where 1=1";*/
		static String BRANCH_EXTRACT_QUERY = "SELECT branch.branch_id, branch.branch_name, hosp.id, hosp.hospital_name, "
				+ "branch.is_active, branch.clustor_no, branch.BRANCH_HEAD_ID, branch.BRANCH_HEAD_NAME, branch.branch_code, "
				+ "branch.updated_at, branch.created_at, branch.leave_credit_day,branch.timezone,branch.branch_type, "
				+ "emp1.EMP_NAME AS created_by, "
				+ "emp2.EMP_NAME AS updated_by "
				+ "FROM t_branch branch "
				+ "INNER JOIN feeder_hospitals hosp ON branch.company_id = hosp.id "
				+ "LEFT JOIN t_employee emp1 ON branch.created_by = emp1.emp_id "
				+ "LEFT JOIN t_employee emp2 ON branch.updated_by = emp2.emp_id "
				+ "WHERE 1=1";


		static String DEPARTMENT_EXTRACT_QUERY = "SELECT branch.branch_id , dept.DEPARTMENT_ID ,dept.DEPARTMENT_NAME, dept.IS_ACTIVE_DEPARTMENT FROM "
				+ "t_branch branch inner join department_branch  deptBranch on branch.branch_id  = deptBranch.branch_id  "
				+ "inner JOIN  t_department dept  on deptBranch.department_id  = dept.DEPARTMENT_ID where branch.branch_id in (:branchList)";
	}

	interface DepartmentQuery {
		static String DEPARTMENT_EXTRACT_COUNT_QUERY = "SELECT  COUNT(distinct  dept.DEPARTMENT_ID) AS row_count FROM "
				+ "t_department dept left join department_branch  deptBranch on dept.department_id  = dept.department_id  "
				+ "inner join t_branch branch  on branch.branch_id  = deptBranch.branch_id where 1=1";

		/*static String DEPARTMENT_EXTRACT_QUERY = "SELECT  distinct  dept.DEPARTMENT_ID ,dept.DEPARTMENT_NAME,dept.DEPARTMENT_CODE, dept.CATEGORY, dept.IS_ACTIVE_DEPARTMENT FROM "
				+ "t_department dept left join department_branch  deptBranch on dept.department_id  = dept.department_id  "
				+ "inner join t_branch branch  on branch.branch_id  = deptBranch.branch_id where 1=1";
*/

		static String DEPARTMENT_EXTRACT_QUERY = "SELECT  distinct  dept.DEPARTMENT_ID ,dept.DEPARTMENT_NAME,dept.DEPARTMENT_CODE, dept.CATEGORY, dept.IS_ACTIVE_DEPARTMENT, dept.CREATED_BY, c_emp.EMP_NAME AS CREATED_BY_NAME, dept.CREATED_AT, dept.UPDATED_BY, u_emp.EMP_NAME AS UPDATED_BY_NAME, dept.UPDATED_AT  FROM "
				+ "t_department dept" +
				" left join department_branch  deptBranch on dept.department_id  = dept.department_id  "
				+ "inner join t_branch branch  on branch.branch_id  = deptBranch.branch_id " +
				"LEFT JOIN t_employee c_emp ON dept.CREATED_BY = c_emp.EMP_ID " +
				"LEFT JOIN t_employee u_emp ON dept.UPDATED_BY = u_emp.EMP_ID " +
				"where 1=1";

		static String DEPARTMENT_BRANCH_EXTRACT_QUERY = "SELECT deptBranch.department_id, branch.branch_id , branch.branch_name , branch.is_active FROM "
				+ "t_branch branch  left join department_branch  deptBranch on deptBranch.branch_id  = branch.branch_id where deptBranch.department_id in (:departmentList)";
	}

	interface EmployeeQuery {
		static String Employee_EXTRACT_COUNT_QUERY = "SELECT COUNT(emp.EMP_ID)" +
				"  FROM t_employee emp " +
				"  left join t_branch br on br.branch_id=emp.BRANCH_ID" +
				"  left join t_department dt on dt.DEPARTMENT_ID = emp.DEPARTMENT_ID" +
				"  left join t_employee e2 on e2.EMP_ID=emp.upper_id" +
				"  left join t_time_slot ts on ts.time_slot_id=emp.default_time_slot_id" +
				" left join t_designation desg on desg.designation_id=emp.designation_id" +
				" where 1=1";


		static String EMPLOYEE_EXTRACT_QUERY = "SELECT emp.EMP_ID, emp.COMPANY_ID, emp.DEPARTMENT_ID, emp.EMP_CODE, emp.EMP_NAME, " +
				"emp.UID, emp.ID_NO, emp.ORG_ID, emp.UPPER_ID, emp.REGION_ID, emp.COUNTRY_ID, emp.PROVINCE_ID, " +
				"emp.CITY," +
				"emp.HIRE_DATE, emp.GENDER, emp.BIRTHDAY, emp.NATION, " +
				"emp.MARRIED, emp.PHONE_NO, emp.MOBILE_NO, emp.EMAIL, " +
				"emp.NATIVE_PLACE, emp.ZIP_CODE, emp.IS_HISTORY, emp.IN_SERVICE, emp.REMARK, " +
				"emp.CREATED_BY, emp.CREATED_TIME, emp.UPDATED_BY, emp.UPDATED_TIME, emp.VERSION, emp.NATIVE_LANGUAGE, " +
				"emp.FOREIGN_LANGUAGES, emp.WORK_YEARS, emp.GRADUATE_SCHOOL, emp.GRADUATE_TIME, " +
				"emp.HIGHEST_DEGREE, emp.IMG_URE, emp.BRANCH_ID, emp.time_slot_id, " +
				"emp.working_day_count_in_week, emp.DESIGNATION_ID, emp.DEFAULT_TIME_SLOT_ID, " +
				"emp.LEAVE_ON_DAYS, emp.UNIT_NO, emp.STREET, emp.alternateNumber, emp.emergencyContact1, " +
				"emp.emergencyContact2, emp.emergencyContactname1, emp.emergencyContactname2, emp.alternateEmail, emp.probation_period, " +
				"br.branch_name, dt.DEPARTMENT_NAME, e2.emp_name as UPPER_NAME," +
				"(CONVERT(VARCHAR(5), ts.start_time, 108) + ' - ' + CONVERT(VARCHAR(5), ts.end_time, 108)) AS default_shift, desg.designation_name," +
				"(select sum(assigned_leave) from t_employee_leave_balance where emp_id=emp.EMP_ID) as assigned_leave, (select sum(balance_leave) from t_employee_leave_balance where emp_id=emp.EMP_ID) as balance_leave," +
				"(SELECT ISNULL(SUM(CASE " +
				"WHEN a.CHECK_OUT_TIME IS NOT NULL AND ts2.end_time IS NOT NULL " +
				"AND CAST(a.CHECK_OUT_TIME AS TIME) > CAST(ts2.end_time AS TIME) " +
				"THEN DATEDIFF(MINUTE, ts2.end_time, a.CHECK_OUT_TIME) / 60.0 " +
				"ELSE 0.0 END), 0) " +
				"FROM t_attendance a " +
				"LEFT JOIN t_employee_schedule es ON a.EMP_ID = es.EMP_ID AND a.DATE = es.date " +
				"LEFT JOIN t_time_slot ts2 ON ts2.time_slot_id = COALESCE(es.modified_shift, es.actual_shift) " +
				"WHERE a.EMP_ID = emp.EMP_ID) as total_overtime_hours" +

				"  FROM t_employee emp" +
				"  left join t_branch br on br.branch_id=emp.BRANCH_ID" +
				"  left join t_department dt on dt.DEPARTMENT_ID = emp.DEPARTMENT_ID" +
				"  left join t_employee e2 on e2.EMP_ID=emp.upper_id" +
				"  left join t_time_slot ts on ts.time_slot_id=emp.default_time_slot_id" +
				" left join t_designation desg on desg.designation_id=emp.designation_id" +
				//" LEFT JOIN t_employee_leave_balance elb ON elb.emp_id = emp.emp_id"+
				" where 1=1";
	}

	interface EmailConstant{

		static String TICKET_ASSIGNMENT_EMAIL = "<html><body><img src='cid:identifier1234'></body></html>";

		static String TICKET_CREATION_EMAIL = "";
	}

	interface AttendanceConstant{
		static String ATTENDANCE_SYNC_QUERY= "SELECT id, datetime, date, time, status, device, deviceno, empname, cardno, consume_status "
				+ "FROM atteninfo where date = :attendance_date and consume_status = 0 order by datetime asc ";
		static String ATTENDANCE_SYNC_COUNT_QUERY= "SELECT COUNT(*) FROM atteninfo where date = :attendance_date and consume_status = 0";
		static String ATTENDANCE_SYNC_UPDATE_QUERY= "update atteninfo set consume_status = 1 where date=:? and id in (:id)";
		static String ATTENDANCE_SYNC_INSERT_QUERY = "INSERT INTO t_attendance_audit ( EMP_ID, PUNCH_IN_TIME, DEVICE_TYPE, CREATED_BY, CREATED_DATE, DEVICE_DUMP, DATE) VALUES(?,?,?,?,?,?,?)";

		static String EMPLOYEE_DETAIL_QUERY= "SELECT uid, emp_id "
				+ "FROM t_employee where UID in (:UID) ";
	}

	interface TimeSlotContant {
		static String TIME_SLOT_QUERY = "SELECT ts.time_slot_id slotId FROM  t_schedule schedule join t_branch br  on schedule.branch_id = br.branch_id  "
				+ " join t_department dt on schedule.department_id = dt.department_id  "
				+ " join t_time_slot ts on ts.schedule_id= schedule.schedule_id   WHERE  "
				+ " schedule.branch_id = :branchId and schedule.department_id =:departmentId  "
				+ " and ts.start_time = CAST( :startTime AS time )  and ts.end_time = CAST( :endTime AS time ) ";

		static String DEFAULT_TIME_SLOT_QUERY = "SELECT ts.time_slot_id slotId FROM  t_schedule schedule join t_branch br  on schedule.branch_id = br.branch_id  "
				+ " join t_department dt on schedule.department_id = dt.department_id  "
				+ " join t_time_slot ts on ts.schedule_id= schedule.schedule_id   WHERE  "
				+ " schedule.branch_id = :branchId and schedule.department_id =:departmentId and is_default = 1";
	}

	interface HikVisionQueryConstant{
		static String ELIGIBLE_EMP_FOR_HIKVISION_QUERY = "SELECT te.EMP_CODE, te.EMP_NAME, te.MOBILE_NO, te.EMAIL, te.GENDER, te.BRANCH_ID, te.UID "
				+ "FROM t_employee te  "
				+ "WHERE NOT EXISTS ("
				+ "    SELECT EMP_CODE "
				+ "    FROM t_employee_hikvision_data t2"
				+ "    WHERE te.EMP_CODE = t2.EMP_CODE"
				+ ") AND te.EMP_NAME IS NOT NULL;";

		static String ELIGIBLE_EMP_FOR_PROFILE_HIKVISION_QUERY = "SELECT te.EMP_CODE, te.BRANCH_ID, te.IMG_URE, t2.person_id ,t2.id "
				+ "FROM t_employee te inner join   t_employee_hikvision_data t2 on te.EMP_CODE = t2.EMP_CODE AND image_mapped = 0 WHERE te.IMG_URE<>''";

		static String MERGE_QUERY_ATTENDANCE_AUDIT = """
			    MERGE INTO t_attendance_audit AS target
			    USING (SELECT ? AS EMP_ID, ? AS PUNCH_IN_TIME, ? AS CREATED_DATE, ? AS DEVICE_DUMP, ? AS DATE) AS source
			    ON target.EMP_ID = source.EMP_ID 
			       AND CAST(target.PUNCH_IN_TIME AS DATETIME) = source.PUNCH_IN_TIME
			       AND target.DATE = source.DATE
			    WHEN NOT MATCHED THEN 
			        INSERT (EMP_ID, PUNCH_IN_TIME, DEVICE_TYPE, CREATED_BY, CREATED_DATE, DEVICE_DUMP, DATE)
			        VALUES (source.EMP_ID, source.PUNCH_IN_TIME, 'Scanner', NULL, source.CREATED_DATE, source.DEVICE_DUMP, source.DATE);
			""";
	}

	interface HikVisionURIConstant {
		static String HIKVISION_TOKEN_API = "/api/hccgw/platform/v1/token/get";
		static String HIKVISION_PROFILE_ADD_API = "/api/hccgw/person/v1/persons/add";
		static String HIKVISION_PROFILE_PIC_API = "/api/hccgw/person/v1/persons/photo";
		static String HIKVISION_EMPLOYEE_ATTENDANCE_API = "/api/hccgw/attendance/v1/report/totaltimecard/list";
		static String HIKVISION_EMPLOYEE_CERTIFICATERECORDS_SEARCH_API = "/api/hccgw/acs/v1/event/certificaterecords/search";



	}
}
