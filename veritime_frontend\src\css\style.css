/* ApprovalsDashboard.css */
.filter-button{
  background: transparent!important;
    color: black!important;
    border: none!important;
    display: flex;
    border: none!important;
    box-shadow: none!important;
}
.dashboard {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f3f4f6;
  }
  .dashboard-content {
    display: flex;
    flex: 1;
  }
  .CircularProgressbar{
    width:40%!important
  }
  .main-content {
    flex: 1;
    padding: 2rem;
  }
  .tab-pannel-test{
    margin-top: 70px;
    background: white;
  }
  .main-container{
    background-color: '#F3F2F7';
  }
  /* Header styles */
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1rem;
    background-color: #F3F2F7;
    
  }
  .search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    width: 700px;
    margin-right: 238px;
  }
  
  .user-info {
    display: flex;
    align-items: center;
  }
  
  .notification-icon {
    margin-right: 1rem;
  }
  
  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-left: 1rem;
  }
  
  /* Sidebar styles */
  .sidebar {
    width: 64px;
    background-color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 2rem;
  }
  .sliderImg{
    position: fixed;
    margin-top: 118px
  }
  .sidebar-icon {
    margin-bottom: 2rem;
    color: #718096;
    cursor: pointer;
  }
  .leftSlider{
    margin-left: 160px;
  }
  
  /* Filters styles */
  .filters {
    display: flex;
    margin-left: 3rem;

  }
  
  .filter-select {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    background-color: #EEE9DA;
    width: 186px;
    color:#1F5F9A
  }
  
  /* This targets the react-select dropdown specifically */
.designation-select {
  width: 180px !important; /* Set the width for the designation dropdown */

}

  .go-button {
    padding: 0.5rem 1rem;
    background-color: #D6E5F3;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    /* height:30px; */
    color:#1F5F9A;
    width: 60px;

  }
  
  .MuiButtonBase-root.MuiListItemButton-root.MuiListItemButton-gutters.MuiListItemButton-root .MuiTypography-root{
    color:#1F5F9A;

  }
  
.active .MuiButtonBase-root.MuiListItemButton-root.MuiListItemButton-gutters{
  background-color: #6D6E71;
}
.active .MuiButtonBase-root.MuiListItemButton-root.MuiListItemButton-gutters.MuiListItemButton-root .MuiTypography-root{
  color: white;


}
.active .MuiSvgIcon-root.MuiSvgIcon-fontSizeMedium{
  color: white;

}
.MuiButtonBase-root.MuiListItemButton-root.MuiListItemButton-gutters{
  margin: 10px;

}
  /* Action buttons styles */
  .action-buttons {
    margin: 1rem;
  }
  
  .action-button {
    padding: 0.5rem 1rem;
    background-color: #D6E5F3;
    color: #1F5F9A;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 20px;
    width: 150px;
    height: 40px;
top: 297px;
left: 271px;
gap: 0px;
border-radius: 10px 10px 10px 10px;
opacity: 0px;

  }
  .carousel-button{
    border-radius: 0px!important;
    padding: 6px!important;;
    line-height: 1.3!important;;
  }
  
  /* Stat cards styles */
  .stat-cards {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .stat-card {
    flex: 1;
    background-color: white;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 5px 8px 3px rgba(0, 0, 0, 0.1);
  }
  
  .stat-info h2 {
    font-size: 1rem;
    color: #4a5568;
    margin-bottom: 0.5rem;
  }
  
  .stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2d3748;
  }
  
  .stat-percentage {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
  }
  
  .stat-card.green .stat-percentage { background-color: #c6f6d5; color: #38a169; }
  .stat-card.yellow .stat-percentage { background-color: #fefcbf; color: #d69e2e; }
  .stat-card.red .stat-percentage { background-color: #fed7d7; color: #e53e3e; }
  .stat-card.blue .stat-percentage { background-color: #D2E1EB; color: #007DE0; }
  .stat-card.purple .stat-percentage { background-color: #EBD2E5; color: #E000A1;
; }

  
  /* Approval table styles */
  .approval-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  }
  
  .approval-table th,
  .approval-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .approval-table th {
    background-color: #f7fafc;
    font-weight: 600;
    color: #4a5568;
  }
  
  .approval-table tr:last-child td {
    border-bottom: none;
  }
  
  .employee-id {
    color: #4299e1;
  }
  
  .approve-button,
  .deny-button {
    background-color: #fed7d7;
    color: #e53e3e;
    width: 81px;
    margin-top: 12px;
    line-height: 20px;
    height: 28px;
    border: none;
    border-radius: 10px;
  }
  .MuiAccordionDetails-root{
display: flex;
flex-direction: column;
align-items: center;
  }
  .approve-button {
    background-color: #c6f6d5;
    color: #38a169;
  }
  
  .deny-button {
    background-color: #fed7d7;
    color: #e53e3e;
    width: 81px;
    height: 28px;
  }
  /* .custom-container{
   display:flex;
    background:white;
    width:100%
  } */
  .divider-style{
    width:7px;
     margin:20px;
     background:#D9D9D9
  }
.carousal-container, .filter-row,.mobile-accordian{
  display: none;
}
.tab-container{
 width: 20%;
}
#tableWrapper {
  overflow-x: auto;
  min-width: 100%;
  table-layout: fixed;
}
.p-paginator button{
  width: 25px !important;
    height: 25px;
    margin: 10px;
    border-radius: 6px;
}
.p-paginator-page.p-paginator-element.p-link.p-highlight{
  background-color: #6D6E71 !important;
}

  /* Responsive adjustments */
  @media (max-width: 767px) {

    .deny-button {
      width: 62px;
      height: 24px;
      font-size: 0.7rem;
  }
    .tab-container,  .dropdown-row ,.stat-card-web{
      display: none!important;
    }


    .search-input {
      margin-right: 0px;
      width:105px
    }
    .filter-row,.carousal-container, .mobile-accordian{
      display: block;
    }
    .main-container{
      margin-left: 0px;
    }
    .dashboard-content {
      flex-direction: column;
    }
    .custom-container{
      display:inline-block;

    }
    .stat-card{
      width: 100px;
    }
    .divider-style,.MuiTabs-root.tab-pannel-test{
      width: 100%;
    }
    .sliderImg{

      width: 100px;
    }
    .sidebar {
      width: 100%;
      height: auto;
      flex-direction: row;
      justify-content: center;
      padding: 1rem 0;
    }
  
    .sidebar-icon {
      margin: 0 1rem;
    }

    .stat-cards {
      flex-direction: column;
      align-items: center;
    }
  
    
   
  
    .approval-table {
      font-size: 0.6rem;
    }
  }
  .MuiTabs-flexContainer{
    display: flex;
    flex-direction: column;
  }
  .MuiTabs-indicator{
    display: none;
  }
  .MuiButtonBase-root.MuiTab-root, .carousa-button{
    text-transform:inherit;
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 20px auto;
    width: 150px;
    height: 40px;
border-radius: 10px 10px 10px 10px;
box-shadow: 0px 4px 4px 0px #00000080;
border: 1px solid #C8C8C8;
/* font-family: Inter; */
font-weight: 600;
line-height: 38.73px;
text-align: left;
color: #747171;
  }
  .modal-button{
    background-color: #D6E5F3;
    color: #1F5F9A;
  }
  .branch-button{
    background: #FBFAE9;
    color:#000000;
    /* font-family: Fira Sans; */
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    height: 20px;
    border-radius: 4px;
    border: none;
    margin: 0 5px;
  }

  .dept-btn{
    background: #BDCBE0

  }

  
   /* Size for canvas on large screens - Tablets (768px to 1024px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .stat-cards {
      width: 90px !important;
      height: 110px !important;
  }

  .stat-card{
    width: 185px !important;
    height: 105px !important;

  }
}

/* For screens between 1025px and 1100px */
@media (min-width: 1024px) and (max-width: 1100px) {
  .stat-cards {
      width: 150px ;
      height: 125px ;
  }
}

/* For screens between 1100px and 1200px */
@media (min-width: 1100px) and (max-width: 1200px) {
  .stat-cards {
      width: 180px ;
      height: 145px ;
  }
}

/* search bar css of employee list */

.cusm-form-control-searchterm {
  border: 1px solid #d9d9d9; 
  text-align: center;
  border-radius: 5px;
  margin-bottom: 10px;
  margin-left: -36px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease; 
}

.cusm-form-control-searchterm:focus {
  border-color: #007bff; 
  box-shadow: 0 0 5px rgba(38, 143, 255, 0.5); 
  outline: none; 
}


@media (min-width: 1024px) and (max-width: 1280px) {
  .cusm-form-control-searchterm {
    font-size: 10px;
    height: 40px;
    width: 80px;
    margin-bottom: 20px; 
  
  }

  
  .go-button-employee{
    
  font-size: 8px;
  height: 50px;
  width: 20px;
  margin-left: -30px;
  margin-bottom: 20px;
  }

}

@media (min-width: 1281px) and (max-width: 1480px) {
  .cusm-form-control-searchterm {
    font-size: 14px;
    height: 40px;
    width: 110px;
    margin-bottom: 20px; 
    
  }

  .go-button-employee{
    
    font-size: 10px;
    height: 50px;
    width: 20px;
    margin-left: -30px;
    margin-bottom: 20px;
    }

    .icon-black {
      filter: brightness(0) saturate(100%);
    }
    

}