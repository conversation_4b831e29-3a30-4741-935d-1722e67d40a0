package com.company.wfm.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DesignationResponseDTO {
    private Long id;
    private String name;
    private String code;
    private Long level;
    private String role;
    private BigDecimal noticePeriod;
    private boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long createdBy;
    private Long updatedBy;
    private Long departmentId;
    private String departmentName;
}
