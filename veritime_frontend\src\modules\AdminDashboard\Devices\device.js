import React from 'react';
import './device.css'; 

const DeviceStatusCard = (data) => {
  return (
      <div className="card shadow-sm custom-card-device">
        <div className="card-body">
          <div className="row mb-3">
            <div className="col-6 d-flex align-items-center">
              <span className="device-label">Active Devices</span>
            </div>
            <div className="col-6 d-flex align-items-center justify-content-end">
              <span className="device-value">{data?.activeDevicesCount?.total}</span>
            </div>
            <div className="col-12">
              <div className="progress">
                <div
                  className="progress-bar bg-success"
                  role="progressbar"
                  style={{ width: `${data?.activeDevicesCount?.progress}%`, backgroundColor: '#90EE90' }}
                ></div>
              </div>
            </div>
          </div>
          <div className="row">
            <div className="col-6 d-flex align-items-center">
              <span className="device-label">Total Devices</span>
            </div>
            <div className="col-6 d-flex align-items-center justify-content-end">
              <span className="device-value">{data?.totalDevicesCount?.total}</span>
            </div>
            <div className="col-12">
              <div className="progress">
                <div
                  className="progress-bar bg-info"
                  role="progressbar"
                  style={{ width: `${data?.totalDevicesCount?.progress}%`, backgroundColor: '#4CAF50' }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

const Devices = ({data}) => {
  return (
    <div>
      <DeviceStatusCard totalDevicesCount={data?.totalDevicesCount} activeDevicesCount={data?.activeDevicesCount}/>
    </div>
  );
};

export default Devices;