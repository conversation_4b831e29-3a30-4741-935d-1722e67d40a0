package com.company.wfm.dto;



import java.time.LocalDate;
import java.util.List;

import com.company.wfm.entity.ApprovalStatus;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceRegularizationResponse {
    private Long id;
    private String employeeId;
    private String employeeName;
    private String employeeImage;  // Added field for employee image
    private String employeeDesignation;  // Added field for employee designation
   // private LocalDate date;
    private String timeRange;
    private String reason;
    private ApprovalStatus approvalStatus;
    private String actionReason;
    private List<LocalDate> multiDates;
}
