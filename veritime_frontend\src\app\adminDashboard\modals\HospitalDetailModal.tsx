import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

// Define the type for hospital details
interface HospitalDetails {
  name?: string;
  type?: string;
  address?: string;
  facility?: string;
  province?: string;
  district?: string;
  subDistrict?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Define the props for the HospitalDetailModal
interface HospitalDetailModalProps {
  show: boolean;
  onHide: () => void;
  hospital: HospitalDetails;
}

// Function to format date from an array [year, month, day]
const formatDateFromArray = (dateArray?: number[]): string => {
  if (!dateArray || dateArray.length < 3) return "N/A";
  const [year, month, day] = dateArray;
  return `${year}-${String(month).padStart(2, "0")}-${String(day).padStart(
    2,
    "0"
  )}`;
};
const HospitalDetailModal: React.FC<HospitalDetailModalProps> = ({
  show,
  onHide,
  hospital,
}) => {
  console.log("Hospital Data: ", hospital);
  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>Hospital Details</Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ overflowY: "auto", maxHeight: "400px" }}>
        <div style={{ padding: "10px" }}>
          {/* Hospital Name */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Hospital Name
            </div>
            <div>{hospital?.name || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Hospital Type */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>Type</div>
            <div>{hospital?.type || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Address */}
          {/* <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Address
            </div>
            <div>{hospital?.address || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} /> */}

          {/* Facility and Province structured similarly */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Province
              </div>
              <div>{hospital?.province || "N/A"}</div>
            </div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* District and Sub District */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                District
              </div>
              <div>{hospital?.district || "N/A"}</div>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Sub District
              </div>
              <div>{hospital?.subDistrict || "N/A"}</div>
            </div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Created At and Updated At */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Created At
              </div>
              <div>
                {" "}
                {formatDateFromArray(
                  hospital?.createdAt as unknown as number[]
                )}
              </div>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Updated At
              </div>
              <div>
                {formatDateFromArray(
                  hospital?.createdAt as unknown as number[]
                )}
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={onHide}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default HospitalDetailModal;
