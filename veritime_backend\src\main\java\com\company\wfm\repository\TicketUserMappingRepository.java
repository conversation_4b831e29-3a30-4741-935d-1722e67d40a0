package com.company.wfm.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.TicketUserMapping;

import jakarta.transaction.Transactional;

@Repository
public interface TicketUserMappingRepository extends JpaRepository<TicketUserMapping, Long> {

  //  boolean existsByUserIdAndDepartmentBranchId(Long userId, Long departmentBranchId);

   /* @Query("SELECT COUNT(t) > 0 FROM TicketUserMapping t WHERE t.user.id = :userId AND t.departmentBranch.id = :departmentBranchId")
    boolean isUserSupportPerson(@Param("userId") Long userId, @Param("departmentBranchId") Long departmentBranchId);*/

    @Modifying
    @Transactional
    @Query("DELETE FROM TicketUserMapping tum WHERE tum.branch.id = :branchId AND tum.department.departmentId = :departmentId")
    void deleteByBranchIdAndDepartmentId(@Param("branchId") Long branchId, @Param("departmentId") Long departmentId);

   /* boolean existsByUser_IdAndDepartment_DepartmentId(Long userId, Long departmentId);

    // Method to check if a mapping exists for the given userId and branchId
    boolean existsByUser_IdAndBranch_BranchId(Long userId, Long branchId);*/

    boolean existsByUser_IdAndDepartment_DepartmentId(Long userId, Long departmentId);

    // New method to check if a user is a support person by userId, branchId, and departmentId
    boolean existsByUser_IdAndBranch_IdAndDepartment_DepartmentId(Long userId, Long branchId, Long departmentId);


    @Override
	List<TicketUserMapping> findAll();

    @Query("SELECT t FROM TicketUserMapping t WHERE t.department.departmentId = :departmentId")
    List<TicketUserMapping> findByDepartmentId(@Param("departmentId") Long departmentId);


    @Query("SELECT t FROM TicketUserMapping t WHERE t.department.departmentId = :departmentId AND t.branch.id = :branchId")
    List<TicketUserMapping> findByDepartmentIdAndBranchId(@Param("departmentId") Long departmentId, @Param("branchId") Long branchId);


}