
.conter h5 {
    color: var(--blue12); 
  }
  .conter-ticket{
    /* width:400px; */
    border-radius:7px;

  }
  .srch-ticket{
    width:300px;
  }
  
  .input-group-text {
    background-color: transparent;
    border-left: 0;
  }
  .file-icon {
 
    color: gray;
    height: 18px;
  }

 
  
  .ticket-title {
    font-weight: bold;
    font-size: 14px;
    color: var(--grey3); 
  }
  
  .list-group-item {
    border: none;
    padding-left: 0;
    padding-right: 0;
  }
  
  .badge {
    position: relative !important;
  }

  .badge-danger {
    background-color: #D89B9B !important; 
    color: black !important;
    padding: 0.5em 1em !important;
    /* width: 80px; */
  }
  
  .badge-success {
    background-color: #C5E0D8 !important; 
    color: black !important;
    padding: 0.5em 1em !important;
  }
  
  .scrollable-ticket-container {
    max-height: 650px; /* Fixed height for the ticket list */
    overflow-y: auto;  /* Make the container scrollable */
    border: 1px solid #ddd;
    padding: 10px;
  }
  
  .loader {
    text-align: center;
    padding: 10px;
    font-weight: bold;
    color: #007bff;
  }
  
  .no-more-tickets {
    text-align: center;
    padding: 10px;
    color: grey;
  }
  
  /* .srch-ticket {
    margin-bottom: 15px;
    width: 100%;
  } */

  @media (max-width: 768px) {
    .container-ticket {
      width: auto; 
      padding: 10px; 
      margin-left: 3px;
      /* margin: 10px auto;  */
      max-width: 90%; 
    }
  
    .ticket-dropdown {
      width: 100%; 
      margin-top: 15px;
      display: block; 
    }
  
    .srch-ticket {
      width: 100%; 
      margin-top: 10px; 
    }
  
    .list-group-item {
      font-size: 13px; 
      padding: 8px 10px; 
    }
  
    .badge {
      font-size: 11px; 
    }
  
    .badge-danger {
      width: auto; 
    }
  
    .ticket-title {
      font-weight: bold;
      font-size: 12px; 
    }
  
    .text {
      font-size: 9px; 
    }
  }
  
  @media (min-width: 768px) and (max-width: 1024px) {
    .container-ticket {
      width: auto; 
      padding: 15px; 
      margin: 0 auto; 
      max-width: 600px;
    }
  
    .ticket-dropdown {
      width: 80%; 
      margin: 10px auto;
      display: block; 
    }
  
    .srch-ticket {
      width: 80%; 
      margin: 10px auto; 
    }
  
    .list-group-item {
      font-size: 14px; 
    }
  
    .badge {
      font-size: 12px; 
    }
  
    .badge-danger {
      width: auto; 
    }
  
    .ticket-title {
      font-weight: bold;
      font-size: 13px; 
    }
  
    .text {
      font-size: 10px; 
    }
  }
  @media (min-width: 1025px) and (max-width: 1280px) {
    .ticket-title {
      font-weight: bold; 
      font-size: 14px;
    }
  
    .text {
      font-size: 10px; 
    }
  
    .badge {
      font-size: 12px; 
    }
  
    .container-ticket {
      max-width: 900px; 
      padding: 20px; 
      margin: 0 auto; 
    }
  
    .ticket-dropdown {
      width: 70%; 
      margin: 15px auto;
    }
  
    .srch-ticket {
      width: 80%; 
      margin: 10px auto; 
    }
  
    .list-group-item {
      font-size: 15px; 
      padding: 10px 15px; 
    }
  
    .badge-danger {
      width: auto; 
      font-size: 12px; 
    }
  }
  
  
  @media (max-width: 1300px) {
    .ticket-title {
      font-weight: bold;
      font-size: 13px;
    }
    .text {
      font-size: 9px;
    }
    .badge {
      font-size: 11px;
    }
  }
  