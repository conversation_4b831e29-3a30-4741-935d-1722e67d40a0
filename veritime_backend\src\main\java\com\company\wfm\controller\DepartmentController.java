package com.company.wfm.controller;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.BranchFilterDTO;
import com.company.wfm.dto.CreateDepartmentDTO;
import com.company.wfm.dto.DepartmentDTO;
import com.company.wfm.dto.DepartmentIdNameDTO;
import com.company.wfm.dto.UpdateDepartmentDTO;
import com.company.wfm.entity.Department;
import com.company.wfm.service.impl.DepartmentServiceImpl;
import com.company.wfm.vo.BranchVO;
import com.company.wfm.vo.DepartmentBranchVO;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/departments")
@CrossOrigin(origins = "*")
@Slf4j
public class DepartmentController {
    @Autowired
    private DepartmentServiceImpl departmentServiceImpl;


    @PostMapping("/create")
    public ResponseEntity<?> createDepartment(@RequestBody CreateDepartmentDTO departmentDTO) {
        try {
            Department createdDepartment = departmentServiceImpl.createDepartment(departmentDTO);
            return new ResponseEntity<>(createdDepartment, HttpStatus.CREATED);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>("An error occurred while creating the designation", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

   @PostMapping("/branches")
    public ResponseEntity<?> getAllBranches(@RequestBody BranchFilterDTO filter) {
        try {
            // Manually create the Pageable object from filter's offset and limit
            Pageable pageable = PageRequest.of(filter.getOffset(), filter.getLimit());

            Object result = departmentServiceImpl.getAllBranches(filter, pageable);

            if (result instanceof Page) {
                // Handle the paginated result
                Page<BranchVO> branchesPage = (Page<BranchVO>) result;
                return ResponseEntity.ok(branchesPage);
                // Process branchesPage here
            } else if (result instanceof List) {
                // Handle the non-paginated result
                List<BranchVO> branchList = (List<BranchVO>) result;
                return ResponseEntity.ok(branchList);
                // Process branchList here
            } else {
                // Handle unexpected result type
                throw new IllegalStateException("Unexpected result type: " + result.getClass());
            }



        } catch (RuntimeException e) {
            log.error(e.getMessage());
            return new ResponseEntity<>(e.getMessage(), HttpStatus.UNAUTHORIZED);
        } catch (Exception e) {
            log.error(e.getMessage());
            return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    //Lookup for department
    @GetMapping("/names")
    public ResponseEntity<List<String>> getAllDepartmentNames() {
        List<String> departmentNames = departmentServiceImpl.getAllDepartmentNames();
        return ResponseEntity.ok(departmentNames);
    }

    @GetMapping("/all")
    public ResponseEntity<List<DepartmentDTO>> getAllDepartment() {
        List<DepartmentDTO> departments = departmentServiceImpl.getAllDepartment();
        return ResponseEntity.ok(departments);
    }

    @GetMapping("/ids-names")
    public List<DepartmentIdNameDTO> getDepartmentIdsAndNames() {
        return departmentServiceImpl.getAllActiveDepartmentIdsAndNames();
    }

    @PostMapping("/create-dept-branch")
    public ResponseEntity<String> createDepartmentBranch(@RequestBody CreateDepartmentDTO departmentDTO) {
        try {
            departmentServiceImpl.createDepartmentBranch(departmentDTO);
            return new ResponseEntity<>("Department and Branch records created successfully.", HttpStatus.CREATED);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>("An error occurred while creating the designation", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping("/allDepartments")
    public ResponseEntity<Page<DepartmentBranchVO>> searchDepartments(
            @RequestBody BranchFilterDTO filter) {
        // Call the service method with the calculated page and limit
        Page<DepartmentBranchVO> departments = departmentServiceImpl.searchDepartments(filter);
        return ResponseEntity.ok(departments);
    }


    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> deleteDepartment(@PathVariable Long id) {

        try {
            departmentServiceImpl.deleteDepartment(id);
            return ResponseEntity.ok("Department marked as inactive successfully");
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>("An error occurred while deleting the department", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PutMapping("/update/{departmentId}")
    public ResponseEntity<String> updateDepartmentBranch(
            @PathVariable Long departmentId,
            @RequestBody UpdateDepartmentDTO departmentDTO) {


        // Set the department ID in the DTO
        departmentDTO.setDepartmentId(departmentId);

        // Call the service to update the department and branches
        departmentServiceImpl.updateDepartmentBranch(departmentDTO);

        return ResponseEntity.ok("Department and branches updated successfully");
    }


   @GetMapping("/branchWiseDepartment")
    public ResponseEntity<?> getDepartmentsForLoggedInUser() {
        try {
            List<DepartmentIdNameDTO> departments = departmentServiceImpl.getDepartmentsForLoggedInUser();
            return ResponseEntity.ok(departments);
        } catch (IllegalArgumentException ex) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Collections.singletonMap("error", ex.getMessage()));
        } catch (RuntimeException ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Collections.singletonMap("error", ex.getMessage()));
        }
    }

    @PostMapping("/branchWiseDepartment1")
    public ResponseEntity<?> getDepartmentsForBranch(@RequestBody Map<String, Long> request) {
        try {
            Long branchId = request.get("branchId"); // Extract branchId from the request
            List<DepartmentIdNameDTO> departments = departmentServiceImpl.getDepartmentsForLoggedInUser1(branchId);
            return ResponseEntity.ok(departments);
        } catch (IllegalArgumentException ex) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Collections.singletonMap("error", ex.getMessage()));
        } catch (RuntimeException ex) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Collections.singletonMap("error", ex.getMessage()));
        }
    }








}
