package com.company.wfm.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.EmployeeDocument;

@Repository
public interface EmployeeDocumentRepository extends JpaRepository<EmployeeDocument, Long> {
    @Override
	Optional<EmployeeDocument> findById(Long id);
  //  List<EmployeeDocument> findByEmpIdAndIsActiveTrue(Long empId);
    List<EmployeeDocument> findByEmpIdAndIsActiveTrueOrderByIdDesc(Long empId);
    @Query("SELECT e FROM EmployeeDocument e WHERE e.empId = :empId AND e.documentId = :documentId")
    Optional<EmployeeDocument> findByEmpIdAndDocumentId(@Param("empId") Long empId, @Param("documentId") Long documentId);


}
