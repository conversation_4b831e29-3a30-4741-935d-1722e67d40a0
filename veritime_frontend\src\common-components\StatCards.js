import '../css/style.css';
import StatCard from './StatCard';
import Accordion from '@mui/material/Accordion';
import AccordionActions from '@mui/material/AccordionActions';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
const StatCards = ({ arr , key}) => {
  return (
    <>   
     <div key={key}  className="stat-cards stat-card-web" style={{marginTop:-24,paddingBottom:12}}>

      {arr?.map(({ title, value, percentage, color, pathcolor, index }) => <StatCard key={index} title={title} value={value} percentage={percentage} color={color} pathcolor={pathcolor} />)}

    </div>
      <div className="stat-cards mobile-accordian">
        <Accordion>
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls="panel1-content"
            id="panel1-header"
          >
            <h2>Statistics</h2>
          </AccordionSummary>
          <AccordionDetails>
            {arr?.map(({ title, value, percentage, color, pathcolor ,index}) => <StatCard  key={index} title={title} value={value} percentage={percentage} color={color} pathcolor={pathcolor} />)}
          </AccordionDetails>
        </Accordion>
      </div>
    </>
  );
};
export default StatCards;
