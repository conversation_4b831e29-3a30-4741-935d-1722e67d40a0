.cldr_modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 1s ease;
}

.cldr_modal-overlay.show {
    opacity: 1;
    pointer-events: all;
}

.cldr_modal {
    background-color: rgb(255 255 255 / 0%);
    width: 100%;
    max-height: 80vh;
    transform: translateX(100%);
    transition: transform 0.5s ease;
    display: flex;
    flex-direction: column;
}

.cldr_modal_content {
    overflow-y: hidden;
    flex-grow: 1;
    padding: 20px;
}

.cldr_modal.show {
    transform: translateX(0);
}

.cldr_modal.hide {
    transform: translateX(100%);
}
