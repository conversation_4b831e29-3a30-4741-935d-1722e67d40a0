package com.company.wfm.service.impl;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import com.company.wfm.dto.EncryptedResponse;
import com.company.wfm.entity.AssignedTickets;
import com.company.wfm.entity.Employee;
import com.company.wfm.repository.AssignedTicketRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.util.EncryptionUtil;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.security.core.token.TokenService;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;

import com.company.wfm.dto.JwtAuthResponse;
import com.company.wfm.dto.TicketDTO;
import com.company.wfm.dto.TicketResponseDTO;
import com.company.wfm.entity.Ticket;
import com.company.wfm.repository.TicketRepository;
import com.company.wfm.service.VendorApiService;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import javax.crypto.SecretKey;

@Slf4j
@Service
public class VendorApiServiceImpl implements VendorApiService {

	@Autowired
	private WebClient webClient;

	@Autowired
	private TicketRepository ticketRepository;

	@Autowired
	private EncryptionUtil encryptionUtil;

	@Autowired
	private UserTokenService tokenService;

	@Autowired
	private AssignedTicketRepository assignedTicketRepository;

	private EmployeeRepository employeeRepository;
	//	@Autowired
//	private TokenService tokenService;
	@Override
	public boolean pushTicketToVendorApi(String vendorEndpoint, Long ticketId, String userName, String password) {


		try {

//			String token = generateToken("/api/auth/login", userName, password);
			String vendorUrl = vendorEndpoint+"/api/auth/login";
			log.info("The login URL for vendor : {}", vendorUrl);
			String token = generateToken( vendorUrl, userName, password);
			if (token == null) {
				log.error("Failed to generate token. Aborting ticket push.");
				return false;
			}

			log.info("Token generated successfully: {}", token);
			Ticket ticket = ticketRepository.findById(ticketId)
					.orElseThrow(() -> new EntityNotFoundException("Ticket not found"));

			TicketDTO dto = new TicketDTO();
			dto.setTicketSubject(ticket.getTicketSubject());
			dto.setTicketMessage(ticket.getTicketMessage());
			//dto.setEmpId(ticket.getEmpId());
			dto.setDepartmentId(2L);
			dto.setStatus("Open");
			dto.setCategory("5");

			//JSON payload to be sent in the POST request body
			String jsonString = (new ObjectMapper()).writeValueAsString(dto) + ";type=application/json";

			MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();

			if (StringUtils.isNoneBlank(ticket.getFilePath())) {
				List<String> filePathList = Arrays.asList(ticket.getFilePath().split(";"));

				filePathList.stream().forEach(fileName -> {
					Path path = Paths.get(fileName);
					try {
						byte[] fileBytes = Files.readAllBytes(path);
						ByteArrayResource fileResource = new ByteArrayResource(fileBytes) {
							@Override
							public String getFilename() {
								return path.getFileName().toString();
							}
						};
						formData.add("files", fileResource);
					} catch (IOException exception) {
						log.error("Failed to read file {}", exception);
					}
				});
			}

			HttpHeaders jsonHeaders = new HttpHeaders();
			jsonHeaders.setContentType(MediaType.APPLICATION_JSON);
			// Wrap the JSON payload in an HttpEntity with the headers
			HttpEntity<String> jsonEntity = new HttpEntity<>(jsonString, jsonHeaders);
			formData.add("ticket", jsonEntity);
			String vendorUrlTicket = vendorEndpoint+"/api/v1/tickets/create";
			log.info("The ticket URL for vendor : {}", vendorUrlTicket);
			EncryptedResponse result = webClient.post().uri(vendorUrlTicket)
					.header(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_MIXED_VALUE)
					.header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
					.bodyValue(formData).retrieve()
					.onStatus(HttpStatusCode::is4xxClientError, response -> {
						//Handle client errors (4xx)
						return response.bodyToMono(EncryptedResponse.class)
								.flatMap(errorBody -> {
									log.error("Client error: {}", errorBody);
									return Mono.error(new RuntimeException("Client error: " + errorBody));
								});
					})
					.onStatus(HttpStatusCode::is5xxServerError, response -> {
						//Handle server errors (5xx)
						return response.bodyToMono(EncryptedResponse.class)
								.flatMap(errorBody -> {
									log.error("Server error: {}", errorBody);
									return Mono.error(new RuntimeException("Server error: " + errorBody));
								});
					})
					.bodyToMono(EncryptedResponse.class)
					.doOnSuccess(response -> {
						log.info("Response received with a new ticket id created: {}", response);
					})
					.doOnError(error -> {
						log.error("An error occurred: {}", error.getMessage());
					})
					.block();
// Generate Secret Key
			SecretKey secretKey = encryptionUtil.generateKey();

			// Decrypt the data
			String decryptedData = encryptionUtil.decrypt(result.getEncryptedData(), secretKey);
			log.info("Encrypted Data: {}", decryptedData);


			// Convert JSON Response to Java Object
			Gson gson = new Gson();
			TicketResponseDTO resultResponse = gson.fromJson(decryptedData, TicketResponseDTO.class);
			AssignedTickets assignedTickets = new AssignedTickets();
//			Optional<Employee> optionalEmployee = employeeRepository.findById(tokenService.getEmployeeIdFromToken());
//			Employee employee = optionalEmployee.get();
//			assignedTickets.setEmployee(employee);
			assignedTickets.setTicket(ticket);
			log.info("The reference Id will be {}", resultResponse.getTicketId());
			assignedTickets.setReferenceId(resultResponse.getTicketId());
			assignedTickets.setType("external");
			assignedTickets.setRemark(ticket.getLatest_remark());
			assignedTickets.setCreatedAt(LocalDateTime.now());
			assignedTicketRepository.save(assignedTickets);
			log.info("The ticked saved in assigned ticket table with ticketId : {}", ticket.getTicketId());
			return null != result ? true : false;
		} catch (Exception e) {
			log.error("Failed to create ticket {}", e);
			return false;
		}
	}

	/**
	 * @param loginUrl
	 * @param userName
	 * @param password
	 * @return token value
	 */
//    private String generateToken(String loginUrl, String userName, String password) {
//        try {
//
//        	String jsonInputString = "{ \"username\": \"" + userName + "\", \"password\": \"" + password + "\" }";
//           	JwtAuthResponse jwtTokenResponse = webClient.post().uri(loginUrl)
//         	.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
//			.bodyValue(jsonInputString).retrieve()
//		    .onStatus(HttpStatusCode::is4xxClientError, response -> {
//		        // Handle client errors (4xx)
//		        return response.bodyToMono(TicketResponseDTO.class)
//		            .flatMap(errorBody -> {
//		                log.error("Client error 4xx series: {}", errorBody);
//		                return Mono.error(new RuntimeException("Client error: " + errorBody));
//		            });
//		    })
//		    .onStatus(HttpStatusCode::is5xxServerError, response -> {
//		        // Handle server errors (5xx)
//		        return response.bodyToMono(JwtAuthResponse.class)
//		            .flatMap(errorBody -> {
//		            	log.error("Server error 5xx series: {}" , errorBody);
//		                return Mono.error(new RuntimeException("Server error: " + errorBody));
//		            });
//		    })
//		    .bodyToMono(JwtAuthResponse.class)
//		    .doOnSuccess(response -> {
//		    	 log.info("Token generated successfully");
//		    })
//		    .doOnError(error -> {
//		        log.error("API throw an error: " + error.getMessage());
//		    })
//		    .block();
//
//			return null != jwtTokenResponse ? jwtTokenResponse.getAccessToken() : null;
//        } catch (Exception e) {
//        	log.error("Failed to execute request {}",e);
//            return null; // Return null in case of an exception
//        }
//    }
	public String generateToken(String loginUrl, String userName, String password) {
		try {
			//String userName = "<EMAIL>";
			//String password = "robert456";

			log.info("========= Token Generation Started =========");

			// Prepare plain text for encryption
			String plainText = "{ \"username\": \"" + userName + "\", \"password\": \"" + password + "\" }";
			log.info("Plain Text Before Encryption: {}", plainText);

			// Generate Secret Key
			SecretKey secretKey = encryptionUtil.generateKey();

			// Encrypt the data
			String encryptedData = encryptionUtil.encrypt(plainText, secretKey);
			log.info("Encrypted Data: {}", encryptedData);

			// Prepare request body
			String requestBody = "{ \"encryptedData\": \"" + encryptedData + "\" }";
			log.info("Request Body To Send: {}", requestBody);

			// Call Login API
			EncryptedResponse encryptedResponse = webClient.post()
					.uri(loginUrl)
					.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
					.bodyValue(requestBody)
					.retrieve()
					.bodyToMono(EncryptedResponse.class)
					.block();

			if (encryptedResponse == null || encryptedResponse.getEncryptedData() == null) {
				log.error("Error: Empty API Response");
				throw new RuntimeException("Failed to get token from server.");
			}

			// Decrypt Response
			String decryptedData = encryptionUtil.decrypt(encryptedResponse.getEncryptedData(), secretKey);
			log.info("Decrypted Response: {}", decryptedData);

			// Convert JSON Response to Java Object
			Gson gson = new Gson();
			JwtAuthResponse jwtAuthResponse = gson.fromJson(decryptedData, JwtAuthResponse.class);

			if (jwtAuthResponse == null || jwtAuthResponse.getAccessToken() == null) {
				log.error("Error: Token not found in response.");
				throw new RuntimeException("Token not found in response.");
			}

			log.info("========= Token Generated Successfully =========");
			log.info("Access Token: {}", jwtAuthResponse.getAccessToken());

			return jwtAuthResponse.getAccessToken();

		} catch (Exception e) {
			log.error("Exception while generating token: {}", e.getMessage());
			throw new RuntimeException("Error generating token", e);
		}
	}
}
