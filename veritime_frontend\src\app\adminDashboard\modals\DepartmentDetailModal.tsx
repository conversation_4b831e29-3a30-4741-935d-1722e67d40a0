import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
// Function to format date from an array [year, month, day]
const formatDateFromArray = (dateArray?: number[]): string => {
  if (!dateArray || dateArray.length < 3) return "N/A";
  const [year, month, day] = dateArray;
  return `${year}-${String(month).padStart(2, "0")}-${String(day).padStart(
    2,
    "0"
  )}`;
};

function DepartmentDetailModal({ show, onClose, departmentDetails }: any) {
  console.log(departmentDetails); // Check what data is received
  return (
    <Modal show={show} onHide={onClose}>
      <Modal.Header closeButton>
        <Modal.Title>Department Details</Modal.Title>
      </Modal.Header>

      <Modal.Body style={{ overflowY: "auto", maxHeight: "400px" }}>
        <div style={{ padding: "10px" }}>
          {/* Department Name */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Department Name
            </div>
            <div>{departmentDetails?.departmentName || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Department Code and Category in one row */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Department Code
              </div>
              <div>{departmentDetails?.departmentCode || "N/A"}</div>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Category
              </div>
              <div>{departmentDetails?.category || "N/A"}</div>
            </div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />
          {/* Facility List with Scrollbar */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Associate Facilities
            </div>
            <div
              style={{
                maxHeight: "150px", // Set a fixed height for scrolling
                overflowY: "auto", // Enable vertical scroll
                padding: "5px",
                marginTop: "5px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "5px",
                }}
              >
                {departmentDetails?.branchList?.length > 0 ? (
                  departmentDetails.branchList.map(
                    (branch: any, index: number) => (
                      <div
                        key={index}
                        style={{
                          backgroundColor: "#e0e0e0",
                          borderRadius: "8px",
                          padding: "5px 10px",
                          fontSize: "14px",
                          color: "#333",
                          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                        }}
                      >
                        {branch.branchName}
                      </div>
                    )
                  )
                ) : (
                  <div>No facilities assigned</div>
                )}
              </div>
            </div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Created By and Updated By structured similarly */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Created By
              </div>
              <div>{departmentDetails?.createdByName || "N/A"}</div>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Updated By
              </div>
              <div>{departmentDetails?.updatedByName || "N/A"}</div>
            </div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Created At and Updated At structured similarly */}
          {/* Created At and Updated At in one row */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Created At
              </div>
              <div>
                {formatDateFromArray(
                  departmentDetails?.createdAt as unknown as number[]
                )}
              </div>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Updated At
              </div>
              <div>
                {formatDateFromArray(
                  departmentDetails?.updatedAt as unknown as number[]
                )}
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={onClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export default DepartmentDetailModal;
