package com.company.wfm.repository;


import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.Employee;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long> {

    boolean existsByEmail(String emailId);
    Optional<Employee> findByEmail(String emailId);

    @Query("SELECT e.empId, e.empName FROM Employee e WHERE e.inService = true")
    List<Object[]> findAllActiveEmployeeIdsAndNames();

    @Override
	Optional<Employee> findById(Long id);

    @Override
	List<Employee> findAll();


 /*   @Query("SELECT e FROM Employee e " +
            "WHERE (:branchIds IS NULL OR e.branch.id IN :branchIds) " +
            "AND (:departmentIds IS NULL OR e.department.departmentId IN :departmentIds) " +
            "AND (:designationIds IS NULL OR e.designation.id IN :designationIds) " +
            "AND (:query IS NULL OR :query = '' OR e.empName LIKE %:query%)")
    Page<Employee> findEmployeesByFilters(
            @Param("branchIds") List<Long> branchIds,
            @Param("departmentIds") List<Long> departmentIds,
            @Param("designationIds") List<Long> designationIds,
            @Param("query") String query,
            Pageable pageable);*/
//new

   /* @Query("SELECT e FROM Employee e " +
            "WHERE (:branchIds IS NULL OR e.branch.id IN :branchIds) " +
            "AND (:departmentIds IS NULL OR e.department.departmentId IN :departmentIds) " +
            "AND (:designationIds IS NULL OR e.designation.id IN :designationIds) " +
            "AND (:query IS NULL OR :query = '' OR e.empName LIKE %:query%) " +
            "ORDER BY e.empId DESC")
    Page<Employee> findEmployeesByFilters(
            @Param("branchIds") List<Long> branchIds,
            @Param("departmentIds") List<Long> departmentIds,
            @Param("designationIds") List<Long> designationIds,
            @Param("query") String query,
            Pageable pageable);*/


    @Query("SELECT e FROM Employee e " +
            "WHERE (:branchIds IS NULL OR e.branch.id IN :branchIds) " +
            "AND (:departmentIds IS NULL OR e.department.departmentId IN :departmentIds) " +
            "AND (:designationIds IS NULL OR e.designation.id IN :designationIds) " +
            "AND (:query IS NULL OR :query = '' OR e.empName LIKE %:query%) " +
            "ORDER BY e.empId DESC")
    Page<Employee> findEmployeesByFilters(
            @Param("branchIds") List<Long> branchIds,
            @Param("departmentIds") List<Long> departmentIds,
            @Param("designationIds") List<Long> designationIds,
            @Param("query") String query,
            Pageable pageable);


    @Query("SELECT e FROM Employee e WHERE e.upperId = :upperId")
    List<Employee> findByUpperId(@Param("upperId") Long upperId);

    @Query("SELECT e FROM Employee e WHERE e.branch.id = :branchId")
    List<Employee> findByBranchId(@Param("branchId") Long branchId);

    @Query("SELECT e FROM Employee e WHERE e.companyId= :companyId")
    List<Employee> findByCompanyId(@Param("companyId") Long companyId);


    @Query("SELECT e FROM Employee e WHERE e.upperId = :currentUserId")
    List<Employee> findEmployeesBySupervisor(@Param("currentUserId") Long currentUserId);

    // Find employees in the same branch (for admin)
    @Query("SELECT e FROM Employee e WHERE e.branch.id = :branchId")
    List<Employee> findEmployeesByAdminBranch(@Param("branchId") Long branchId);

    // Find employees in the same company (for superadmin)
    @Query("SELECT e FROM Employee e WHERE e.companyId = :companyId")
    List<Employee> findEmployeesBySuperadminCompany(@Param("companyId") Long companyId);

//new

  /*  @Query("SELECT new com.company.wfm.dto.EmployeeDepartmentDTO(e.empId, e.empName, d.departmentName, b.branchName) " +
            "FROM Employee e " +
            "JOIN e.department d " +
            "JOIN e.branch b " +
            "WHERE b.id = :branchId")
    List<EmployeeDepartmentDTO> findEmployeesByBranchId(Long branchId);*/

  /*  @Query("SELECT new com.company.wfm.dto.UserDTO(e.empId, e.empName, e.imgUrl) " +
            "FROM Employee e " +
            "WHERE e.branch.id = :branchId AND e.department.id = :departmentId")
    List<Object[]> findEmployeesByBranchAndDepartment(@Param("branchId") Long branchId, @Param("departmentId") Long departmentId);*/

    @Query("SELECT e FROM Employee e " +
            "JOIN e.department d " +
            "JOIN e.branch b " +
            "WHERE b.id = :branchId AND d.departmentId = :departmentId")
    List<Employee> findEmployeesByBranchAndDepartment(@Param("branchId") Long branchId, @Param("departmentId") Long departmentId);

    @Override
	Page<Employee> findAll(Pageable pageable);

  //  String findEmailById(Long empId);

    @Query("SELECT e.email FROM Employee e WHERE e.empId = :empId")
    String findEmailById(@Param("empId") Long empId);


   // Long findEmpIdByUid(String uid);

    Employee findByUid(String uid);

    @Query("SELECT COUNT(e) > 0 FROM Employee e WHERE e.empId = :empId AND e.department.departmentId = :departmentId")
    boolean existsByEmpIdAndDepartmentId(Long empId, Long departmentId);


    @Query("SELECT e.upperId FROM Employee e WHERE e.empId = :empId")
    Long findUpperIdByEmpId(@Param("empId") Long empId);



  /* @Query("SELECT e FROM Employee e WHERE e.department.departmentId = :departmentId")
   Page<Employee> findByDepartment_DepartmentId(@Param("departmentId") Long departmentId, Pageable pageable);*/

    @Query("SELECT e FROM Employee e WHERE (:branchId IS NULL OR e.branch.id = :branchId) AND (:departmentId IS NULL OR e.department.departmentId = :departmentId)")
    Page<Employee> findByBranchIdAndDepartmentId(@Param("branchId") Long branchId, @Param("departmentId") Long departmentId, Pageable pageable);


    @Query("SELECT e FROM Employee e " +
            "WHERE e.branch.id = :branchId " +
            "AND (:search IS NULL OR :search = '' OR LOWER(e.empName) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<Employee> findByBranchIdNew(@Param("branchId") Long branchId, @Param("search") String search,  Pageable pageable);

    @Query("SELECT e.branch.id FROM Employee e WHERE e.empId = :empId")
    Long findBranchIdByEmployeeId(@Param("empId") Long empId);

   /* @Query("SELECT e FROM Employee e WHERE e.empCode = :empCode")
    Optional<Employee> findByEmpCodeCustom(String empCode);*/

    @Query("SELECT e FROM Employee e WHERE e.empCode = :empCode")
    List<Employee> findByEmpCodeCustom(String empCode);

    boolean existsByEmpCode(String empCode);

    boolean existsByUid(String uid);

    boolean existsByNationalId(String nationalId);

    // Fetch employees by list of employee IDs
    List<Employee> findByEmpIdIn(List<Long> empIds);

    @Query("SELECT e.empName FROM Employee e WHERE e.empId = :id")
    String findEmployeeNameById(@Param("id") Long id);


    Optional<Employee> findByEmpCode(String empCode );

    @Query(value = "SELECT * FROM Employee e WHERE e.IN_SERVICE = 1", nativeQuery = true)
    List<Employee> findActiveEmployees();
    
    @Query(value = "SELECT * FROM Employee e WHERE e.BRANCH_ID = :branchId AND e.IN_SERVICE = 1", nativeQuery = true)
    List<Employee> findByBranchIdAndIsActive(Long branchId);


}

