import { API_URLS } from "@/constants/apiConstants";
import { getRequest } from "@/services/apiService";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

function DetailsForm({ data, onSubmit }) {
  const [designations, setDesignations] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showRePassword, setShowRePassword] = useState(false);

  const [formData, setFormData] = useState({
    userCode: data.userCode,
    username: data.username,
    password: "",
    rePassword: "",
    role: data.role,
    employeeId: data.employeeId,
  });

  const { hospitalId, branchId } = useParams();

  // Build the URL parameters if available
  const urlParams = hospitalId && branchId
    ? `?hospitalId=${hospitalId}&branchId=${branchId}`
    : "";

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e, action) => {
    e.preventDefault();
  
    if (formData.password !== formData.rePassword) {
      setError("Passwords do not match!");
      return;
    }
  
    setError("");  
    const formDataWithAction = {
      ...formData,
      action: action,  
    };
  
    // Pass the formDataWithAction to onSubmit
    onSubmit(formDataWithAction);
    // console.log("Form submitted with action:", formDataWithAction);
  };
  

  // useEffect(() => {
  //   const fetchFilterOptions = async () => {
  //     try {
  //       const designationsData = await getRequest(API_URLS.GET_DESIGNATION);
  //       if (designationsData)
  //         console.log('designationsData::: ', designationsData);
  //       setDesignations(designationsData);
  //     } catch (error) {
  //       console.error('Error fetching filter options:', error);
  //     }
  //   };

  //   fetchFilterOptions();
  // }, []);

  // Function to toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const toggleRePasswordVisibility = () => {
    setShowRePassword(!showRePassword);
  };

  return (
    <form style={{ width: "500px", margin: "20px auto" }}>
      <h2>Details</h2>
      <div className="mb-3">
        <label htmlFor="userCode" className="form-label">
          User Code
        </label>
        <input
          type="text"
          className="form-control"
          id="userCode"
          name="userCode"
          style={{
            width: "100%",
            borderRadius: "1px",
            border: "1px solid black",
          }}
          value={formData.userCode}
          onChange={handleChange}
          readOnly
          required
        />
      </div>

      <div className="mb-3">
        <label htmlFor="username" className="form-label">
          Username
        </label>
        <input
          type="text"
          className="form-control"
          id="username"
          name="username"
          style={{
            width: "100%",
            borderRadius: "1px",
            border: "1px solid black",
          }}
          value={formData.username}
          onChange={handleChange}
          required
          readOnly
        />
      </div>

      <div className="mb-3">
        <label htmlFor="password" className="form-label">
          Password
        </label>
        <div style={{ position: "relative" }}>
          <input
            type={showPassword ? "text" : "password"}
            className="form-control"
            id="password"
            name="password"
            style={{
              width: "100%",
              borderRadius: "1px",
              border: "1px solid black",
            }}
            value={formData.password}
            onChange={handleChange}
            required
          />
          <span
            onClick={togglePasswordVisibility}
            style={{
              position: "absolute",
              right: "15px",
              top: "50%",
              transform: "translateY(-50%)",
              cursor: "pointer",
            }}
          >
            {showPassword ? "👁️" : "👁️‍🗨️"}
          </span>
        </div>
      </div>

      <div className="mb-3">
        <label htmlFor="rePassword" className="form-label">
          Re-enter Password
        </label>
        <div style={{ position: "relative" }}>
          <input
           type={showRePassword ? "text" : "password"}
            className="form-control"
            id="rePassword"
            name="rePassword"
            style={{
              width: "100%",
              borderRadius: "1px",
              border: "1px solid black",
            }}
            value={formData.rePassword}
            onChange={handleChange}
            required
          />
          <span
            onClick={toggleRePasswordVisibility}
            style={{
              position: "absolute",
              right: "15px",
              top: "50%",
              transform: "translateY(-50%)",
              cursor: "pointer",
            }}
          >
            {showRePassword ? "👁️" : "👁️‍🗨️"}
          </span>
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      <div style={formRowStyle}>
        <button
          type="button"
          style={submitButtonStyle}
          onClick={(e) => handleSubmit(e, "save")}
        >
          Save
        </button>
        {/* <button onClick={() => window.location.href = "/adminDashboard/employee/newEmployee"+urlParams} type="button" style={{ ...submitButtonStyle, marginLeft: '10px' }}>Create more</button> */}
        <button
          type="button"
          style={{ ...submitButtonStyle, marginLeft: '10px' }}
          onClick={(e) => handleSubmit(e, "saveAndCreateMore")}
        >
          Save & Create More
        </button>
      </div>
    </form>
  );
}

const formRowStyle = {
  width: "100%",
  display: "flex",
  marginBottom: "20px",
};
const submitButtonStyle = {
  width: "200px",
  padding: "10px 20px",
  borderRadius: "4px",
  border: "none",
  backgroundColor: "#007bff",
  color: "#fff",
  fontSize: "16px",
  cursor: "pointer",
  textAlign: "center",
};

export default DetailsForm;
