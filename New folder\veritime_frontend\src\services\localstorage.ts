import { Dispatch, SetStateAction, useEffect, useState } from "react";

const useLocalStorage = (key: string, defaultValue: any): [any, Dispatch<SetStateAction<any>>] => {
    const [value, setValue] = useState<any>(() => {
        if (typeof window !== "undefined") {
            const storedValue = localStorage.getItem(key);
            if (storedValue) {
                try {
                    return JSON.parse(storedValue);
                } catch (error) {
                    return storedValue;
                }
            } else {
                const valueToStore = typeof defaultValue === 'string' ? defaultValue : JSON.stringify(defaultValue);
                localStorage.setItem(key, valueToStore);
                return defaultValue;
            }
        }
        return defaultValue; // In case `window` is undefined (for server-side rendering)
    });

    useEffect(() => {
        if (typeof window !== "undefined") {
            const valueToStore = typeof value === 'string' ? value : JSON.stringify(value);
            localStorage.setItem(key, valueToStore);
        }
    }, [key, value]);

    return [value, setValue];
};

export default useLocalStorage;
