package com.company.wfm.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.SideMenuService;
import com.company.wfm.service.UserTokenService;

@RestController
@RequestMapping("/api/v1/sidemenu")
@CrossOrigin(origins = "*")
public class SideMenuController {

    @Autowired
    private SideMenuService sideMenuService;

    @Autowired
    private UserTokenService tokenService;

    @GetMapping
    public ResponseEntity<?> getSideMenu() {
        try {
            Long empId = tokenService.getEmployeeIdFromToken();
            String role = sideMenuService.getRoleByEmployeeId(empId);

            List<Map<String, Object>> sideMenu = sideMenuService.getSideMenuByRole(role);
            return new ResponseEntity<>(sideMenu, HttpStatus.OK);

        } catch (Exception e) {
            return new ResponseEntity<>("Failed to retrieve side menu", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
