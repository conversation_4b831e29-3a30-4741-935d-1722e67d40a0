package com.company.wfm.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;

@Data
public class ProvinceDTO {
	private Long id;
    private String name;
    @JsonInclude(Include.NON_NULL)
    private List<DistrictDTO> districts;

	public ProvinceDTO(Long id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	public ProvinceDTO(Long id, String name, List<DistrictDTO> districts) {
		super();
		this.id = id;
		this.name = name;
		this.districts = districts;
	}


}