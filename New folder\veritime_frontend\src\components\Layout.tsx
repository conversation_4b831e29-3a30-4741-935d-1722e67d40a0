import React, { useEffect, useRef, useState } from 'react';
import '../css/Home.css';
import MastersCard1 from './Sidemenu/SAdmin/Masters';
import MastersCard from './Sidemenu/Masters/Masters';
import Header from './Header/Header';
import Sidemenu from './Sidemenu/Sidemenu';
import useLocalStorage from '@/services/localstorage';
import { usePathname } from 'next/navigation';
import { roleBasedRedirect } from '@/services/roleBasedRedirect';
import OneSignal from 'react-onesignal';

const TopLoader = () => {
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const pathname = usePathname();
  const hideTimer = useRef<any>(null);

  useEffect(() => {
    const handleClick = (e: any) => {
      const target = e.target as HTMLElement;
      const linkElement = target.closest('a');
  
      if (linkElement?.href && 
          !linkElement.href.startsWith('javascript:') && 
          !linkElement.href.includes('#') && 
          !linkElement.href.includes('.xlsx') && 
          linkElement.target !== '_blank') {
        setIsVisible(true);
      }
  
      if (linkElement?.href && linkElement.href.startsWith('javascript:')) {
        e.preventDefault();
      }
  
      if(linkElement?.href == window.location.href){
        setTimeout(() => {
          setIsVisible(false);
        }, 1000);
      }
    };
  
    

    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, []);

  useEffect(() => {
    if (hideTimer.current) clearTimeout(hideTimer.current);
    
    hideTimer.current = setTimeout(() => {
      setIsVisible(false);
    }, 300);

    return () => {
      if (hideTimer.current) clearTimeout(hideTimer.current);
    };
  }, [pathname]);

  if (!isVisible) return null;

  return (
    <div className="loader-container">
      <div className="loader-line"></div>
    </div>
  );
};

const ToggleImageComponent: React.FC<any> = ({ expanded, toggleMenu }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="center-image"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ width: "55px", height: "80%", left: expanded ? "238px" : "88px" }}
    >
      <img
        src={expanded ? '/image/leftarrowSidemenu.png' : '/image/rightarrowSidemenu.png'}
        alt="Toggle Menu"
        onClick={toggleMenu}
        style={{
          display: expanded && !isHovered ? 'none' : 'block',
          width: "22px",
          height: "22px"
        }}
      />
    </div>
  );
};

const Layout = ({ children }:any) => {
  const [role, setRole] = useLocalStorage("role", "");
  const [expanded, setExpanded] = useState(false);
  const [showCard, setShowCard] = useState(false);
  const [showCard1, setShowCard1] = useState(false);
  const [cardPosition, setCardPosition] = useState<any>({});
  const mastersButtonRef = useRef<any>(null);
  const [isMobileView, setIsMobileView] = useState(false);
  const [arrowDirection, setArrowDirection] = useState('left');
  const [isLoggedIn, setisLoggedIn] = useLocalStorage("username", "");

  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (!localStorage.getItem("accessToken") || !localStorage.getItem("role")) {
        localStorage.clear()
      localStorage.setItem("currentPath",window.location.href)

        window.location.href = "/login?sessionExpired=1";
      }
      roleBasedRedirect(window.location.pathname);

      OneSignal.Notifications.addEventListener('click', (event) => {
      });
      OneSignal.Notifications.addEventListener('foregroundWillDisplay', (event) => {
      });
      OneSignal.Notifications.addEventListener('permissionChange', (permission) => {
      });
    }
  }, []);

  const toggleMenu = () => {
    setExpanded(!expanded);
  };

  const toggleCard = () => {
    setShowCard(prevState => !prevState);
  };

  const toggleCard1 = () => {
    setShowCard1(prevState => !prevState);
  };

  const updateCardPosition = () => {
    if (mastersButtonRef.current) {
      const buttonRect = mastersButtonRef.current.getBoundingClientRect();
      //{console.log("masters left",console.log("Hii",buttonRect.top -80 +window.scrollY))}
      if (isMobileView || expanded) {
        setCardPosition({
          top: `${buttonRect.top+ 15+window.scrollY}px`, //424
          left: `${buttonRect.right + 15}px`,
        });
        
      } else {
        setCardPosition({
          top: `${buttonRect.top +15+ window.scrollY}px`,
          left: `${buttonRect.right + 10}px`,
        });
        setArrowDirection('left');
      }
    }
  };

  useEffect(() => {
    
    const handleResize = () => {
      setIsMobileView(window.innerWidth <= 768);
      if (showCard) {
        updateCardPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, [showCard, expanded]);

  useEffect(() => {
    if (showCard) {
      updateCardPosition();
    }
  }, [showCard, expanded]);

  return (
    <div className={`home-container ${expanded ? 'menu-expanded' : ''}`}>
      <TopLoader />
      <div id="alert-container"></div>
      {isLoggedIn && <Header toggleMenu={toggleMenu} />}
      <div className="main-container">
        {isLoggedIn && (
          <Sidemenu
            toggleMenu={toggleMenu}
            expanded={expanded}
            toggleCard={toggleCard}
            mastersButtonRef={mastersButtonRef}
          />
        )}
        <div className="content-container">
          {React.cloneElement(children)}
        </div>
        {showCard && (
          <div className="mastercard-popup" style={cardPosition}>
            <MastersCard arrowDirection={arrowDirection} />
          </div>
        )}
        {showCard1 && (
          <div className="mastercard-popup" style={cardPosition}>
            <MastersCard1 arrowDirection={arrowDirection} />
          </div>
        )}
      </div>
      {isLoggedIn && (
        <ToggleImageComponent toggleMenu={toggleMenu} expanded={expanded} />
      )}
    </div>
  );
};

export default Layout;
