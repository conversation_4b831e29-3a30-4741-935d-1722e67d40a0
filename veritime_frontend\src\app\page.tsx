"use client"
import React, { use, useEffect, useState } from 'react';
import { appConstants } from '../constants/appConstants';
import EmployeeDashboard from './employeeDashboard/page';
import useLocalStorage from '../services/localstorage';
import Main from './Dashboard/page';
import AdminDashboard from './adminDashboard/page';

const Home = () => {
  useEffect(() => {
      if (typeof window !== 'undefined') {
        if (!localStorage.getItem("accessToken") || !localStorage.getItem("role")) {
          localStorage.clear()
          window.location.href = "/login";
        } else {
          window.location.href = "/dashboards";
        }
      }
  }, [])
  return (
    <></>
  )

};

export default Home;