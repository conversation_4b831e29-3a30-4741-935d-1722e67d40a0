
/* .bg-custom{
    background-color: var(--blue6);
  }
  @media (min-width: 768px) {
    .col-md-1 {
        flex: 0 0 auto;
        width: 4.333333%;
    }
}@media (min-width: 992px) {
  .col-lg-3 {
      flex: 0 0 auto;
      width: 17%;
      margin-left: 22px;
  }
} */
.my-3 {
  margin-top: 1rem !important;
  margin-bottom: -0rem !important; 
}


/* Base styles */

 
.react-select__control {
  font-size: 1rem; 
  width: 12em; 
  min-height: 45px; 
}

.cusm-form-control-searchterm-view {
  font-size: 0.7rem; 
  text-align: left;
  height: 45px;
  padding: 0 9px; 
  text-align: center;
  width: 100px; 
}

.buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1px; 
  justify-content: flex-end;
}

.unique-go-button,
.unique-reset-button {
  flex-shrink: 0;
}

.unique-go-button button,
.unique-reset-button button {
  font-size: 1rem;
  padding: 0.5em 1em; 
  min-width: 6em; 
}

/* Dropdown container styles */
.dropdown-tickets-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin-bottom: 1px; 
}
@media (min-width: 768px) and (max-width: 868px) {
  .react-select__control {
    font-size: 0.4rem;
    width: 15em; 
    min-height: 35px;
  }
  .cusm-form-control-searchterm-view {
    font-size: 0.6rem;
    height: 35px;
    width: 6em;
    margin-left: 5px;
  }
  .buttons-container {
    margin-right: 10px; 
    gap: 1px;
  }
  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.6rem;
    padding: 0.3em 0.6em; 
    min-width: 4em; 
    height: 35px;
  }
}

 

@media (min-width: 968px) and (max-width: 1024px) {
  .react-select__control {
    font-size: 10px;
    width: 11.50em;
    min-height: 40px;
  }


  .dropdown-tickets-container {
    gap: 1px;
  }
  .cusm-form-control-searchterm-view {
    font-size: 10px;
    height: 38px;
    width: 9.90em;
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 0px;
    gap: 1px;
  }
  


  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem;
    height: 35px;
    padding: 0.4em 0.8em;
    min-width: 3em;
  }

  
}


@media (min-width: 1025px) and (max-width: 1040px) {
  .react-select__control {
    font-size: 10px;
    width: 11em;
    min-height: 40px;
  }

  .dropdown-tickets-container {
    gap: 1px;
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 11em;
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 0px;
    gap: 1px;
  }

 

  .unique-reset-button {
    margin-right: 13px;
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem;
    height: 35px;
    padding: 0.4em 0.8em;
    min-width: 3em;
  }
}

@media (min-width: 1041px) and (max-width: 1060px) {
  .react-select__control {
    font-size: 10px;
    width: 11em;
    min-height: 40px;
  }

  .dropdown-tickets-container {
    gap: 1px;
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 13em;
    margin-left: 13px;
  }

  .buttons-container {
    margin-right: 0px;
    gap: 1px;
  }


  .unique-reset-button {
    margin-right: 13px;
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem;
    height: 35px;
    padding: 0.4em 0.8em;
    min-width: 3em;
  }
}


@media (min-width: 1061px) and (max-width: 1080px) {
  .react-select__control {
    font-size: 10px;
    width: 12em;
    min-height: 40px;
  }

  .dropdown-tickets-container {
    gap: 0.1px;
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 13em;
    margin-left: 13px;
  }

  .buttons-container {
    margin-right: 0px;
    
  }

  
  .unique-reset-button {
    margin-right: 5px;
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.6rem;
    height: 35px;
    padding: 0.4em 0.8em;
    min-width: 2em;
  }
}




@media (min-width: 1081px) and (max-width: 1100px) {
  .react-select__control {
    font-size: 10px;
    width: 12em;
    min-height: 40px;
  }

  .dropdown-tickets-container {
    gap: 0.5px;
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 15em;
    margin-left: 13px;
  }

  .buttons-container {
    margin-right: 0px;
    
  }



  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem;
    height: 35px;
    padding: 0.4em 0.8em;
    min-width: 3em;
  }
}




@media (min-width: 1101px) and (max-width: 1110px) {
  .react-select__control {
    font-size: 10px;
    width: 13.40em;
    min-height: 40px;
  }

  .dropdown-tickets-container {
    gap: 0.5px;
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 10em;
    margin-left: 20px;
  }

  .buttons-container {
    margin-right: 0px;
    
  }

  .unique-go-button {
    margin-left: 1px;
  }

  .unique-reset-button {
    margin-right: 10px;
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem;
    height: 35px;
    padding: 0.4em 0.8em;
    min-width: 3em;
  }
}



@media (min-width: 1111px) and (max-width: 1120px) {
  .react-select__control {
    font-size: 10px;
    width: 13.40em;
    min-height: 40px;
  }

  .dropdown-tickets-container {
    gap: 0.5px;
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 10em;
    margin-left: 20px;
  }

  .buttons-container {
    margin-right: 0px;
    
  }

  .unique-go-button {
    margin-left: 1px;
  }

  .unique-reset-button {
    margin-right: 10px;
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem;
    height: 35px;
    padding: 0.4em 0.8em;
    min-width: 3em;
  }
}



@media (min-width: 1121px) and (max-width: 1140px) {
  .react-select__control {
    font-size: 10px;
    width: 13.90em;
    min-height: 40px;
  }

  .dropdown-tickets-container {
    gap: 0.5px;
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 10em;
    margin-left: 20px;
  }

  .buttons-container {
    margin-right: 0px;
    
  }

  .unique-go-button {
    margin-left: 1px;
  }

  .unique-reset-button {
    margin-right: 10px;
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem;
    height: 35px;
    padding: 0.4em 0.8em;
    min-width: 3em;
  }
}

@media (min-width: 1141px) and (max-width: 1160px) {
  .react-select__control {
    font-size: 0.75rem;
    width: 11.20em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 1px;
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 10em; 
    margin-left: 15px;
  }

  .buttons-container {
    margin-right: 5px; 
    gap: 0.1px; 
  }

  
  .unique-reset-button {
    margin-right: 10px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.75rem; 
    padding: 0.4em 0.9em; 
    min-width: 3.5em; 
  }
}


@media (min-width: 1161px) and (max-width: 1180px) {
  .react-select__control {
    font-size: 0.75rem;
    width: 12em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 1px; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 11em; 
    margin-left: 13px;
  }

  .buttons-container {
    margin-right: 5px; 
    gap: 0.10em; 
  }

  .unique-go-button {
    margin-left: 1px; 
  }

  .unique-reset-button {
    margin-right: 10px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.75rem; 
    padding: 0.4em 0.9em; 
    min-width: 4em; 
  }
}


@media (min-width: 1181px) and (max-width: 1190px) {
  .react-select__control {
    font-size: 0.75rem;
    width: 12em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 0.50px; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 12em; 
    margin-left: 13px;
  }

  .buttons-container {
    margin-right: 5px; 
    gap: 0.10em; 
  }

  

  .unique-reset-button {
    margin-right: 10px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.75rem; 
    padding: 0.4em 0.9em; 
    min-width: 3.5em; 
  }
}


@media (min-width: 1191px) and (max-width: 1209px) {
  .react-select__control {
    font-size: 0.75rem;
    width: 12.20em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 1px; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 13em; 
    margin-left: 13px;
  }

  .buttons-container {
    margin-right: 5px; 
    gap: 0.10px; 
  
  }


  .unique-reset-button {
    margin-right: 10px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.75rem; 
    padding: 0.4em 0.9em; 
    min-width: 3.5em; 
    height: 35px;
  }
}

@media (min-width: 1210px) and (max-width: 1220px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 12em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 0.2em; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 13em; 
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 0.2px; 
  }

  .unique-go-button {
    margin-left: 10px;
  }



  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}



@media (min-width: 1221px) and (max-width: 1250px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 13.10em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 0.2em; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 12em; 
    margin-left: 12px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 0.2em; 
  }



  .unique-reset-button {
    margin-right: 5px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}


@media (min-width: 1251px) and (max-width: 1270px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 14em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 0.2em; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 12em; 
    margin-left: 12px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 0.2em; 
  }

 
  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}


@media (min-width: 1271px) and (max-width: 1300px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 14.40em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 0.2em; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 12em; 
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 0.2em; 
  }

  

  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}



@media (min-width: 1301px) and (max-width: 1320px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 14.90em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 0.2em; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 12em; 
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 0.2em; 
  }



  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}


@media (min-width: 1321px) and (max-width: 1340px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 15.40em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 0.2em; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 12.30em; 
    margin-left: 15px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 0.2em; 
  }

 

  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}


@media (min-width: 1341px) and (max-width: 1360px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 15.70em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 0.2em; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 12.60em; 
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 0.2em; 
  }

 

  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}


@media (min-width: 1361px) and (max-width: 1380px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 15em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 1px; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 12.90em; 
    margin-left: 10px;
  }

  .buttons-container {
    margin-right: 5px;
    gap: 0.1px; 
  }


  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}



@media (min-width: 1381px) and (max-width: 1400px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 16em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 1px; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 14em; 
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 1px; 
  }

  
  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}




@media (min-width: 1401px) and (max-width: 1420px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 16em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 1px; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 15em; 
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 1px; 
  }


  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}



@media (min-width: 1421px) and (max-width: 1440px) {
  .react-select__control {
    font-size: 0.75rem; 
    width: 17em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 1px; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 16em; 
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 1px; 
  }

 

  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}



@media (min-width: 1441px)  {
  .react-select__control {
    font-size: 0.75rem; 
    width: 17em; 
    min-height: 42px;
  }

  .dropdown-tickets-container {
    gap: 1px; 
  }

  .cusm-form-control-searchterm-view {
    font-size: 0.7rem;
    height: 38px;
    width: 18em; 
    margin-left: 14px;
  }

  .buttons-container {
    margin-right: 8px;
    gap: 1px; 
  }

 

  .unique-reset-button {
    margin-right: 0px; 
  }

  .unique-go-button button,
  .unique-reset-button button {
    font-size: 0.7rem; 
    height: 30px;
    padding: 0.4em 0.8em; 
    min-width: 3.20em; 
    height: 35px;
  }
}