package com.company.wfm.entity;

import java.time.LocalDateTime;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "t_employee_leave_balance")
public class EmployeeLeaveBalance {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EMP_LEAVE_ID")
    private Long empLeaveId;

    @Column(name = "LEAVE_ID")
    private Long leaveId;

    @Column(name = "EMP_ID")
    private Long empId;

    @Column(name = "ASSIGNED_LEAVE")
    private Integer assignedLeave;

    @Column(name = "BALANCE_LEAVE")
    private Integer balanceLeave;

    @Column(name = "CREATED_TIME")
    private LocalDateTime createdTime;

    @Column(name = "UPDATED_TIME")
    private LocalDateTime updatedTime;

    @Column(name = "credit_date")
    private Date creditDate;



}
