import { API_URLS } from '@/constants/apiConstants';
import { getRequest } from './apiService';

export const getCount=async (url)=>{
    try {     
        const data = await getRequest(url)
        return data?.length || 0;
    } catch (error) {
        return 0;
    }

}

export const getLeaveCount = async(url,status)=>{
    try {
        const data  = await getRequest(url)
        const count = data?.filter((item)=>item.approvalStatus==status)
        return count?.length || 0;
    } catch (error) {
        return 0;
    }
}

export const getTeamData = (id)=>{
    const data = [{
        id:1,
        name : "<PERSON>",
        dept : "Human Resource",
        totalLeaves : "30",
        pendingLeaves : "10",
        overtime : "40 hours",
        profImg : "/image/avatar.png",
        email: "<EMAIL>"
    },{
        id:2,
        name : "<PERSON>",
        dept : "Information Technnology",
        totalLeaves : "30",
        pendingLeaves : "10",
        overtime : "40 hours",
        profImg : "/image/profile.png",
        email: "<EMAIL>"
    },{
        id:3,
        name : "<PERSON>",
        dept : "Information Technnology",
        totalLeaves : "30",
        pendingLeaves : "10",
        overtime : "40 hours",
        profImg : "/image/avatar.png",
        email: "<EMAIL>"
    }
    ]

    return data.filter((item)=>item.id==id)[0] || data[0]
}