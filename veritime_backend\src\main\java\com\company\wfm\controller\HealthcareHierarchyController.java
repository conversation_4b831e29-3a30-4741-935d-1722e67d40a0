package com.company.wfm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.DistrictDTO;
import com.company.wfm.dto.HospitalDTO;
import com.company.wfm.dto.HospitalUpdateDTO;
import com.company.wfm.dto.ProvinceDTO;
import com.company.wfm.dto.SubDistrictDTO;
import com.company.wfm.entity.FeederHospital;
import com.company.wfm.service.impl.HealthcareHierarchyService;

@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = "*")
public class HealthcareHierarchyController {
    @Autowired
    private HealthcareHierarchyService healthcareHierarchyService;

    @PostMapping("/create")
    public ResponseEntity<FeederHospital> createHospital(@RequestBody HospitalDTO hospitalDTO) {
        FeederHospital createdHospital = healthcareHierarchyService.createHospital(hospitalDTO);
        return new ResponseEntity<>(createdHospital, HttpStatus.CREATED);
    }
    @GetMapping("/details")
    public ResponseEntity<List<HospitalDTO>> getAllHospitals() {
        List<HospitalDTO> hospitals = healthcareHierarchyService.getAllHospitals();
        return ResponseEntity.ok(hospitals);
    }

    @GetMapping("/search")
    public ResponseEntity<List<HospitalDTO>> searchHospitals(
            @RequestParam(required = false) String province,
            @RequestParam(required = false) String district,
            @RequestParam(required = false) String subDistrict,
            @RequestParam(required = false) String name) {
        List<HospitalDTO> hospitals = healthcareHierarchyService.searchHospitals(province, district, subDistrict, name);
        return ResponseEntity.ok(hospitals);
    }
    @PutMapping("/update/{id}")
    public ResponseEntity<HospitalDTO> updateHospital(@PathVariable Long id, @RequestBody HospitalUpdateDTO updateDTO) {
        try {
            HospitalDTO updatedHospital = healthcareHierarchyService.updateHospital(id, updateDTO);
            return ResponseEntity.ok(updatedHospital);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteHospital(@PathVariable Long id) {
        try {
            healthcareHierarchyService.deleteHospital(id);
            return ResponseEntity.noContent().build();
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }


    //The below method is for lookup for dropdown
    @GetMapping("/provinces")
    public ResponseEntity<List<ProvinceDTO>> getAllProvinces() {
        List<ProvinceDTO> provinces = healthcareHierarchyService.getProvinces();
        return ResponseEntity.ok(provinces);
    }

    @GetMapping("/provinces/{provinceId}/districts")
    public ResponseEntity<List<DistrictDTO>> getDistrictsByProvince(@PathVariable("provinceId") Long provinceId) {
        try {
            List<DistrictDTO> districts = healthcareHierarchyService.getDistrictsByProvince(provinceId);
            return ResponseEntity.ok(districts);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/provinces/{provinceId}/districts/{districtId}/subdistricts")
    public ResponseEntity<List<SubDistrictDTO>> getSubDistrictsByDistrict(
            @PathVariable("provinceId") Long provinceId,
            @PathVariable("districtId") Long districtId) {
        try {
            List<SubDistrictDTO> subDistricts = healthcareHierarchyService.getSubDistrictsByDistrict(provinceId, districtId);
            return ResponseEntity.ok(subDistricts);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }


}
