package com.company.wfm.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeaveTypeDTO {
    private Long leaveId;
    private String type;
    private BigDecimal leaveCount;
    private Boolean allowFileUpload;
    private Integer minimumCount;
    private Integer daysAppliedBefore;
    private Boolean is_Active;





}
