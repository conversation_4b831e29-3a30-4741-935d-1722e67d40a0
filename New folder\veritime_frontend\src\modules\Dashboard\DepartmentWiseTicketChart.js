import React from 'react';
import { Pie } from 'react-chartjs-2';
import 'chart.js/auto';

const DepartmentWiseTicketChart = (value) => {
  const data = {
    labels: value.data.axisLbl,
    datasets: [
      {
        data: value.data.axis,
        backgroundColor: ['#B9E4D4', '#6FCF97', '#FFE9B9', '#FDD7AA'],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
  };

  return (
    <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-3">
      <div className="card shadow-sm">
        <div className="card-body">
          <p className="h6">Ticket wise Report</p>
          <div style={{ position: 'relative', width: '100%', height: "210px" }}>
            <Pie data={data} options={options} />
          </div>
        </div>
      </div>
    </div>

  );
};

export default DepartmentWiseTicketChart;
