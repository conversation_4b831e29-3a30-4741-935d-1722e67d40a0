import { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from "react-bootstrap";
import { putRequest, postRequest, getRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import { showSuccessAlert2 } from "@/services/alertService";
import Select from "react-select"; // Import react-select

interface Department {
  departmentId: number;
  departmentName: string;
}

const EditCategoryModal = ({
  show,
  onClose,
  category,
  categories,
  onUpdate,
}: any) => {
  const [categoryName, setCategoryName] = useState(category?.category || "");
  const [priority, setPriority] = useState(category?.priority || "");
  const [eta, setEta] = useState(category?.eta || "");
  const [deptNames, setDeptNames] = useState<Department[]>([]); // State for departments, now typed
  const [selectedDept, setSelectedDept] = useState<{
    value: number;
    label: string;
  } | null>(null); // State for selected department

  const [errors, setErrors] = useState({
    categoryName: "",
    priority: "",
    eta: "",
    department: "",
  });

  // Inside EditCategoryModal component

  const resetForm = () => {
    setCategoryName("");
    setPriority("");
    setEta("");
    setSelectedDept(null);
    setErrors({
      categoryName: "",
      priority: "",
      eta: "",
      department: "",
    });
  };

  // Reset form on modal close
  useEffect(() => {
    if (!show) {
      resetForm();
    }
  }, [show]);

  const handleCategorySave = async () => {
    // Validate Category Name
    //  Ensure formErrors is defined correctly
    const formErrors: {
      categoryName: string;
      priority: string;
      eta: string;
      department: string;
    } = {
      categoryName: "",
      priority: "",
      eta: "",
      department: "",
    };

    // Validate Category Name
    if (!categoryName.trim()) {
      formErrors.categoryName = "Issue type is mandatory.";
    }

    // Validate ETA (only numbers allowed and must be positive)
    if (!eta.trim()) {
      formErrors.eta = "ETA is mandatory.";
    } else if (!/^\d+$/.test(eta)) {
      formErrors.eta = "ETA must be a positive number.";
    }

    // Validate Priority
    if (!priority || priority === "Select") {
      formErrors.priority = "Priority is mandatory.";
    }

    // Validate ETA
    if (!eta.trim()) {
      formErrors.eta = "ETA is mandatory.";
    }

    // Validate Department
    if (!selectedDept) {
      formErrors.department = "Department is mandatory.";
    }

    // Update errors state
    setErrors(formErrors);

    // Stop execution if there are any errors
    if (Object.values(formErrors).some((error) => error)) return;
    const updatedCategory = {
      category: categoryName,
      priority: priority,
      eta: eta,
      departmentId: selectedDept ? selectedDept.value : null,
    };

    try {
      let response;
      let successMessage;

      if (category) {
        response = await putRequest(
          `${API_URLS.UPDATE_CATEGORY}${category.id}`,
          updatedCategory
        );
        successMessage = "Issues Updated";

        const updatedCategories = categories.map((cat: any) =>
          cat.id === category.id ? { ...cat, ...updatedCategory } : cat
        );
        onUpdate(updatedCategories);
      } else {
        response = await postRequest(API_URLS.CREATE_CATEGORY, updatedCategory);
        successMessage = "Issues Created";

        const newCategories = [...categories, response];
        onUpdate(newCategories);
      }

      showSuccessAlert2(successMessage);
      onUpdate(response);
      onClose();
    } catch (error) {
      alert("An error occurred while saving the issue.");
    }
  };

  const fetchDeptNames = async () => {
    try {
      const data = await getRequest(API_URLS.DEPT_LOOKUP);
      if (data && Array.isArray(data)) {
        setDeptNames(data);
      } else {
      }
    } catch (error) {}
  };

  // Effect to update the form values when the category changes
  // useEffect(() => {
  //   if (category) {
  //     setCategoryName(category.category);
  //     setPriority(category.priority);
  //     setEta(category.eta);
  //     if (category.departmentId) {
  //       const dept = deptNames.find(
  //         (d) => d.departmentId === category.departmentId
  //       );
  //       setSelectedDept(
  //         dept ? { value: dept.departmentId, label: dept.departmentName } : null
  //       );
  //     }
  //   }
  // }, [category, deptNames]); // Only run this effect when category changes

  useEffect(() => {
    if (show) {
      // When modal opens
      fetchDeptNames();

      if (category) {
        // Editing an issue - pre-fill values
        setCategoryName(category.category);
        setPriority(category.priority);
        setEta(category.eta);
        setSelectedDept(
          category.departmentId
            ? deptNames.find((d) => d.departmentId === category.departmentId)
              ? {
                  value: category.departmentId,
                  label:
                    deptNames.find(
                      (d) => d.departmentId === category.departmentId
                    )?.departmentName || "",
                }
              : null
            : null
        );
      } else {
        // Adding a new issue - reset fields
        setCategoryName("");
        setPriority("");
        setEta("");
        setSelectedDept(null);
      }
    }
  }, [show, category]); // Run effect when modal opens or category changes

  // Fetch department names when the modal is shown
  useEffect(() => {
    if (show) {
      fetchDeptNames();
    }
  }, [show]);

  const handleDepartmentSelect = (
    selectedOption: { value: number; label: string } | null
  ) => {
    setSelectedDept(selectedOption);
  };

  return (
    <Modal show={show} onHide={onClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>{category ? "Edit Issue" : "Add Issue"}</Modal.Title>
      </Modal.Header>
      <label
        htmlFor="ticketSubject"
        className="ticket-text-primary"
        style={{ marginLeft: "10px" }}
      >
        <span className="text-danger">
          The fields with * marks are mandatory
        </span>
      </label>
      <Modal.Body>
        <Form>
          {deptNames.length === 0 ? (
            <div>Loading department data...</div>
          ) : (
            <div className="form-group col-md-4" style={{ width: "100%" }}>
              <label htmlFor="department" className="ticket-txt-primary mb-1">
                Department <span className="text-danger">*</span>
              </label>
              <Select
                options={deptNames.map((dept) => ({
                  value: dept.departmentId,
                  label: dept.departmentName,
                }))}
                value={selectedDept}
                onChange={handleDepartmentSelect}
                placeholder="Select Department"
                isClearable
                classNamePrefix="react-select"
              />
              {errors.department && (
                <div className="text-danger mt-1">{errors.department}</div>
              )}
            </div>
          )}

          {/* Category Input */}
          <Form.Group controlId="categoryName" style={{ width: "100%" }}>
            <Form.Label>
              Issues <span className="text-danger">*</span>
            </Form.Label>
            <Form.Control
              type="text"
              value={categoryName}
              onChange={(e) => setCategoryName(e.target.value)}
              placeholder="Issue Type"
            />
            {errors.categoryName && (
              <div className="text-danger mt-1">{errors.categoryName}</div>
            )}
          </Form.Group>

          {/* Priority Select */}
          <Form.Group controlId="priority" style={{ width: "100%" }}>
            <Form.Label>
              Priority <span className="text-danger">*</span>
            </Form.Label>
            <Form.Control
              as="select"
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
            >
              <option>Select</option>
              <option>Low</option>
              <option>Medium</option>
              <option>High</option>
            </Form.Control>
            {errors.priority && (
              <div className="text-danger mt-1">{errors.priority}</div>
            )}
          </Form.Group>

          {/* ETA Input */}
          <Form.Group controlId="eta">
            <Form.Label>
              ETA <span className="text-danger">*</span>{" "}
              <span className="text-muted"> (Enter ETA 1)</span>
            </Form.Label>
            <Form.Control
              type="text"
              value={eta}
              onChange={(e) => setEta(e.target.value)}
              // placeholder="Enter ETA 1"
            />
            {errors.eta && <div className="text-danger mt-1">{errors.eta}</div>}
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onClose}>
          Close
        </Button>
        <Button variant="primary" onClick={handleCategorySave}>
          Save
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default EditCategoryModal;
