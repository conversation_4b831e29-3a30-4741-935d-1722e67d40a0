import '../css/style.css';
// import CircularProgressBar from './CircularProgressBar';
import { Doughnut } from 'react-chartjs-2';


const StatCard = ({ title, value, percentage, color , pathcolor}) => {
  const data = {
    datasets: [{
      data: [+percentage, 100-percentage],
      backgroundColor: [
        color,
        pathcolor
      ],
      hoverOffset: 4
    }]
  }
  const options = {
    maintainAspectRatio: false,  
    animation: {
      delay: 800,
      duration: 2000, 
    },
  };
    return (
<>
      <div className={`stat-card ${color}`} style={{width:'200px'}}>
        <div className="stat-info">
          <h2>{title}</h2>
          <p className="stat-value">{value}</p>
        </div>
    <Doughnut data={data} options={options}/>

      </div>
</>
    );
  };
export default StatCard;  