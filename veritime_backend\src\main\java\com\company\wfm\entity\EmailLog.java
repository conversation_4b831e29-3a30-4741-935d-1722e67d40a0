package com.company.wfm.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_emailLog")
public class EmailLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id") // Specifies the column name for id
    private Long id;

    @Column(name = "from_email", nullable = true)
    private String fromEmail;

    @Column(name = "to_email", nullable = true)
    private String toEmail;

    @Column(name = "subject", nullable = true)
    private String subject;

    @Column(name = "message", nullable = true)
    private String message;

    @Column(name = "cc", nullable = true)
    private String cc;

    @Column(name = "bcc", nullable = true)
    private String bcc;

    @Column(name = "attachment", nullable = true)
    private String attachment;

    @Column(name = "status", nullable = false)
    private String status; // e.g., SENT, FAILED

    @Column(name = "error_message", nullable = true)
    private String errorMessage; // If any error occurs

    @Column(name = "timestamp", nullable = true)
    private LocalDateTime timestamp; // Timestamp of email log entry

}
