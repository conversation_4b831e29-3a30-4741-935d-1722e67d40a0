import axios from "axios";
import { API_URLS, BASE_URL } from "../constants/apiConstants";

import { showErrorAlert } from "./alertService";
import { decryptData, encryptData } from "@/utils/encryption";

const CACHED_GET_REQUESTS = {
  [`${BASE_URL}/api/v1/departments/ids-names`]: { expiry: 86400 },
  [`${BASE_URL}/api/v1/departments/names`]: { expiry: 86400 },
  [`${BASE_URL}/v1/designation/lookupDesgination`]: { expiry: 86400 },
  [`${BASE_URL}/api/v1/employees/ids-names`]: { expiry: 86400 },
};

const CACHED_POST_REQUESTS = {
  "http://www.aitechiez.com/api/v1/departments/branches": {
    payloadCondition: (payload) => payload.type === "select",
    expiry: 86400,
  },
  "http://www.aitechiez.com/api/v1/hospitals/getHospitals": {
    payloadCondition: (payload) => payload.type === "select",
    expiry: 86400,
  },
  "http://www.aitechiez.com/api/v1/departments/allDepartments": {
    payloadCondition: (payload) => payload.type === "select",
    expiry: 86400,
  },
};

const cacheData = async (key, data, expiry) => {
  if (!data) return;
  const cacheItem = {
    data: await encryptData(data),
    expiry: Date.now() + expiry * 1000,
  };
  localStorage.setItem(btoa(key), JSON.stringify(cacheItem));
};

const getCachedData = async (key) => {
  const cachedItem = localStorage.getItem(btoa(key));
  if (!cachedItem) return null;

  const { data, expiry } = JSON.parse(cachedItem);
  if (Date.now() > expiry) {
    localStorage.removeItem(key);
    return null;
  }

  return await decryptData(data);
};

const handleError = (error) => {
  if (error.response) {
    const status = error.response.status;
    if (status === 400 || status === 500) {
      const errorMsg = error?.response?.data;
      showErrorAlert(`  ${errorMsg?.error ? errorMsg?.error : errorMsg}`);
    }
  } else {
    if (error.code === "ERR_NETWORK") {
      showErrorAlert("Server is down. Please try after sometime.");
    } else if (error.code === "ECONNABORTED") {
      showErrorAlert("Request timed out error");
    } else {
      showErrorAlert("An unexpected error occurred:");
    }
  }
};
const getToken = () => {
  return localStorage.getItem("accessToken");
  // return localStorage.getItem('accessToken');
};

export const getRequest = async (url, showLoader = false) => {
  const cacheConfig = CACHED_GET_REQUESTS[url];
  const cacheKey = `${url?.split("v1/")?.[1]}`;

  if (cacheConfig) {
    const cachedData = await getCachedData(cacheKey);
    if (cachedData) return cachedData;
  }

  const loaderId = generateLoaderId();
  if (showLoader) startLoader(loaderId);

  try {
    const token = getToken();
    const response = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });
    stopLoader(loaderId);

    if (cacheConfig) cacheData(cacheKey, response.data, cacheConfig.expiry);

    return response.data;
  } catch (error) {
    stopLoader(loaderId);
    // handleError(error);
    throw error;
  }
};

export const postRequest = async (url, data, showLoader = false) => {
  const cacheConfig = CACHED_POST_REQUESTS[url];
  const cacheKey = `${url?.split("v1/")?.[1]}${JSON.stringify(data)}`;

  if (cacheConfig && cacheConfig.payloadCondition(data)) {
    const cachedData = await getCachedData(cacheKey);
    if (cachedData) return cachedData;
  }

  const loaderId = generateLoaderId();
  if (showLoader) startLoader(loaderId);

  try {
    const token = getToken();
    const response = await axios.post(url, data, {
      headers: { Authorization: `Bearer ${token}` },
    });
    stopLoader(loaderId);

    if (cacheConfig && cacheConfig.payloadCondition(data)) {
      cacheData(cacheKey, response.data, cacheConfig.expiry);
    }

    return response.data;
  } catch (error) {
    stopLoader(loaderId);
    // handleError(error);
    throw error;
  }
};

export const postFormRequest = async (url, data, showLoader = false) => {
  const loaderId = generateLoaderId();
  if (showLoader) startLoader(loaderId);
  try {
    const token = getToken();
    const response = await axios.post(url, data, {
      headers: {
        Authorization: "Bearer " + token,
        "Content-Type": "multipart/form-data",
      },
    });
    stopLoader(loaderId);

    return response.data;
  } catch (error) {
    stopLoader(loaderId);

    // console.error('Error in postFormRequest:', error);
    throw error;
  }
};

export const putRequest = async (url, data, showLoader = false) => {
  const loaderId = generateLoaderId();

  if (showLoader) startLoader(loaderId);
  try {
    const token = getToken();
    const response = await axios.put(url, data, {
      headers: {
        // uncomment it only after deepak or rakesh approval
        // "Access-Control-Allow-Headers": "Content-Type",
        // "Access-Control-Allow-Origin": "*",
        // "Access-Control-Allow-Methods": "OPTIONS,POST,GET",
        Authorization: "Bearer " + token,
      },
    });
    stopLoader(loaderId);
    return response.data;
  } catch (error) {
    stopLoader(loaderId);
    // console.log("error::: ", error);
    handleError(error);
    throw error;
  }
};

export const postRequestNoToken = async (url, data, showLoader = false) => {
  const loaderId = generateLoaderId();

  if (showLoader) startLoader(loaderId);
  try {
    const response = await axios.post(url, data, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    stopLoader(loaderId);
    return response?.data;
  } catch (error) {
    stopLoader(loaderId);

    // console.log("error::: ", error);
    // handleError(error);
    throw error;
  }
};

export const deleteRequest = async (url, showLoader = false) => {
  const loaderId = generateLoaderId();

  if (showLoader) startLoader(loaderId);
  try {
    const token = getToken();
    const response = await axios.delete(url, {
      headers: {
        Authorization: "Bearer " + token,
      },
    });
    stopLoader(loaderId);

    return response.data;
  } catch (error) {
    stopLoader(loaderId);

    handleError(error);
    throw error;
  }
};

export const fileDownload = async (url, showLoader = false) => {
  const loaderId = generateLoaderId();

  if (showLoader) startLoader(loaderId);
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${getToken()}`,
      },
    });
    stopLoader(loaderId);
    return response;
  } catch (error) {
    stopLoader(loaderId);

    handleError(error);
    throw error;
  }
};

export const fileUploadRequest = async (url, formData, showLoader = false) => {
  const loaderId = generateLoaderId();

  if (showLoader) startLoader(loaderId);
  try {
    const token = getToken();
    const headers = new Headers();
    headers.append("Authorization", `Bearer ${token}`);

    const requestOptions = {
      method: "POST",
      headers: headers,
      body: formData,
      redirect: "follow",
    };

    const response = await fetch(url, requestOptions);
    const result = await response.json();
    stopLoader(loaderId);

    if (response?.status == 200) return result;
    else {
      showErrorAlert(result?.ticketMessage);
      throw result;
    }
  } catch (error) {
    stopLoader(loaderId);
    await handleErrorWithEcryption(error);
    // console.error("Error with file upload request:", error);
    throw error;
  }
};

const generateLoaderId = () => {
  const randomString = Math.random().toString(36).substring(2, 8);
  return `loader_${randomString}`;
};

const startLoader = (loaderId) => {
  const loaderContainer = document.createElement("div");
  loaderContainer.className = `${loaderId}-container`;

  const loaderLine = document.createElement("div");
  loaderLine.className = `${loaderId}-line`;

  loaderContainer.appendChild(loaderLine);
  document.body.appendChild(loaderContainer);

  const styleSheet = document.createElement("style");
  styleSheet.type = "text/css";
  styleSheet.innerHTML = `
    .${loaderId}-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 5px;
      background: #4193FF;
      overflow: hidden;
      z-index: 9999;
      pointer-events: none;
    }
    
    .${loaderId}-line {
      position: absolute;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        to right,
        rgba(189, 204, 214, 0),
        #BDCCD6,
        rgba(189, 204, 214, 0)
      );
      animation: loading 1.0s infinite linear;
    }
    
    @keyframes loading {
      0% {
        transform: translateX(-100%);
      }
      100% {
        transform: translateX(100%);
      }
    }
  `;
  document.head.appendChild(styleSheet);
};

const stopLoader = (loaderId) => {
  const loaderContainer = document.querySelector(`.${loaderId}-container`);
  if (loaderContainer) {
    document.body.removeChild(loaderContainer);
  }
};

const handleErrorWithEcryption = async (error) => {
  if (error.response) {
    const status = error.response.status;
    if (status === 400 || status === 500) {
      console.log("error?.response?.data::: ", error?.response?.data);
      const errorMsg = await decryptData(error?.response?.data?.encryptedData);
      showErrorAlert(`  ${errorMsg?.error ? errorMsg?.error : errorMsg}`);
    }
  } else {
    if (error.code === "ERR_NETWORK") {
      showErrorAlert("Server is down. Please try after sometime.");
    } else if (error.code === "ECONNABORTED") {
      showErrorAlert("Request timed out error");
    } else {
      showErrorAlert("An unexpected error occurred:");
    }
  }
};

// Security Api Functions

export const postRequestNoTokenWithSecurity = async (
  url,
  data,
  showLoader = false
) => {
  const loaderId = generateLoaderId();

  if (showLoader) startLoader(loaderId);
  try {
    const encryptedText = await encryptData(data);
    const response = await axios.post(
      url,
      { encryptedData: encryptedText },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    stopLoader(loaderId);
    const responseData = await decryptData(response.data?.encryptedData);
    // console.log('responseData::: ', responseData);
    return responseData;
  } catch (error) {
    stopLoader(loaderId);

    // console.log("error::: ", error);
    // handleError(error);
    throw error;
  }
};

export const postRequestWithSecurity = async (
  url,
  data,
  showLoader = false
) => {
  const cacheConfig = CACHED_POST_REQUESTS[url];
  const cacheKey = `${url?.split("v1/")?.[1]}${JSON.stringify(data)}`;

  if (cacheConfig && cacheConfig.payloadCondition(data)) {
    const cachedData = await getCachedData(cacheKey);
    if (cachedData) return cachedData;
  }

  const loaderId = generateLoaderId();
  if (showLoader) startLoader(loaderId);

  try {
    const token = getToken();
    const response = await axios.post(
      url,
      { encryptedData: await encryptData(data) },
      {
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    stopLoader(loaderId);

    if (cacheConfig && cacheConfig.payloadCondition(data)) {
      cacheData(cacheKey, response.data, cacheConfig.expiry);
    }
    const responseData = await decryptData(response.data?.encryptedData);
    // console.log('responseData::: ',response.data?.encryptedData);
    return responseData;
  } catch (error) {
    stopLoader(loaderId);
    await handleErrorWithEcryption(error);
    throw error;
  }
};

export const getRequestWithSecurity = async (url, showLoader = false) => {
  const cacheConfig = CACHED_GET_REQUESTS[url];
  const cacheKey = `${url?.split("v1/")?.[1]}`;

  if (cacheConfig) {
    const cachedData = await getCachedData(cacheKey);
    if (cachedData) return cachedData;
  }

  const loaderId = generateLoaderId();
  if (showLoader) startLoader(loaderId);

  try {
    const token = getToken();
    const response = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` },
    });
    stopLoader(loaderId);

    if (cacheConfig) cacheData(cacheKey, response.data, cacheConfig.expiry);

    const responseData = await decryptData(response.data?.encryptedData);
    // console.log('responseData::: ', responseData);
    return responseData;
  } catch (error) {
    stopLoader(loaderId);
    handleError(error);
    throw error;
  }
};

export const putRequestWithSecurity = async (url, data, showLoader = false) => {
  const loaderId = generateLoaderId();

  if (showLoader) startLoader(loaderId);

  try {
    const token = getToken();

    const encryptedData = await encryptData(data);

    const response = await axios.put(
      url,
      { encryptedData: await encryptData(data) }, // Wrap encrypted data in an object
      {
        headers: {
          "Content-Type": "application/json", // Explicitly set the content type
          Authorization: "Bearer " + token,
        },
      }
    );

    stopLoader(loaderId);
    return await decryptData(response.data?.encryptedData);
  } catch (error) {
    stopLoader(loaderId);
    // console.log("error::: ", error);
    handleError(error);
    throw error;
  }
};

export const postRequestWithOnlyResponseDecrypted = async (
  url,
  data,
  showLoader = false,
  customErrorhandle = false
) => {
  // console.log("url::: ", url);

  const loaderId = generateLoaderId();
  if (showLoader) startLoader(loaderId);

  try {
    const token = await getToken();

    // console.log('data::: ', data);
    for (var pair of data.entries()) {
      // console.log(pair[0]+ ', ' + JSON.stringify(pair[1]));
    }
    const response = await axios.post(url, data, {
      headers: { Authorization: `Bearer ${token}` },
    });

    if (!response?.data?.encryptedData) {
      throw new Error("No encrypted data in the response.");
    }

    const responseData = await decryptData(response.data?.encryptedData);
    //  console.log('responseData::: ', responseData);
    return responseData;
  } catch (error) {
    await handleErrorWithEcryption(error);
    throw error;
  } finally {
    if (showLoader) {
      stopLoader(loaderId);
    }
  }
};
