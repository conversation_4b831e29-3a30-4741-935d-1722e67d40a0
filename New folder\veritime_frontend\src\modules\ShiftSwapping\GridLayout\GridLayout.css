

.grid-layout-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  background-color: var(--blue6);
  width: 100%;
  overflow-x: scroll; 

}

.grid-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-x: auto; 
  max-width: 100%;
}

.grid-header, .grid-content {
  display: flex;
  flex-wrap: nowrap; 
  width: 100%;
}

.grid-header .header-cell,
.grid-content .shift-column {
  min-width: 260px; 
  text-align: center;
  padding: 10px;
  border: 1px solid #9CAAB1;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.grid-header {
  border-bottom: 1px solid #9CAAB1;
}

.grid-content .shift-column {
  display: flex;
  flex-direction: column;
  min-width: 260px;
}

.grid-cell1 {
  min-width: 250px;
  height: 105px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #9CAAB1;
  white-space: nowrap;
  overflow: hidden;
  border : none
}

.grid-cell1.empty {
  /* background-color: var(--red12); */
  border: 1px dotted gray !important;
  border-radius:10px;
}

/* .grid-cell1.filled {
  background-color: var(--green4);
} */

.draggable-item {
  background-color: white;
  cursor: grab;
  width: 240px;
  height: 60px;
  border-radius: 16px;
  color: black;
  padding: 10px;
  display: flex;
  gap: 5px;
  font-size: 12px;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-timing {
  background-color: var(--blue14);
  cursor: grab;
  width: 240px;
  height: 50px;
  border-radius: 10px;
  color: black;
  padding: 10px;
  display: flex;
  font-size: 13px;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--blue4);
  box-shadow: 0 4px 8px var(--neutral3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive breakpoints */
/* @media (max-width: 1100px) {
  .grid-cell1 {
    min-width: 141px;
    height: 105px;
  }
}

@media (min-width: 1100px) and (max-width: 1350px) {
  .grid-cell1 {
    min-width: 141px;
    height: 105px;
  }
}

@media (min-width: 1350px) and (max-width: 1550px) {
  .grid-cell1 {
    min-width: 250px;
    height: 105px;
  }
}

@media (min-width: 1550px) {
  .grid-cell1 {
    min-width: 213px;
    height: 105px;
  }
} */
