"use client";
import React, { useEffect, useState } from "react";
import "./Admin.css";
import "../../css/statuscard.css";
import Team from "../../components/Team/Team";
import { Card } from "react-bootstrap";
import Layout from "../../components/Layout";
import Biometric from "../../modules/AdminDashboard/BiometricCard/biometric";
import QuickLink from "../../modules/AdminDashboard/QuickLinks/quicklink";
import BranchTicket from "../../modules/AdminDashboard/BranchTicket/Branch";
import Devices from "../../modules/AdminDashboard/Devices/device";
import Calenderr from "../../components/Calender/Calenderr";
import { appConstants } from "../../constants/appConstants";
import useLocalStorage from "@/services/localstorage";
import TotalApproved from "../../components/Tickets/countComponentWithoutIcon";
import TicketApproval from "../../components/Tickets/TotalApproval";

import { API_URLS } from "@/constants/apiConstants";
import { getCount, getLeaveCount } from "../../services/commonApi";
import { getRequest } from "@/services/apiService";
import Link from "next/link";

const AdminDashboard = () => {
  const [username, setusername] = useLocalStorage(appConstants?.username, "");
  const [isMounted, setIsMounted] = useState(false);
  const [blocksData, setBlocksData] = useState<any>({
    devicesCount: 0,
    activeDevicesCount: 0,
    teams: [],
    empCount: 0,
    branchCount: 0,
    deptCount: 0,
  });

  useEffect(() => {
    getDashboardData();

    if (typeof window !== "undefined") {
      setTimeout(() => {
        const chart = document.querySelectorAll("[data-percentage]");
        chart.forEach((element) => {
          const percentage = element.getAttribute("data-percentage");
          if (percentage) {
            (element as HTMLElement).style.setProperty(
              "--percentage",
              percentage
            );
          }
        });
      }, 100);
    }
  }, []);

  const getDashboardData = async () => {
    try {
      const data = await getRequest(API_URLS?.GET_ADMIN_DASHBOARD);
      setBlocksData(data);
    } catch (error) {}
  };

  const [teams, setTeams] = useState([]);
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        const response = await getRequest(API_URLS.EMPLOYEE_LIST);
        const mappedTeams = response.map((employee: any) => ({
          id: employee.empId,
          name: employee.empName || "Unknown",
          dept: employee.deptName || "Unknown",
          totalLeaves: 0,
          pendingLeaves: 0,
          overtime: 0,
          profImg:
            process.env.NEXT_PUBLIC_S3_URL + employee.imgUre ||
            "/image/person3.png",
        }));
        setTeams(mappedTeams);
      } catch (error) {}
    };

    fetchTeams();
  }, []);

  useEffect(() => {}, []);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Layout>
      <div className="container-fluid bg-custom p-4 pt-5">
        <div className="row mb-3">
          <div className="col-12 col-md-9">
            {/* <h1 className="h2"> Admin Dashboard</h1> */}
            {/* <p className="small" style={{ textTransform: "capitalize" }}>
              Hi, {username ? username?.toLowerCase() : ""}. Welcome back to
              Veritime Workforce Management System!
            </p> */}
          </div>
          <Calenderr />
        </div>
        <Link href={"./adminDashboard/branchList"}>
          <div className="row">
            <div className="col-12 col-md-6 col-lg-3 mb-3">
              <Card className="shadow-sm ">
                <Card.Body
                  style={{ height: 150, cursor: "pointer" }}
                  className="d-flex justify-content-center align-items-center"
                >
                  <div
                    style={{
                      backgroundColor: "#E0EEFA",
                      padding: 15,
                      borderRadius: 60,
                      marginRight: 15,
                    }}
                  >
                    <img
                      src="/image/branch.png"
                      alt="Foreground Icon"
                      className="foreground-icon"
                    />
                  </div>
                  <div>
                    <div className="h2">{blocksData.branchCount}</div>
                    <div className="small text-muted">Facility</div>
                  </div>
                </Card.Body>
              </Card>
            </div>
            <Link href={"./adminDashboard/departmentList"}>
              <div className="col-12 col-md-6 col-lg-3 mb-3">
                <Card className="shadow-sm">
                  <Card.Body
                    style={{ height: 150, cursor: "pointer" }}
                    className="d-flex justify-content-center align-items-center"
                  >
                    <div
                      style={{
                        backgroundColor: "#FFD8D8",
                        padding: 15,
                        borderRadius: 60,
                        marginRight: 15,
                      }}
                    >
                      <img
                        src="/image/Department.png"
                        alt="Foreground Icon"
                        className="foreground-icon"
                      />
                    </div>
                    <div>
                      <div className="h2">{blocksData.deptCount}</div>
                      <div className="small text-muted">Departments</div>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Link>
            <Link href={"./adminDashboard/employee"}>
              <div className="col-12 col-md-6 col-lg-3 mb-3">
                <Card className="shadow-sm">
                  <Card.Body
                    style={{ height: 150, cursor: "pointer" }}
                    className="d-flex justify-content-center align-items-center"
                  >
                    <div
                      style={{
                        backgroundColor: "#FEFFCD",
                        padding: 15,
                        borderRadius: 60,
                        marginRight: 15,
                      }}
                    >
                      <img
                        src="/image/enployee.png"
                        alt="Foreground Icon"
                        className="foreground-icon"
                      />
                    </div>
                    <div>
                      <div className="h2">{blocksData.empCount}</div>
                      <div className="small text-muted">Employee</div>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Link>

            <div className="col-12 col-md-6 col-lg-3 mb-3">
              <Card className="shadow-sm">
                <Card.Body
                  style={{ height: 150 }}
                  className="d-flex justify-content-center align-items-center"
                >
                  <div
                    style={{
                      backgroundColor: "#BFBBD9",
                      padding: 15,
                      borderRadius: 60,
                      marginRight: 15,
                    }}
                  >
                    <img
                      src="/image/device.png"
                      alt="Foreground Icon"
                      className="foreground-icon"
                    />
                  </div>
                  <div>
                    <div className="h2">{blocksData.devicesCount}</div>
                    <div className="small text-muted">Devices</div>
                  </div>
                </Card.Body>
              </Card>
            </div>
          </div>
        </Link>

        <div className="row">
          <div className="col-12 col-lg-6 col-xl-6 col-md-12 mb-3">
            <Biometric
              totalEnrollmentsCount={blocksData.totalEnrollmentsCount}
              totalEmpCount={blocksData.totalEmpCount}
            />
          </div>
          <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-3">
            <div
              className="card shadow-sm"
              style={{ height: "270px", marginTop: "15px", cursor: "pointer" }}
            >
              <div className="card-body">
                <p className="text fw-bold">Total Approvals</p>
                {/* <TotalApproved count={blocksData.totalApprovals} /> */}
              </div>
            </div>
          </div>
          <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-3">
            <div
              className="card shadow-sm"
              style={{ height: "270px", marginTop: "15px", cursor: "pointer" }}
            >
              <div className="card-body">
                <p className="text text-green fw-bold">Total Approved</p>
                <TicketApproval count={blocksData.totalApproved} />
              </div>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-12 col-lg-6 col-xl-6 col-md-12 mb-3">
            <div className="card shadow-sm">
              <div className="card-body">
                <h2 className="h5">Top 3 Facility</h2>
                <div className="d-flex justify-content-around">
                  {blocksData?.top3branches &&
                    blocksData?.top3branches.map((item: any) => (
                      <div className="text-center">
                        <div
                          className="pie-chart"
                          data-percentage={`${item.value.toFixed(2)}%`}
                        >
                          <div className="pie-chart-center">
                            {item.value.toFixed(2)}%
                          </div>
                        </div>
                        <p className="small">{item.name}</p>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </div>
          <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-2">
            <QuickLink />
          </div>
          <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-2">
            <BranchTicket data={blocksData.branchTicketList} />
          </div>
          {/* <Devices
            activeDevicesCount={blocksData.activeDevicesCount}
            totalDevicesCount={blocksData.totalDevicesCount}
          /> */}
        </div>
        {/* {teams.length > 0 ? ( */}
        {/* // <Team teamsCount={teams.length} teamsList={teams} /> */}
        {/* ) : (
          ""
        )} */}
      </div>
    </Layout>
  );
};

export default AdminDashboard;
