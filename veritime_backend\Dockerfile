# Use Azul Zulu OpenJDK 21 LTS as a parent image
FROM azul/zulu-openjdk:21

# Set the working directory in the container
WORKDIR /app

# Copy the JAR file into the container
COPY build/libs/wfm-manager-0.0.1-SNAPSHOT.jar app.jar

# Expose the port the application runs on
#EXPOSE 8081

# Set environment variables for the database connection
ENV DB_URL=*********************************************************************************************************************************************
ENV DB_USERNAME=admin
ENV DB_PASSWORD=Admin4db1433

# Run the JAR file
ENTRYPOINT ["java", "-jar", "app.jar"]