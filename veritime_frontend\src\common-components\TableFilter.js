import * as React from "react";
import { DataGrid } from "@mui/x-data-grid";

export default function DataTable({ columns, rows, autoRowHeight = false }) {
  const [searchTerm, setSearchTerm] = React.useState("");
  const [filteredRows, setFilteredRows] = React.useState(rows);

  React.useEffect(() => {
    const lowercasedSearchTerm = searchTerm.toLowerCase();
    const newFilteredRows = rows?.filter((row) =>
      columns.some((column) =>
        String(row[column.field]).toLowerCase().includes(lowercasedSearchTerm)
      )
    );
    setFilteredRows(newFilteredRows);
  }, [searchTerm, rows, columns]);

  return (
    <div style={{ height: "100%", width: "100%", overflow: "hidden" }}>
      <DataGrid
        rows={filteredRows}
        columns={columns}
        initialState={{
          pagination: {
            paginationModel: { page: 0, pageSize: 10 },
          },
        }}
        pageSizeOptions={[10, 25, 50, 100]}
        style={{ background: "white", overflow: "hidden", }}
        getRowHeight={autoRowHeight ? () => "auto" : undefined}
      />
    </div>
  );
}
