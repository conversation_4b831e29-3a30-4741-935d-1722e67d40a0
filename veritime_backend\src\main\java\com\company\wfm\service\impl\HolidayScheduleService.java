package com.company.wfm.service.impl;


import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.HolidayScheduleDTO;
import com.company.wfm.dto.HolidayScheduleExcelDTO;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.HolidaySchedule;
import com.company.wfm.entity.User;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.HolidayScheduleRepository;
import com.company.wfm.repository.UserRepository;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class HolidayScheduleService {

    @Autowired
    private HolidayScheduleRepository holidayScheduleRepository;

    @Autowired
    private UserRepository userRepository;


    @Autowired
    private EmployeeRepository employeeRepository;

    // Fetch all employees and store in a map for quick access
    private Map<Long, Employee> getEmployeeMap() {
        return employeeRepository.findAll().stream()
                .filter(employee -> employee.getEmpId() != null)
                .collect(Collectors.toMap(Employee::getEmpId, Function.identity()));
    }


    public HolidayScheduleDTO saveHolidaySchedule(HolidayScheduleDTO holidayScheduleDTO) {
        HolidaySchedule holidaySchedule = new HolidaySchedule();

        // Fetch the logged-in user's details
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();  // Assuming username is the login credential

        // Fetch the employee ID using the username
        Long employeeId = findEmployeeIdByUsername(username);

        // Fetch the user's name based on the employee ID
        String createdByName = fetchUserNameById(employeeId);

        holidaySchedule.setDate(holidayScheduleDTO.getDate());
        holidaySchedule.setHoliday(holidayScheduleDTO.getHoliday());
        holidaySchedule.setIsOptional(holidayScheduleDTO.getIsOptional());
        holidaySchedule.setCreatedBy(employeeId);

        holidaySchedule.setIsActive(true);


        HolidaySchedule savedHolidaySchedule = holidayScheduleRepository.saveAndFlush(holidaySchedule);

        // Prepare the response DTO
        HolidayScheduleDTO responseDTO = new HolidayScheduleDTO();
        responseDTO.setId(savedHolidaySchedule.getId());
        responseDTO.setDate(savedHolidaySchedule.getDate());
        responseDTO.setHoliday(savedHolidaySchedule.getHoliday());
        responseDTO.setIsOptional(savedHolidaySchedule.getIsOptional());
        responseDTO.setCreatedBy(savedHolidaySchedule.getCreatedBy());
        responseDTO.setCreatedByName(createdByName);  // Set createdByName
        responseDTO.setCreatedAt(savedHolidaySchedule.getCreatedAt());
        responseDTO.setUpdatedBy(savedHolidaySchedule.getUpdatedBy());
        responseDTO.setModifiedByName(null);
        responseDTO.setUpdatedAt(savedHolidaySchedule.getUpdatedAt());
        responseDTO.setIsActive(savedHolidaySchedule.getIsActive());

        return responseDTO;
    }


    private Long findEmployeeIdByUsername(String username) {

        User user = userRepository.findByUsername(username);

        // Check if the user or employee exists to avoid NullPointerException
        if (user != null && user.getEmployee() != null) {
            // Return the employee ID
            return user.getEmployee().getEmpId();
        }

        // If user or employee is not found, handle accordingly
        throw new IllegalArgumentException("User or Employee not found for username: " + username);
    }
// name

    private String fetchUserNameById(Long employeeId) {
        User user = userRepository.findByEmployee_EmpId(employeeId)
                .orElseThrow(() -> new IllegalArgumentException("User not found for employee ID: " + employeeId));
        return user.getUsername();  // Assuming you want the username
    }


    //update
    public HolidaySchedule updateHolidaySchedule(Long id, HolidayScheduleDTO holidayScheduleDTO) {
        Optional<HolidaySchedule> optionalHolidaySchedule = holidayScheduleRepository.findById(id);
        if (optionalHolidaySchedule.isPresent()) {
            HolidaySchedule holidaySchedule = optionalHolidaySchedule.get();
            holidaySchedule.setDate(holidayScheduleDTO.getDate());
            holidaySchedule.setHoliday(holidayScheduleDTO.getHoliday());
            holidaySchedule.setIsOptional(holidayScheduleDTO.getIsOptional());


            // Fetch the logged-in user's details
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            Long employeeId = findEmployeeIdByUsername(username);

            holidaySchedule.setUpdatedBy(employeeId);
            return holidayScheduleRepository.saveAndFlush(holidaySchedule);
        } else {
            throw new IllegalArgumentException("Holiday schedule not found with ID: " + id);
        }

    }


//delete
public void softDeleteHolidaySchedule(Long id) {
    Optional<HolidaySchedule> optionalHolidaySchedule = holidayScheduleRepository.findById(id);
    if (optionalHolidaySchedule.isPresent()) {
        HolidaySchedule holidaySchedule = optionalHolidaySchedule.get();
        holidaySchedule.setIsActive(false); // Update is_active to 0
        holidayScheduleRepository.saveAndFlush(holidaySchedule);
    } else {
        throw new IllegalArgumentException("Holiday schedule not found with ID: " + id);
    }
}  // pagination

    public Page<HolidayScheduleDTO> getHolidaySchedules(LocalDate fromDate, LocalDate toDate, String query, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Order.desc("id")));
        Page<HolidaySchedule> holidaySchedules;

        // Determine which holiday schedules to fetch based on provided filters
        if (fromDate != null && toDate != null && query != null && !query.isEmpty()) {
            holidaySchedules = holidayScheduleRepository.findByDateBetweenAndHolidayNameContaining(fromDate, toDate, query, pageable);
        } else if (fromDate != null && toDate != null) {
            holidaySchedules = holidayScheduleRepository.findByDateBetween(fromDate, toDate, pageable);
        } else if (query != null && !query.isEmpty()) {
            holidaySchedules = holidayScheduleRepository.findByHolidayNameContaining(query, pageable);
        } else {
            holidaySchedules = holidayScheduleRepository.findAll(pageable);
        }

        // Map the HolidaySchedule to HolidayScheduleDTO
        return holidaySchedules.map(holidaySchedule -> {
            HolidayScheduleDTO dto = new HolidayScheduleDTO();
            dto.setId(holidaySchedule.getId());
            dto.setDate(holidaySchedule.getDate());
            dto.setHoliday(holidaySchedule.getHoliday());
            dto.setIsOptional(holidaySchedule.getIsOptional());
            dto.setIsActive(holidaySchedule.getIsActive());
            dto.setCreatedAt(holidaySchedule.getCreatedAt());
            dto.setUpdatedAt(holidaySchedule.getUpdatedAt()); // Set updatedAt from holidaySchedule

            // Fetch createdBy details
            Long createdById = holidaySchedule.getCreatedBy();
            if (createdById != null) {
                Employee createdBy = employeeRepository.findById(createdById).orElse(null);
                if (createdBy != null) {
                    dto.setCreatedByName(createdBy.getEmpName());
                    dto.setCreatedByImage(createdBy.getImgUre());
                }
                dto.setCreatedBy(createdById); // Add the createdBy ID
            }

            // Fetch updatedBy details
            Long updatedById = holidaySchedule.getUpdatedBy();
            if (updatedById != null) {
                Employee updatedBy = employeeRepository.findById(updatedById).orElse(null);
                if (updatedBy != null) {
                    dto.setModifiedByName(updatedBy.getEmpName());
                    dto.setModifiedByImage(updatedBy.getImgUre());
                }
                dto.setUpdatedBy(updatedById); // Add the updatedBy ID
            }

            return dto;
        });
    }



    /// read excel data and save according filed in database

   public void uploadExcelFile(MultipartFile file) {
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            List<HolidayScheduleExcelDTO> holidaySchedules = new ArrayList<>();

            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                try {
                HolidayScheduleExcelDTO dto = new HolidayScheduleExcelDTO();
                dto.setDate(row.getCell(0).getLocalDateTimeCellValue().toLocalDate());
                dto.setHoliday(row.getCell(1).getStringCellValue());
                dto.setIsOptional(row.getCell(2).getBooleanCellValue());
                holidaySchedules.add(dto);
                } catch (IllegalArgumentException e) {
                    throw e;  // Re-throw to be handled in the controller
                } catch (Exception e) {
                    throw new IllegalArgumentException("Error processing row " + (row.getRowNum() + 1) + ": " + e.getMessage());
                }
            }

            saveHolidaySchedules(holidaySchedules);
        } catch (IOException e) {
        	log.error("Exception occurred while uploadExcelFile", e);
        }
    }

    private void saveHolidaySchedules(List<HolidayScheduleExcelDTO> holidaySchedules) {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        Long employeeId = findEmployeeIdByUsername(username);

        String createdByName = fetchUserNameById(employeeId);

        for (HolidayScheduleExcelDTO dto : holidaySchedules) {
            HolidaySchedule holidaySchedule = new HolidaySchedule();
            holidaySchedule.setDate(dto.getDate());
            holidaySchedule.setHoliday(dto.getHoliday());
            holidaySchedule.setIsOptional(dto.getIsOptional());
            holidaySchedule.setCreatedBy(employeeId);
            holidaySchedule.setCreatedAt(LocalDateTime.now());
            holidayScheduleRepository.saveAndFlush(holidaySchedule);
        }
    }

}



