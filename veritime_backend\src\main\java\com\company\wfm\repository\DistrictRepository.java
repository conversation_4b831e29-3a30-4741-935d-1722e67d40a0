package com.company.wfm.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.District;
import com.company.wfm.entity.Province;

@Repository
public interface DistrictRepository extends JpaRepository<District, Long> {
    Optional<District> findByNameAndProvince(String name, Province province);
    Optional<District> findByIdAndProvince(Long id, Province province);
    Optional<District> findByName(String name);
}

