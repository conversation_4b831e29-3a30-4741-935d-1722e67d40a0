package com.company.wfm.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.AttendanceAuditService;
import com.company.wfm.service.TeamAttendanceService;

@RestController
@RequestMapping("/api/v1/attendance")
@CrossOrigin(origins = "*")
public class TeamAttendanceController {

    private static final Logger logger = LoggerFactory.getLogger(TeamAttendanceController.class);

    @Autowired
    private TeamAttendanceService teamAttendanceService;
    
    @Autowired
    private AttendanceAuditService attendanceService;    

    @PostMapping("/teamAttendance")
    public ResponseEntity<?> getTeamAttendance(@RequestBody TeamAttendanceRequest request) {
        try {

            // Log the incoming request details
            logger.info("Received teamAttendance request: {}", request);

            List<Map<String, Object>> attendanceList = teamAttendanceService.getTeamAttendanceRequest(
                    request.getUserid(),
                    request.getFromDate(),
                    request.getToDate()
            );

            if (attendanceList.isEmpty()) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("No team members assigned to you or no attendance data available.");
            }

            // Log the successful response
            logger.info("Attendance data fetched successfully: {}", attendanceList);
            return ResponseEntity.ok(attendanceList);
        } catch (DataAccessException e) {
            // Log the exception details
            logger.error("Database error occurred while fetching team attendance data for request: {}", request, e);
            // Log the exception for debugging purposes
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("A database error occurred while fetching attendance data.");
        } catch (Exception e) {
            // Log the exception details
            logger.error("Unexpected error occurred while processing team Attendance request: {}", request, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An unexpected error occurred. Please try again later.");
        }
    }

    

    @PostMapping("/attendanceSummary")
    public Map<String, Object> getAttendanceSummary(@RequestBody TeamAttendanceRequest request) {
        return teamAttendanceService.getAttendanceSummary(request.getUserid(), request.getFromDate(), request.getToDate());
    }
    
    @Async
    @GetMapping("/process-daily")
    public void processDailyAttendance(@RequestParam("date") String dateStr) {
        	 LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
             attendanceService.processDailyAttendance(date);
     }
}

class TeamAttendanceRequest {
    private List<Long> userid;
    private String fromDate;
    private String toDate;

    // Getters and Setters
    public List<Long> getUserid() {
        return userid;
    }

    public void setUserid(List<Long> userid) {
        this.userid = userid;
    }

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    @Override
    public String toString() {
        return "TeamAttendanceRequest{" +
                "userid=" + userid +
                ", fromDate='" + fromDate + '\'' +
                ", toDate='" + toDate + '\'' +
                '}';
    }
}