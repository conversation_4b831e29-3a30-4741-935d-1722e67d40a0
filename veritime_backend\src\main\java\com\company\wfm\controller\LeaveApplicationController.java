package com.company.wfm.controller;


import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.EncryptedResponse;
import com.company.wfm.dto.LeaveApplicationDTO;
import com.company.wfm.entity.LeaveMaster;
import com.company.wfm.scheduler.LeaveJobSchedulerService;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.service.impl.LeaveApplicationService;
import com.company.wfm.util.EncryptionUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api/v1/")
@CrossOrigin(origins = "*")
public class LeaveApplicationController {

	private static final Logger logger = LoggerFactory.getLogger(LeaveApplicationController.class);

	@Autowired
    private LeaveApplicationService leaveApplicationService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserTokenService tokenService;

	@Autowired
	private EncryptionUtil encryptionUtil;

    @Autowired
    private LeaveJobSchedulerService leaveJobSchedulerService;
    
    /**
     *
     * @param leaveApplicationDTO
     * @param file
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("employees/leave/applications")
    public ResponseEntity<EncryptedResponse> applyLeaveOnEmployeeBehalf(
            @RequestParam("leaveApplicationDTO") String leaveApplicationDTO,
            @RequestPart(value = "file", required = false) MultipartFile file,
            HttpServletRequest request) throws Exception {
        try {
            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

            LeaveApplicationDTO dto = objectMapper.readValue(leaveApplicationDTO, LeaveApplicationDTO.class);

            // Prepare the header map to log all header values
            Map<String, String> headersMap = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                headersMap.put(headerName, headerValue); // Add each header to the map
            }

            // Prepare file metadata for logging
            Map<String, Object> fileMetadata = new HashMap<>();
            if (file != null) {
                fileMetadata.put("fileName", file.getOriginalFilename());
                fileMetadata.put("fileSize", file.getSize());
                fileMetadata.put("fileType", file.getContentType());
            }

            // Applying the leave
            if (dto.getEmpId() == null) {
                leaveApplicationService.applyLeave(loggedInEmpId, dto, loggedInEmpId, file, request);
            } else {
                leaveApplicationService.applyLeave(dto.getEmpId(), dto, loggedInEmpId, file, request);
            }

            // Encrypt the success message
            String encryptedSuccessMessage = encryptionUtil.encrypt("Leave application submitted successfully",encryptionUtil.generateKey());

            // Build log response
            Map<String, Object> log = new HashMap<>();
            log.put("request", objectMapper.readTree(leaveApplicationDTO));
            log.put("file", fileMetadata);  // Log the file metadata
            log.put("header", headersMap);  // Log all headers
            log.put("response", encryptedSuccessMessage);

            // Prepare response
            EncryptedResponse encryptedResponse = new EncryptedResponse(encryptedSuccessMessage);
            encryptedResponse.setLog(log);

            // Return response with log
            return ResponseEntity.ok(encryptedResponse);
        }catch (IllegalStateException e) {
            // Log specific exception
            logger.error("IllegalStateException occurred: {}", e.getMessage(), e);

            String encryptedErrorMessage = encryptionUtil.encrypt(e.getMessage(),encryptionUtil.generateKey());

            Map<String, Object> log = new HashMap<>();
            log.put("request", leaveApplicationDTO);
            log.put("file", file != null ? file.getOriginalFilename() : null);
            log.put("header", request.getHeader("Authorization"));
            log.put("response", encryptedErrorMessage);

            EncryptedResponse encryptedResponse = new EncryptedResponse(encryptedErrorMessage);
            encryptedResponse.setLog(log);

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(encryptedResponse);
        } catch (Exception e) {
        	logger.error("An error occurred while processing your request",e);
            // Handle error cases
            String errorMessage = e instanceof IllegalArgumentException ? e.getMessage() : "An error occurred while processing your request";
            String encryptedErrorMessage = encryptionUtil.encrypt(errorMessage,encryptionUtil.generateKey());

            // Build log response for error
            Map<String, Object> log = new HashMap<>();
            log.put("request", leaveApplicationDTO);
            log.put("file", file != null ? file.getOriginalFilename() : null);  // Log the file name if present
            log.put("header", request.getHeader("Authorization"));
            log.put("response", encryptedErrorMessage);

            // Return error response with log
            EncryptedResponse encryptedResponse = new EncryptedResponse(encryptedErrorMessage);
            encryptedResponse.setLog(log);

            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(encryptedResponse);
        }
    }




    // ritesh work start

    @PostMapping("employees/leave/save")
    public ResponseEntity<String> saveLeave(@RequestBody LeaveMaster leaveMaster) {
        try {
            LeaveMaster savedLeave = leaveApplicationService.saveLeave(leaveMaster);
            return ResponseEntity.ok("The leave type added successfully");

        } catch (RuntimeException e) {
            // Handle custom business logic errors (thrown from service)
            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(e.getMessage());

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred. Please try again later.");
        }
    }


    @GetMapping("employees/leave/getAll")
    public ResponseEntity<List<LeaveMaster>> getAllLeaves(
            @RequestParam(value = "isActive", required = false) String isActive) {
        try {
            // Declare leaves variable here once
            List<LeaveMaster> leaves = null;

            // Check the value of isActive and assign the appropriate leaves list
            if (isActive == null || isActive.equalsIgnoreCase("true") || isActive.equals("1")) {
                // Default case: Fetch active leaves
                leaves = leaveApplicationService.getAllLeaves(true);
            } else if (isActive.equalsIgnoreCase("false") || isActive.equals("0")) {
                // Fetch inactive leaves
                leaves = leaveApplicationService.getAllLeaves(false);
            } else if (isActive.equals("-1")) {
                // Fetch all leaves when isActive = -1
                leaves = leaveApplicationService.getAllLeaves(null);
            } else {
                return ResponseEntity.badRequest().body(null); // Invalid input case
            }

            // Return the response with the leaves
            return ResponseEntity.ok(leaves);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }



    //update

    @PutMapping("employees/leave/{id}")
    public ResponseEntity<?> updateLeave(@PathVariable("id") Long id, @RequestBody LeaveMaster leaveMaster) {
        try {
            LeaveMaster updatedLeave = leaveApplicationService.updateLeave(id, leaveMaster);
            return ResponseEntity.ok(updatedLeave);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    /**
     *
     * @param id
     * @return
     */
    @DeleteMapping("employees/leave/{id}")
    public ResponseEntity<String> deleteLeave(@PathVariable Long id) {
        try {
            boolean isDeleted = leaveApplicationService.deleteLeave(id);
            if (isDeleted) {
                return ResponseEntity.ok("Leave record deactivated successfully.");
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Leave record not found.");
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred while deactivating the leave record.");
        }
    }

    @GetMapping("employees/leave/getAllLeaveMaster")
    public ResponseEntity<List<LeaveMaster>> getAllLeaves(@RequestParam(name = "isActive") boolean isActive) {
        try {
            List<LeaveMaster> leaves = leaveApplicationService.getAllLeavesByActive(isActive);
            return ResponseEntity.ok(leaves);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @Async
    @GetMapping("/credit/job")
    public ResponseEntity<String> triggerLeaveCreditJob() {
        leaveJobSchedulerService.runLeaveCreditingJob();
        return ResponseEntity.ok("Leave crediting job triggered successfully.");
    }

}
