package com.company.wfm.service.impl;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.company.wfm.dto.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.Designation;
import com.company.wfm.entity.District;
import com.company.wfm.entity.DocumentMaster;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.EmployeeDocument;
import com.company.wfm.entity.EmployeeLeaveBalance;
import com.company.wfm.entity.LeaveMaster;
import com.company.wfm.entity.Province;
import com.company.wfm.entity.SubDistrict;
import com.company.wfm.entity.TimeSlot;
import com.company.wfm.entity.User;
import com.company.wfm.repository.BranchJDBCRepository;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.DesignationRepository;
import com.company.wfm.repository.DistrictRepository;
import com.company.wfm.repository.DocumentMasterRepository;
import com.company.wfm.repository.EmployeeDocumentRepository;
import com.company.wfm.repository.EmployeeLeaveBalanceRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.FeederHospitalRepository;
import com.company.wfm.repository.LeaveMasterRepository;
import com.company.wfm.repository.ProvinceRepository;
import com.company.wfm.repository.SubDistrictRepository;
import com.company.wfm.repository.TimeSlotRepository;
import com.company.wfm.repository.UserRepository;
import com.company.wfm.scheduler.EmployeeScheduleExecutor;
import com.company.wfm.service.AmazonS3Service;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.EmailTemplateService;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.util.CommonConstant;
import com.company.wfm.vo.EmployeeVO;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import static org.apache.commons.collections4.MapUtils.getLongValue;
import static org.apache.poi.util.Configurator.getIntValue;


@Service
public class EmployeeService {


    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private DistrictRepository districtRepository;

    @Autowired
    private SubDistrictRepository subDistrictRepository;

    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private DesignationRepository designationRepository;

    @Autowired
    private EmployeeLeaveBalanceRepository employeeLeaveBalanceRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Autowired
    private EmployeeScheduleExecutor employeeScheduleExecutor;

    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private BranchJDBCRepository branchJDBCRepository;

    @Autowired
    private LeaveMasterRepository leaveMasterRepository;

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailTemplateService template;

    @Autowired
    private TimeSlotRepository timeSlotRepository;

    @Autowired
    private EmailService emailSender;

    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    @Autowired
    private CommonNotificationService notificationService;

    @Autowired
    private EmployeeDocumentRepository employeeDocumentRepository;

    @Autowired
    private DocumentMasterRepository documentMasterRepository;

    @Autowired
    AmazonS3Service s3Service;

    @Autowired
    private ProvinceRepository provinceRepository;

    @Autowired
    private FeederHospitalRepository feederHospitalRepository;




    private static final Logger log = LoggerFactory.getLogger(EmployeeService.class);


    private static final String BASE_UPLOAD_DIRECTORY = "uploads/";
    private static final List<String> ALLOWED_FILE_TYPES = Arrays.asList("image/jpeg");

    @Transactional
    public EmployeeDetailsDto createEmployee(EmployeeDTO employeeDTO, MultipartFile file) throws IOException {
        // Check if email ID already exists
        if (employeeRepository.existsByEmail(employeeDTO.getEmail())) {
            throw new IllegalArgumentException("Email ID already exists");
        }

        if (employeeRepository.existsByEmpCode(employeeDTO.getEmpCode())) {
            throw new IllegalArgumentException("Employee Code already exists");
        }

        if (employeeRepository.existsByUid(employeeDTO.getBiometricID())) {
            throw new IllegalArgumentException("BiometricId already exists");
        }

        if (employeeRepository.existsByNationalId(employeeDTO.getNationalId())) {
            throw new IllegalArgumentException("National ID already exists");
        }


        // Fetch the department and designation based on their IDs
        Department department = departmentRepository.findById(employeeDTO.getDepartmentId())
                .orElseThrow(() -> new EntityNotFoundException("Department not found"));
        Designation designation = designationRepository.findById(employeeDTO.getDesignationId())
                .orElseThrow(() -> new EntityNotFoundException("Designation not found"));

        Employee employee = new Employee();
        BeanUtils.copyProperties(employeeDTO, employee);
        employee.setEmpId(employeeDTO.getEmpId());
        // employee.setCompanyId(employeeDTO.getCompanyId());
        Long companyId;
        Long empId = tokenService.getEmployeeIdFromToken();
        if (employeeDTO.getCompanyId() != null && employeeDTO.getCompanyId() > 0) {
            companyId = employeeDTO.getCompanyId();
        } else {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String loggedInUsername = authentication.getName();

            User loggedInUser = userRepository.findByUsername(loggedInUsername);
            if (loggedInUser == null) {
                throw new EntityNotFoundException("Logged-in user not found");
            }
            companyId = loggedInUser.getEmployee().getCompanyId();
        }
        employee.setCompanyId(companyId);

        employee.setDepartment(department); // Set the full department object
        employee.setDesignation(designation); // Set the full designation object
        employee.setEmpCode(employeeDTO.getEmpCode());
        employee.setEmpName(employeeDTO.getEmpName());
        employee.setUid(employeeDTO.getBiometricID());
        // employee.setIdNo(employeeDTO.getIdNo());
        // employee.setOrgId(employeeDTO.getOrgId());
        employee.setUpperId(employeeDTO.getUpperId());
        employee.setRegionId(employeeDTO.getSuburb());
        employee.setCountryId(employeeDTO.getCountryId());
        employee.setProvinceId(employeeDTO.getProvinceId());
        employee.setCity(employeeDTO.getCity());// in city district id store
        employee.setUpperId(employeeDTO.getReportingPerson());
        employee.setWorkZone(employeeDTO.getWorkZone());
        employee.setHireDate(employeeDTO.getHireDate());
        employee.setGender(employeeDTO.getGender());
        employee.setBirthday(employeeDTO.getBirthday());
        // employee.setNation(employeeDTO.getNation());
        employee.setNation("South Africa");
        employee.setMarried(employeeDTO.getMarried());
        employee.setPhoneNo(employeeDTO.getPhoneNo());
        employee.setMobileNo(employeeDTO.getMobileNo());
        employee.setEmail(employeeDTO.getEmail());
        employee.setNativePlace(employeeDTO.getNativePlace());
        employee.setZipCode(employeeDTO.getZipCode());
        employee.setIsHistory(employeeDTO.getIsHistory());
        employee.setInService(employeeDTO.getInService());
        employee.setRemark(employeeDTO.getRemark());
        employee.setCreatedBy(Long.valueOf(empId));
        employee.setCreatedTime(LocalDateTime.now());
//        employee.setUpdatedBy(employeeDTO.getUpdatedBy());
//        employee.setUpdatedTime(employeeDTO.getUpdatedTime());
//        employee.setVersion(employeeDTO.getVersion());
        employee.setNativeLanguage(employeeDTO.getNativeLanguage());
        //  employee.setForeignLanguages(employeeDTO.getForeignLanguages());
        if (employeeDTO.getForeignLanguages() != null) {
            String foreignLanguages = String.join(",", employeeDTO.getForeignLanguages());
            employee.setForeignLanguages(foreignLanguages);
        }
        employee.setWorkYears(employeeDTO.getWorkYears());
        employee.setProbationPeriod(employeeDTO.getProbationPeriod());
        employee.setGraduateSchool(employeeDTO.getGraduateSchool());
        employee.setGraduateTime(employeeDTO.getGraduateTime());
        employee.setHighestDegree(employeeDTO.getHighestDegree());
        employee.setImgUre(employeeDTO.getImgUre());
        employee.setEmergencyContact1(employeeDTO.getEmergencyContact1());
        employee.setEmergencyContact2(employeeDTO.getEmergencyContact2());
        employee.setAlternateEmail(employeeDTO.getAlternateEmail());
        employee.setEmergencyContactname1(employeeDTO.getEmergencyContactname1());
        employee.setEmergencyContactname2(employeeDTO.getEmergencyContactname2());
        employee.setAlternateNumber(employeeDTO.getAlternateNumber());
        employee.setEthnicity(employeeDTO.getEthnicity());
        employee.setNationalId(employee.getNationalId());
       /* byte[] imageBytes = Base64.getDecoder().decode(employeeDTO.getBase64EncodeFile());
        uploadProfilePic(imageBytes, String.valueOf(employeeDTO.getCompanyId()), employeeDTO.getFileName(), employee);*/

        //file work.

       /* if (employeeDTO.getBase64EncodeFile() != null && !employeeDTO.getBase64EncodeFile().isEmpty()) {
            byte[] imageBytes = Base64.getDecoder().decode(employeeDTO.getBase64EncodeFile());
           // uploadProfilePic(imageBytes, String.valueOf(employeeDTO.getCompanyId()), employeeDTO.getFileName(), employee);
            validateFile(imageBytes, employeeDTO.getFileName());
            String uniqueFileName = uploadFileToS3(imageBytes, employeeDTO.getFileName(), employeeDTO.getCompanyId());
            employee.setImgUre(uniqueFileName);

        } else {
            log.warn("Base64 encoded file is missing. Skipping profile picture upload.");
        }*/
        if (file != null && !file.isEmpty()) {
            // Upload file to S3 bucket
            String fileName = file.getOriginalFilename(); // Get the file name
            long fileSize = file.getSize();
            String fileContentType = file.getContentType();

            // Validate file type (allow only image/jpeg)
            if (fileContentType == null || !ALLOWED_FILE_TYPES.contains(fileContentType)) {
                throw new IllegalArgumentException("Only .jpeg files are allowed.");
            }

            // Validate file size (15 KB to 250 KB)
            if (fileSize < 15 * 1024 || fileSize > 250 * 1024) {
                throw new IllegalArgumentException("File size must be between 15 KB and 250 KB.");
            }
            String uniqueFileName = "employee/" + companyId + "/" + System.currentTimeMillis() + "_" + fileName;

            s3Service.uploadFile(file, uniqueFileName);

            // Set the file name to be saved in the database
            employee.setImgUre(uniqueFileName);
        }
        // Step 1: Check if the branchId is provided in the request body
        if (employeeDTO.getBranchId() != null && employeeDTO.getBranchId() > 0) {
            BranchDTO branchDTO = branchJDBCRepository.validateHospitalBranch(employeeDTO.getBranchId(), employeeDTO.getCompanyId());
            if (ObjectUtils.isEmpty(branchDTO)) {
                throw new RuntimeException("Branch and company did not match");
            }
            // branchId = employeeDTO.getBranchId(); // Set branchId from DTO
            Branch branch = branchRepository.findById(employeeDTO.getBranchId())
                    .orElseThrow(() -> new EntityNotFoundException("Branch not found"));
            employee.setBranch(branch);
        } else {
            // Step 2: If branchId is not provided, retrieve from logged-in user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String loggedInUsername = authentication.getName();

            User loggedInUser = userRepository.findByUsername(loggedInUsername);
            if (loggedInUser != null && loggedInUser.getEmployee() != null) {
                Employee loggedInEmployee = loggedInUser.getEmployee();

                // Step 3: Check if the employee has a valid branch
                if (loggedInEmployee.getBranch() != null) {
                    Branch loggedInBranch = loggedInEmployee.getBranch();
                    //   employee.setBranch(loggedInEmployee.getBranch());
                    //   branchId = loggedInEmployee.getBranch().getId(); // Set branchId from the employee's branch
                    Branch branch = new Branch();
                    branch.setId(loggedInEmployee.getBranch().getId());
                    branch.setBranchName(loggedInBranch.getBranchName());
                    employee.setBranch(branch);
                } else {
                    log.warn("Logged-in user does not have a valid branch. Branch ID will be set to null.");
                    // Optionally, set a flag or log this scenario
                }
            } else {
                log.warn("Logged-in user does not have a valid employee. Branch ID will be set to null.");
            }
        }

        // employee.setBranchId(branchId);


        employee.setUnitNumber(employeeDTO.getUnitNumber());
        employee.setStreet(employeeDTO.getStreet());


        employee.setDefaultTimeSlotId(employeeDTO.getDefaultTimeSlotId());
        employee.setLeaveOnDays(employeeDTO.getLeaveOnDays().stream().map(Object::toString).collect(Collectors.joining(", ")));
        Date hireDate = employee.getHireDate();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String hireDateString = sdf.format(hireDate);


        employeeRepository.saveAndFlush(employee);
        List<Integer> leaveOnDaysIntegers = convertLeaveOnDaysToIntegers(employeeDTO.getLeaveOnDays().stream().map(Object::toString).collect(Collectors.joining(", ")));

        for (Integer leaveDay : leaveOnDaysIntegers) {
            employeeScheduleExecutor.executeEmployeeSchedule(employee.getEmpId(), hireDateString, leaveDay.toString(), "1");
        }

        //email and notification work
        Long savedEmpId = employee.getEmpId();
        Long upperId = employeeRepository.findUpperIdByEmpId(savedEmpId);
        if (upperId != null && upperId != 0L) {
            // Notify the supervisor
            notifySupervisor(savedEmpId, employee.getEmpName());
        }

        //  employeeScheduleExecutor.executeEmployeeSchedule(employee.getEmpId(), hireDateString, employeeDTO.getLeaveOnDays(), "1");
        // return employee;
        return mapToEmployeeDetailsDto(employee);
    }


    private void notifySupervisor(Long empId, String empName) {

        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        // Find the supervisor (upper) ID for this saved employee
        Long upperId = employeeRepository.findUpperIdByEmpId(empId);
        if (upperId == null) {
            //  throw new IllegalStateException("No supervisor found for employee ID: " + empId);
            log.error("No supervisor found for employee ID: " + loggedInEmpId + " to send notification");
            return;
        }

        // Retrieve supervisor information
        employeeRepository.findById(upperId)
                .orElseThrow(() -> new EntityNotFoundException("Supervisor not found"));

        String notificationMessage;
        String emailMessage;

        notificationMessage = "A new employee has been added with ID: " + empId + " and name: " + empName;
        emailMessage = "A new employee has been added with ID: " + empId + " and name: " + empName;

        // Send Email Notification to Supervisor
        String emailId = employeeRepository.getReferenceById(upperId).getEmail();
        EmailData data = new EmailData();
        data.setEmailId(emailId);
        data.setSubject("New Employee Created");
        data.setMessage(emailMessage);
        emailService.sendEmail(data);

        // notification
        // Send notification to the employee
        NotificationDTO notificationDTO = new NotificationDTO(
                "New Employee Created",
                notificationMessage,
                "employee",
                String.valueOf(upperId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(upperId, notificationDTO.getTitle(), notificationDTO.getBody(), "employee", String.valueOf(loggedInEmpId));
    }


    private EmployeeDetailsDto mapToEmployeeDetailsDto(Employee employee) {
        EmployeeDetailsDto employeeDetailsDto = new EmployeeDetailsDto();
        BeanUtils.copyProperties(employee, employeeDetailsDto);
        String companyName = feederHospitalRepository.findCompanyNameById(employee.getCompanyId());

        // Fetch district name
        String districtName = districtRepository.findById(Long.valueOf(employee.getCity()))
                .map(District::getName)
                .orElse("Unknown District");

        // Fetch sub-district name
        String subDistrictName = subDistrictRepository.findById(employee.getRegionId())
                .map(SubDistrict::getName)
                .orElse("Unknown Sub-District");
        employeeDetailsDto.setCompanyName(companyName);


        // Convert hireDate to LocalDate
        Date hireDate = employee.getHireDate();
        BigDecimal probationPeriod = employee.getProbationPeriod(); // Fetch probation period in months

        LocalDateTime probationEndDate = null;
        String isInProbation = null; // Default value

        if (hireDate != null && probationPeriod != null) {
            LocalDate localHireDate = hireDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            probationEndDate = localHireDate.plusMonths(probationPeriod.intValue()).atStartOfDay();

           /* // Check if the employee is still in the probation period
            if (probationEndDate.isAfter(LocalDateTime.now())) {
                isInProbation = "Yes"; // Employee is still in probation
            }*/

            // Check if the employee is still in the probation period
            isInProbation = probationEndDate.isAfter(LocalDateTime.now()) ? "Yes" : "No";
        }

        // Set additional fields for EmployeeDetailsDto from related entities
        employeeDetailsDto.setDeptName(employee.getDepartment() != null ? employee.getDepartment().getDepartmentName() : null);
        employeeDetailsDto.setDesignationName(employee.getDesignation() != null ? employee.getDesignation().getName() : null);
        employeeDetailsDto.setDepartmentId(employee.getDepartment() != null ? employee.getDepartment().getDepartmentId() : null);
        employeeDetailsDto.setDesignationId(employee.getDesignation() != null ? employee.getDesignation().getId() : null);
        employeeDetailsDto.setBranchId(employee.getBranch() != null ? employee.getBranch().getId() : null);
        employeeDetailsDto.setBranchName(employee.getBranch() != null ? employee.getBranch().getBranchName() : null);
        employeeDetailsDto.setRole(employee.getDesignation().getRole());
        employeeDetailsDto.setEmpCode(employee.getEmpCode());
        employeeDetailsDto.setEmpName(employee.getEmpName());
        employeeDetailsDto.setUid(employee.getUid());
        employeeDetailsDto.setIdNo(employee.getIdNo());
        employeeDetailsDto.setOrgId(employee.getOrgId());
        employeeDetailsDto.setUpperId(employee.getUpperId());
        // Check if upperId is not null before calling findById()
        if (employee.getUpperId() != null) {
            Employee upperEmployee = employeeRepository.findById(employee.getUpperId()).orElse(null);
            if (upperEmployee != null && upperEmployee.getEmpName() != null) {
                employeeDetailsDto.setUpperName(upperEmployee.getEmpName());
            } else {
                employeeDetailsDto.setUpperName("null");
            }
        } else {
            employeeDetailsDto.setUpperName("null");
        }

        employeeDetailsDto.setRegionId(employee.getRegionId());
        employeeDetailsDto.setSubDistrictName(subDistrictName);
        employeeDetailsDto.setCountryId(employee.getCountryId());
        employeeDetailsDto.setProvinceId(employee.getProvinceId());
        if (employee.getProvinceId() != null) {
            Province province = provinceRepository.findById(employee.getProvinceId()).orElse(null);
            if (province != null && province.getName() != null) {
                employeeDetailsDto.setProvinceName(province.getName());
            } else {
                employeeDetailsDto.setProvinceName("null");
            }
        } else {
            employeeDetailsDto.setProvinceName("null");
        }
        employeeDetailsDto.setCity(employee.getCity());
        employeeDetailsDto.setDistrictName(districtName);
        employeeDetailsDto.setHireDate(employee.getHireDate());
        employeeDetailsDto.setGender(employee.getGender());
        employeeDetailsDto.setBirthday(employee.getBirthday());
        employeeDetailsDto.setNation(employee.getNation());
        employeeDetailsDto.setMarried(employee.getMarried());
        employeeDetailsDto.setPhoneNo(employee.getPhoneNo());
        employeeDetailsDto.setMobileNo(employee.getMobileNo());
        employeeDetailsDto.setEmail(employee.getEmail());
        employeeDetailsDto.setNativePlace(employee.getNativePlace());
        employeeDetailsDto.setZipCode(employee.getZipCode());
        employeeDetailsDto.setHistory(employee.getIsHistory());
        employeeDetailsDto.setInService(employee.getInService());
        employeeDetailsDto.setRemark(employee.getRemark());
        employeeDetailsDto.setCreatedBy(employee.getCreatedBy());
        // Check and set createdBy and updatedBy
        if (employee.getCreatedBy() != null) {
            Employee createByName = employeeRepository.findById(employee.getCreatedBy()).orElse(null);
            if (createByName != null) {
                employeeDetailsDto.setCreateByName(createByName.getEmpName());
            } else {
                employeeDetailsDto.setCreateByName("null");
            }
        } else {
            employeeDetailsDto.setCreateByName("null");
        }
        employeeDetailsDto.setCreatedTime(employee.getCreatedTime());
        employeeDetailsDto.setUpdatedBy(employee.getUpdatedBy());
        if (employee.getUpdatedBy() != null) {
            Employee updateByName = employeeRepository.findById(employee.getUpdatedBy()).orElse(null);
            if (updateByName != null) {
                employeeDetailsDto.setUpdateByName(updateByName.getEmpName());
            } else {
                employeeDetailsDto.setUpdateByName("null");
            }
        } else {
            employeeDetailsDto.setUpdateByName("null");
        }
        employeeDetailsDto.setUpdatedTime(employee.getUpdatedTime());
        employeeDetailsDto.setVersion(employee.getVersion());
        employeeDetailsDto.setNativeLanguage(employee.getNativeLanguage());
        employeeDetailsDto.setForeignLanguages(employee.getForeignLanguages());
        employeeDetailsDto.setLeaveOnDays(employee.getLeaveOnDays());
        employeeDetailsDto.setWorkYears(employee.getWorkYears());
        employeeDetailsDto.setGraduateSchool(employee.getGraduateSchool());
        employeeDetailsDto.setGraduateTime(employee.getGraduateTime());
        employeeDetailsDto.setHighestDegree(employee.getHighestDegree());
        employeeDetailsDto.setImgUre(employee.getImgUre());
        employeeDetailsDto.setDefaultTimeSlot(employee.getDefaultTimeSlotId());
        if (employee.getDefaultTimeSlotId() != null) {
            TimeSlot timeSlot = timeSlotRepository.findById(employee.getDefaultTimeSlotId()).orElse(null);
            if (timeSlot != null) {
                // Safely set startTime and endTime if they are not null
                employeeDetailsDto.setStartTime(timeSlot.getStartTime() != null ? timeSlot.getStartTime() : null);
                employeeDetailsDto.setEndTime(timeSlot.getEndTime() != null ? timeSlot.getEndTime() : null);
            } else {
                // Set startTime and endTime to null if timeSlot is not found
                employeeDetailsDto.setStartTime(null);
                employeeDetailsDto.setEndTime(null);
            }
        } else {
            // Set startTime and endTime to null if defaultTimeSlotId is null
            employeeDetailsDto.setStartTime(null);
            employeeDetailsDto.setEndTime(null);
        }


        employeeDetailsDto.setUnitNumber(employee.getUnitNumber());
        employeeDetailsDto.setStreet(employee.getStreet());
        employeeDetailsDto.setAlternateNumber(employee.getAlternateNumber());
        employeeDetailsDto.setAlternateEmail(employee.getAlternateEmail());
        employeeDetailsDto.setEmergencyContact1(employee.getEmergencyContact1());
        employeeDetailsDto.setEmergencyContact2(employee.getEmergencyContact2());
        employeeDetailsDto.setEmergencyContactname1(employee.getEmergencyContactname1());
        employeeDetailsDto.setEmergencyContactname2(employee.getEmergencyContactname2());
        employeeDetailsDto.setEthnicity(employee.getEthnicity());
        employeeDetailsDto.setNationalId(employee.getNationalId());
        employeeDetailsDto.setProbationPeriod(employee.getProbationPeriod());
        employeeDetailsDto.setProbationEndDate(probationEndDate);
        employeeDetailsDto.setIsInProbation(isInProbation);

        return employeeDetailsDto;
    }

    public boolean uploadProfilePic(byte[] imageBytes, String hospitalId, String fileName, Employee employee) throws IOException {

        Path folderPath = Paths.get(BASE_UPLOAD_DIRECTORY + String.valueOf(hospitalId));
        if (!Files.exists(folderPath)) {
            Files.createDirectories(folderPath);
        }
        Files.write(folderPath.resolve(fileName), imageBytes);
        String imageUrl = "/api/profile/images/" + hospitalId + "/" + fileName;
        employee.setImgUre(imageUrl);
        return true;
    }

    //new method
    private List<Integer> convertLeaveOnDaysToIntegers(String leaveOnDays) {
        Map<String, Integer> dayMappings = new HashMap<>();
        dayMappings.put("Monday", 1);
        dayMappings.put("Tuesday", 2);
        dayMappings.put("Wednesday", 3);
        dayMappings.put("Thursday", 4);
        dayMappings.put("Friday", 5);
        dayMappings.put("Saturday", 6);
        dayMappings.put("Sunday", 7);

        List<Integer> leaveOnDaysIntegers = new ArrayList<>();
        for (String day : leaveOnDays.split(",")) {
            if (dayMappings.containsKey(day)) {
                leaveOnDaysIntegers.add(dayMappings.get(day));
            }
        }
        return leaveOnDaysIntegers;
    }


    public Employee getEmployeeById(Long id) {
        return employeeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found"));
    }


   /* @Transactional
    public Employee updateEmployee(Long id, EmployeeDTO employeeDTO) {
        Employee employee = getEmployeeById(id);

        Department department = departmentRepository.findById(employeeDTO.getDepartmentId())
                .orElseThrow(() -> new EntityNotFoundException("Department not found"));
        employee.setEmpId(employeeDTO.getEmpId());
        employee.setCompanyId(employeeDTO.getCompanyId());
        employee.setDepartment(department);
        employee.setEmpCode(employeeDTO.getEmpCode());
        employee.setEmpName(employeeDTO.getEmpName());
        employee.setUid(employeeDTO.getBiometricID());
       // employee.setIdNo(employeeDTO.getIdNo());
       // employee.setOrgId(employeeDTO.getOrgId());
        employee.setUpperId(employeeDTO.getUpperId());
        employee.setRegionId(employeeDTO.getSuburb());
        employee.setCountryId(employeeDTO.getCountryId());
        employee.setProvinceId(employeeDTO.getProvinceId());
        employee.setCity(employeeDTO.getCity());
        employee.setUpperName(employeeDTO.getUpperName());
        employee.setHireDate(employeeDTO.getHireDate()); // Assuming hireDate is passed as a Date object
        employee.setGender(employeeDTO.getGender());
        employee.setBirthday(employeeDTO.getBirthday()); // Assuming birthday is passed as a Date object
        employee.setNation(employeeDTO.getNation());
        employee.setMarried(employeeDTO.getMarried());
        employee.setPhoneNo(employeeDTO.getPhoneNo());
        employee.setMobileNo(employeeDTO.getMobileNo());
        employee.setEmail(employeeDTO.getEmail());
        employee.setNativePlace(employeeDTO.getNativePlace());
        employee.setZipCode(employeeDTO.getZipCode());
        employee.setIsHistory(employeeDTO.getIsHistory());
        employee.setInService(employeeDTO.getInService());
        employee.setRemark(employeeDTO.getRemark());
        employee.setCreatedBy(employeeDTO.getCreatedBy());
        employee.setCreatedTime(employeeDTO.getCreatedTime()); // Assuming createdTime is passed as a LocalDateTime object
        employee.setUpdatedBy(employeeDTO.getUpdatedBy());
        employee.setUpdatedTime(employeeDTO.getUpdatedTime()); // Assuming updatedTime is passed as a LocalDateTime object
        employee.setVersion(employeeDTO.getVersion());
        employee.setNativeLanguage(employeeDTO.getNativeLanguage());
        employee.setForeignLanguages(employeeDTO.getForeignLanguages());
        employee.setWorkYears(employeeDTO.getWorkYears());
        employee.setGraduateSchool(employeeDTO.getGraduateSchool());
        employee.setGraduateTime(employeeDTO.getGraduateTime()); // Assuming graduateTime is passed as a Date object
        employee.setHighestDegree(employeeDTO.getHighestDegree());
        employee.setImgUre(employeeDTO.getImgUre());

        return employeeRepository.saveAndFlush(employee);
    }*/

    @Transactional
    public void deleteEmployee(Long id) {
        Employee employee = getEmployeeById(id);
        employeeRepository.delete(employee);
    }

    public Page<EmployeeDetailsDto> filterEmployees(List<Long> branchIds, List<Long> departmentIds, List<Long> designationIds, String query, Pageable pageable, HttpServletRequest request) {
        // Normalize filter lists (if null, make them empty lists)
        branchIds = normalizeFilter(branchIds);
        departmentIds = normalizeFilter(departmentIds);
        designationIds = normalizeFilter(designationIds);

        // Get the current user and their role
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loggedInUsername = authentication.getName();
        User loggedInUser = userRepository.findByUsername(loggedInUsername);
        Long currentUserId = loggedInUser.getEmployee().getEmpId();

        String role = getCurrentUserRole();

        // Fetch branch and company details based on logged-in user
        Long branchIdByUser = getBranchIdByUser();
        Long companyIdByUser = getCompanyIdByUser();

        // If no filters are provided, return all employees
        if (areFiltersEmpty(branchIds, departmentIds, designationIds) && (query == null || query.isEmpty())) {
            //  return getAllEmployees(pageable);
            return getEmployeesByRole(role, currentUserId, branchIdByUser, companyIdByUser, pageable, request);
        } else {
            // Retrieve filtered employees from the repository
            Page<Employee> employees = employeeRepository.findEmployeesByFilters(branchIds, departmentIds, designationIds, query, pageable);

            // Apply additional filtering based on the user's role
            List<Employee> filteredEmployees = employees.stream()
                    .filter(employee -> filterByRolePost(employee, role, currentUserId, branchIdByUser, companyIdByUser))
                    .collect(Collectors.toList());

            // Paginate the filtered employees manually
            int start = Math.min((int) pageable.getOffset(), filteredEmployees.size());
            int end = Math.min((start + pageable.getPageSize()), filteredEmployees.size());
            List<Employee> paginatedEmployees = filteredEmployees.subList(start, end);

            // Convert the filtered employees to DTO
            List<EmployeeDetailsDto> employeeDTOs = paginatedEmployees.stream()
                    .map(emp -> {
                        return convertToDto1(emp, request);
                    })
                    .collect(Collectors.toList());

            // Return the filtered result as a Page object
            return new PageImpl<>(employeeDTOs, pageable, filteredEmployees.size());
        }
    }


    private boolean filterByRolePost(Employee employee, String role, Long currentUserId, Long branchIdByUser, Long companyIdByUser) {
        role = role.trim().toLowerCase();

        switch (role) {
            case "role_supervisor":
                return employee.getUpperId() != null && employee.getUpperId().equals(currentUserId);

            case "role_admin":
                return employee.getBranch() != null && employee.getBranch().getId() != null && employee.getBranch().getId().equals(branchIdByUser);

            case "role_superadmin":
                return companyIdByUser != null && employee.getCompanyId() != null && employee.getCompanyId().equals(companyIdByUser);

            default:
                return false;
        }
    }

    private Page<EmployeeDetailsDto> getEmployeesByRole(String role, Long currentUserId, Long branchIdByUser, Long companyIdByUser, Pageable pageable, HttpServletRequest request) {
        List<Employee> employees = employeeRepository.findAll(); // Fetch all employees (or you can implement your own repository method)

        // Apply role-based filtering
        List<Employee> filteredEmployees = employees.stream()
                .filter(employee -> filterByRolePost(employee, role, currentUserId, branchIdByUser, companyIdByUser))
                .sorted(Comparator.comparing(Employee::getEmpId).reversed())
                .collect(Collectors.toList());

        // Paginate the filtered employees manually
        int start = Math.min((int) pageable.getOffset(), filteredEmployees.size());
        int end = Math.min((start + pageable.getPageSize()), filteredEmployees.size());
        List<Employee> paginatedEmployees = filteredEmployees.subList(start, end);

        // Convert the filtered employees to DTO
        List<EmployeeDetailsDto> employeeDTOs = paginatedEmployees.stream()
                .map(emp -> {
                    return convertToDto1(emp, request);
                })
                .collect(Collectors.toList());

        // Return the filtered result as a Page object
        return new PageImpl<>(employeeDTOs, pageable, filteredEmployees.size());
    }


    // Method to fetch all employees with pagination
    private Page<EmployeeDetailsDto> getAllEmployees(Pageable pageable, HttpServletRequest request) {
        Pageable sortedByEmpId = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by("empId").descending());
        Page<Employee> employees = employeeRepository.findAll(sortedByEmpId); // Fetch all employees
        return employees.map(emp -> {
            // return convertToDto(emp, request);
            return convertToDTO(emp, request);
        }); // Convert to DTOs
    }

    private EmployeeDetailsDto convertToDTO(Employee employee, HttpServletRequest request) {
        EmployeeDetailsDto dto = new EmployeeDetailsDto();
        dto.setEmpId(employee.getEmpId());
        if (employee.getBranch() != null) {
            dto.setBranchId(employee.getBranch().getId());
            dto.setBranchName(employee.getBranch().getBranchName());
        }
        dto.setCompanyId(employee.getCompanyId());
        dto.setDepartmentId(employee.getDepartment() != null ? employee.getDepartment().getDepartmentId() : null);
        dto.setDeptName(employee.getDepartment() != null ? employee.getDepartment().getDepartmentName() : null);
        dto.setDesignationId(employee.getDesignation() != null ? employee.getDesignation().getId() : null);
        dto.setDesignationName(employee.getDesignation() != null ? employee.getDesignation().getName() : null);
        dto.setRole(employee.getDesignation() != null ? employee.getDesignation().getRole() : null);
        //dto.setRole(employee.getProvinceId() != null ? employee.getProvinceId().toString() : null);
        dto.setEmpCode(employee.getEmpCode());
        dto.setEmpName(employee.getEmpName());
        dto.setUid(employee.getUid());
        dto.setIdNo(employee.getIdNo());
        dto.setOrgId(employee.getOrgId());
        dto.setUpperId(employee.getUpperId());
        dto.setRegionId(employee.getRegionId());
        dto.setCountryId(employee.getCountryId());
        dto.setProvinceId(employee.getProvinceId());
        dto.setCity(employee.getCity());
        dto.setUpperName(employee.getUpperName());
        dto.setHireDate(employee.getHireDate());
        dto.setGender(employee.getGender());
        dto.setBirthday(employee.getBirthday());
        dto.setNation(employee.getNation());
        dto.setMarried(employee.getMarried());
        dto.setPhoneNo(employee.getPhoneNo());
        dto.setMobileNo(employee.getMobileNo());
        dto.setEmail(employee.getEmail());
        dto.setNativePlace(employee.getNativePlace());
        dto.setZipCode(employee.getZipCode());
        dto.setHistory(employee.getIsHistory());
        dto.setInService(employee.getInService());
        dto.setRemark(employee.getRemark());
        dto.setCreatedBy(employee.getCreatedBy());
        dto.setCreatedTime(employee.getCreatedTime());
        dto.setUpdatedBy(employee.getUpdatedBy());
        dto.setUpdatedTime(employee.getUpdatedTime());
        dto.setVersion(employee.getVersion());
        dto.setNativeLanguage(employee.getNativeLanguage());
        dto.setForeignLanguages(employee.getForeignLanguages());
        dto.setWorkYears(employee.getWorkYears());
        dto.setGraduateSchool(employee.getGraduateSchool());
        dto.setGraduateTime(employee.getGraduateTime());
        dto.setHighestDegree(employee.getHighestDegree());
        String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
        dto.setImgUre(baseUrl + employee.getImgUre());

        return dto;
    }

    private List<Long> normalizeFilter(List<Long> filterList) {
        return (filterList != null && filterList.isEmpty()) ? null : filterList;
    }

    private boolean areFiltersEmpty(List<Long> branchIds, List<Long> departmentIds, List<Long> designationIds) {
        return (branchIds == null || branchIds.isEmpty()) &&
                (departmentIds == null || departmentIds.isEmpty()) &&
                (designationIds == null || designationIds.isEmpty());
    }


    // end of getting list of employee

    @Transactional
    public void saveEmployeeLeaveBalances(List<LeaveCountSaveDTO> leaveCounts) {
        try {
            List<EmployeeLeaveBalance> savedLeaveBalances = new ArrayList<>();
            for (LeaveCountSaveDTO leaveCountSaveDTO : leaveCounts) {
                Long leaveId = leaveCountSaveDTO.getLeaveTypeId();
                Long empId = leaveCountSaveDTO.getEmpId();
                Integer assignedLeave = leaveCountSaveDTO.getAssignedLeave();
                Integer balanceLeave = leaveCountSaveDTO.getBalanceLeave();

                // Create EmployeeLeaveBalance entity
                EmployeeLeaveBalance employeeLeaveBalance = new EmployeeLeaveBalance();
                employeeLeaveBalance.setEmpId(empId);
                employeeLeaveBalance.setLeaveId(leaveId);
                employeeLeaveBalance.setAssignedLeave(assignedLeave);
                employeeLeaveBalance.setBalanceLeave(balanceLeave); // Use balanceLeave from DTO
                employeeLeaveBalance.setCreatedTime(LocalDateTime.now());
                employeeLeaveBalance.setUpdatedTime(null); // No update time for initial save

                // Save each record to the database
                savedLeaveBalances.add(employeeLeaveBalance);
                employeeLeaveBalanceRepository.saveAndFlush(employeeLeaveBalance);
            }
        } catch (Exception e) {
            // Handle exceptions during the save operation
            log.error("Failed to save leave balances: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to save leave balances: " + e.getMessage(), e);
        }
    }

    public List<EmployeeIdNameDTO> getAllActiveEmployeeIdsAndNames() {
        List<Object[]> results = employeeRepository.findAllActiveEmployeeIdsAndNames();
        return results.stream()
                .map(result -> new EmployeeIdNameDTO((Long) result[0], (String) result[1]))
                .collect(Collectors.toList());
    }

  /*  public EmployeeDetailsDto getCurrentEmployeeDetails() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();

        User user = userRepository.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("User not found");
        }

        Employee employee = user.getEmployee();
        if (employee == null) {
            throw new RuntimeException("Employee details not found for user");
        }

        return convertToDTO(employee);
    }
*/


    public EmployeeDetailsDto getCurrentEmployeeDetails() {
        // Get the username from SecurityContextHolder
        String username = SecurityContextHolder.getContext().getAuthentication().getName();

        // Check if username is valid
        if (username == null || username.isEmpty()) {
            throw new RuntimeException("Authenticated user not found");
        }

        // Retrieve the user by username
        User user = userRepository.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("User not found for username: " + username);
        }

        // Retrieve employee details from user
        Employee employee = user.getEmployee();
        if (employee == null) {
            throw new RuntimeException("Employee details not found for user: " + username);
        }
        List<EmployeeLeaveBalance> leaveBalances = employeeLeaveBalanceRepository.findByEmpId(employee.getEmpId());
        List<EmployeeDocumentVO> documentVOs = employeeDocumentRepository.findByEmpIdAndIsActiveTrueOrderByIdDesc(employee.getEmpId())
                .stream()
                .map(doc -> {
                    // Fetch document metadata
                    DocumentMaster documentMetadata = documentMasterRepository.findById(doc.getDocumentId())
                            .orElseThrow(() -> new EntityNotFoundException("Document not found with id: " + doc.getDocumentId()));

                    // Map to EmployeeDocumentVO
                    return EmployeeDocumentVO.builder()
                            .id(doc.getId())
                            .documentId(doc.getDocumentId())
                            .documentName(documentMetadata.getName())
                            .documentType(documentMetadata.getType())
                            .filePath(doc.getFile())
                            .build();
                })
                .collect(Collectors.toList());
        return convertToDTO(employee, leaveBalances, documentVOs);
    }

    private EmployeeLeaveBalanceDto convertLeaveBalanceToDTO(EmployeeLeaveBalance leaveBalance) {

        String leaveName = leaveMasterRepository.findByLeaveId(leaveBalance.getEmpLeaveId())
                .map(LeaveMaster::getType) // Retrieve the type if LeaveMaster is found
                .orElse(null);

        String leaveName1 = leaveMasterRepository.findByLeaveId(leaveBalance.getLeaveId())
                .map(LeaveMaster::getType) // Retrieve the type if LeaveMaster is found
                .orElse(null);
        EmployeeLeaveBalanceDto dto = new EmployeeLeaveBalanceDto();
        dto.setEmpLeaveId(leaveBalance.getEmpLeaveId());
        dto.setEmpLeaveIdName(leaveName);
        dto.setEmpId(leaveBalance.getEmpId());
        dto.setLeaveId(leaveBalance.getLeaveId());
        dto.setLeaveName(leaveName1);
        dto.setAssignedLeave(leaveBalance.getAssignedLeave());
        dto.setBalanceLeave(leaveBalance.getBalanceLeave());
        dto.setCreatedTime(leaveBalance.getCreatedTime());
        dto.setUpdatedTime(leaveBalance.getUpdatedTime());
        return dto;
    }

    private EmployeeDetailsDto convertToDTO(Employee employee, List<EmployeeLeaveBalance> leaveBalances, List<EmployeeDocumentVO> documents) {
        EmployeeDetailsDto dto = mapToEmployeeDetailsDto(employee);
        List<EmployeeLeaveBalanceDto> leaveBalanceDtos = leaveBalances.stream()
                .map(this::convertLeaveBalanceToDTO)
                .collect(Collectors.toList());

        dto.setLeaveBalances(leaveBalanceDtos);

        // Add documents to DTO
        dto.setDocuments(documents);

        return dto;
    }
    //excel upload new work

    @Transactional
    public void createEmployeesExcel(InputStream inputStream, HttpServletRequest httpRequest) throws IOException, ParseException {
        List<EmployeeInfoDTO> employees = parseExcelFile(inputStream);
        for (EmployeeInfoDTO employeeDTO : employees) {
            createEmployeeFromExcel(employeeDTO, httpRequest);
        }
    }

    public List<EmployeeInfoDTO> parseExcelFile(InputStream inputStream) {
        List<EmployeeInfoDTO> employeeList = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);

            // Iterate over each row
            for (Row row : sheet) {
                int counter = 0;
                if (row.getRowNum() == 0) continue;

                boolean isRowEmpty = true;
                for (Cell cell : row) {
                    String cellValue = getCellValue(cell);
                    if (cellValue != null && !cellValue.trim().isEmpty()) {
                        isRowEmpty = false;
                        break;
                    }
                }
                if (isRowEmpty) {
                    break;
                }

                EmployeeInfoDTO employee = new EmployeeInfoDTO();


                //   employee.setHospitalName(getCellValue(row.getCell(counter++)));
                employee.setFacility(getCellValue(row.getCell(counter++)));
                employee.setEmpCode(getCellValue(row.getCell(counter++)));
                employee.setEmpName(getCellValue(row.getCell(counter++)));
                employee.setDateOfBirth(getCellDateValue(row.getCell(counter++)));
                employee.setGender(getCellValue(row.getCell(counter++)));
                employee.setEthnicity(getCellValue(row.getCell(counter++)));
                employee.setMarried(getCellValue(row.getCell(counter++)));
                employee.setPhoneNo(getCellValue(row.getCell(counter++)));
                employee.setMobileNo(getCellValue(row.getCell(counter++)));
                log.info("Extracted PhoneNo: {}", employee.getPhoneNo());
                log.info("Extracted MobileNo: {}", employee.getMobileNo());
                employee.setNationalId(getCellValue(row.getCell(counter++)));
                employee.setDepartmentName(getCellValue(row.getCell(counter++)));
                employee.setDesignationName(getCellValue(row.getCell(counter++)));
                employee.setReportingPersonCode(getCellValue(row.getCell(counter++)));
                employee.setBiometricId(getCellValue(row.getCell(counter++)));
                employee.setAddressLine1(getCellValue(row.getCell(counter++)));
                employee.setAddressLine2(getCellValue(row.getCell(counter++)));
                employee.setProvince(getCellValue(row.getCell(counter++)));
                employee.setDistrict(getCellValue(row.getCell(counter++)));
                employee.setSubDistrict(getCellValue(row.getCell(counter++)));
                employee.setNation(getCellValue(row.getCell(counter++)));
                employee.setZipcode(getCellValue(row.getCell(counter++)));
                employee.setNativePlace(getCellValue(row.getCell(counter++)));
                employee.setJoiningDate(getCellDateValue(row.getCell(counter++)));
                employee.setWorkZone(getCellValue(row.getCell(counter++)));
                employee.setEmail(getCellValue(row.getCell(counter++)));
                employee.setInService(getCellValue(row.getCell(counter++)));
                employee.setRemark(getCellValue(row.getCell(counter++)));
                employee.setNativeLanguage(getCellValue(row.getCell(counter++)));
                employee.setForeignLanguages(getCellValue(row.getCell(counter++)));

                // Handle parsing integer values
                try {
                    employee.setExperienceYears(Integer.parseInt(getCellValue(row.getCell(counter++))));
                } catch (NumberFormatException e) {
                    employee.setExperienceYears(0);
                }

                employee.setDefaultTimeSlot(getCellValue(row.getCell(counter++)));
                employee.setLeaveOnDays(getCellValue(row.getCell(counter++)));
                //  employee.setLeaveType(getCellValue(row.getCell(counter++)));
                employee.setEmergencyContactName(getCellValue(row.getCell(counter++)));
                employee.setEmergencyContactNumber(getCellValue(row.getCell(counter++)));
                employee.setEmergencyContactName2(getCellValue(row.getCell(counter++)));
                employee.setEmergencyContactNumber2(getCellValue(row.getCell(counter++)));
                employee.setProbationPeriod(getCellValue(row.getCell(counter++)));
                employeeList.add(employee);
            }

        } catch (IOException e) {
            log.error("Exception occured while parseExcelFile", e);
        }

        return employeeList;
    }

    private static String getCellValue(Cell cell) {
        if (cell == null) return "";
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return BigDecimal.valueOf(cell.getNumericCellValue()).toPlainString();
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    private Date getCellDateValue(Cell cell) {
        if (cell == null || cell.getCellType() != CellType.NUMERIC || !DateUtil.isCellDateFormatted(cell)) {
            return null;
        }
        return cell.getDateCellValue();
    }

//    private <T> T getEntityByName(JpaRepository<T, ?> repository, String name, String entityName) {
//    	return repository.findByName(name) .orElseThrow(() -> new IllegalArgumentException(entityName + " not found with name: " + name));
//    }

    //save employee work
    @Transactional
    public Employee createEmployeeFromExcel(EmployeeInfoDTO employeeDTO, HttpServletRequest httpRequest) throws ParseException {

        if (employeeRepository.existsByEmail(employeeDTO.getEmail())) {
            throw new IllegalArgumentException("Email ID already exists");
        }

        if (employeeRepository.existsByNationalId(employeeDTO.getNationalId())) {
            throw new IllegalArgumentException("National ID already exists");
        }


        String query = "";
        boolean executeDefault = false;
        Long timeSlotId = null;

        Department department = departmentRepository.findByName(employeeDTO.getDepartmentName())
                .orElseThrow(() -> new IllegalArgumentException("Department not found with name: " + employeeDTO.getDepartmentName()));

        Designation designation = designationRepository.findByName(employeeDTO.getDesignationName())
                .orElseThrow(() -> new IllegalArgumentException("Designation not found with name: " + employeeDTO.getDesignationName()));

        Province province = provinceRepository.findByName(employeeDTO.getProvince())
                .orElseThrow(() -> new IllegalArgumentException("Province not found with name: " + employeeDTO.getProvince()));

        District district = districtRepository.findByName(employeeDTO.getDistrict())
                .orElseThrow(() -> new IllegalArgumentException("District not found with name: " + employeeDTO.getDistrict()));

        SubDistrict subDistrict = subDistrictRepository.findByName(employeeDTO.getSubDistrict())
                .orElseThrow(() -> new IllegalArgumentException("SubDistrict not found with name: " + employeeDTO.getSubDistrict()));

        Branch branch = branchRepository.findByBranchName(employeeDTO.getFacility())
                .orElseThrow(() -> new IllegalArgumentException("Branch not found with name: " + employeeDTO.getFacility()));

        if (StringUtils.isNoneBlank(employeeDTO.getDefaultTimeSlot())) {
            MapSqlParameterSource params = new MapSqlParameterSource();
            String[] timeSlotArray = employeeDTO.getDefaultTimeSlot().split("-");
            query = CommonConstant.TimeSlotContant.TIME_SLOT_QUERY;
            params.addValue("branchId", branch.getId());
            params.addValue("departmentId", department.getDepartmentId());

            params.addValue("startTime", LocalTime.parse(covertDateTo24HoursFormat(timeSlotArray[0].trim() + ":00 AM"), DateTimeFormatter.ofPattern("HH:mm:ss")));
            params.addValue("endTime", LocalTime.parse(covertDateTo24HoursFormat(timeSlotArray[0].trim() + ":00 PM"), DateTimeFormatter.ofPattern("HH:mm:ss")));

            List<Long> timeSlotIds = jdbcTemplate.query(query, params, (rs, rowNum) -> {
                return rs.getLong("slotId");
            });

            if (timeSlotIds.isEmpty())
                executeDefault = true;
            else
                timeSlotId = timeSlotIds.get(0);
        }

        if (executeDefault || StringUtils.isBlank(employeeDTO.getDefaultTimeSlot())) {
            MapSqlParameterSource params = new MapSqlParameterSource();
            query = CommonConstant.TimeSlotContant.DEFAULT_TIME_SLOT_QUERY;
            params.addValue("branchId", branch.getId());
            params.addValue("departmentId", department.getDepartmentId());

            List<Long> timeSlotIds = jdbcTemplate.query(query, params, (rs, rowNum) -> {
                return rs.getLong("slotId");
            });

            if (timeSlotIds.isEmpty()) {
                log.error("Schedule not available for facility {} and department {}.", branch.getId(),
                        department.getDepartmentId());
                throw new IllegalArgumentException("Schedule not available for facility [" + branch.getBranchName() + "] and department ["
                        + department.getDepartmentName() + "]");
            }

            timeSlotId = timeSlotIds.get(0);
        }

        // Create a new Employee entity
        Employee employee = new Employee();
        employee.setEmpCode(employeeDTO.getEmpCode());
        employee.setEmpName(employeeDTO.getEmpName());
        employee.setUid(employeeDTO.getBiometricId());
        employee.setDepartment(department);
        employee.setDesignation(designation);
        employee.setCompanyId(branch.getCompany().getId());
        employee.setBranch(branch);

        try {

            long upperId = 0;
            // Check if ReportingPersonCode is empty or null
            if (StringUtils.isNotEmpty(employeeDTO.getReportingPersonCode())) {
                try {
                    String reportingPersonCode = employeeDTO.getReportingPersonCode();
                    // Use parameterized query to prevent SQL injection
                    String sql = "SELECT EMP_ID FROM t_employee WHERE emp_code = :emp_code";
                    Map<String, String> param = new HashMap<>();
                    param.put("emp_code", reportingPersonCode);
                    upperId = jdbcTemplate.queryForObject(sql, param, Long.class);
                } catch (EmptyResultDataAccessException e) {
                    // Log the error or handle the case where no result is found
                    // No record was found for the given reportingPersonCode, so upperId remains 0
                    log.warn("No employee found for reportingPersonCode: {}", employeeDTO.getReportingPersonCode());
                } catch (Exception e) {
                    // Log any other exceptions and handle appropriately
                    log.error("Error fetching upperId for reportingPersonCode: {}", employeeDTO.getReportingPersonCode(), e);
                }
            }
            employee.setUpperId(upperId);

        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid Reporting Person Code: " + employeeDTO.getReportingPersonCode(), e);
        }

        employee.setRegionId(subDistrict.getId());
        employee.setProvinceId(province.getId());
        employee.setCity(String.valueOf(district.getId()));
        employee.setHireDate(employeeDTO.getJoiningDate());
        employee.setGender(employeeDTO.getGender());
        employee.setBirthday(employeeDTO.getDateOfBirth());
        employee.setNation(employeeDTO.getNation());
        //  employee.setMarried(Boolean.parseBoolean(employeeDTO.getMarried())); // Use Boolean.parseBoolean
        if ("yes".equalsIgnoreCase(employeeDTO.getMarried())) {
            employee.setMarried(true);
        } else {
            employee.setMarried(false);
        }
        employee.setPhoneNo(employeeDTO.getPhoneNo());
        employee.setMobileNo(employeeDTO.getMobileNo());
        employee.setNationalId(employeeDTO.getNationalId());
        employee.setEmail(employeeDTO.getEmail());
        employee.setNativePlace(employeeDTO.getNativePlace());
        employee.setZipCode(employeeDTO.getZipcode());
        employee.setInService(Boolean.parseBoolean(employeeDTO.getInService())); // Use Boolean.parseBoolean
        employee.setRemark(employeeDTO.getRemark());
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
        employee.setCreatedBy(loggedInEmpId);
        employee.setCreatedTime(LocalDateTime.now()); // Set created timestamp
        employee.setUpdatedBy(null);
        employee.setUpdatedTime(null);
        employee.setNativeLanguage(employeeDTO.getNativeLanguage());
        employee.setForeignLanguages(employeeDTO.getForeignLanguages());
        employee.setWorkYears(employeeDTO.getExperienceYears());

        if (null != timeSlotId) {
            employee.setDefaultTimeSlotId(timeSlotId);
        }
        employee.setLeaveOnDays(employeeDTO.getLeaveOnDays());
        employee.setCountryId(1L);
        //  employee.setInService(Boolean.valueOf(employeeDTO.getInService()));
        if ("yes".equalsIgnoreCase(employeeDTO.getInService())) {
            employee.setInService(true);
        } else {
            employee.setInService(false);
        }
        //new fileds
        employee.setEthnicity(employeeDTO.getEthnicity());
        employee.setWorkZone(employeeDTO.getWorkZone());
        employee.setEmergencyContactname1(employeeDTO.getEmergencyContactName());
        employee.setEmergencyContact1(employeeDTO.getEmergencyContactNumber());
        employee.setEmergencyContactname2(employeeDTO.getEmergencyContactName2());
        employee.setEmergencyContact2(employeeDTO.getEmergencyContactNumber2());
        //  employee.setProbationPeriod(employeeDTO.getProbationPeriod());
        String probationStr = employeeDTO.getProbationPeriod();
        BigDecimal probationPeriod = null;  // Default to null

        if (probationStr != null && !probationStr.trim().isEmpty()) {
            try {
                probationPeriod = new BigDecimal(probationStr.trim());
            } catch (NumberFormatException e) {
                log.error("Invalid probation period format in Excel: " + probationStr, e);
            }
        }

        employee.setProbationPeriod(probationPeriod);


        // Save the employee entity
        employeeRepository.save(employee);
        log.info("Employee created: {}", employee);

      /*  //creating new leave balance
        List<LeaveMaster> leaveTypes = leaveMasterRepository.findAllActiveLeaveTypes();
        List<EmployeeLeaveBalance> leaveBalances = new ArrayList<>();
        for (LeaveMaster leaveType : leaveTypes) {
            Long leaveCount = leaveMasterRepository.findLeaveCountByLeaveId(leaveType.getLeaveId());
            if (leaveCount != null) {
                EmployeeLeaveBalance leaveBalance = new EmployeeLeaveBalance();
                leaveBalance.setLeaveId(leaveType.getLeaveId());
                leaveBalance.setEmpId(employee.getEmpId());
                leaveBalance.setAssignedLeave(leaveCount.intValue());
                leaveBalance.setBalanceLeave(leaveCount.intValue());
                leaveBalance.setCreatedTime(LocalDateTime.now());
                leaveBalance.setUpdatedTime(null);

                // Add to the list
                leaveBalances.add(leaveBalance);
            }
        }

        if (!leaveBalances.isEmpty()) {
            employeeLeaveBalanceRepository.saveAll(leaveBalances);
        }*/

        //user creating

        User user = new User();
        user.setEmployee(employee);
        user.setUserCode(employee.getEmpCode());
        user.setUsername(employee.getEmail());
        Long designationId = designation.getId();
        String sql = "SELECT role FROM t_designation WHERE designation_id = :designationId";
        Map<String, Long> param = new HashMap<>();
        param.put("designationId", designationId);  // 'designation' is assumed to be a Long value

        // Execute the query to get the role as a String
        String role = jdbcTemplate.queryForObject(sql, param, String.class);

        // Set the role on the user object
        user.setRole(role);

        // Set default password - predefined or generated
        String defaultPassword = generateRandomPassword();
        user.setPassword(passwordEncoder.encode(defaultPassword));
        userRepository.save(user);

        log.info("User created: {}", user);

        //added frontend url
        // Retrieve frontend URL from the request attribute
        String frontendUrl = (String) httpRequest.getAttribute("frontendUrl");

        // Construct the reset URL with frontend URL, falling back to server URL if needed
        String resetUrl;
        if (frontendUrl != null) {
            resetUrl = frontendUrl + "/login/resetDefault-password/" + defaultPassword;
        } else {
            resetUrl = httpRequest.getRequestURL().toString().replace(httpRequest.getRequestURI(), "") + "/login/resetDefault-password/" + defaultPassword;
        }
        //  sendRestPasswordEmail(user.getUsername(),employee.getEmail(), defaultPassword);
        sendRestPasswordEmail(user.getUsername(), employee.getEmail(), defaultPassword, resetUrl);

        return employee;
    }

    private String generateRandomPassword() {
        int length = 8;
        return RandomStringUtils.randomAlphanumeric(length);
    }


    private void sendRestPasswordEmail(String name, String email, String defaultPassword, String resetUrl) {
        //  String resetUrl = "http://www.aitechiez.com:3001/resetDefault-password/" + defaultPassword;
        String subject = "Password Reset Request";
        String instructions = "Please change it upon login.";

        // Construct the email body using a template
        String messageBody = template.getWelcomeTemplate(
                WelcomeEmailTemplateDto.builder()
                        .name(name)
                        .defaultPassword(defaultPassword)
                        .instructions(instructions)
                        .resetLink(resetUrl)
                        .build()
        );

        EmailData data = new EmailData();
        data.setEmailId(email);
        data.setSubject(subject);
        data.setMessage(messageBody);
        data.setHtml(true);

        // Send the email
        emailSender.sendEmail(data);
    }

    //reset default password
    @Transactional
    public String resetPassword(PasswordResetDto passwordResetDto) {

        User user = userRepository.findByUsername(passwordResetDto.getUsername());
        if (user == null) {
            throw new IllegalArgumentException("User not found");
        }
        // Verify default password
        if (!passwordEncoder.matches(passwordResetDto.getDefaultPassword(), user.getPassword())) {
            throw new IllegalArgumentException("Default password is incorrect");
        }

        // Check that new password and confirm password match
        if (!passwordResetDto.getNewPassword().equals(passwordResetDto.getConfirmPassword())) {
            throw new IllegalArgumentException("New password and confirm password do not match");
        }
        // Encode the new password and update user
        user.setPassword(passwordEncoder.encode(passwordResetDto.getNewPassword()));
        userRepository.save(user);

        return "Password reset successful";
    }


    public List<EmployeeDetailsDto> getAllEmployeesAll(HttpServletRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loggedInUsername = authentication.getName();
        User loggedInUser = userRepository.findByUsername(loggedInUsername);
        Long currentUserId = loggedInUser.getEmployee().getEmpId();
        String role = getCurrentUserRole();

        List<Employee> employees = employeeRepository.findAll();
        return employees.stream()
                .filter(employee -> {
                    boolean matchesRole = filterByRole(employee, role, currentUserId);
                    return matchesRole;
                })
                .map(emp -> {
                    return convertToDto1(emp, request);
                })
                .collect(Collectors.toList());
    }

    private EmployeeDetailsDto convertToDto1(Employee employee, HttpServletRequest request) {
        EmployeeDetailsDto dto = new EmployeeDetailsDto();

        // Set Employee ID and other common fields
        dto.setEmpId(employee.getEmpId());
        dto.setCompanyId(employee.getCompanyId());
        dto.setEmpCode(employee.getEmpCode());
        dto.setEmpName(employee.getEmpName());
        dto.setUid(employee.getUid());
        dto.setIdNo(employee.getIdNo());
        dto.setOrgId(employee.getOrgId());
        dto.setUpperId(employee.getUpperId());
        dto.setRegionId(employee.getRegionId());
        dto.setCountryId(employee.getCountryId());
        dto.setProvinceId(employee.getProvinceId());
        dto.setCity(employee.getCity());
        dto.setUpperName(employee.getUpperName());
        dto.setHireDate(employee.getHireDate());
        dto.setGender(employee.getGender());
        dto.setBirthday(employee.getBirthday());
        dto.setNation(employee.getNation());
        dto.setMarried(employee.getMarried());
        dto.setPhoneNo(employee.getPhoneNo());
        dto.setMobileNo(employee.getMobileNo());
        dto.setEmail(employee.getEmail());
        dto.setNativePlace(employee.getNativePlace());
        dto.setZipCode(employee.getZipCode());
        dto.setHistory(employee.getIsHistory());
        dto.setInService(employee.getInService());
        dto.setRemark(employee.getRemark());
        dto.setCreatedBy(employee.getCreatedBy());
        dto.setCreatedTime(employee.getCreatedTime());
        dto.setUpdatedBy(employee.getUpdatedBy());
        dto.setUpdatedTime(employee.getUpdatedTime());
        dto.setVersion(employee.getVersion());
        dto.setNativeLanguage(employee.getNativeLanguage());
        dto.setForeignLanguages(employee.getForeignLanguages());
        dto.setWorkYears(employee.getWorkYears());
        dto.setGraduateSchool(employee.getGraduateSchool());
        dto.setGraduateTime(employee.getGraduateTime());
        dto.setHighestDegree(employee.getHighestDegree());
        String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
        dto.setImgUre(baseUrl + employee.getImgUre());

        // Set branch information
        if (employee.getBranch() != null) {
            dto.setBranchId(employee.getBranch().getId());
            dto.setBranchName(employee.getBranch().getBranchName());
        }

        // Set department information
        if (employee.getDepartment() != null) {
            dto.setDepartmentId(employee.getDepartment().getDepartmentId());
            dto.setDeptName(employee.getDepartment().getDepartmentName());
        }

        // Set designation information
        if (employee.getDesignation() != null) {
            dto.setDesignationId(employee.getDesignation().getId());
            dto.setDesignationName(employee.getDesignation().getName());
            dto.setRole(employee.getDesignation().getRole());
        }

        return dto;
    }


    private String getCurrentUserRole() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
            for (GrantedAuthority authority : authorities) {
                return authority.getAuthority();
            }
        }
        return null;
    }

    private boolean filterByRole(Employee employee, String role, Long currentUserId) {
        Long branchIdByUser = getBranchIdByUser();
        Long companyIdByUser = getCompanyIdByUser();
        role = role.trim().toLowerCase();
        switch (role.toLowerCase()) {
            case "role_supervisor":
                return employee.getUpperId() != null && employee.getUpperId().equals(currentUserId);
            case "role_admin":
                return employee.getBranch() != null && employee.getBranch().getId() != null && employee.getBranch().getId().equals(branchIdByUser);
            case "role_superadmin":
                return companyIdByUser != null && employee.getCompanyId() != null && employee.getCompanyId().equals(companyIdByUser);
            default:
                return false;
        }
    }

    public Long getBranchIdByUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loggedInUsername = authentication.getName();

        User loggedInUser = userRepository.findByUsername(loggedInUsername);

        if (loggedInUser != null && loggedInUser.getEmployee() != null &&
                loggedInUser.getEmployee().getBranch() != null) {
            return loggedInUser.getEmployee().getBranch().getId();
        }
        return null;
    }

    public Long getCompanyIdByUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loggedInUsername = authentication.getName();

        User loggedInUser = userRepository.findByUsername(loggedInUsername);

        if (loggedInUser != null && loggedInUser.getEmployee() != null) {
            return loggedInUser.getEmployee().getCompanyId();
        }
        return null;
    }

    //ritesh
    public Page<EmployeeVO> searchEmployees(EmployeeFilterRequest filter) {

        log.info("Initiating call to fetch employee details");
        User employee = tokenService.getEmployeeFromToken();

        Pageable pageable = PageRequest.of(filter.getOffset(), filter.getLimit());

        if (employee != null && employee.getRole().equals("employee")) {
            throw new RuntimeException("You are not authorized to view the branch details");
        }

        StringBuilder query = new StringBuilder();
        MapSqlParameterSource params = new MapSqlParameterSource();

        if (employee.getRole().equalsIgnoreCase("superadmin")) {
            query.append(" AND emp.company_id = :companyId");
            params.addValue("companyId", employee.getEmployee().getCompanyId());

        } else if (employee.getRole().equalsIgnoreCase("admin")) {
            query.append(" AND emp.branch_id = :branchId");
            params.addValue("branchId", employee.getEmployee().getBranch().getId());

        } else if (employee.getRole().equalsIgnoreCase("supervisor")) {
            query.append(" AND emp.upper_id = :empId");
            params.addValue("empId", employee.getEmployee().getEmpId());

        } else if (employee.getRole().equalsIgnoreCase("ceo")) {
            if (filter.getHospitalId() != null) {
                query.append(" AND emp.company_id = :companyId");
                params.addValue("companyId", filter.getHospitalId());
            }
        }

        // Handle hospitalIds
        List<Long> hospitalIds = filter.getHospitalId();
        if (hospitalIds != null && !hospitalIds.isEmpty()) {
            query.append(" AND emp.company_id IN (:hospitalIds)");
            params.addValue("hospitalIds", hospitalIds);
        }

        // Handle branchIds
        List<Long> branchIds = filter.getBranchIds();
        if (branchIds != null && !branchIds.isEmpty()) {
            query.append(" AND emp.branch_id IN (:branchIds)");
            params.addValue("branchIds", branchIds);
        }

        // Handle departmentIds
        List<Long> departmentIds = filter.getDepartmentIds();
        if (departmentIds != null && !departmentIds.isEmpty()) {
            query.append(" AND emp.department_id IN (:departmentIds)");
            params.addValue("departmentIds", departmentIds);
        }

        // Handle designationIds
        List<Long> designationIds = filter.getDesignationIds();
        if (designationIds != null && !designationIds.isEmpty()) {
            query.append(" AND emp.designation_id IN (:designationIds)");
            params.addValue("designationIds", designationIds);
        }

        // Handle search text query
        if (!StringUtils.isEmpty(filter.getQuery())) {
            query.append(" AND ( dt.department_name LIKE :searchText " +
                    " OR emp.emp_name LIKE :searchText " +
                    " OR emp.upper_name LIKE :searchText " +
                    " OR br.branch_name LIKE :searchText " +
                    " OR emp.mobile_no LIKE :searchText " +
                    " OR emp.email LIKE :searchText )");
            params.addValue("searchText", "%" + filter.getQuery() + "%");
            log.info("query===={}", query);
        }

        // Apply inService filter with three conditions
        if (filter.getInService() == null || filter.getInService().equals(1)) {
            query.append(" AND emp.in_service = 1");
        } else if (filter.getInService().equals(0)) {
            query.append(" AND emp.in_service = 0");
        } // No condition for -1, as per the provided logic

        String countQuery = CommonConstant.EmployeeQuery.Employee_EXTRACT_COUNT_QUERY + query;
        int total = namedParameterJdbcTemplate.queryForObject(countQuery, params, (rs, rowNum) -> rs.getInt(1));

        if (total == 0) {
            return new PageImpl<>(Collections.emptyList(), pageable, total);
        }

        // Adjust the pagination offset
        int offset2 = Math.max(0, filter.getOffset());
        if (StringUtils.isEmpty(filter.getType())) {
            query.append(" ORDER BY emp.DEPARTMENT_ID OFFSET  :offset ROWS FETCH FIRST :limit ROWS ONLY");
            params.addValue("limit", filter.getLimit());
            params.addValue("offset", (offset2 > 0) ? offset2 * filter.getLimit() : 0);
        } else {
            log.info("reached outside type");
            query.append(" ORDER BY emp.EMP_NAME ASC");
        }

        String resultQuery = CommonConstant.EmployeeQuery.EMPLOYEE_EXTRACT_QUERY + query;

        List<EmployeeVO> employeeList = namedParameterJdbcTemplate.query(resultQuery, params, (rs, rowNum) -> {

            EmployeeVO.EmployeeVOBuilder employeeBuilder = EmployeeVO.builder()
                    .empId(rs.getLong("emp_id"))
                    .companyId(rs.getLong("company_id"))
                    .departmentId(rs.getLong("department_id"))
                    .department(rs.getString("department_name"))
                    .empCode(rs.getString("emp_code"))
                    .empName(rs.getString("emp_name"))
                    .uid(rs.getString("uid"))
                    .biometricID(rs.getString("uid"))
                    .idNo(rs.getString("id_no"))
                    .upperId(rs.getLong("upper_id"))
                    .regionId(rs.getLong("region_id"))
                    .countryId(rs.getLong("country_id"))
                    .provinceId(rs.getLong("province_id"))
                    .city(rs.getString("city"))
                    .upperName(rs.getString("upper_name"))
                    .hireDate(rs.getDate("hire_date") != null ? rs.getDate("hire_date") : null) // Handle null
                    .gender(rs.getString("gender"))
                    .birthday(rs.getDate("birthday") != null ? rs.getDate("birthday") : null) // Adjust for date types
                    .nation(rs.getString("nation"))
                    .married(rs.getBoolean("married")) // Adjust if boolean
                    .phoneNo(rs.getString("phone_no"))
                    .mobileNo(rs.getString("mobile_no"))
                    .email(rs.getString("email"))
                    .nativePlace(rs.getString("native_place"))
                    .zipCode(rs.getString("zip_code"))
                    .isHistory(rs.getBoolean("is_history")) // Adjust if boolean
                    .inService(rs.getBoolean("in_service")) // Adjust if boolean
                    .remark(rs.getString("remark"))
                    .createdBy(rs.getLong("created_by"))
                    .createdTime(rs.getTimestamp("created_time") != null ? rs.getTimestamp("created_time").toLocalDateTime() : null)
                    .updatedBy(rs.getLong("updated_by"))
                    .updatedTime(rs.getTimestamp("updated_time") != null ? rs.getTimestamp("updated_time").toLocalDateTime() : null)
                    .version(rs.getLong("version"))
                    .nativeLanguage(rs.getString("native_language"))
                    .foreignLanguages(rs.getString("foreign_languages"))
                    .workYears(rs.getInt("work_years"))
                    .graduateSchool(rs.getString("graduate_school"))
                    .graduateTime(rs.getDate("graduate_time") != null ? rs.getDate("graduate_time") : null) // Adjust for date types
                    .highestDegree(rs.getString("highest_degree"))
                    .imgUre(rs.getString("img_ure"))
                    .branchId(rs.getLong("branch_id"))
                    .branchName(rs.getString("branch_name"))
                    .defaultShift(rs.getString("default_shift"))
                    .workingDayCountInWeek(rs.getString("working_day_count_in_week"))
                    .leaveOnDays(rs.getString("leave_on_days"))
                    .designationId(rs.getLong("designation_id"))
                    .designation(rs.getString("designation_name"))
                    .defaultTimeSlotId(rs.getLong("default_time_slot_id"))
                    .leaveOnDays(rs.getString("leave_on_days"))
                    .unitNumber(rs.getString("unit_no"))
                    .street(rs.getString("street"))
                    .alternateNumber(rs.getString("alternateNumber"))
                    .emergencyContact1(rs.getString("emergencyContact1"))
                    .emergencyContact2(rs.getString("emergencyContact2"))
                    .emergencyContactname1(rs.getString("emergencyContactname1"))
                    .emergencyContactname2(rs.getString("emergencyContactname2"))
                    .alternateEmail(rs.getString("alternateEmail"))
                    .totalLeaves(rs.getInt("assigned_leave"))
                    .pendingLeaves(rs.getInt("balance_leave"))
                    .overTimeHours(rs.getInt("total_overtime_hours"));

            // Document data mapping
            Long empId = rs.getLong("emp_id");
            String documentQuery = """
                    SELECT ed.*, dm.name AS document_name, dm.type AS document_type
                    FROM t_employee_document ed
                    JOIN t_document_master dm ON ed.document_id = dm.DOCUMENT_ID
                    WHERE ed.emp_id = :empId AND ed.is_active = 1
                    """;

            Map<String, Object> documentParams = new HashMap<>();
            documentParams.put("empId", empId);

            List<EmployeeDocumentVO> documentList = namedParameterJdbcTemplate.query(documentQuery, documentParams, (docRs, docRowNum) -> {
                return EmployeeDocumentVO.builder()
                        .id(docRs.getLong("id"))
                        .documentName(docRs.getString("document_name"))
                        .documentType(docRs.getString("document_type"))
                        .filePath(docRs.getString("file_path"))
                        .build();
            });

            employeeBuilder.documents(documentList);

            return employeeBuilder.build();
        });

        return new PageImpl<>(employeeList, pageable, total);
    }

    //employee listing
    public void saveExcelFileLocally(List<EmployeeVO> employees) {
        // Define the file path where you want to save the Excel file
        String directoryPath = "C:\\downloadfile\\";
        String baseFileName = "employees";  // Base file name

        // Create a file object for the directory
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            directory.mkdirs();  // Create the directory if it doesn't exist
        }

        // Check if a file already exists with the base name, and if so, generate a unique name
        String filePath = getUniqueFilePath(directoryPath, baseFileName);

        try (// Create an in-memory workbook
             Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Employees");

            // Create Header Row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                    "Employee ID", "Company ID", "Department ID", "Department Name", "Employee Code",
                    "Employee Name", "UID", "Biometric ID", "ID No", "Upper ID", "Region ID",
                    "Country ID", "Province ID", "City", "Upper Name", "Hire Date",
                    "Gender", "Birthday", "Nation", "Married", "Phone No", "Mobile No", "Email",
                    "Native Place", "Zip Code", "Is History", "In Service", "Remark", "Created By",
                    "Created Time", "Updated By", "Version", "Native Language", "Foreign Languages",
                    "Work Years", "Graduate School", "Graduate Time", "Highest Degree", "Image URL",
                    "Branch ID", "Branch Name", "Default Shift", "Working Days in Week", "Leave On Days",
                    "Designation ID", "Designation Name", "Default Time Slot ID", "Unit Number", "Street",
                    "Alternate Number", "Emergency Contact 1", "Emergency Contact 2",
                    "Emergency Contact Name 1", "Emergency Contact Name 2", "Alternate Email",
                    "Total Leaves", "Pending Leaves", "Overtime Hours"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // Populate Employee Data
            int rowNum = 1;
            for (EmployeeVO employee : employees) {
                Row row = sheet.createRow(rowNum++);
                int col = 0;
                row.createCell(col++).setCellValue(employee.getEmpId());
                row.createCell(col++).setCellValue(employee.getCompanyId());
                row.createCell(col++).setCellValue(employee.getDepartmentId());
                row.createCell(col++).setCellValue(employee.getDepartment());
                row.createCell(col++).setCellValue(employee.getEmpCode());
                row.createCell(col++).setCellValue(employee.getEmpName());
                row.createCell(col++).setCellValue(employee.getUid());
                row.createCell(col++).setCellValue(employee.getBiometricID());
                row.createCell(col++).setCellValue(employee.getIdNo());
                row.createCell(col++).setCellValue(employee.getUpperId());
                row.createCell(col++).setCellValue(employee.getRegionId());
                row.createCell(col++).setCellValue(employee.getCountryId());
                row.createCell(col++).setCellValue(employee.getProvinceId());
                row.createCell(col++).setCellValue(employee.getCity());
                row.createCell(col++).setCellValue(employee.getUpperName());
                row.createCell(col++).setCellValue(employee.getHireDate() != null ? employee.getHireDate().toString() : "");
                row.createCell(col++).setCellValue(employee.getGender());
                row.createCell(col++).setCellValue(employee.getBirthday() != null ? employee.getBirthday().toString() : "");
                row.createCell(col++).setCellValue(employee.getNation());
                row.createCell(col++).setCellValue(employee.getMarried());
                row.createCell(col++).setCellValue(employee.getPhoneNo());
                row.createCell(col++).setCellValue(employee.getMobileNo());
                row.createCell(col++).setCellValue(employee.getEmail());
                row.createCell(col++).setCellValue(employee.getNativePlace());
                row.createCell(col++).setCellValue(employee.getZipCode());
                row.createCell(col++).setCellValue(employee.getIsHistory());
                row.createCell(col++).setCellValue(employee.getInService());
                row.createCell(col++).setCellValue(employee.getRemark());
                row.createCell(col++).setCellValue(employee.getCreatedBy());
                row.createCell(col++).setCellValue(employee.getCreatedTime() != null ? employee.getCreatedTime().toString() : "");
                row.createCell(col++).setCellValue(employee.getUpdatedBy());
                row.createCell(col++).setCellValue(employee.getVersion());
                row.createCell(col++).setCellValue(employee.getNativeLanguage());
                row.createCell(col++).setCellValue(employee.getForeignLanguages());
                row.createCell(col++).setCellValue(employee.getWorkYears());
                row.createCell(col++).setCellValue(employee.getGraduateSchool());
                row.createCell(col++).setCellValue(employee.getGraduateTime() != null ? employee.getGraduateTime().toString() : "");
                row.createCell(col++).setCellValue(employee.getHighestDegree());
                row.createCell(col++).setCellValue(employee.getImgUre());
                row.createCell(col++).setCellValue(employee.getBranchId());
                row.createCell(col++).setCellValue(employee.getBranchName());
                row.createCell(col++).setCellValue(employee.getDefaultShift());
                row.createCell(col++).setCellValue(employee.getWorkingDayCountInWeek());
                row.createCell(col++).setCellValue(employee.getLeaveOnDays());
                row.createCell(col++).setCellValue(employee.getDesignationId());
                row.createCell(col++).setCellValue(employee.getDesignation());
                row.createCell(col++).setCellValue(employee.getDefaultTimeSlotId());
                row.createCell(col++).setCellValue(employee.getUnitNumber());
                row.createCell(col++).setCellValue(employee.getStreet());
                row.createCell(col++).setCellValue(employee.getAlternateNumber());
                row.createCell(col++).setCellValue(employee.getEmergencyContact1());
                row.createCell(col++).setCellValue(employee.getEmergencyContact2());
                row.createCell(col++).setCellValue(employee.getEmergencyContactname1());
                row.createCell(col++).setCellValue(employee.getEmergencyContactname2());
                row.createCell(col++).setCellValue(employee.getAlternateEmail());
                row.createCell(col++).setCellValue(employee.getTotalLeaves());
                row.createCell(col++).setCellValue(employee.getPendingLeaves());
                row.createCell(col++).setCellValue(employee.getOverTimeHours());
            }

            // Auto-size columns for better readability
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            // Write the file to the local directory
            File file = new File(filePath);
            try (FileOutputStream fileOut = new FileOutputStream(file)) {
                workbook.write(fileOut);
            } catch (IOException e) {
                log.error("Error saving Excel file", e);
                throw new RuntimeException("Error saving Excel file", e);
            }
        } catch (IOException e) {
            // TODO Auto-generated catch block
            log.error("Error saving Excel file", e);
            throw new RuntimeException("Error saving Excel file", e);
        }
    }

    // Helper method to generate a unique file path
    private String getUniqueFilePath(String directoryPath, String baseFileName) {
        File file = new File(directoryPath + baseFileName + ".xlsx");
        int counter = 1;

        // Check if a file already exists with this name, and if so, increment the counter
        while (file.exists()) {
            file = new File(directoryPath + baseFileName + counter + ".xlsx");
            counter++;
        }

        return file.getPath();  // Return the unique file path
    }


    public Page<EmployeeVO> myEmployees(EmployeeFilterRequest filter) {

        log.info("Initiating call to fetch employees reporting to the logged-in user.");
        User employee = tokenService.getEmployeeFromToken();
        Long loggedInEmpId = employee.getEmployee().getEmpId();

        Pageable pageable = PageRequest.of(filter.getOffset(), filter.getLimit());

        // Ensure only authorized roles can access this
        if (employee != null && employee.getRole().equals("employee")) {
            throw new RuntimeException("You are not authorized to view these employee details");
        }

        StringBuilder query = new StringBuilder(" and emp.upper_id = :loggedInEmpId");
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("loggedInEmpId", loggedInEmpId);

        // Additional filters based on role, company, or branch
        if (employee.getRole().equalsIgnoreCase("superadmin")) {
            query.append(" and emp.company_id = :companyId");
            params.addValue("companyId", employee.getEmployee().getCompanyId());
        } else if (employee.getRole().equalsIgnoreCase("admin")) {
            query.append(" and emp.branch_id = :branchId");
            params.addValue("branchId", employee.getEmployee().getBranch().getId());
        }

        // Hospital
        List<Long> hospitalIds = filter.getHospitalId();
        if (hospitalIds != null && !hospitalIds.isEmpty()) {
            query.append(" AND emp.company_id IN (:hospitalIds)");
            params.addValue("hospitalIds", hospitalIds);
        }

        // Branch
        List<Long> branchIds = filter.getBranchIds();
        if (branchIds != null && !branchIds.isEmpty()) {
            query.append(" AND emp.branch_id IN (:branchIds)");
            params.addValue("branchIds", branchIds);
        }

        // Department
        List<Long> departmentIds = filter.getDepartmentIds();
        if (departmentIds != null && !departmentIds.isEmpty()) {
            query.append(" AND emp.department_id IN (:departmentIds)");
            params.addValue("departmentIds", departmentIds);
        }

        // Designation
        List<Long> designationIds = filter.getDesignationIds();
        if (designationIds != null && !designationIds.isEmpty()) {
            query.append(" AND emp.designation_id IN (:designationIds)");
            params.addValue("designationIds", designationIds);
        }

        // Search Query
        if (!StringUtils.isEmpty(filter.getQuery())) {
            query.append(" and ( dt.department_name like :searchText " +
                    " OR emp.emp_name like :searchText" +
                    " OR emp.upper_name like :searchText" +
                    " OR br.branch_name like :searchText" +
                    " OR emp.mobile_no like :searchText" +
                    " OR emp.email like :searchText)");
            params.addValue("searchText", "%" + filter.getQuery() + "%");
        }

        // Count Query
        String countQuery = CommonConstant.EmployeeQuery.Employee_EXTRACT_COUNT_QUERY + query;
        int total = namedParameterJdbcTemplate.queryForObject(countQuery, params, Integer.class);

        if (total == 0) {
            return new PageImpl<>(Collections.emptyList(), pageable, total);
        }

        // Main query to fetch paginated employee details
        query.append(" ORDER BY emp.DEPARTMENT_ID OFFSET :offset ROWS FETCH FIRST :limit ROWS ONLY");
        params.addValue("limit", filter.getLimit());
        params.addValue("offset", Math.max(0, filter.getOffset() - 1) * filter.getLimit());

        String resultQuery = CommonConstant.EmployeeQuery.EMPLOYEE_EXTRACT_QUERY + query;

        // Map results to EmployeeVO
        List<EmployeeVO> employeeList = namedParameterJdbcTemplate.query(resultQuery, params, (rs, rowNum) ->
                EmployeeVO.builder()
                        .empId(rs.getLong("emp_id"))
                        .companyId(rs.getLong("company_id"))
                        .departmentId(rs.getLong("department_id"))
                        .department(rs.getString("department_name"))
                        .empCode(rs.getString("emp_code"))
                        .empName(rs.getString("emp_name"))
                        .uid(rs.getString("uid"))
                        .biometricID(rs.getString("uid"))
                        .idNo(rs.getString("id_no"))
                        .upperId(rs.getLong("upper_id"))
                        .regionId(rs.getLong("region_id"))
                        .countryId(rs.getLong("country_id"))
                        .provinceId(rs.getLong("province_id"))
                        .city(rs.getString("city"))
                        .upperName(rs.getString("upper_name"))
                        .hireDate(rs.getDate("hire_date") != null ? rs.getDate("hire_date") : null)
                        .gender(rs.getString("gender"))
                        .birthday(rs.getDate("birthday") != null ? rs.getDate("birthday") : null)
                        .nation(rs.getString("nation"))
                        .married(rs.getBoolean("married"))
                        .phoneNo(rs.getString("phone_no"))
                        .mobileNo(rs.getString("mobile_no"))
                        .email(rs.getString("email"))
                        .nativePlace(rs.getString("native_place"))
                        .zipCode(rs.getString("zip_code"))
                        .isHistory(rs.getBoolean("is_history"))
                        .inService(rs.getBoolean("in_service"))
                        .remark(rs.getString("remark"))
                        .createdBy(rs.getLong("created_by"))
                        .createdTime(rs.getTimestamp("created_time").toLocalDateTime())
                        .updatedBy(rs.getLong("updated_by"))
                        .version(rs.getLong("version"))
                        .nativeLanguage(rs.getString("native_language"))
                        .foreignLanguages(rs.getString("foreign_languages"))
                        .workYears(rs.getInt("work_years"))
                        .graduateSchool(rs.getString("graduate_school"))
                        .graduateTime(rs.getDate("graduate_time") != null ? rs.getDate("graduate_time") : null)
                        .highestDegree(rs.getString("highest_degree"))
                        .imgUre(rs.getString("img_ure"))
                        .branchId(rs.getLong("branch_id"))
                        .branchName(rs.getString("branch_name"))
                        .defaultShift(rs.getString("default_shift"))
                        .workingDayCountInWeek(rs.getString("working_day_count_in_week"))
                        .leaveOnDays(rs.getString("leave_on_days"))
                        .designationId(rs.getLong("designation_id"))
                        .designation(rs.getString("designation_name"))
                        .defaultTimeSlotId(rs.getLong("default_time_slot_id"))
                        .unitNumber(rs.getString("unit_no"))
                        .street(rs.getString("street"))
                        .alternateNumber(rs.getString("alternateNumber"))
                        .emergencyContact1(rs.getString("emergencyContact1"))
                        .emergencyContact2(rs.getString("emergencyContact2"))
                        .emergencyContactname1(rs.getString("emergencyContactname1"))
                        .emergencyContactname2(rs.getString("emergencyContactname2"))
                        .alternateEmail(rs.getString("alternateEmail"))
                        .totalLeaves(rs.getInt("assigned_leave"))
                        .pendingLeaves(rs.getInt("balance_leave"))
                        .overTimeHours(rs.getInt("total_overtime_hours"))
                        .build()
        );

        return new PageImpl<>(employeeList, pageable, total);
    }


    //get employee detail by id
  /*  public EmployeeDetailsDto getEmployeeByIdDetails(Long id, HttpServletRequest request) {
        // Assuming you have an EmployeeRepository that handles data fetching
        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found with id: " + id));

        // Map the Employee entity to EmployeeDetailsDto
        return mapToEmployeeDetailsDtoForIdWiseData(employee, request);
    }

    private EmployeeDetailsDto mapToEmployeeDetailsDtoForIdWiseData(Employee employee, HttpServletRequest request) {
        EmployeeDetailsDto dto = new EmployeeDetailsDto();
        // Populate dto fields from employee entity
        dto.setEmpId(employee.getEmpId());
        dto.setCompanyId(employee.getCompanyId());
        dto.setEmpCode(employee.getEmpCode());
        dto.setEmpName(employee.getEmpName());
        dto.setUid(employee.getUid());
        dto.setIdNo(employee.getIdNo());
        dto.setOrgId(employee.getOrgId());
        dto.setUpperId(employee.getUpperId());
        dto.setRegionId(employee.getRegionId());
        dto.setCountryId(employee.getCountryId());
        dto.setProvinceId(employee.getProvinceId());
        dto.setCity(employee.getCity());
        dto.setUpperName(employee.getUpperName());
        dto.setHireDate(employee.getHireDate());
        dto.setGender(employee.getGender());
        dto.setBirthday(employee.getBirthday());
        dto.setNation(employee.getNation());
        dto.setMarried(employee.getMarried());
        dto.setPhoneNo(employee.getPhoneNo());
        dto.setMobileNo(employee.getMobileNo());
        dto.setEmail(employee.getEmail());
        dto.setNativePlace(employee.getNativePlace());
        dto.setZipCode(employee.getZipCode());
        dto.setHistory(employee.getIsHistory());
        dto.setInService(employee.getInService());
        dto.setRemark(employee.getRemark());
        dto.setCreatedBy(employee.getCreatedBy());
        dto.setCreatedTime(employee.getCreatedTime());
        dto.setUpdatedBy(employee.getUpdatedBy());
        dto.setUpdatedTime(employee.getUpdatedTime());
        dto.setVersion(employee.getVersion());
        dto.setNativeLanguage(employee.getNativeLanguage());
        dto.setForeignLanguages(employee.getForeignLanguages());
        dto.setWorkYears(employee.getWorkYears());
        dto.setGraduateSchool(employee.getGraduateSchool());
        dto.setGraduateTime(employee.getGraduateTime());
        dto.setHighestDegree(employee.getHighestDegree());

        String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
        dto.setImgUre(baseUrl + employee.getImgUre());  // Consider changing imgUre to imgUrl for clarity

        // Set branch information
        if (employee.getBranch() != null) {
            dto.setBranchId(employee.getBranch().getId());
            dto.setBranchName(employee.getBranch().getBranchName());
        }

        // Set department information
        if (employee.getDepartment() != null) {
            dto.setDepartmentId(employee.getDepartment().getDepartmentId());
            dto.setDeptName(employee.getDepartment().getDepartmentName());
        }

        // Set designation information
        if (employee.getDesignation() != null) {
            dto.setDesignationId(employee.getDesignation().getId());
            dto.setDesignationName(employee.getDesignation().getName());
            dto.setRole(employee.getDesignation().getRole());
        }
        // Add other fields as necessary
        return dto;
    }*/

    public EmployeeVO getEmployeeByIdDetails(Long id, HttpServletRequest request) {
        Employee employee = employeeRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found with id: " + id));

        // Fetch documents for the employee

        List<EmployeeDocument> documents = employeeDocumentRepository.findByEmpIdAndIsActiveTrueOrderByIdDesc(id);
        List<EmployeeLeaveBalance> leaveBalances = employeeLeaveBalanceRepository.findByEmpId(employee.getEmpId());

        // Map the Employee entity to EmployeeVO DTO
        return mapToEmployeeVO(employee, documents, leaveBalances, request);
    }

    private EmployeeVO mapToEmployeeVO(Employee employee, List<EmployeeDocument> documents, List<EmployeeLeaveBalance> leaveBalances, HttpServletRequest request) {

        String companyName = feederHospitalRepository.findCompanyNameById(employee.getCompanyId());
        // Fetch district name
        String districtName = districtRepository.findById(Long.valueOf(employee.getCity()))
                .map(District::getName)
                .orElse("Unknown District");

        // Fetch sub-district name
        String subDistrictName = subDistrictRepository.findById(employee.getRegionId())
                .map(SubDistrict::getName)
                .orElse("Unknown Sub-District");


        // Convert hireDate to LocalDate
        Date hireDate = employee.getHireDate();
        BigDecimal probationPeriod = employee.getProbationPeriod(); // Fetch probation period in months

        LocalDateTime probationEndDate = null;
        String isInProbation = null; // Default value

        if (hireDate != null && probationPeriod != null) {
            LocalDate localHireDate = hireDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            probationEndDate = localHireDate.plusMonths(probationPeriod.intValue()).atStartOfDay();

           /* // Check if the employee is still in the probation period
            if (probationEndDate.isAfter(LocalDateTime.now())) {
                isInProbation = "Yes"; // Employee is still in probation
            }*/
            // Check if the employee is still in the probation period
            isInProbation = probationEndDate.isAfter(LocalDateTime.now()) ? "Yes" : "No";
        }

        // Map documents to EmployeeDocumentVO
        List<EmployeeDocumentVO> documentVOs = documents.stream().map(doc -> {
            // Fetch document metadata
            DocumentMaster documentMetadata = documentMasterRepository.findById(doc.getDocumentId())
                    .orElseThrow(() -> new EntityNotFoundException("Document not found with id: " + doc.getDocumentId()));

            // Map to EmployeeDocumentVO
            return EmployeeDocumentVO.builder()
                    .id(doc.getId())
                    .documentId(doc.getDocumentId())
                    .documentName(documentMetadata.getName()) // Add name
                    .documentType(documentMetadata.getType()) // Add type
                    .filePath(doc.getFile())
                    .build();
        }).toList();


        // Map leave balances to EmployeeLeaveBalanceVO
        List<EmployeeLeaveBalanceDto> leaveBalanceVOs = leaveBalances.stream().map(balance -> {
            String leaveType = leaveMasterRepository.findById(balance.getLeaveId())
                    .map(LeaveMaster::getType)
                    .orElse("Unknown"); // Fetch leave type from master or default to "Unknown"

            return EmployeeLeaveBalanceDto.builder()
                    .empLeaveId(balance.getEmpLeaveId())
                    .empId(balance.getEmpId())
                    .leaveId(balance.getLeaveId())
                    .leaveName(leaveType)
                    .assignedLeave(balance.getAssignedLeave())
                    .balanceLeave(balance.getBalanceLeave())
                    .build();
        }).toList();


        // Build the EmployeeVO object using a builder pattern
        return EmployeeVO.builder()
                .empId(employee.getEmpId())
                .companyId(employee.getCompanyId())
                .companyName(companyName)
                .empCode(employee.getEmpCode())
                .empName(employee.getEmpName())
                .uid(employee.getUid())
                .workZone(employee.getWorkZone())
                .idNo(employee.getIdNo())
                .orgId(employee.getOrgId())
                .upperId(employee.getUpperId())
                .regionId(employee.getRegionId())
                .subDistrictName(subDistrictName)
                .countryId(employee.getCountryId())
                .provinceId(employee.getProvinceId())
                .provinceName(employee.getProvinceId() != null ? provinceRepository.findById(employee.getProvinceId())
                        .map(Province::getName).orElse("Unknown Province") : "Unknown Province")
                .city(employee.getCity())
                .districtName(districtName)
               // .upperName(employee.getUpperName())
                .upperName(employee.getUpperId()!= null ? employeeRepository.findById(employee.getUpperId())
                        .map(Employee::getEmpName).orElse("Unknown Employee") : "Unknown Employee")
                .hireDate(employee.getHireDate())
                .gender(employee.getGender())
                .birthday(employee.getBirthday())
                .nation(employee.getNation())
                .married(employee.getMarried())
                .phoneNo(employee.getPhoneNo())
                .mobileNo(employee.getMobileNo())
                .email(employee.getEmail())
                .nativePlace(employee.getNativePlace())
                .zipCode(employee.getZipCode())
                .isHistory(employee.getIsHistory())
                .inService(employee.getInService())
                .remark(employee.getRemark())
                .createdBy(employee.getCreatedBy())
                .createdTime(employee.getCreatedTime())
                .updatedBy(employee.getUpdatedBy())
                .updatedTime(employee.getUpdatedTime())
                .version(employee.getVersion())
                .nativeLanguage(employee.getNativeLanguage())
                .foreignLanguages(employee.getForeignLanguages())
                .workYears(employee.getWorkYears())
                .graduateSchool(employee.getGraduateSchool())
                .graduateTime(employee.getGraduateTime())
                .highestDegree(employee.getHighestDegree())
                // .imgUre(getImageUrl(employee.getImgUre(), request))
                .imgUre(employee.getImgUre())
                .branchName(employee.getBranch() != null ? employee.getBranch().getBranchName() : null)
                .branchId(employee.getBranch() != null ? employee.getBranch().getId() : null)
                .designation(employee.getDesignation() != null ? employee.getDesignation().getName() : null)
                .designationId(employee.getDesignation() != null ? employee.getDesignation().getId() : null)
                .role(employee.getDesignation() != null ? employee.getDesignation().getRole() : null)
                .department(employee.getDepartment() != null ? employee.getDepartment().getDepartmentName() : null)
                .departmentId(employee.getDepartment() != null ? employee.getDepartment().getDepartmentId() : null)
                .defaultTimeSlotId(employee.getDefaultTimeSlotId())
                .startTime(employee.getDefaultTimeSlotId() != null ?
                        timeSlotRepository.findById(employee.getDefaultTimeSlotId())
                                .map(TimeSlot::getStartTime).orElse(null) : null)
                .endTime(employee.getDefaultTimeSlotId() != null ?
                        timeSlotRepository.findById(employee.getDefaultTimeSlotId())
                                .map(TimeSlot::getEndTime).orElse(null) : null)
                .leaveOnDays(employee.getLeaveOnDays())
                .street(employee.getStreet())
                .unitNumber(employee.getUnitNumber())
                .alternateNumber(employee.getAlternateNumber())
                .alternateEmail(employee.getAlternateEmail())
                .emergencyContact1(employee.getEmergencyContact1())
                .emergencyContact2(employee.getEmergencyContact2())
                .emergencyContactname1(employee.getEmergencyContactname1())
                .emergencyContactname2(employee.getEmergencyContactname2())
                .biometricID(employee.getUid())
                .ethnicity(employee.getEthnicity())
                .nationalId(employee.getNationalId())
                .probationPeriod(employee.getProbationPeriod())
                .isInProbation(isInProbation)
                .probationEndDate(probationEndDate)
                .documents(documentVOs)
                .leaveBalances(leaveBalanceVOs)
                .build();
    }

    private String getImageUrl(String imgUre, HttpServletRequest request) {
        if (imgUre == null || imgUre.isEmpty()) {
            return null;
        }
        String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
        return baseUrl + imgUre;
    }


    //update emp

//    @Transactional
//    public EmployeeDetailsDto updateEmployee(Long empId, EmployeeDTO employeeDTO) throws IOException {
//        // Fetch the existing employee from the database
//        Employee employee = employeeRepository.findById(empId)
//                .orElseThrow(() -> new EntityNotFoundException("Employee not found with ID: " + empId));
//
//        // Check if email ID is being changed and if the new email already exists
//        if (!employee.getEmail().equals(employeeDTO.getEmail()) && employeeRepository.existsByEmail(employeeDTO.getEmail())) {
//            throw new IllegalArgumentException("Email ID already exists");
//        }
//
//        // Fetch the department and designation based on their IDs
//        Department department = departmentRepository.findById(employeeDTO.getDepartmentId())
//                .orElseThrow(() -> new EntityNotFoundException("Department not found"));
//        Designation designation = designationRepository.findById(employeeDTO.getDesignationId())
//                .orElseThrow(() -> new EntityNotFoundException("Designation not found"));
//
//        Branch branch = branchRepository.findById(employeeDTO.getBranchId())
//                .orElseThrow(() -> new EntityNotFoundException("Branch not found"));
//        // Update the existing employee entity with new values from employeeDTO
//        BeanUtils.copyProperties(employeeDTO, employee, "empId", "createdBy", "createdTime", "companyId"); // Avoid overwriting these fields
//
//        Long companyId;
//        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
//
//        // Update companyId logic similar to the createEmployee method
//        if (employeeDTO.getCompanyId() != null && employeeDTO.getCompanyId() > 0) {
//            companyId = employeeDTO.getCompanyId();
//        } else {
//            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//            String loggedInUsername = authentication.getName();
//            User loggedInUser = userRepository.findByUsername(loggedInUsername);
//            if (loggedInUser == null) {
//                throw new EntityNotFoundException("Logged-in user not found");
//            }
//            companyId = loggedInUser.getEmployee().getCompanyId();
//        }
//        employee.setCompanyId(companyId);
//
//        // Update department, designation, and other fields
//        employee.setDepartment(department);
//        employee.setDesignation(designation);
//        employee.setBranch(branch);
//        employee.setUid(employeeDTO.getBiometricID());
//        employee.setRegionId(employeeDTO.getSuburb());
//        employee.setUpperId(employeeDTO.getReportingPerson());
//        employee.setWorkZone(employeeDTO.getWorkZone());
//        employee.setProbationPeriod(employeeDTO.getProbationPeriod());
//        employee.setEthnicity(employeeDTO.getEthnicity());
//
//
//        if (employeeDTO.getForeignLanguages() != null) {
//            String foreignLanguages = String.join(",", employeeDTO.getForeignLanguages());
//            employee.setForeignLanguages(foreignLanguages);
//        }
//
//        if (employeeDTO.getLeaveOnDays() != null) {
//            String leaveOnDays = String.join(",", employeeDTO.getLeaveOnDays());
//            employee.setLeaveOnDays(leaveOnDays);
//        }
//
//
//        // Handle profile picture update (if provided)
//        if (employeeDTO.getBase64EncodeFile() != null && !employeeDTO.getBase64EncodeFile().isEmpty()) {
//            byte[] imageBytes = Base64.getDecoder().decode(employeeDTO.getBase64EncodeFile());
//            uploadProfilePic(imageBytes, String.valueOf(employeeDTO.getCompanyId()), employeeDTO.getFileName(), employee);
//        }
//
//        // Update timestamps
//        employee.setUpdatedBy(Long.valueOf(loggedInEmpId));
//        employee.setUpdatedTime(LocalDateTime.now());
//
//        if (employeeDTO.getReportingPerson() != null && employeeDTO.getReportingPerson() != 0) {
//            Employee upperEmployee = employeeRepository.findById(employeeDTO.getReportingPerson())
//                    .orElseThrow(() -> new EntityNotFoundException("Upper employee not found with ID: " + employeeDTO.getUpperId()));
//            employee.setUpperName(upperEmployee.getEmpName());
//        }
//
//
//        // Save the updated employee back to the database
//        employeeRepository.saveAndFlush(employee);
//
//        // Check if the user exists for the given empId before updating the role
//        Optional<User> optionalUser = userRepository.findByEmpId(empId);
//        if (optionalUser.isPresent()) {
//            User user = optionalUser.get();
//            String role = designation.getRole();
//            user.setRole(role);
//            userRepository.save(user);
//        }
//
//        // Execute employee schedule logic (if needed)
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        List<Integer> leaveOnDaysIntegers = convertLeaveOnDaysToIntegers(employeeDTO.getLeaveOnDays().stream().map(Object::toString).collect(Collectors.joining(", ")));
//        for (Integer leaveDay : leaveOnDaysIntegers) {
//            employeeScheduleExecutor.executeEmployeeSchedule(employee.getEmpId(), sdf.format(employee.getHireDate()), leaveDay.toString(), "1");
//        }
//
//        // Map updated entity to DTO and return
//        return mapToEmployeeDetailsDto(employee);
//    }

    @Transactional
    public EmployeeDetailsDto updateEmployee(Long empId, EmployeeDTO employeeDTO, MultipartFile image) throws IOException {
        // Fetch the existing employee from the database
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found with ID: " + empId));

        // Check if email ID is being changed and if the new email already exists
        if (!employee.getEmail().equals(employeeDTO.getEmail()) && employeeRepository.existsByEmail(employeeDTO.getEmail())) {
            throw new IllegalArgumentException("Email ID already exists");
        }

        // Fetch the department and designation based on their IDs
        Department department = departmentRepository.findById(employeeDTO.getDepartmentId())
                .orElseThrow(() -> new EntityNotFoundException("Department not found"));
        Designation designation = designationRepository.findById(employeeDTO.getDesignationId())
                .orElseThrow(() -> new EntityNotFoundException("Designation not found"));

        Branch branch = branchRepository.findById(employeeDTO.getBranchId())
                .orElseThrow(() -> new EntityNotFoundException("Branch not found"));
        // Update the existing employee entity with new values from employeeDTO
        BeanUtils.copyProperties(employeeDTO, employee, "empId", "createdBy", "createdTime", "companyId"); // Avoid overwriting these fields

        Long companyId;
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        // Update companyId logic similar to the createEmployee method
        if (employeeDTO.getCompanyId() != null && employeeDTO.getCompanyId() > 0) {
            companyId = employeeDTO.getCompanyId();
        } else {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String loggedInUsername = authentication.getName();
            User loggedInUser = userRepository.findByUsername(loggedInUsername);
            if (loggedInUser == null) {
                throw new EntityNotFoundException("Logged-in user not found");
            }
            companyId = loggedInUser.getEmployee().getCompanyId();
        }
        employee.setCompanyId(companyId);

        //Updating image of employee
        if (image != null && !image.isEmpty()) {
            // Upload file to S3 bucket
            String fileName = image.getOriginalFilename(); // Get the file name
            long fileSize = image.getSize();
            String fileContentType = image.getContentType();

            // Validate file type (allow only image/jpeg)
            if (fileContentType == null || !ALLOWED_FILE_TYPES.contains(fileContentType)) {
                throw new IllegalArgumentException("Only .jpeg files are allowed.");
            }

            // Validate file size (15 KB to 250 KB)
            if (fileSize < 15 * 1024 || fileSize > 250 * 1024) {
                throw new IllegalArgumentException("File size must be between 15 KB and 250 KB.");
            }
            String uniqueFileName = "employee/" + companyId + "/" + System.currentTimeMillis() + "_" + fileName;

            s3Service.uploadFile(image, uniqueFileName);

            // Set the file name to be saved in the database
            employee.setImgUre(uniqueFileName);
        }

        // Update department, designation, and other fields
        employee.setDepartment(department);
        employee.setDesignation(designation);
        employee.setBranch(branch);
        employee.setUid(employeeDTO.getBiometricID());
        employee.setRegionId(employeeDTO.getSuburb());
        employee.setUpperId(employeeDTO.getReportingPerson());
        employee.setWorkZone(employeeDTO.getWorkZone());
        employee.setProbationPeriod(employeeDTO.getProbationPeriod());
        employee.setEthnicity(employeeDTO.getEthnicity());


        if (employeeDTO.getForeignLanguages() != null) {
            String foreignLanguages = String.join(",", employeeDTO.getForeignLanguages());
            employee.setForeignLanguages(foreignLanguages);
        }

        if (employeeDTO.getLeaveOnDays() != null) {
            String leaveOnDays = String.join(",", employeeDTO.getLeaveOnDays());
            employee.setLeaveOnDays(leaveOnDays);
        }


//        // Handle profile picture update (if provided)
//        if (employeeDTO.getBase64EncodeFile() != null && !employeeDTO.getBase64EncodeFile().isEmpty()) {
//            byte[] imageBytes = Base64.getDecoder().decode(employeeDTO.getBase64EncodeFile());
//            uploadProfilePic(imageBytes, String.valueOf(employeeDTO.getCompanyId()), employeeDTO.getFileName(), employee);
//        }

        // Update timestamps
        employee.setUpdatedBy(Long.valueOf(loggedInEmpId));
        employee.setUpdatedTime(LocalDateTime.now());

        if (employeeDTO.getReportingPerson() != null && employeeDTO.getReportingPerson() != 0) {
            Employee upperEmployee = employeeRepository.findById(employeeDTO.getReportingPerson())
                    .orElseThrow(() -> new EntityNotFoundException("Upper employee not found with ID: " + employeeDTO.getUpperId()));
            employee.setUpperName(upperEmployee.getEmpName());
        }


        // Save the updated employee back to the database
        employeeRepository.saveAndFlush(employee);

        // Check if the user exists for the given empId before updating the role
        Optional<User> optionalUser = userRepository.findByEmpId(empId);
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            String role = designation.getRole();
            user.setRole(role);
            userRepository.save(user);
        }

        // Execute employee schedule logic (if needed)
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<Integer> leaveOnDaysIntegers = convertLeaveOnDaysToIntegers(employeeDTO.getLeaveOnDays().stream().map(Object::toString).collect(Collectors.joining(", ")));
        for (Integer leaveDay : leaveOnDaysIntegers) {
            employeeScheduleExecutor.executeEmployeeSchedule(employee.getEmpId(), sdf.format(employee.getHireDate()), leaveDay.toString(), "1");
        }

        // Map updated entity to DTO and return
        return mapToEmployeeDetailsDto(employee);
    }

    //This method will be for updating employee details by employee.
    public SelfEmployeeEditDTO updateSelfEmployee(SelfEmployeeEditDTO selfEmployeeEditDTO) {
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
        Optional<Employee> employee = employeeRepository.findById(loggedInEmpId);
        Employee employee1 = employee.get();
        employee1.setEmergencyContactname1(selfEmployeeEditDTO.getEmergencyContactname1());
        employee1.setEmergencyContactname2(selfEmployeeEditDTO.getEmergencyContactname2());
        employee1.setEmergencyContact1(selfEmployeeEditDTO.getEmergencyContact1());
        employee1.setEmergencyContact2(selfEmployeeEditDTO.getEmergencyContact2());
        employeeRepository.save(employee1);
        return selfEmployeeEditDTO;
    }
   /* public Long findEmpIdById(Long id) {
        // Convert the Long ID to String format (assuming ID corresponds to UID in atteninfo)
        String uid = id.toString();

        // Use the repository method to find empId by uid
        return employeeRepository.findEmpIdByUid(uid);
    }
*/

    public Long findEmpIdById(Long id) {
        // Convert Long ID to String format (assuming it matches the UID type)
        String uid = id.toString();

        // Find the Employee entity by UID and return its empId
        Employee employee = employeeRepository.findByUid(uid);
        return employee != null ? employee.getEmpId() : null; // Return null if no match found
    }

    private String covertDateTo24HoursFormat(String date) throws ParseException {
        DateFormat dateFormat = new SimpleDateFormat("hh:mm:ss aa");

        // Change the pattern into 24 hour format
        DateFormat format = new SimpleDateFormat("HH:mm:ss");

        Date time = null;
        // Converting the input String to Date
        time = dateFormat.parse(date);
        return format.format(time);

    }


    /*public void exportEmployeesToExcel(String filePath) throws IOException {
        List<Employee> employees = employeeRepository.findAll();

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Employees");

        // Define header row with all columns
        Row headerRow = sheet.createRow(0);
        String[] columns = {"EMP_ID", "COMPANY_ID", "DEPARTMENT_ID", "EMP_CODE", "EMP_NAME", "UID", "ID_NO",
                "ORG_ID", "UPPER_ID", "REGION_ID", "COUNTRY_ID", "PROVINCE_ID", "CITY", "UPPER_NAME",
                "HIRE_DATE", "GENDER", "BIRTHDAY", "NATION", "MARRIED", "PHONE_NO", "MOBILE_NO", "EMAIL",
                "NATIVE_PLACE", "ZIP_CODE", "IS_HISTORY", "IN_SERVICE", "REMARK", "CREATED_BY",
                "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "VERSION", "NATIVE_LANGUAGE",
                "FOREIGN_LANGUAGES", "WORK_YEARS", "GRADUATE_SCHOOL", "GRADUATE_TIME", "HIGHEST_DEGREE",
                "IMG_URE", "BRANCH_ID", "time_slot_id", "working_day_count_in_week", "DESIGNATION_ID",
                "DEFAULT_TIME_SLOT_ID", "LEAVE_ON_DAYS", "UNIT_NO", "STREET", "alternateNumber",
                "emergencyContact1", "emergencyContact2", "emergencyContactname1", "emergencyContactname2",
                "alternateEmail"};

        for (int i = 0; i < columns.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(columns[i]);
        }
          int rowNum = 1;
        for (Employee emp : employees) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(emp.getEmpId() != null ? emp.getEmpId() : 0L);
            row.createCell(1).setCellValue(emp.getCompanyId() != null ? emp.getCompanyId() : 0L);
            row.createCell(2).setCellValue(emp.getDepartment() != null && emp.getDepartment().getDepartmentId() != null ? emp.getDepartment().getDepartmentId() : 0L);
            row.createCell(3).setCellValue(emp.getEmpCode() != null ? emp.getEmpCode() : "");
            row.createCell(4).setCellValue(emp.getEmpName() != null ? emp.getEmpName() : "");
            row.createCell(5).setCellValue(emp.getUid() != null ? emp.getUid() : "");
            row.createCell(6).setCellValue(emp.getIdNo() != null ? emp.getIdNo() : "");
            row.createCell(7).setCellValue(emp.getOrgId() != null ? emp.getOrgId() : 0L);
            row.createCell(8).setCellValue(emp.getUpperId() != null ? emp.getUpperId() : 0L);
            row.createCell(9).setCellValue(emp.getRegionId() != null ? emp.getRegionId() : 0L);
            row.createCell(10).setCellValue(emp.getCountryId() != null ? emp.getCountryId() : 0L);
            row.createCell(11).setCellValue(emp.getProvinceId() != null ? emp.getProvinceId() : 0L);
            row.createCell(12).setCellValue(emp.getCity() != null ? emp.getCity() : "");
            row.createCell(13).setCellValue(emp.getUpperName() != null ? emp.getUpperName() : "");
            row.createCell(14).setCellValue(emp.getHireDate() != null ? emp.getHireDate().toString() : "");
            row.createCell(15).setCellValue(emp.getGender() != null ? emp.getGender() : "");
            row.createCell(16).setCellValue(emp.getBirthday() != null ? emp.getBirthday().toString() : "");
            row.createCell(17).setCellValue(emp.getNation() != null ? emp.getNation() : "");
           // row.createCell(18).setCellValue(emp.getMarried() != null ? emp.getMarried() : "");
            row.createCell(18).setCellValue(emp.getMarried() != null ? (emp.getMarried() ? "Yes" : "No") : "");
            row.createCell(19).setCellValue(emp.getPhoneNo() != null ? emp.getPhoneNo() : "");
            row.createCell(20).setCellValue(emp.getMobileNo() != null ? emp.getMobileNo() : "");
            row.createCell(21).setCellValue(emp.getEmail() != null ? emp.getEmail() : "");
            row.createCell(22).setCellValue(emp.getNativePlace() != null ? emp.getNativePlace() : "");
            row.createCell(23).setCellValue(emp.getZipCode() != null ? emp.getZipCode() : "");
           // row.createCell(24).setCellValue(emp.getIsHistory() != null ? emp.getIsHistory() : 0);
          //  row.createCell(24).setCellValue(emp.getIsHistory() != null ? (emp.getIsHistory() ? 1 : 0) : 0);
            row.createCell(24).setCellValue(emp.getIsHistory() != null ? (emp.getIsHistory() ? "Yes" : "No") : "No");

           // row.createCell(25).setCellValue(emp.getInService() != null ? emp.getInService() : 0);
            row.createCell(25).setCellValue(emp.getInService() != null ? (emp.getInService() ? "Yes" : "No") : "No");

            row.createCell(26).setCellValue(emp.getRemark() != null ? emp.getRemark() : "");
           // row.createCell(27).setCellValue(emp.getCreatedBy() != null ? emp.getCreatedBy() : "");
            row.createCell(27).setCellValue(emp.getCreatedBy() != null ? String.valueOf(emp.getCreatedBy()) : "");

            row.createCell(28).setCellValue(emp.getCreatedTime() != null ? emp.getCreatedTime().toString() : "");
           // row.createCell(29).setCellValue(emp.getUpdatedBy() != null ? emp.getUpdatedBy() : "");
            row.createCell(29).setCellValue(emp.getUpdatedBy() != null ? String.valueOf(emp.getUpdatedBy()) : "");

            row.createCell(30).setCellValue(emp.getUpdatedTime() != null ? emp.getUpdatedTime().toString() : "");
            row.createCell(31).setCellValue(emp.getVersion() != null ? emp.getVersion() : 0);
            row.createCell(32).setCellValue(emp.getNativeLanguage() != null ? emp.getNativeLanguage() : "");
            row.createCell(33).setCellValue(emp.getForeignLanguages() != null ? emp.getForeignLanguages() : "");
            row.createCell(34).setCellValue(emp.getWorkYears() != null ? emp.getWorkYears() : 0);
            row.createCell(35).setCellValue(emp.getGraduateSchool() != null ? emp.getGraduateSchool() : "");
            row.createCell(36).setCellValue(emp.getGraduateTime() != null ? emp.getGraduateTime().toString() : "");
            row.createCell(37).setCellValue(emp.getHighestDegree() != null ? emp.getHighestDegree() : "");
            row.createCell(38).setCellValue(emp.getImgUre() != null ? emp.getImgUre() : "");
            row.createCell(39).setCellValue(emp.getBranch() != null && emp.getBranch().getId() != null ? emp.getBranch().getId() : 0L);
            row.createCell(40).setCellValue(emp.getDefaultTimeSlotId() != null ? emp.getDefaultTimeSlotId() : 0L);
           // row.createCell(41).setCellValue(emp.getLeaveOnDays() != null ? emp.getLeaveOnDays() : 0);
            row.createCell(41).setCellValue(emp.getLeaveOnDays() != null ? emp.getLeaveOnDays() : "");
            row.createCell(42).setCellValue(emp.getUnitNumber() != null ? emp.getUnitNumber() : "");
            row.createCell(43).setCellValue(emp.getStreet() != null ? emp.getStreet() : "");
            row.createCell(44).setCellValue(emp.getAlternateNumber() != null ? emp.getAlternateNumber() : "");
            row.createCell(45).setCellValue(emp.getEmergencyContact1() != null ? emp.getEmergencyContact1() : "");
            row.createCell(46).setCellValue(emp.getEmergencyContact2() != null ? emp.getEmergencyContact2() : "");
            row.createCell(47).setCellValue(emp.getEmergencyContactname1() != null ? emp.getEmergencyContactname1() : "");
            row.createCell(48).setCellValue(emp.getEmergencyContactname2() != null ? emp.getEmergencyContactname2() : "");
            row.createCell(49).setCellValue(emp.getAlternateEmail() != null ? emp.getAlternateEmail() : "");
        }


        // Auto-size columns
        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // Write the output to a file
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
        }
        workbook.close();
    }*/

    public void exportEmployeesToExcel(HttpServletResponse response) throws IOException {
        String uniqueFilePath = generateUniqueFilePath();
        List<Employee> employees = employeeRepository.findAll();
        // Workbook workbook = new XSSFWorkbook();
        Workbook workbook = new SXSSFWorkbook(100);
        Sheet sheet = workbook.createSheet("Employees");

        // Create the header row with dynamic column names (adjust based on your fields)
        Row headerRow = sheet.createRow(0);
        String[] columns = {
                "EmpId", "CompanyId", "DepartmentId", "EmpCode", "EmpName", "UID", "IDNo",
                "OrgId", "UpperId", "RegionId", "CountryId", "ProvinceId", "City", "UpperName",
                "HireDate", "Gender", "Birthday", "Nation", "Married", "PhoneNo", "MobileNo",
                "Email", "NativePlace", "ZipCode", "IsHistory", "InService", "Remark",
                "CreatedBy", "CreatedTime", "UpdatedBy", "UpdatedTime", "Version", "NativeLanguage",
                "ForeignLanguages", "WorkYears", "GraduateSchool", "GraduateTime", "HighestDegree",
                "ImgUre", "BranchId", "TimeSlotId", "WorkingDayCountInWeek", "DESIGNATION_ID", "DefaultTimeSlotId",
                "LeaveOnDays", "UnitNumber", "Street", "AlternateNumber",
                "EmergencyContact1", "EmergencyContact2", "EmergencyContactName1",
                "EmergencyContactName2", "AlternateEmail"
        };

        // Create header cells
        for (int col = 0; col < columns.length; col++) {
            headerRow.createCell(col).setCellValue(columns[col]);
        }

        // Populate the data rows with employee information using counter++
        int rowNum = 1;
        for (Employee emp : employees) {
            Row row = sheet.createRow(rowNum++);

            // Initialize a counter to iterate over each cell in the row
            int counter = 0;

            // Reading and setting the employee data in the row
            row.createCell(counter++).setCellValue(emp.getEmpId());
            row.createCell(counter++).setCellValue(emp.getCompanyId());
            row.createCell(counter++).setCellValue(emp.getDepartment() != null && emp.getDepartment().getDepartmentId() != null ? emp.getDepartment().getDepartmentId() : 0L);
            row.createCell(counter++).setCellValue(emp.getEmpCode());
            row.createCell(counter++).setCellValue(emp.getEmpName());
            row.createCell(counter++).setCellValue(emp.getUid());
            row.createCell(counter++).setCellValue(emp.getIdNo());
            row.createCell(counter++).setCellValue(emp.getOrgId() != null ? emp.getOrgId() : 0L); // Default to 0 if null
            row.createCell(counter++).setCellValue(emp.getUpperId() != null ? emp.getUpperId() : 0L);
            row.createCell(counter++).setCellValue(emp.getRegionId() != null ? emp.getRegionId() : 0L);
            row.createCell(counter++).setCellValue(emp.getCountryId() != null ? emp.getCountryId() : 0L);
            row.createCell(counter++).setCellValue(emp.getProvinceId() != null ? emp.getProvinceId() : 0L);
            row.createCell(counter++).setCellValue(emp.getCity());
            row.createCell(counter++).setCellValue(emp.getUpperName());
            row.createCell(counter++).setCellValue(emp.getHireDate() != null ? emp.getHireDate().toString() : "");
            row.createCell(counter++).setCellValue(emp.getGender());
            row.createCell(counter++).setCellValue(emp.getBirthday() != null ? emp.getBirthday().toString() : "");
            row.createCell(counter++).setCellValue(emp.getNation());
            row.createCell(counter++).setCellValue(emp.getMarried() != null ? (emp.getMarried() ? "Yes" : "No") : "");
            row.createCell(counter++).setCellValue(emp.getPhoneNo());
            row.createCell(counter++).setCellValue(emp.getMobileNo());
            row.createCell(counter++).setCellValue(emp.getEmail());
            row.createCell(counter++).setCellValue(emp.getNativePlace());
            row.createCell(counter++).setCellValue(emp.getZipCode());
            row.createCell(counter++).setCellValue(emp.getIsHistory() != null ? (emp.getIsHistory() ? 1 : 0) : 0);
            row.createCell(counter++).setCellValue(emp.getInService());
            row.createCell(counter++).setCellValue(emp.getRemark());
            row.createCell(counter++).setCellValue(emp.getCreatedBy());
            row.createCell(counter++).setCellValue(emp.getCreatedTime() != null ? emp.getCreatedTime().toString() : "");
            row.createCell(counter++).setCellValue(emp.getUpdatedBy() != null ? String.valueOf(emp.getUpdatedBy()) : "");
            row.createCell(counter++).setCellValue(emp.getUpdatedTime() != null ? emp.getUpdatedTime().toString() : "");
            row.createCell(counter++).setCellValue(emp.getVersion() != null ? emp.getVersion() : 0);
            row.createCell(counter++).setCellValue(emp.getNativeLanguage());
            row.createCell(counter++).setCellValue(emp.getForeignLanguages());
            row.createCell(counter++).setCellValue(emp.getWorkYears() != null ? emp.getWorkYears() : 0);
            row.createCell(counter++).setCellValue(emp.getGraduateSchool());
            row.createCell(counter++).setCellValue(emp.getGraduateTime() != null ? emp.getGraduateTime().toString() : "");
            row.createCell(counter++).setCellValue(emp.getHighestDegree());
            row.createCell(counter++).setCellValue(emp.getImgUre());
            row.createCell(counter++).setCellValue(emp.getBranch() != null && emp.getBranch().getId() != null ? emp.getBranch().getId() : 0L);
            row.createCell(counter++).setCellValue((String) null);  // For WorkingDayCountInWeek
            row.createCell(counter++).setCellValue((String) null);
            row.createCell(counter++).setCellValue(emp.getDesignation() != null && emp.getDesignation().getId() != null ? emp.getDesignation().getId() : 0L);
            row.createCell(counter++).setCellValue(emp.getDefaultTimeSlotId() != null ? emp.getDefaultTimeSlotId() : 0L);
            row.createCell(counter++).setCellValue(emp.getLeaveOnDays());
            row.createCell(counter++).setCellValue(emp.getUnitNumber());
            row.createCell(counter++).setCellValue(emp.getStreet());
            row.createCell(counter++).setCellValue(emp.getAlternateNumber());
            row.createCell(counter++).setCellValue(emp.getEmergencyContact1());
            row.createCell(counter++).setCellValue(emp.getEmergencyContact2());
            row.createCell(counter++).setCellValue(emp.getEmergencyContactname1());
            row.createCell(counter++).setCellValue(emp.getEmergencyContactname2());
            row.createCell(counter++).setCellValue(emp.getAlternateEmail());
        }

        ((SXSSFSheet) sheet).trackAllColumnsForAutoSizing();

        // Auto-size columns
        for (int i = 0; i < columns.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // Write the output to a file
        try (FileOutputStream fileOut = new FileOutputStream(uniqueFilePath)) {
            workbook.write(fileOut);
        }
        // Dispose of temporary files (to prevent memory overflow in streaming mode)
        ((SXSSFWorkbook) workbook).dispose();
        workbook.close();
    }


    public String generateUniqueFilePath() {
        // Base directory and file name
        String directory = "C:/exports/";
        String fileName = "employees.xlsx";  // Base file name
        String uniqueFilePath = directory + fileName;

        // Check if the file already exists and increment the name
        File file = new File(uniqueFilePath);
        int count = 1;

        // Loop until we find a unique file name (e.g., employees(1).xlsx)
        while (file.exists()) {
            // Generate a new file name with incremented counter
            uniqueFilePath = directory + "employees(" + count + ").xlsx";
            file = new File(uniqueFilePath);
            count++;
        }

        return uniqueFilePath;
    }


    //employee document upload
    /*@Transactional
    public String saveFiles(Long documentId,MultipartFile[] files, HttpServletRequest request) throws IOException {

        Long empId = tokenService.getEmployeeIdFromToken();
        StringBuilder responseMessage = new StringBuilder();

        // Fetch the DocumentMaster by documentId
        DocumentMaster documentMaster = documentMasterRepository.findById(documentId)
                .orElseThrow(() -> new EntityNotFoundException("Document not found for ID: " + documentId));

        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                throw new IllegalArgumentException("One of the uploaded files is empty. Please upload valid files.");
            }

            // Validate file size
            if (file.getSize() > MAX_FILE_SIZE) {
                throw new IllegalArgumentException("File size exceeds the maximum limit of 5 MB.");
            }

            // Save the file to the appropriate folder
            Path folderPath = Paths.get(BASE_UPLOAD_DIRECTORY + documentId);
            if (!Files.exists(folderPath)) {
                Files.createDirectories(folderPath); // Create folder if it doesn't exist
            }

            Path filePath = folderPath.resolve(file.getOriginalFilename());
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // Build the file access URL
            String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
            String fileUrl = baseUrl + "/uploads/history/" + documentId + "/" + file.getOriginalFilename();
            //  String fileUrl ="/api/upload/employeHistory/" + documentId + "/" + file.getOriginalFilename();
            // Save metadata in the EmployeeDocument entity
            EmployeeDocument employeeDocument = new EmployeeDocument();
            employeeDocument.setEmpId(empId);
            employeeDocument.setDocumentId(documentMaster.getDocumentId());
            employeeDocument.setCreatedBy(empId);
            employeeDocument.setCreatedAt(LocalDateTime.now());
            employeeDocument.setIsActive(true);
            employeeDocument.setFile(fileUrl);
            employeeDocumentRepository.save(employeeDocument);

           // responseMessage.append("File uploaded successfully: ").append(fileUrl).append("\n");
            responseMessage.append("File uploaded successfully: ");
        }

        return responseMessage.toString();
    }*/


    @Transactional
    public String saveFile(Long empId, Long documentId, MultipartFile file, HttpServletRequest request) throws IOException {

        Long empIdtoken = tokenService.getEmployeeIdFromToken();
        try {
            // Fetch the DocumentMaster by documentId
            DocumentMaster documentMaster = documentMasterRepository.findById(documentId)
                    .orElseThrow(() -> new EntityNotFoundException("Document not found for ID: " + documentId));

            if (file.isEmpty()) {
                throw new IllegalArgumentException("The uploaded file is empty. Please upload a valid file.");
            }

            // Validate file size
            /*if (file.getSize() <= MAX_FILE_SIZE1) {
              //  throw new IllegalArgumentException("File size exceeds the maximum limit of 2 MB.");
                throw new MaxUploadSizeExceededException(MAX_FILE_SIZE1);
            }*/

            //new work of file upload
            String fileName = file.getOriginalFilename(); // Get the file name
//            String uniqueFileName = "leavemasterid/" +  + "/" + System.currentTimeMillis() + "_" + fileName;
            String uniqueFileName = "documentId/" + documentMaster.getDocumentId() + "/" + System.currentTimeMillis() + "_" + fileName;
            s3Service.uploadFile(file, uniqueFileName);

            // Set the file name to be saved in the database
            String fileUrl = uniqueFileName;


            // Save metadata in the EmployeeDocument entity
            EmployeeDocument employeeDocument = new EmployeeDocument();
            employeeDocument.setEmpId(empId);
            employeeDocument.setDocumentId(documentMaster.getDocumentId());
            employeeDocument.setCreatedBy(empIdtoken);
            employeeDocument.setCreatedAt(LocalDateTime.now());
            employeeDocument.setIsActive(true);
            employeeDocument.setFile(fileUrl);
            employeeDocumentRepository.save(employeeDocument);

            // return "File uploaded successfully: " + fileUrl;
            return "File uploaded successfully. ";
        } catch (EntityNotFoundException ex) {
            log.error("Document not found for document ID: {}. Employee ID: {}. Error: {}", documentId, empId, ex.getMessage());
            throw ex; // Propagate exception to the caller for further handling
        } catch (IllegalArgumentException ex) {
            log.error("Validation failed for employee ID: {}. Error: {}", empId, ex.getMessage());
            throw ex; // Propagate exception to the caller
        } catch (Exception ex) {
            log.error("Unexpected error occurred for employee ID: {}. Error: {}", empId, ex.getMessage());
            throw new RuntimeException("An unexpected error occurred. Please try again later.", ex);
        }


    }


    //delete api
    public String softDeleteDocument(Long empId, Long documentId) {
        try {
            Optional<EmployeeDocument> existingDocumentOptional = employeeDocumentRepository.findByEmpIdAndDocumentId(empId, documentId);

            if (!existingDocumentOptional.isPresent()) {
                log.error("Document not found with ID: {}", documentId);
                return "Document not found with ID: " + documentId;
            }

            EmployeeDocument existingDocument = existingDocumentOptional.get();

            // Set isActive to false (soft delete)
            existingDocument.setIsActive(false);  // Set this to false or 0 based on your design
            employeeDocumentRepository.save(existingDocument);  // Save the updated document

            log.info("Document with ID {} has been successfully soft deleted.", documentId);

            return "Employee Document has been soft deleted.";
        } catch (Exception e) {
            // Log the exception details
            log.error("Error occurred while trying to soft delete document with ID {}: {}", documentId, e.getMessage(), e);
            return "An error occurred while trying to soft delete the document with ID: " + documentId;
        }
    }


    public Page<EmployeeResponseDTO> getEmployeesByLoggedInUserBranch( Long branchId, String search, int offset, int limit) {
        try {
            // Get the branch ID of the logged-in user
            Long loggedInUserId = tokenService.getEmployeeIdFromToken(); // Fetch logged-in employee ID
            if (branchId == null) {
                loggedInUserId = tokenService.getEmployeeIdFromToken(); // Fetch logged-in employee ID
                branchId = employeeRepository.findById(loggedInUserId)
                        .orElseThrow(() -> new RuntimeException("Employee not found"))
                        .getBranch()
                        .getId();
            }

            // Pagination
            Pageable pageable = PageRequest.of(offset, limit);

            // Fetch employees matching the branch ID with pagination
            Page<Employee> employeesPage = employeeRepository.findByBranchIdNew(branchId, search, pageable);

            // Map to DTOs and return as Page
            return employeesPage.map(emp -> new EmployeeResponseDTO(emp.getEmpId(), emp.getEmpName(), emp.getBranch().getId(), emp.getBranch().getBranchName(), emp.getDepartment().getDepartmentId(), emp.getDepartment().getDepartmentName(), emp.getDesignation().getId(), emp.getDesignation().getName(), emp.getImgUre()));

        } catch (Exception e) {
            // Log the error and throw a custom exception
            throw new RuntimeException("Error while fetching employees: " + e.getMessage());
        }
    }


    //excel export base on filter

    public byte[] generateExcelFile(List<EmployeeVO> employees) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Employees");

            // Create Header Row
            String[] headers = {
                    "Employee ID", "Company ID", "Department ID", "Department Name", "Employee Code",
                    "Employee Name", "UID", "Biometric ID", "ID No", "Upper ID", "Region ID",
                    "Country ID", "Province ID", "City", "Upper Name", "Hire Date",
                    "Gender", "Birthday", "Nation", "Married", "Phone No", "Mobile No", "Email",
                    "Native Place", "Zip Code", "Is History", "In Service", "Remark", "Created By",
                    "Created Time", "Updated By", "Version", "Native Language", "Foreign Languages",
                    "Work Years", "Graduate School", "Graduate Time", "Highest Degree", "Image URL",
                    "Branch ID", "Branch Name", "Default Shift", "Working Days in Week", "Leave On Days",
                    "Designation ID", "Designation Name", "Default Time Slot ID", "Unit Number", "Street",
                    "Alternate Number", "Emergency Contact 1", "Emergency Contact 2",
                    "Emergency Contact Name 1", "Emergency Contact Name 2", "Alternate Email",
                    "Total Leaves", "Pending Leaves", "Overtime Hours"
            };

            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
            }

            // Populate Employee Data
            int rowNum = 1;
            for (EmployeeVO employee : employees) {
                Row row = sheet.createRow(rowNum++);
                int col = 0;
                row.createCell(col++).setCellValue(employee.getEmpId());
                row.createCell(col++).setCellValue(employee.getCompanyId());
                row.createCell(col++).setCellValue(employee.getDepartmentId());
                row.createCell(col++).setCellValue(employee.getDepartment());
                row.createCell(col++).setCellValue(employee.getEmpCode());
                row.createCell(col++).setCellValue(employee.getEmpName());
                row.createCell(col++).setCellValue(employee.getUid());
                row.createCell(col++).setCellValue(employee.getBiometricID());
                row.createCell(col++).setCellValue(employee.getIdNo());
                row.createCell(col++).setCellValue(employee.getUpperId());
                row.createCell(col++).setCellValue(employee.getRegionId());
                row.createCell(col++).setCellValue(employee.getCountryId());
                row.createCell(col++).setCellValue(employee.getProvinceId());
                row.createCell(col++).setCellValue(employee.getCity());
                row.createCell(col++).setCellValue(employee.getUpperName());
                row.createCell(col++).setCellValue(employee.getHireDate() != null ? employee.getHireDate().toString() : "");
                row.createCell(col++).setCellValue(employee.getGender());
                row.createCell(col++).setCellValue(employee.getBirthday() != null ? employee.getBirthday().toString() : "");
                row.createCell(col++).setCellValue(employee.getNation());
                row.createCell(col++).setCellValue(employee.getMarried());
                row.createCell(col++).setCellValue(employee.getPhoneNo());
                row.createCell(col++).setCellValue(employee.getMobileNo());
                row.createCell(col++).setCellValue(employee.getEmail());
                row.createCell(col++).setCellValue(employee.getNativePlace());
                row.createCell(col++).setCellValue(employee.getZipCode());
                row.createCell(col++).setCellValue(employee.getIsHistory());
                row.createCell(col++).setCellValue(employee.getInService());
                row.createCell(col++).setCellValue(employee.getRemark());
                row.createCell(col++).setCellValue(employee.getCreatedBy());
                row.createCell(col++).setCellValue(employee.getCreatedTime() != null ? employee.getCreatedTime().toString() : "");
                row.createCell(col++).setCellValue(employee.getUpdatedBy());
                row.createCell(col++).setCellValue(employee.getVersion());
                row.createCell(col++).setCellValue(employee.getNativeLanguage());
                row.createCell(col++).setCellValue(employee.getForeignLanguages());
                row.createCell(col++).setCellValue(employee.getWorkYears());
                row.createCell(col++).setCellValue(employee.getGraduateSchool());
                row.createCell(col++).setCellValue(employee.getGraduateTime() != null ? employee.getGraduateTime().toString() : "");
                row.createCell(col++).setCellValue(employee.getHighestDegree());
                row.createCell(col++).setCellValue(employee.getImgUre());
                row.createCell(col++).setCellValue(employee.getBranchId());
                row.createCell(col++).setCellValue(employee.getBranchName());
                row.createCell(col++).setCellValue(employee.getDefaultShift());
                row.createCell(col++).setCellValue(employee.getWorkingDayCountInWeek());
                row.createCell(col++).setCellValue(employee.getLeaveOnDays());
                row.createCell(col++).setCellValue(employee.getDesignationId());
                row.createCell(col++).setCellValue(employee.getDesignation());
                row.createCell(col++).setCellValue(employee.getDefaultTimeSlotId());
                row.createCell(col++).setCellValue(employee.getUnitNumber());
                row.createCell(col++).setCellValue(employee.getStreet());
                row.createCell(col++).setCellValue(employee.getAlternateNumber());
                row.createCell(col++).setCellValue(employee.getEmergencyContact1());
                row.createCell(col++).setCellValue(employee.getEmergencyContact2());
                row.createCell(col++).setCellValue(employee.getEmergencyContactname1());
                row.createCell(col++).setCellValue(employee.getEmergencyContactname2());
                row.createCell(col++).setCellValue(employee.getAlternateEmail());
                row.createCell(col++).setCellValue(employee.getTotalLeaves());
                row.createCell(col++).setCellValue(employee.getPendingLeaves());
                row.createCell(col++).setCellValue(employee.getOverTimeHours());
            }

            // System.out.println("Number of employees being downloaded: " + employees.size());

            // Write workbook to ByteArrayOutputStream
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            workbook.write(bos);
            workbook.close();
            return bos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Error generating Excel file", e);
        }
    }



    public String uploadExcelFile(MultipartFile file) {

        try {
            List<EmployeeLeaveBalance> employeeLeaveBalances = new ArrayList<>();
            List<String> missingEmployees = new ArrayList<>();
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            boolean isSheetEmpty = true;
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                isSheetEmpty = false; // Since we have at least one data row


                String empCode = getCellStringValue(row.getCell(0));
                String leaveType = getCellStringValue(row.getCell(1));
                Integer assignedLeave = getCellIntegerValue(row.getCell(2));
                Integer balanceLeave = getCellIntegerValue(row.getCell(3));
                Date creditDate = getCellDateValue(row.getCell(4));

                Long empId = extractEmployeeId(empCode);
                Long leaveId = extractLeaveId(leaveType);

               // if (empId == null || leaveId == null) continue;

                if (empId == null) {
                    missingEmployees.add(empCode);
                    continue;
                }
                if (leaveId == null) continue;

                // Check if record exists
                Optional<EmployeeLeaveBalance> existingRecord =
                        employeeLeaveBalanceRepository.findByEmpIdAndLeaveId(empId, leaveId);

                if (existingRecord.isPresent()) {
                    // Update existing record
                    EmployeeLeaveBalance leaveBalance = existingRecord.get();
                    leaveBalance.setAssignedLeave(assignedLeave);
                    leaveBalance.setBalanceLeave(balanceLeave);
                    leaveBalance.setCreditDate(creditDate);
                    leaveBalance.setUpdatedTime(LocalDateTime.now());
                    employeeLeaveBalanceRepository.save(leaveBalance);
                } else {
                    // Insert new record
                    EmployeeLeaveBalance leaveBalance = new EmployeeLeaveBalance();
                    leaveBalance.setEmpId(empId);
                    leaveBalance.setLeaveId(leaveId);
                    leaveBalance.setAssignedLeave(assignedLeave);
                    leaveBalance.setBalanceLeave(balanceLeave);
                    leaveBalance.setCreditDate(creditDate);
                    leaveBalance.setCreatedTime(LocalDateTime.now());
                    leaveBalance.setUpdatedTime(LocalDateTime.now());
                    employeeLeaveBalances.add(leaveBalance);
                }
            }
            workbook.close();
            if (isSheetEmpty) {
                return "Excel file is empty.";
            }

            if (!missingEmployees.isEmpty()) {
                return "The following employees are not present: " + String.join(", ", missingEmployees);
            }
            // Save all new records
            if (!employeeLeaveBalances.isEmpty()) {
                employeeLeaveBalanceRepository.saveAll(employeeLeaveBalances);
            }

            return "Excel file uploaded successfully!";
        }catch (IllegalArgumentException e) {
            return "Error: " + e.getMessage();
        } catch (IOException e) {
            return "Error processing Excel file: " + e.getMessage();
        }catch (Exception e) {
            return "An unexpected error occurred: " + e.getMessage();
        }
    }

    private String getCellStringValue(Cell cell) {
        return (cell != null) ? cell.getStringCellValue().trim() : "";
    }

    private Integer getCellIntegerValue(Cell cell) {
        return (cell != null && cell.getCellType() == CellType.NUMERIC) ? (int) cell.getNumericCellValue() : null;
    }

//    private Date getCellDateValue(Cell cell) {
//        return (cell != null && cell.getCellType() == CellType.NUMERIC) ? cell.getDateCellValue() : null;
//    }

    private Long extractEmployeeId(String empCode) {
        Optional<Employee> employee = employeeRepository.findByEmpCode(empCode);
        return employee.map(Employee::getEmpId).orElse(null);
    }

    private Long extractLeaveId(String type) {
        Optional<LeaveMaster> leave = leaveMasterRepository.findByType(type);
        return leave.map(LeaveMaster::getLeaveId).orElse(null);
    }

//new code

    public List<EmployeeVO> searchAllEmployees(EmployeeFilterRequest filter) {
        log.info("Fetching all employees for download");

        User employee = tokenService.getEmployeeFromToken();
        StringBuilder query = new StringBuilder();
        MapSqlParameterSource params = new MapSqlParameterSource();

        if (employee.getRole().equalsIgnoreCase("employee")) {
            throw new RuntimeException("You are not authorized to view the branch details");
        }

        if (employee.getRole().equalsIgnoreCase("superadmin")) {
            query.append(" AND emp.company_id = :companyId");
            params.addValue("companyId", employee.getEmployee().getCompanyId());
        } else if (employee.getRole().equalsIgnoreCase("admin")) {
            query.append(" AND emp.branch_id = :branchId");
            params.addValue("branchId", employee.getEmployee().getBranch().getId());
        } else if (employee.getRole().equalsIgnoreCase("supervisor")) {
            query.append(" AND emp.upper_id = :empId");
            params.addValue("empId", employee.getEmployee().getEmpId());
        }else if (employee.getRole().equalsIgnoreCase("ceo")) {
            if (filter.getHospitalId() != null) {
                query.append(" AND emp.company_id = :companyId");
                params.addValue("companyId", filter.getHospitalId());
            }
        }





        if (filter != null) {
            if (filter.getHospitalId() != null) {
                query.append(" AND emp.company_id = :companyId");
                params.addValue("companyId", filter.getHospitalId());
            }

            List<Long> hospitalIds = filter.getHospitalId();
            if (hospitalIds != null && !hospitalIds.isEmpty()) {
                query.append(" AND emp.company_id IN (:hospitalIds)");
                params.addValue("hospitalIds", hospitalIds);
            }

            List<Long> branchIds = filter.getBranchIds();
            if (branchIds != null && !branchIds.isEmpty()) {
                query.append(" AND emp.branch_id IN (:branchIds)");
                params.addValue("branchIds", branchIds);
            }

            List<Long> departmentIds = filter.getDepartmentIds();
            if (departmentIds != null && !departmentIds.isEmpty()) {
                query.append(" AND emp.department_id IN (:departmentIds)");
                params.addValue("departmentIds", departmentIds);
            }

            List<Long> designationIds = filter.getDesignationIds();
            if (designationIds != null && !designationIds.isEmpty()) {
                query.append(" AND emp.designation_id IN (:designationIds)");
                params.addValue("designationIds", designationIds);
            }

            if (!StringUtils.isEmpty(filter.getQuery())) {
               // query.append(" AND (emp.emp_name LIKE :searchText OR emp.email LIKE :searchText OR emp.mobile_no LIKE :searchText)");
           //     params.addValue("searchText", "%" + filter.getQuery() + "%");
                query.append(" AND ( dt.department_name LIKE :searchText " +
                        " OR emp.emp_name LIKE :searchText " +
                        " OR emp.upper_name LIKE :searchText " +
                        " OR br.branch_name LIKE :searchText " +
                        " OR emp.mobile_no LIKE :searchText " +
                        " OR emp.email LIKE :searchText )");
                params.addValue("searchText", "%" + filter.getQuery() + "%");

            }

            // Apply inService filter (matching logic from searchEmployees)
            if (filter.getInService() == null || filter.getInService().equals(1)) {
                query.append(" AND emp.in_service = 1");
            } else if (filter.getInService().equals(0)) {
                query.append(" AND emp.in_service = 0");
            }
        }

        String resultQuery = CommonConstant.EmployeeQuery.EMPLOYEE_EXTRACT_QUERY + query;
        log.info("Executing query: {}", resultQuery);
        List<EmployeeVO> employees = namedParameterJdbcTemplate.query(resultQuery, params, (rs, rowNum) -> {
            EmployeeVO.EmployeeVOBuilder employeeBuilder = EmployeeVO.builder()
                    .empId(rs.getLong("emp_id"))
                    .companyId(rs.getLong("company_id"))
                    .departmentId(rs.getLong("department_id"))
                    .department(rs.getString("department_name"))
                    .empCode(rs.getString("emp_code"))
                    .empName(rs.getString("emp_name"))
                    .uid(rs.getString("uid"))
                    .biometricID(rs.getString("uid"))
                    .idNo(rs.getString("id_no"))
                    .upperId(rs.getLong("upper_id"))
                    .regionId(rs.getLong("region_id"))
                    .countryId(rs.getLong("country_id"))
                    .provinceId(rs.getLong("province_id"))
                    .city(rs.getString("city"))
                    .upperName(rs.getString("upper_name"))
                    .hireDate(rs.getDate("hire_date"))
                    .gender(rs.getString("gender"))
                    .birthday(rs.getDate("birthday"))
                    .nation(rs.getString("nation"))
                    .married(rs.getBoolean("married"))
                    .phoneNo(rs.getString("phone_no"))
                    .mobileNo(rs.getString("mobile_no"))
                    .email(rs.getString("email"))
                    .nativePlace(rs.getString("native_place"))
                    .zipCode(rs.getString("zip_code"))
                    .isHistory(rs.getBoolean("is_history"))
                    .inService(rs.getBoolean("in_service"))
                    .remark(rs.getString("remark"))
                    .createdBy(rs.getLong("created_by"))
                    .createdTime(rs.getTimestamp("created_time") != null ? rs.getTimestamp("created_time").toLocalDateTime() : null)
                    .updatedBy(rs.getLong("updated_by"))
                    .updatedTime(rs.getTimestamp("updated_time") != null ? rs.getTimestamp("updated_time").toLocalDateTime() : null)
                    .version(rs.getLong("version"))
                    .nativeLanguage(rs.getString("native_language"))
                    .foreignLanguages(rs.getString("foreign_languages"))
                    .workYears(rs.getInt("work_years"))
                    .graduateSchool(rs.getString("graduate_school"))
                    .graduateTime(rs.getDate("graduate_time"))
                    .highestDegree(rs.getString("highest_degree"))
                    .imgUre(rs.getString("img_ure"))
                    .branchId(rs.getLong("branch_id"))
                    .branchName(rs.getString("branch_name"))
                    .defaultShift(rs.getString("default_shift"))
                    .workingDayCountInWeek(rs.getString("working_day_count_in_week"))
                    .leaveOnDays(rs.getString("leave_on_days"))
                    .designationId(rs.getLong("designation_id"))
                    .designation(rs.getString("designation_name"))
                    .defaultTimeSlotId(rs.getLong("default_time_slot_id"))
                    .unitNumber(rs.getString("unit_no"))
                    .street(rs.getString("street"))
                    .alternateNumber(rs.getString("alternateNumber"))
                    .emergencyContact1(rs.getString("emergencyContact1"))
                    .emergencyContact2(rs.getString("emergencyContact2"))
                    .emergencyContactname1(rs.getString("emergencyContactname1"))
                    .emergencyContactname2(rs.getString("emergencyContactname2"))
                    .alternateEmail(rs.getString("alternateEmail"))
                    .totalLeaves(rs.getInt("assigned_leave"))
                    .pendingLeaves(rs.getInt("balance_leave"))
                    .overTimeHours(rs.getInt("total_overtime_hours"));

            // Fetch Document Data
            String documentQuery = """
            SELECT ed.*, dm.name AS document_name, dm.type AS document_type
            FROM t_employee_document ed
            JOIN t_document_master dm ON ed.document_id = dm.DOCUMENT_ID
            WHERE ed.emp_id = :empId AND ed.is_active = 1
            """;

            MapSqlParameterSource documentParams = new MapSqlParameterSource("empId", rs.getLong("emp_id"));

            List<EmployeeDocumentVO> documentList = namedParameterJdbcTemplate.query(documentQuery, documentParams, (docRs, docRowNum) ->
                    EmployeeDocumentVO.builder()
                            .id(docRs.getLong("id"))
                            .documentName(docRs.getString("document_name"))
                            .documentType(docRs.getString("document_type"))
                            .filePath(docRs.getString("file_path"))
                            .build()
            );

            employeeBuilder.documents(documentList);
            return employeeBuilder.build();
        });


        return employees;
    }




}




