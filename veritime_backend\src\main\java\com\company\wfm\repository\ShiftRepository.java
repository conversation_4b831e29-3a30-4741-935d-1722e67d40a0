package com.company.wfm.repository;


import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.Shift;

@Repository

public interface ShiftRepository extends JpaRepository<Shift, Long> {
    @Query(value = "SELECT * FROM t_shift s WHERE " +
            "CAST(s.SHIFT_DATE AS DATE) BETWEEN CAST(:startDate AS DATE) AND CAST(:endDate AS DATE) AND " +
            "CAST(s.START_TIME AS TIME) >= CAST(:startTime AS TIME) AND " +
            "CAST(s.END_TIME AS TIME) <= CAST(:endTime AS TIME)",
            nativeQuery = true)
    List<Shift> findMatchingShifts(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );


    Optional<Shift> findByShiftId(Long shiftId);


}

