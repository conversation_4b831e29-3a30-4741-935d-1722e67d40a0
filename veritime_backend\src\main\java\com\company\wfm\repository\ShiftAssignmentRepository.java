package com.company.wfm.repository;



import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.company.wfm.entity.Shift;
import com.company.wfm.entity.ShiftAssignment;

public interface ShiftAssignmentRepository extends JpaRepository<ShiftAssignment, Long> {
    List<ShiftAssignment> findByShift(Shift shift);
    Optional<ShiftAssignment> findByShiftAssignmentId(Long shiftAssignmentId);
}