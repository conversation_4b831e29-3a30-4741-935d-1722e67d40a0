// Simple Synchronous Login Script for Postman
// This avoids async issues and should work immediately

const CryptoJS = require('crypto-js');

// Get credentials from environment
const username = pm.environment.get('testUsername') || '<EMAIL>';
const password = pm.environment.get('testPassword') || 'raj456123';
const aesPassword = pm.environment.get('aesPassword') || 'uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabqAU=';
const aesSalt = pm.environment.get('aesSalt') || 'uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabBA==';

console.log('🔐 Starting simple login encryption...');
console.log('Username:', username);

// Create login payload
const loginPayload = {
    username: username,
    password: password
};

const jsonString = JSON.stringify(loginPayload);
console.log('Payload:', jsonString);

// Try multiple encryption methods
let encryptedData;
let method = '';

try {
    // Method 1: Simple AES with base64 password (this worked in your test)
    encryptedData = CryptoJS.AES.encrypt(jsonString, aesPassword).toString();
    method = 'Simple AES with base64 password';
    console.log('✅ Method 1 successful:', method);
} catch (error1) {
    console.log('❌ Method 1 failed:', error1.message);
    
    try {
        // Method 2: PBKDF2 with fewer iterations
        const passwordWordArray = CryptoJS.enc.Base64.parse(aesPassword);
        const saltWordArray = CryptoJS.enc.Base64.parse(aesSalt);
        
        const key = CryptoJS.PBKDF2(passwordWordArray, saltWordArray, {
            keySize: 256/32,
            iterations: 1000 // Much fewer iterations for speed
        });
        
        encryptedData = CryptoJS.AES.encrypt(jsonString, key).toString();
        method = 'PBKDF2 with 1000 iterations';
        console.log('✅ Method 2 successful:', method);
    } catch (error2) {
        console.log('❌ Method 2 failed:', error2.message);
        
        // Method 3: Direct string encryption
        encryptedData = CryptoJS.AES.encrypt(jsonString, 'fallback-key').toString();
        method = 'Fallback encryption';
        console.log('✅ Method 3 (fallback) used');
    }
}

console.log('Encryption method used:', method);
console.log('Encrypted data length:', encryptedData.length);
console.log('Encrypted data:', encryptedData);

// Create request body
const requestBody = {
    encryptedData: encryptedData
};

// Update request
pm.request.body.mode = 'raw';
pm.request.body.raw = JSON.stringify(requestBody, null, 2);

// Set headers
pm.request.headers.upsert({
    key: 'Content-Type',
    value: 'application/json'
});

// Verify request setup
console.log('✅ Request updated');
console.log('Request body:', pm.request.body.raw);
console.log('Request body length:', pm.request.body.raw.length);
console.log('Content-Type header set');

// Store for debugging
pm.environment.set('lastEncryptedPayload', encryptedData);
pm.environment.set('lastMethod', method);

console.log('🚀 Simple encryption completed successfully!');
