package com.company.wfm.service.impl;

import java.sql.Time;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.company.wfm.service.WorkingHourService;

@Service
public class WorkingHourServiceImpl implements WorkingHourService {
    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Override
    public Map<String, Object> getTotalAndExpectedWorkingHours(Long empId, LocalDate startDate, LocalDate endDate) {
        AtomicLong totalWorkingHours = new AtomicLong(0);
        AtomicLong expectedWorkingHours = new AtomicLong(0);

        // Query to fetch employee attendance for the specified date range
        String attendanceQuery = "SELECT date, MAX(CHECK_IN_TIME) AS CHECK_IN_TIME, MAX(CHECK_OUT_TIME) AS CHECK_OUT_TIME " +
                "FROM t_attendance WHERE EMP_ID = ? AND date BETWEEN ? AND ? GROUP BY date";

        jdbcTemplate.query(attendanceQuery, new Object[]{empId, startDate, endDate}, (rs) -> {
            LocalDate date = rs.getDate("date").toLocalDate();
            Time checkInTime = rs.getTime("CHECK_IN_TIME");
            Time checkOutTime = rs.getTime("CHECK_OUT_TIME");

            // Calculate total working hours for the day using latest check-in and check-out time
            if (checkInTime != null && checkOutTime != null) {
                long workingDuration = checkOutTime.getTime() - checkInTime.getTime();
                totalWorkingHours.addAndGet(workingDuration / (1000 * 60 * 60)); // Convert to hours
            }
        });

        // Query to fetch employee's actual and modified shift timings
        String shiftQuery = "SELECT es.date, " +
                "es.actual_shift AS actualShiftId, " +
                "es.modified_shift AS modifiedShiftId, " +
                "ts1.time_slot_id AS actualTimeSlotId, ts1.start_time AS actualStartTime, ts1.end_time AS actualEndTime, " +
                "ts2.time_slot_id AS modifiedTimeSlotId, ts2.start_time AS modifiedStartTime, ts2.end_time AS modifiedEndTime " +
                "FROM t_employee_schedule es " +
                "LEFT JOIN t_time_slot ts1 ON es.actual_shift = ts1.time_slot_id " +
                "LEFT JOIN t_time_slot ts2 ON es.modified_shift = ts2.time_slot_id " +
                "WHERE es.emp_id = ? AND es.date BETWEEN ? AND ?";

        // Fetch shift details from the database
        jdbcTemplate.query(shiftQuery, new Object[]{empId, startDate, endDate}, (rs) -> {
            LocalDate shiftDate = rs.getDate("date").toLocalDate();
            Time actualStartTime = rs.getTime("actualStartTime");
            Time actualEndTime = rs.getTime("actualEndTime");
            Time modifiedStartTime = rs.getTime("modifiedStartTime");
            Time modifiedEndTime = rs.getTime("modifiedEndTime");

            // Handle null values in check-in/out or shift times by checking before using them
            LocalTime shiftStartTime = null;
            LocalTime shiftEndTime = null;

            // Choose actual or modified shift based on the available data
            if (actualStartTime != null) {
                shiftStartTime = actualStartTime.toLocalTime();
            } else if (modifiedStartTime != null) {
                shiftStartTime = modifiedStartTime.toLocalTime();
            }

            if (actualEndTime != null) {
                shiftEndTime = actualEndTime.toLocalTime();
            } else if (modifiedEndTime != null) {
                shiftEndTime = modifiedEndTime.toLocalTime();
            }

            // Calculate expected working hours
            if (shiftStartTime != null && shiftEndTime != null) {
                Duration shiftDuration = Duration.between(shiftStartTime, shiftEndTime);
                expectedWorkingHours.addAndGet(shiftDuration.toHours()); // Add expected working hours
            }
        });

        // Return a map containing total and expected working hours
        Map<String, Object> result = new HashMap<>();
        result.put("totalWorkingHours", totalWorkingHours.get());
        result.put("expectedWorkingHours", expectedWorkingHours.get());

        return result;
    }


    //attendence activity

    @Override
    public Map<String, Object> getAttendanceActivity(Long empId, String period) {
        Map<String, Object> response = new HashMap<>();

        switch (period.toLowerCase()) {
            case "day":
                response.put("day", getDayActivity(empId));
                break;
            case "week":
                response.put("week", getWeekActivity(empId));
                break;
            case "month":
                response.put("month", getMonthActivity(empId));
                break;
            default:
                response.put("error", "Invalid period");
                return response;
        }

        return response;
    }

    private Map<String, Object> getDayActivity(Long empId) {
        Map<String, Object> dayActivity = new HashMap<>();

        // Calculate the previous day
        LocalDate today = LocalDate.now();
        LocalDate previousDay = today.minusDays(1);

        // Generate a label for the previous day
        String label = previousDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")); // e.g., "2025-01-23"
        dayActivity.put("date", label); // Adding date to the response

        // Initialize total hours and minutes worked for the previous day
        long totalMinutesWorked = 0L;

        // Query to fetch punch-in logs for the previous day
        String query = "SELECT PUNCH_IN_TIME " +
                "FROM t_attendance_audit " +
                "WHERE EMP_ID = ? AND DATE = ? " +
                "ORDER BY PUNCH_IN_TIME";
        List<Map<String, Object>> logs = jdbcTemplate.queryForList(
                query, empId, java.sql.Date.valueOf(previousDay)
        );

        // Process the logs to calculate hours worked
        List<Map<String, Object>> activityDetails = new ArrayList<>();
        Time previousPunchIn = null;

        for (Map<String, Object> log : logs) {
            Map<String, Object> detail = new HashMap<>();
            Time punchInTime = (Time) log.get("PUNCH_IN_TIME");

            detail.put("punchInTime", punchInTime != null ? punchInTime.toString() : "N/A");

            if (previousPunchIn != null && punchInTime != null) {
                // Calculate the time difference in minutes
                long minutesWorked = Duration.between(previousPunchIn.toLocalTime(), punchInTime.toLocalTime()).toMinutes();

                // Add to the total minutes worked
                totalMinutesWorked += minutesWorked;

                // Convert minutes worked into hours and minutes
                long hoursWorked = minutesWorked / 60;
                long remainingMinutes = minutesWorked % 60;

                detail.put("hoursWorked", hoursWorked + " hours " + remainingMinutes + " minutes");
            } else {
                detail.put("hoursWorked", "0 hours 0 minutes");
            }

            previousPunchIn = punchInTime; // Update the previous punch-in time
            activityDetails.add(detail);
        }

        // Add details and summary to the response
        dayActivity.put("activityDetails", activityDetails);

        // Convert total minutes worked into hours and minutes for summary
        long totalHoursWorked = totalMinutesWorked / 60;
        long totalRemainingMinutes = totalMinutesWorked % 60;
        dayActivity.put("totalHoursWorked", totalHoursWorked + " hours " + totalRemainingMinutes + " minutes");

        // Log the results for debugging
        System.out.println("Day Activity Response: " + dayActivity);

        return dayActivity;
    }


    public Map<String, Object> getWeekActivity(Long empId) {
        Map<String, Object> weekActivity = new HashMap<>();

        LocalDate today = LocalDate.now();
        LocalDate startOfLastWeek = today.minusWeeks(1).with(DayOfWeek.MONDAY);
        LocalDate endOfLastWeek = today.minusWeeks(1).with(DayOfWeek.SUNDAY);

        // List to hold day labels with actual date
        List<String> labels = new ArrayList<>();
        List<String> dates = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            LocalDate day = startOfLastWeek.plusDays(i);
            labels.add("Day " + (i + 1));  // e.g., "Day 1", "Day 2"
            dates.add(day.toString()); // e.g., "2025-02-01", "2025-02-02"
        }
        weekActivity.put("labels", labels);
        weekActivity.put("dates", dates);  // This will provide the actual dates for each day in the week.

        List<Long> hoursWorked = new ArrayList<>(Collections.nCopies(7, 0L));

        // Fetch latest check-in and check-out per day
        String query = """
    SELECT DATE,
           MAX(CHECK_IN_TIME) AS CHECK_IN_TIME,
           MAX(CHECK_OUT_TIME) AS CHECK_OUT_TIME
    FROM t_attendance
    WHERE EMP_ID = ? AND DATE BETWEEN ? AND ?
    GROUP BY DATE
    """;

        List<Map<String, Object>> logs = jdbcTemplate.queryForList(
                query, empId,
                java.sql.Date.valueOf(startOfLastWeek),
                java.sql.Date.valueOf(endOfLastWeek)
        );

        for (Map<String, Object> log : logs) {
            Time checkInTime = (Time) log.get("CHECK_IN_TIME");
            Time checkOutTime = (Time) log.get("CHECK_OUT_TIME");
            LocalDate date = ((java.sql.Date) log.get("DATE")).toLocalDate();

            if (checkInTime != null && checkOutTime != null) {
                long hours = calculateWorkedHours(checkInTime, checkOutTime);
                int dayIndex = (int) ChronoUnit.DAYS.between(startOfLastWeek, date);
                if (dayIndex >= 0 && dayIndex < 7) {
                    hoursWorked.set(dayIndex, hoursWorked.get(dayIndex) + hours);
                }
            }
        }

        weekActivity.put("datasets", Collections.singletonList(Map.of("label", "Hours Worked", "data", hoursWorked)));
        weekActivity.put("weekRange", startOfLastWeek.toString() + " to " + endOfLastWeek.toString()); // Date range for the week

        return weekActivity;
    }



    public Map<String, Object> getMonthActivity(Long empId) {
        Map<String, Object> monthActivity = new HashMap<>();

        LocalDate today = LocalDate.now();
        LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
        LocalDate lastDayOfLastMonth = today.minusMonths(1).withDayOfMonth(firstDayOfLastMonth.lengthOfMonth());

        List<String> labels = new ArrayList<>();
        List<List<String>> dates = new ArrayList<>();  // This will store date ranges
        Map<String, Long> weekHoursMap = new LinkedHashMap<>();
        Map<String, String> weekDateRangeMap = new LinkedHashMap<>();
        LocalDate currentStart = firstDayOfLastMonth;

        // Create week labels and date ranges
        while (currentStart.isBefore(lastDayOfLastMonth)) {
            LocalDate currentEnd = currentStart.plusDays(6).isAfter(lastDayOfLastMonth)
                    ? lastDayOfLastMonth
                    : currentStart.plusDays(6);

            String weekLabel = "Week " + (labels.size() + 1);
            String dateRange = currentStart.toString() + " to " + currentEnd.toString();

            labels.add(weekLabel);  // Only add week labels, no date range
            weekHoursMap.put(weekLabel, 0L);
            weekDateRangeMap.put(weekLabel, dateRange);  // Store the date range for each week
            dates.add(Arrays.asList(currentStart.toString(), currentEnd.toString()));  // Store the date range in the "dates" list

            currentStart = currentEnd.plusDays(1);
        }

        monthActivity.put("labels", labels);  // Updated labels with only week names
        monthActivity.put("dates", dates);  // Adding the "dates" field for the date ranges

        // Fetch latest check-in and check-out per day
        String query = """
    SELECT DATE,
           MAX(CHECK_IN_TIME) AS CHECK_IN_TIME,
           MAX(CHECK_OUT_TIME) AS CHECK_OUT_TIME
    FROM t_attendance
    WHERE EMP_ID = ? AND DATE BETWEEN ? AND ?
    GROUP BY DATE
    """;

        List<Map<String, Object>> logs = jdbcTemplate.queryForList(
                query, empId,
                java.sql.Date.valueOf(firstDayOfLastMonth),
                java.sql.Date.valueOf(lastDayOfLastMonth)
        );

        // Calculate hours worked and assign to corresponding week
        for (Map<String, Object> log : logs) {
            Time checkInTime = (Time) log.get("CHECK_IN_TIME");
            Time checkOutTime = (Time) log.get("CHECK_OUT_TIME");
            LocalDate logDate = ((java.sql.Date) log.get("DATE")).toLocalDate();

            if (checkInTime != null && checkOutTime != null) {
                long hoursWorked = calculateWorkedHours(checkInTime, checkOutTime);

                // Loop through weeks to find the correct week for this log entry
                for (String week : labels) {
                    String weekRange = weekDateRangeMap.get(week);  // Get the date range for this week
                    LocalDate weekStart = LocalDate.parse(weekRange.split(" to ")[0]);
                    LocalDate weekEnd = LocalDate.parse(weekRange.split(" to ")[1]);

                    if ((logDate.isEqual(weekStart) || logDate.isAfter(weekStart)) &&
                            (logDate.isEqual(weekEnd) || logDate.isBefore(weekEnd))) {
                        weekHoursMap.put(week, weekHoursMap.get(week) + hoursWorked);
                        break;
                    }
                }
            }
        }

        List<Long> hoursWorked = new ArrayList<>(weekHoursMap.values());
        monthActivity.put("datasets", Collections.singletonList(Map.of("label", "Hours Worked", "data", hoursWorked)));

        return monthActivity;
    }



    private long calculateWorkedHours(Time checkInTime, Time checkOutTime) {
        LocalTime checkIn = checkInTime.toLocalTime();
        LocalTime checkOut = checkOutTime.toLocalTime();

        // Handle scenario where check-out is after midnight
        if (checkOut.isBefore(checkIn)) {
            return Duration.between(checkIn, LocalTime.MAX).toHours() + Duration.between(LocalTime.MIDNIGHT, checkOut).toHours();
        }
        return Duration.between(checkIn, checkOut).toHours();
    }



}
