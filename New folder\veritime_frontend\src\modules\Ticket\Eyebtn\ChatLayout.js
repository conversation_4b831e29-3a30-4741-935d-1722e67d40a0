import React, { useRef, useState, useEffect } from "react";
import Avatar from "@/components/Avatar";

import { decryptData } from "@/utils/encryption";
import { convertToSouthAfricaTime } from "@/services/utils";
import {
  fileDownload,
  postRequest,
  postRequestWithOnlyResponseDecrypted,
} from "../../../services/apiService";
import { API_URLS } from "../../../constants/apiConstants";
import { showSuccessAlert2, showSuccessAlert } from "@/services/alertService";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { fontSize } from "@mui/system";

const bgColorPalette = [
  "#a5bcd7",
  "#D6C0B3",
  "#FEFAE0",
  "#FFF1DB",

  "#FFCFB3",
  "#F5EFFF",
  "#D2E0FB",
  "#A5B68D",
  "#d7a5a5",
  "#a5d7b2",
  "#bcd7a5",
  "#d7a5b9",
];

const textColorPalette = [
  "#1A1A1D",
  "#493628",
  "#3B1E54",
  "#9A7E6F",
  "#E78F81",
  "#A594F9",
  "#384B70",
  "#798645",
  "#ff33d4",
  "#d4ff33",
  "#6633ff",
  "#33ff66",
];

const baseURL = process.env.NEXT_PUBLIC_S3_URL;
const S3URL = process.env.NEXT_PUBLIC_S3_URL;

const handleDownload = (file) => {
  const fileName = file.split("/").pop();

  fileDownload(`${API_URLS?.TICKET_DOWNLOAD}${file}`, true)
    .then((response) => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.blob(); // Return the blob Promise
    })
    .then((blob) => {
      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.target = "_blank";
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();

      showSuccessAlert(`File "${fileName}" downloaded successfully.`);
    })
    .catch((error) => {
      showErrorAlert(`Error downloading file: ${error.message}`);
    });
};

const getColorForCreatedBy = (createdBy, colorMap, palette) => {
  if (!colorMap[createdBy]) {
    const colorIndex = Object.keys(colorMap).length % palette.length;
    colorMap[createdBy] = palette[colorIndex];
  }
  return colorMap[createdBy];
};

const getInitials = (name) => {
  return name?.charAt(0)?.toUpperCase() || "U";
};

const MessageBox = ({
  image,
  empName,
  remark,
  updatedAt,
  backgroundColor,
  textColor,
  files,
  isOwnMessage,
  ticketStatus,
  allowReply,
  S3URL,
  userData,
}) => (
  <div
    className={`d-flex my-2 ${
      isOwnMessage ? "justify-content-end" : "justify-content-start"
    }`}
    style={{
      display: "flex",
      justifyContent: isOwnMessage ? "flex-end" : "flex-start",
      alignItems: "flex-end",
    }}
  >
    {!isOwnMessage && (
      <div
        style={{
          margin: "0 10px",
          display: "flex",
          alignItems: "center",
        }}
      >
        <Avatar
          intial={getInitials(empName || "Unknown")}
          profileImg={`${S3URL}${userData?.image || ""}`}
          className="rounded-circle"
          styleInital={{
            width: "30px",
            height: "30px",
            backgroundColor: "#AABFD5",
            color: "#000",
            fontSize: 14,
          }}
          styleImg={{ width: "30px", height: "30px" }}
        />
      </div>
    )}

    {/* Message Bubble */}
    <div
      style={{
        maxWidth: "70%",
        minWidth: "100px",
        backgroundColor: backgroundColor,
        borderRadius: "20px",
        padding: "10px 15px",
        textAlign: "left",
        wordWrap: "break-word",
        boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.2)",
        marginLeft: isOwnMessage ? "10px" : "0px",
        marginRight: isOwnMessage ? "0px" : "10px",
        position: "relative",
      }}
    >
      <p
        style={{
          margin: "0 0 5px",
          fontSize: "14px",
          fontWeight: "bold",
          color: textColor,
        }}
      >
        {empName}
      </p>
      <div
        style={{
          margin: 0,
          fontSize: "14px",
          whiteSpace: "pre-line",
          marginTop: "10px",
        }}
        dangerouslySetInnerHTML={{ __html: remark }}
      />

      <span
        style={{
          fontSize: "12px",
          color: "#666",
          float: "right",
          marginTop: "5px",
        }}
      >
        {convertToSouthAfricaTime(updatedAt)}
      </span>

      {/* Download */}
      {allowReply && files && (
        <div
          style={{
            marginTop: "18px",
            marginLeft: "90%",
            paddingTop: "10px",
          }}
        >
          <a
            className="text-primary"
            onClick={() => handleDownload(files)}
            style={{ cursor: "pointer" }}
          >
            <img src="/image/download.png" style={{ width: "25px" }} />
          </a>
        </div>
      )}
    </div>

    {/*  Alphabet  */}
    {isOwnMessage && (
      <div
        style={{
          margin: "0 10px",
          display: "flex",
          alignItems: "center",
        }}
      >
        <Avatar
          intial={getInitials(empName || "Unknown")}
          profileImg={`${S3URL}${userData?.image || ""}`}
          className="rounded-circle"
          styleInital={{
            width: "30px",
            height: "30px",
            backgroundColor: "#AABFD5",
            color: "#000",
            fontSize: 14,
          }}
          styleImg={{ width: "30px", height: "30px" }}
        />
      </div>
    )}
  </div>
);

const ChatLayout = ({
  ticketId,
  assignments,
  IsSuccess,
  ticketStatus,
  allowReply,
}) => {
  const containerRef = useRef(null);
  const [newMessage, setNewMessage] = useState("");
  const [fileAttachment, setFileAttachment] = useState(null);
  const [messages, setMessages] = useState(assignments || []);
  // const [ticket, setTicket] = useState(null);
  const [ticket, setTicket] = useState({
    ticketId: null,
    assigned_to: "",
    history: [],
    ticketStatus: ticketStatus,
    isClosed: false,
  });
  //console.log(ticketStatus, "in chatlayout usestate");
  const [isFileUploaded, setIsFileUploaded] = useState(false);

  const bgEmpColorMap = {};
  const textEmpColorMap = {};

  useEffect(() => {
    //console.log("Received status:", ticketStatus);
    // console.log("ticket.isClosed will be:", ticketStatus === "closed");
    if (ticketId) {
      setTicket({
        ticketId,
        assigned_to: "someUserId",
        history: [],
        // isClosed: ticketStatus === "closed",
        isClosed: ticketStatus === "close" || ticketStatus === "archived",
      });
    }
  }, [ticketStatus, ticketId]);

  useEffect(() => {
    //console.log("Ticket:", ticket);
    //console.log("Ticket Closed Status:", ticket?.isClosed);
  }, [ticket]);

  const handleSendMessage = () => {
    try {
      const formData = new FormData();
      if (fileAttachment) {
        formData.append("files", fileAttachment);
      }

      formData.append(
        "ticket",
        JSON.stringify({
          ticketId: ticket.ticketId,
          responseRemark: newMessage,
        })
      );

      const response = postRequestWithOnlyResponseDecrypted(
        API_URLS.Reply_Update,
        formData
      );

      if (response && response.status === "success") {
        const newMessageObj = {
          createdBy: userData?.empId,
          createdByName: userData?.empName,
          remark: newMessage,
          createdAt: new Date(),
          image: userData?.image,
          files: fileAttachment ? fileAttachment.name : null,
        };

        setMessages((prevMessages) => [newMessageObj, ...prevMessages]);

        setNewMessage("");
        setFileAttachment(null);
        setIsFileUploaded(false);

        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight;
        }
      } else {
        const newMessageObj = {
          createdBy: userData?.empId,
          createdByName: userData?.empName,
          remark: newMessage,
          createdAt: new Date(),
          image: userData?.image,
          files: fileAttachment ? fileAttachment.name : null,
        };

        setMessages((prevMessages) => [newMessageObj, ...prevMessages]);
        setNewMessage("");
        setFileAttachment(null);
        setIsFileUploaded(false);
      }
    } catch (error) {}
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFileAttachment(file);
      setIsFileUploaded(true); // Ensure this state is set when a file is selected
    }
  };

  const handleFileSelect = () => {
    document.getElementById("fileInput").click();
  };

  const handleFileRemove = () => {
    setFileAttachment(null);
    setIsFileUploaded(false);
  };
  const [userData, setUserData] = useState("");

  useEffect(() => {
    const rawEmpId = localStorage.getItem("empId");
    const profileImg = localStorage.getItem("profileImg");

    console.log("Raw empId from localStorage:", rawEmpId);
    console.log("Raw image:", profileImg);

    if (rawEmpId) {
      const updatedUserData = { empId: rawEmpId, image: profileImg || null };
      setUserData(updatedUserData);

      // Log AFTER setting state
      console.log("Stored in state:", updatedUserData.empId);
      console.log("Profile Image URL:", updatedUserData.image);
      console.log("Constructed S3 URL:", `${S3URL}${updatedUserData.image}`);
    } else {
      console.error("No empId found in localStorage");
    }
  }, []);

  return (
    <div
      ref={containerRef}
      style={{
        height: "740px",
        overflowY: "auto",
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        border: "1px solid #ddd",
        borderRadius: "8px",
        // pointerEvents: IsSuccess ? "none" : "auto",
      }}
    >
      {allowReply && !ticket.isClosed && (
        // {!ticket.isClosed && (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            padding: "10px",
            borderBottom: "1px solid #ddd",
            background: "#f9f9f9",
            marginBottom: "10px",
          }}
        >
          {/*file*/}
          <label
            style={{
              marginRight: "10px",
              cursor: "pointer",
              display: "inline-flex",
              alignItems: "center",
            }}
          >
            {isFileUploaded ? (
              <CheckCircleIcon
                sx={{ color: "green", fontSize: 35 }}
                onClick={handleFileRemove}
              />
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                style={{
                  color: "#007bff",
                }}
              >
                {/*Icon */}
                <path d="M14.12 9.88l-3.54 3.54a3 3 0 1 1-4.24-4.24l3.54-3.54a6 6 0 0 1 8.49 8.49l-6.36 6.36a6 6 0 0 1-8.49-8.49l6.36-6.36"></path>
              </svg>
            )}

            <input
              type="file"
              id="fileInput"
              onChange={handleFileChange}
              style={{
                display: "none",
              }}
            />
          </label>

          {/* Message */}
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            style={{
              flex: "1",
              padding: "10px",
              borderRadius: "20px",
              border: "1px solid #ccc",
              outline: "none",
            }}
          />

          {/* Send Button */}
          <button
            onClick={handleSendMessage}
            style={{
              marginLeft: "10px",
              padding: "10px 15px",
              borderRadius: "50%",
              border: "none",
              background: "#007bff",
              color: "white",
              cursor: "pointer",
            }}
          >
            ➤
          </button>
        </div>
      )}
      {messages?.map((msg, index) => {
        console.log(`Message ${index}:`, msg); // Debugging each message object
        console.log(`Created By Name:`, msg?.createdByName); // Debug empName source
        console.log(`Created By ID:`, msg?.createdBy);

        const backgroundColor = getColorForCreatedBy(
          msg?.createdBy,
          bgEmpColorMap,
          bgColorPalette
        );
        const textColor = getColorForCreatedBy(
          msg?.createdBy,
          textEmpColorMap,
          textColorPalette
        );

        const isOwnMessage = Number(msg.createdBy) === Number(userData?.empId);

        return (
          <MessageBox
            style={{ fontSize: "14px" }}
            key={index}
            empName={msg?.createdByName}
            remark={msg?.remark || "N/A"}
            updatedAt={msg?.createdAt}
            image={msg?.image}
            backgroundColor={backgroundColor}
            textColor={textColor}
            files={msg?.files}
            isOwnMessage={isOwnMessage}
            ticketStatus={ticketStatus}
            allowReply={allowReply}
            baseURL={baseURL}
            S3URL={S3URL}
            userData={userData}
          />
        );
      })}

      {assignments?.length === 0 && (
        <p style={{ textAlign: "center" }}>
          No conversations for this assignment found.
        </p>
      )}
    </div>
  );
};

export default ChatLayout;
