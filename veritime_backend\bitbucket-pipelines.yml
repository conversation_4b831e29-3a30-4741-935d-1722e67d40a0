# Template Java Gradle build
# This template allows you to test and build your Java project with <PERSON>rad<PERSON>.
# The workflow allows running tests, code checkstyle, security scans, and SonarQube analysis for pull requests targeting the develop branch.

# Prerequisites: appropriate project structure should exist in the repository.
image: gradle:8.10

pipelines:
  grad:
    - parallel:
      - step:
          name: Build 
          caches:
            - gradle
          script:
            - gradle build
          after-script:
            - pipe: atlassian/checkstyle-report:0.3.0
      - step:
          name: Security Scan
          script:
            # Run a security scan for sensitive data.
            # See more security tools at https://bitbucket.org/product/features/pipelines/integrations?&category=security
            - pipe: atlassian/git-secrets-scan:0.5.1
  pull-requests:
      '**':
        - step:
            name: Sonar Scan 
            caches:
              - gradle
            script:
              - if [ "$BITBUCKET_PR_DESTINATION_BRANCH" = "develop" ]; then gradle build && gradle sonar; echo "SonarQube scan results "https://sonar.xpertlyte.tech/dashboard?id=Spring-Boot" "; else echo "No scans for branches other than develop"; fi 
            