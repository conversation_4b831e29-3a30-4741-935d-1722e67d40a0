package com.company.wfm.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Data;

@Data
public class HolidayScheduleDTO {

   /* private LocalDate date;
    private String holiday;
    private Boolean isOptional;
    private Long createdBy;
    private Long updatedBy;*/

    private Long id;
    private LocalDate date;
    private String holiday;
    private Boolean isOptional;
    private Long createdBy;
    private String createdByName; // Add this field
    private String createdByImage;
    private LocalDateTime createdAt;
    private Long updatedBy;
    private String modifiedByName; // Add this field
    private String modifiedByImage;
    private LocalDateTime updatedAt;
    private Boolean isActive;


}
