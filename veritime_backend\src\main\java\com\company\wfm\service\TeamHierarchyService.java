package com.company.wfm.service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
public class TeamHierarchyService {

    private static final Logger logger = LoggerFactory.getLogger(TeamHierarchyService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    //   to get ceo list
    public List<Map<String, Object>> getAllEmployeesList() {
        String query = """
            SELECT
                u.EMP_ID,
                u.USER_NAME AS name,
                d.designation_name AS designation,
                e.IMG_URE AS avatar,
                u.ROLE AS role
            FROM
                t_user u
            INNER JOIN
                t_employee e ON u.EMP_ID = e.EMP_ID
            LEFT JOIN
                t_designation d ON e.DESIGNATION_ID = d.designation_id
            WHERE
                u.ROLE = 'ceo' AND e.in_service=1
        """;

        return jdbcTemplate.query(query, (rs, rowNum) -> {
            Map<String, Object> employee = new HashMap<>();
            employee.put("emp_id", rs.getLong("EMP_ID"));
            employee.put("name", rs.getString("name"));
            employee.put("designation", rs.getString("designation"));
            employee.put("avatar", rs.getString("avatar"));
            employee.put("role", rs.getString("role"));
            return employee;
        });
    }

    //get employees reporting to a specific employee based on upper_id
    public List<Map<String, Object>> getMyTeams(Long empId) {
        String query = """
            SELECT
                e.EMP_ID,
                e.EMP_NAME AS name,
                d.designation_name AS designation,
                e.IMG_URE AS avatar
            FROM
                t_employee e
            LEFT JOIN
                t_designation d ON e.DESIGNATION_ID = d.designation_id
            WHERE
                e.UPPER_ID = ?
        """;

        return jdbcTemplate.query(query, new Object[]{empId}, (rs, rowNum) -> {
            Map<String, Object> teamMember = new HashMap<>();
            teamMember.put("emp_id", rs.getLong("EMP_ID"));
            teamMember.put("name", rs.getString("name"));
            teamMember.put("designation", rs.getString("designation"));
            teamMember.put("avatar", rs.getString("avatar"));
            return teamMember;
        });
    }

    //hierarchy wise data which willreturn 2 arrays - hierarchy and teams
    public Map<String, List<Map<String, Object>>> getHierarchyData(Long empId) {
        Map<String, List<Map<String, Object>>> hierarchyData = new HashMap<>();

        try {
            logger.info("Fetching hierarchy data for empId: {}", empId);
       /* String hierarchyQuery = """
        WITH RecursiveHierarchy AS (
            SELECT
                e.EMP_ID,
                e.EMP_NAME AS name,
                e.DESIGNATION_ID,
                e.IMG_URE AS avatar,
                e.UPPER_ID
            FROM
                t_employee e
            WHERE
                e.EMP_ID = ?

            UNION ALL

            SELECT
                e.EMP_ID,
                e.EMP_NAME AS name,
                e.DESIGNATION_ID,
                e.IMG_URE AS avatar,
                e.UPPER_ID
            FROM
                t_employee e
            INNER JOIN
                RecursiveHierarchy h ON e.EMP_ID = h.UPPER_ID
        )
        SELECT
            h.EMP_ID,
            h.name,
            COALESCE(d.designation_name, 'Unknown') AS designation,
            h.avatar,
            h.UPPER_ID
        FROM
            RecursiveHierarchy h
        LEFT JOIN
            t_designation d ON h.DESIGNATION_ID = d.designation_id
    """;*/

  //new query
            String hierarchyQuery = """
                    WITH RecursiveHierarchy AS (
                        -- Initial query to start the recursion
                        SELECT
                            e.EMP_ID,
                            e.EMP_NAME AS name,
                            e.DESIGNATION_ID,
                            e.IMG_URE AS avatar,
                            e.UPPER_ID,
                            CAST(e.EMP_ID AS VARCHAR(MAX)) AS path
                        FROM t_employee e
                        WHERE e.EMP_ID = ? AND e.IN_SERVICE = 1

                        UNION ALL

                        -- Recursive part, ensures that the same EMP_ID is not processed again
                        SELECT
                            e.EMP_ID,
                            e.EMP_NAME AS name,
                            e.DESIGNATION_ID,
                            e.IMG_URE AS avatar,
                            e.UPPER_ID,
                            h.path + '->' + CAST(e.EMP_ID AS VARCHAR(MAX)) AS path
                        FROM t_employee e
                        INNER JOIN RecursiveHierarchy h ON e.EMP_ID = h.UPPER_ID
                        WHERE e.IN_SERVICE = 1 AND CHARINDEX(CAST(e.EMP_ID AS VARCHAR(MAX)), h.path) = 0  -- Prevent recursion loops
                    )

                    SELECT
                        h.EMP_ID,
                        h.name,
                        COALESCE(d.designation_name, 'Unknown') AS designation,
                        h.avatar,
                        h.UPPER_ID
                    FROM RecursiveHierarchy h
                    LEFT JOIN t_designation d ON h.DESIGNATION_ID = d.designation_id;

                    """;
        List<Map<String, Object>> leftHierarchy = jdbcTemplate.query(hierarchyQuery, new Object[]{empId}, (rs, rowNum) -> {
            Map<String, Object> hierarchyMember = new HashMap<>();
            hierarchyMember.put("emp_id", rs.getLong("EMP_ID"));
            hierarchyMember.put("name", rs.getString("name"));
            hierarchyMember.put("designation", rs.getString("designation"));
            hierarchyMember.put("avatar", rs.getString("avatar"));
            hierarchyMember.put("upper_id", rs.getString("UPPER_ID"));
            return hierarchyMember;
        });

        Collections.reverse(leftHierarchy);

        String teamQuery = """
        SELECT
            e.EMP_ID,
            e.EMP_NAME AS name,
            COALESCE(d.designation_name, 'Unknown') AS designation,
            e.IMG_URE AS avatar
        FROM
            t_employee e
        LEFT JOIN
            t_designation d ON e.DESIGNATION_ID = d.designation_id
        WHERE
            e.UPPER_ID = ? AND e.IN_SERVICE = 1
    """;

        List<Map<String, Object>> rightTeam = jdbcTemplate.query(teamQuery, new Object[]{empId}, (rs, rowNum) -> {
            Map<String, Object> teamMember = new HashMap<>();
            teamMember.put("emp_id", rs.getLong("EMP_ID"));
            teamMember.put("name", rs.getString("name"));
            teamMember.put("designation", rs.getString("designation"));
            teamMember.put("avatar", rs.getString("avatar"));
            return teamMember;
        });

        hierarchyData.put("hierarchy", leftHierarchy);
        hierarchyData.put("team", rightTeam);
            logger.info("Successfully fetched hierarchy data for empId: {}", empId);
        } catch (Exception ex) {
            logger.error("Error fetching hierarchy data for empId: {}", empId, ex);
            throw new RuntimeException("Error fetching hierarchy data. Please try again later.", ex);
        }
        return hierarchyData;
    }
}