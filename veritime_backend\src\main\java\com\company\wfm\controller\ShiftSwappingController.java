package com.company.wfm.controller;

import java.sql.Time;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.UserTokenService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/shiftswapping")
@CrossOrigin(origins = "*")
@Slf4j
public class ShiftSwappingController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserTokenService userTokenService;

    @PostMapping("/swap")
    public ResponseEntity<?> swapShifts(@RequestBody Map<String, Object> swapData) {
        try {
            Long fromShiftId = Long.valueOf(swapData.get("fromShiftId").toString());
            Long toShiftId = Long.valueOf(swapData.get("toShiftId").toString());
            Long fromEmpId = swapData.get("fromEmpId") != null ? Long.valueOf(swapData.get("fromEmpId").toString()) : null;
            Long toEmpId = swapData.get("toEmpId") != null ? Long.valueOf(swapData.get("toEmpId").toString()) : null;
            String date = swapData.get("date").toString();

            if (fromShiftId == null || toShiftId == null || fromEmpId == null || date == null) {
                return ResponseEntity.badRequest().body("Invalid swap request, missing shiftId, empId, or date");
            }

            if (fromEmpId != null) {
                jdbcTemplate.update("UPDATE t_employee_schedule SET modified_shift = ? WHERE emp_id = ? AND date = ?",
                        toShiftId, fromEmpId, date);
            }

            if (toEmpId != null) {
                jdbcTemplate.update("UPDATE t_employee_schedule SET modified_shift = ? WHERE emp_id = ? AND date = ?",
                        fromShiftId, toEmpId, date);
            }

            return ResponseEntity.ok("Shift modified successfully!");

        } catch (Exception e) {
        	log.error("Exception occured while saveEmployeeLeaveBalances", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error modifying shifts: " + e.getMessage());
        }
    }
    @GetMapping
    public Map<String, Object> getShiftSwappingDetails(
            @RequestParam("departmentId") Long departmentId,
            @RequestParam(value = "date", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {

        if (date == null) {
            date = LocalDate.now().plusDays(1);
        }
        Long loggedInEmpId = userTokenService.getEmployeeIdFromToken();
        String shiftTimingSql = "SELECT DISTINCT ts.time_slot_id, ts.start_time, ts.end_time " +
                "FROM t_time_slot ts " +
                "INNER JOIN t_schedule s ON ts.schedule_id = s.schedule_id " +
                "WHERE s.department_id = ?";

        List<Map<String, Object>> shiftTimings = jdbcTemplate.query(shiftTimingSql, new Object[]{departmentId}, (rs, rowNum) -> {
            Map<String, Object> shiftTiming = new HashMap<>();
            Time startTime = rs.getTime("start_time");
            Time endTime = rs.getTime("end_time");
            long shiftId = rs.getLong("time_slot_id");

            if (startTime != null && endTime != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("h:mm a");
                String formattedStartTime = startTime.toLocalTime().format(formatter);
                String formattedEndTime = endTime.toLocalTime().format(formatter);
                shiftTiming.put("timing", formattedStartTime + " - " + formattedEndTime);
            } else {
                shiftTiming.put("timing", "-");
            }
            shiftTiming.put("shiftId", shiftId);
            return shiftTiming;
        });

        String employeeSql = "SELECT e.EMP_ID AS empId, e.EMP_NAME AS name, d.DEPARTMENT_NAME AS department, e.IMG_URE AS profilePic, " +
                "ts.time_slot_id AS actual_shift_id, ts.start_time AS actual_start_time, ts.end_time AS actual_end_time, " +
                "ts2.time_slot_id AS modified_shift_id, ts2.start_time AS modified_start_time, ts2.end_time AS modified_end_time " +
                "FROM t_employee e " +
                "INNER JOIN t_department d ON e.DEPARTMENT_ID = d.DEPARTMENT_ID " +
                "INNER JOIN t_employee_schedule es ON es.EMP_ID = e.EMP_ID " +
                "INNER JOIN t_time_slot ts ON ts.time_slot_id = es.actual_shift " +
                "LEFT JOIN t_time_slot ts2 ON ts2.time_slot_id = es.modified_shift " +
                "WHERE e.upper_id=? AND e.DEPARTMENT_ID = ? AND es.date = ?";

        List<Map<String, Object>> empList = jdbcTemplate.query(employeeSql, new Object[]{loggedInEmpId, departmentId, date}, (rs, rowNum) -> {
            Map<String, Object> employee = new HashMap<>();
            employee.put("empId", rs.getLong("empId"));
            employee.put("name", rs.getString("name"));
            employee.put("profilePic", rs.getString("profilePic"));
            employee.put("department", rs.getString("department"));

            // Format actual shift timings
            Time actualStartTime = rs.getTime("actual_start_time");
            Time actualEndTime = rs.getTime("actual_end_time");
            long actualShiftId = rs.getLong("actual_shift_id");
            if (actualStartTime != null && actualEndTime != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("h:mm a");
                String formattedStartTime = actualStartTime.toLocalTime().format(formatter);
                String formattedEndTime = actualEndTime.toLocalTime().format(formatter);
                employee.put("actualShift", formattedStartTime + " - " + formattedEndTime);
                employee.put("actualShiftId", actualShiftId);
            } else {
                employee.put("actualShift", "-");
                employee.put("actualShiftId", actualShiftId);
            }

            // Format modified shift timings (if any)
            Time modifiedStartTime = rs.getTime("modified_start_time");
            Time modifiedEndTime = rs.getTime("modified_end_time");
            long modifiedShiftId = rs.getLong("modified_shift_id");
            if (modifiedShiftId != 0 && modifiedStartTime != null && modifiedEndTime != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("h:mm a");
                String formattedModifiedStartTime = modifiedStartTime.toLocalTime().format(formatter);
                String formattedModifiedEndTime = modifiedEndTime.toLocalTime().format(formatter);
                employee.put("modifiedShift", formattedModifiedStartTime + " - " + formattedModifiedEndTime);
                employee.put("modifiedShiftId", modifiedShiftId);
            } else {
                employee.put("modifiedShift", "-");
                employee.put("modifiedShiftId", 0);
            }

            return employee;
        });

        Map<String, Object> response = new HashMap<>();
        response.put("date", date.toString());
        response.put("shiftTimings", shiftTimings);
        response.put("empList", empList);

        return response;
    }
}