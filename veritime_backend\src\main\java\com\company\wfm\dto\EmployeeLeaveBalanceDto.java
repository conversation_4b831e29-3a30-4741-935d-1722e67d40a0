package com.company.wfm.dto;


import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmployeeLeaveBalanceDto {
    private Long empLeaveId;
    private String empLeaveIdName;
    private String empCode;
    private String empName;
    private Long empId;
   private Long leaveId;
   private String leaveName;
    private Integer assignedLeave;
    private Integer balanceLeave;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;
    private Date creditDate;


    public EmployeeLeaveBalanceDto(Long empLeaveId, Long empId, Long leaveId, Integer assignedLeave, Integer balanceLeave, LocalDateTime createdTime, LocalDateTime updatedTime) {
    }
}
