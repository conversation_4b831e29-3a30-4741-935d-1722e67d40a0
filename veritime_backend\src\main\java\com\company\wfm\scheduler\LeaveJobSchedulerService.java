package com.company.wfm.scheduler;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.company.wfm.annotation.TrackSchedularExecution;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.LeaveMaster;
import com.company.wfm.exception.LeaveRuleProcessingException;
import com.company.wfm.leave.service.LeaveManagementService;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.service.LeaveMasterService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * LeaveJobSchedulerService processes monthly leave crediting for employees.
 * 
 * <p>
 * This scheduler runs daily at 2 AM but processes leave credits only for branches
 * where the leave credit run day matches the current date.
 * </p>
 * 
 * <p>
 * Key Responsibilities:
 * <ul>
 *   <li>Fetch all active branches</li>
 *   <li>Check if the leave credit run day matches today's date</li>
 *   <li>Fetch active employees for the eligible branches</li>
 *   <li>Check employee eligibility based on leave rules</li>
 *   <li>Credit applicable leaves to employees</li>
 * </ul>
 * </p>
 */
@Service
@Slf4j
public class LeaveJobSchedulerService {

    @Autowired
    private LeaveManagementService leaveManagementService;

    @Autowired
    private LeaveMasterService leaveMasterService;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private BranchRepository branchRepository;

    // Define a custom thread pool for better performance control
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * Manually triggers the leave crediting job.
     */
    public void processMonthlyLeaveCredit() {
        runLeaveCreditingJob();
    }

    /**
     * Scheduled job to process monthly leave credit based on branch-specific leave
     * run days. Runs every day at 2 AM, but processes leave credit only for branches
     * whose configured leave run day matches the current date.
     */
    @TrackSchedularExecution(name = "LEAVE_CREDIT_SCHEDULE_JOB")
    @Scheduled(cron = "0 0 2 * * ?") // Runs every day at 2 AM
    public void runLeaveCreditingJob() {
        log.info("Starting Leave Credit Job...");

        int today = LocalDate.now().getDayOfMonth();
        log.info("Today's date: {}", today);

        List<Branch> branches = branchRepository.findAllByIsActive(1);
        log.info("Total active branches found: {}", branches.size());

        // Fetch leave types once to avoid redundant calls
        List<LeaveMaster> leaveTypes = leaveMasterService.getAllLeaveTypes();

        // Process each branch in parallel using CompletableFuture
        List<CompletableFuture<Void>> futures = branches.stream()
            .map(branch -> CompletableFuture.runAsync(() -> processBranchLeave(branch, leaveTypes, today), executorService))
            .collect(Collectors.toList());

        // Wait for all tasks to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("Leave Credit Job completed.");
    }

    /**
     * Processes leave crediting for a specific branch.
     *
     * @param branch     The branch to process.
     * @param leaveTypes The list of all active leave types.
     * @param today      The current day of the month.
     */
    private void processBranchLeave(Branch branch, List<LeaveMaster> leaveTypes, int today) {
        try {
            Integer leaveCreditDay = branch.getLeaveCreditDay();

            if (leaveCreditDay == null) {
                log.warn("Skipping branch {} (ID: {}) - Leave credit day not configured.", branch.getBranchCode(), branch.getId());
                return;
            }

            if (!leaveCreditDay.equals(today)) {
                log.info("Skipping branch: {} - Leave credit day ({}) does not match today.", branch.getBranchCode(), leaveCreditDay);
                return;
            }

            log.info("Processing leave credit for branch: {} (ID: {})", branch.getBranchCode(), branch.getId());

            List<Employee> employees = employeeRepository.findByBranchIdAndIsActive(branch.getId());
            log.info("Total active employees found for branch {}: {}", branch.getBranchCode(), employees.size());

            employees.parallelStream().forEach(emp -> processEmployeeLeave(emp, leaveTypes));
        } catch (Exception e) {
            log.error("Error processing branch: {} (ID: {}). Skipping.", branch.getBranchCode(), branch.getId(), e);
        }
    }

    /**
     * Processes leave crediting for an individual employee.
     *
     * @param emp        The employee.
     * @param leaveTypes The list of all active leave types.
     */
    private void processEmployeeLeave(Employee emp, List<LeaveMaster> leaveTypes) {
        try {
            log.info("Processing leave credit for Employee ID: {}", emp.getEmpId());

            // Filter applicable leaves based on eligibility rules
            List<LeaveMaster> applicableLeaves = leaveTypes.stream()
                .filter(leave -> isLeaveApplicable(leave.getLeaveEligibilityRules(), emp))
                .collect(Collectors.toList());

            applicableLeaves.forEach(leave -> {
                try {
                    log.info("Crediting leave {} to Employee ID: {}", leave.getLeaveId(), emp.getEmpId());
                    leaveManagementService.creditLeavesToEmployee(emp.getEmpId(), leave.getLeaveId());
                } catch (Exception e) {
                    log.error("Error crediting leave {} for Employee ID: {}. Skipping.", leave.getLeaveId(), emp.getEmpId(), e);
                }
            });

            log.info("Successfully credited applicable leaves for Employee ID: {}", emp.getEmpId());

        } catch (Exception e) {
            log.error("Error while processing leave credit for Employee ID: {}. Skipping to next employee.", emp.getEmpId(), e);
        }
    }

    /**
     * Checks if an employee is eligible for a specific leave based on dynamic JSON rules.
     *
     * @param jsonRules JSON string containing key-value leave eligibility rules.
     * @param employee  Employee object whose attributes need to be matched against the rules.
     * @return true if the leave is applicable, false otherwise.
     */
    private boolean isLeaveApplicable(String jsonRules, Employee employee) {
        if (jsonRules == null || jsonRules.isEmpty()) {
            return true;
        }

        try {
            Map<String, String> rules = new ObjectMapper().readValue(jsonRules, new TypeReference<>() {});

            return rules.entrySet().stream().allMatch(rule -> {
                String fieldName = rule.getKey();
                String expectedValue = rule.getValue();

                Optional<String> actualValue = getFieldValue(employee, fieldName);
                return actualValue.isPresent() && expectedValue.equalsIgnoreCase(actualValue.get());
            });

        } catch (Exception e) {
            log.error("Error processing leave rules for employee ID: {}", employee.getEmpId(), e);
            throw new LeaveRuleProcessingException("Error processing leave rules for employee ID: " + employee.getEmpId(), e);
        }
    }

    private Optional<String> getFieldValue(Employee employee, String fieldName) {
        try {
            Field field = Employee.class.getDeclaredField(fieldName.toLowerCase());
            field.setAccessible(true);
            return Optional.ofNullable((String) field.get(employee));
        } catch (Exception e) {
            return Optional.empty();
        }
    }
}
