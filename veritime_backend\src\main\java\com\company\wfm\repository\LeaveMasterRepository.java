package com.company.wfm.repository;


import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.LeaveMaster;

@Repository
public interface LeaveMasterRepository extends JpaRepository<LeaveMaster, Long> {
    Optional<LeaveMaster> findByType(String type);

    List<LeaveMaster> findByIsActiveTrue();
    List<LeaveMaster> findByIsActiveFalse();



   // Long findLeaveCountByLeaveId(Long leaveId);
   @Query("SELECT lm.leaveCount FROM LeaveMaster lm WHERE lm.leaveId = :leaveId")
   Long findLeaveCountByLeaveId(Long leaveId);


    @Query("SELECT lm FROM LeaveMaster lm WHERE lm.isActive = true")
    List<LeaveMaster> findAllActiveLeaveTypes();

//    @Query("SELECT lm.* " +
//            "FROM LeaveMaster lm " +
//            "JOIN t_employee_leave_balance elb ON lm.leave_id = elb.leave_id " +
//            "WHERE lm.isActive = 1 " +
//            "AND elb.emp_id = :empid")
//    List<LeaveMaster> findAllActiveLeaveTypesByEmpid(@Param("empid") Long empid );

    @Query(value = "SELECT lm.* FROM t_leave_master lm " +
            "JOIN t_employee_leave_balance elb ON lm.LEAVE_ID = elb.LEAVE_ID " +
            "WHERE lm.IS_ACTIVE = 1 AND elb.EMP_ID = :empid",
            nativeQuery = true)
    List<LeaveMaster> findAllActiveLeaveTypesByEmpid(@Param("empid") Long empid);



    @Query("SELECT lm FROM LeaveMaster lm ORDER BY lm.leaveId DESC")
    List<LeaveMaster> findAllOrderByLeaveIdDesc();

    Optional<LeaveMaster> findByLeaveId(Long leaveId);


    /*// Default method to fetch only active leaves
    List<LeaveMaster> findByIsActiveOrderByLeaveIdDesc(boolean isActive);

    // Custom query to fetch inactive leaves
    @Query("SELECT l FROM LeaveMaster l WHERE l.isActive = false ORDER BY l.leaveId DESC")
    List<LeaveMaster> findInactiveLeaves();

    List<LeaveMaster> findAllByOrderByLeaveIdDesc();*/


    @Query("SELECT l, e1.empName, e2.empName FROM LeaveMaster l " +
            "LEFT JOIN Employee e1 ON l.createdBy = e1.empId " +
            "LEFT JOIN Employee e2 ON l.updatedBy = e2.empId " +
            "ORDER BY l.leaveId DESC")
    List<Object[]> findAllLeavesWithNames();

    @Query("SELECT l, e1.empName, e2.empName FROM LeaveMaster l " +
            "LEFT JOIN Employee e1 ON l.createdBy = e1.empId " +
            "LEFT JOIN Employee e2 ON l.updatedBy = e2.empId " +
            "WHERE l.isActive = true ORDER BY l.leaveId DESC")
    List<Object[]> findActiveLeavesWithNames();

    @Query("SELECT l, e1.empName, e2.empName FROM LeaveMaster l " +
            "LEFT JOIN Employee e1 ON l.createdBy = e1.empId " +
            "LEFT JOIN Employee e2 ON l.updatedBy = e2.empId " +
            "WHERE l.isActive = false ORDER BY l.leaveId DESC")
    List<Object[]> findInactiveLeavesWithNames();






}