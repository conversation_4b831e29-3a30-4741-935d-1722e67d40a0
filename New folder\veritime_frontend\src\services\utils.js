import moment from "moment-timezone";

export const addIdToData = (data) => {
  return data.map((item, index) => ({
    ...item,
    id: index + 1,
    srno: index + 1,
  }));
};

export const convertBranchIdToId = (data) => {
  return data.map((item, index) => ({
    ...item,
    id: item?.branchId,
    srno: index + 1,
  }));
};

export const convertDepartmentIdToId = (data,request) => {
  return data.map((item, index) => ({
    ...item,
    id: item?.departmentId,
    srno: (request.offset * request.limit) + (index + 1),
  }));
};

export const addIdKeyToData = (data) => {
  return data.map((item, index) => ({
    ...item,
    id: item.id,
    srno: index + 1,
  }));
};

export const getFormattedDate = (date) => {
  // console.log(formatDate ,getFormattedDate)
  return moment(date).format("YYYY-MM-DD") || date;
};

export const convertToSouthAfricaTime = (utcDateTime) => {
  return moment(utcDateTime)
    .tz("Africa/Johannesburg")
    .format("DD/MM/YYYY hh:mm A");
};

export const convertAndValidateTimeRange = (timeRange) => {
  const [startTime, endTime] = timeRange.split('-');
  const start = moment(startTime, "HH:mm:ss");
  const end = moment(endTime, "HH:mm:ss");
  if (!start.isValid() || !end.isValid()) {
      return "Invalid time format";
  }
  const startFormatted = start.format("hh:mm A");
  const endFormatted = end.format("hh:mm A");
  return `${startFormatted} - ${endFormatted}`;
};