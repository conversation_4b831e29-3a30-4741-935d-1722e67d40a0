package com.company.wfm.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.company.wfm.dto.TicketCategoryDTO;
import com.company.wfm.entity.TicketCategoryMaster;

public interface TicketCategoryMasterService {

    public TicketCategoryMaster saveCategory(TicketCategoryMaster category);
  //  public Page<TicketCategoryMaster> listCategories(Pageable pageable);
    public TicketCategoryMaster updateCategory(Long id, TicketCategoryMaster updatedCategory);
    public boolean softDeleteCategory(Long id);
    public List<Object[]> listCategoriesForSelect();
    public Page<TicketCategoryDTO> listCategories(Pageable pageable);
  public List<TicketCategoryMaster> getTicketCategoriesByBranchAndDepartment(Long departmentId);
  public TicketCategoryMaster getTicketByID(Long id);
}
