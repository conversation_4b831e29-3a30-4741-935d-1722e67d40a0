package com.company.wfm.entity;


import java.time.LocalDateTime;

import com.company.wfm.util.TicketCodeGenerator;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_ticket")
public class Ticket {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TICKET_ID")
    private Long ticketId;

    @Column(name = "ticket_code", unique = true, nullable = false)
    private String ticketCode;

    @Column(name = "EMP_ID", nullable = false)
    private Long empId;

    @Column(name = "TICKET_SUBJECT", nullable = false)
    private String ticketSubject;

    @Column(name = "TICKET_MESSAGE", nullable = false)
    private String ticketMessage;

    @Column(name = "STATUS", nullable = false)
    private String status;

    @Column(name = "CATEGORY")
    private String category;

    @Column(name = "FILE_PATH")
    private String filePath;

    @Column(name = "UPDATED_BY")
    private Long updatedBy;

    @Column(name = "UPDATED_TIME")
    private LocalDateTime updatedTime;

    @Column(name = "CREATED_BY", nullable = false)
    private Long createdBy;

    @Column(name = "CREATED_TIME", nullable = false)
    private LocalDateTime createdTime;

    @Column(name = "department_id")
    private Long departmentId;

    @Column(name = "assigned_by")
    private Long assigned_by;

    @Column(name = "assigned_to")
    private Long assigned_to;


    @Column(name = "internal_ticket_status")
    private String internal_ticket_status;

    @Column(name = "latest_remark")
    private String latest_remark;

    @Column(name = "current_type")
    private String current_type;

    @Column(name = "last_escalation_time")  // New column for latest escalation time
    private LocalDateTime lastEscalationTime;

    @Column(name = "branch_id", nullable = true) // Allows NULL
    private Long branchId;


    @Transient
    private String categoryName;





    @PrePersist
    public void generateTicketCode() {
        this.ticketCode = TicketCodeGenerator.generateCode();
    }
}
