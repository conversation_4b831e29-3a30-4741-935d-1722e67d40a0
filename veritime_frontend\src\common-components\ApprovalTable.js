import React, { useEffect, useState } from "react";
import Swal from "sweetalert2";
import TableFilter from "./TableFilter";
import { API_URLS } from "../constants/apiConstants";
import {
  fileDownload,
  getRequest,
  postRequest,
  postRequestWithSecurity,
  getRequestWithSecurity,
} from "@/services/apiService";
import VisibilityIcon from "@mui/icons-material/Visibility";
import "../common-components/ApprovalTable.css";

const ApprovalTable = ({ showActionCol, isFromHistory, fetchCount }) => {
  const [rows, setRows] = useState([]);
  const [imageErrors, setImageErrors] = useState({});

  const getInitials = (name) => {
    return name?.charAt(0)?.toUpperCase() || "U";
  };
  const handleImageError = (index) => {
    setImageErrors((prevErrors) => ({
      ...prevErrors,
      [index]: true,
    }));
  };

  useEffect(() => {
    const fetchLeaveHistory = async () => {
      fetchCount("leaverequest");
      try {
        let apiurl = API_URLS.LEAVE_HISTORY;
        if (window.location.pathname == "/employeeDashboard/viewHistory")
          apiurl += "?type=history";
        if(window.location.search==="?tab=Leave&status=Pending")
          apiurl += "?status=PENDING"
        const data = await getRequestWithSecurity(`${apiurl}`);
        if (data) {
          
          console.log("test",data)
          const rowsWithId = data.map((row, index) => ({
            ...row,
            id: index + 1,
          }));
          //console.log("Hi",rowsWithId)
          
          // if(window.location.search==="?tab=Leave")
          // {
          //   const pendingApprovals=rowsWithId.filter(row=>row.approvalStatus=="PENDING")
          //   console.log(pendingApprovals)
          //   setRows(pendingApprovals);
          // }else{
            
          // }
          setRows(rowsWithId);
          
          
        }
      } catch (error) {}
    };

    fetchLeaveHistory();
  }, []);

  const updateSerialNumbers = (updatedRows) => {
    return updatedRows.map((row, index) => ({
      ...row,
      id: index + 1,
    }));
  };

  const handlePullBack = async (leaveHistoryId) => {
    const { value: reason } = await Swal.fire({
      title: "Cancel Leave Request",
      text: "Do you want to Cancel this Leave Request?",
      input: "textarea",
      inputPlaceholder: "Enter your reason for Cancel..",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, cancel!",
      cancelButtonText: "No, keep it in history",
      inputValidator: (value) => {
        if (!value) {
          return "please mention reason!";
        }
      },
    });

    if (reason) {
      try {
        const url = API_URLS.PULL_BACK_LEAVE(leaveHistoryId);
        await postRequest(url, { reason });
        Swal.fire(
          "Cancelled!",
          "The Leave Request has been cancelled.",
          "success"
        ).then(() => window.location.reload());
      } catch (error) {
        Swal.fire("Error!", "Failed to cancel the Leave Request.", "error");
      }
    } else {
      Swal.fire("Error!", "Please mention", "error");
    }
  };

  const handleAccept = async (leaveHistoryId) => {
    const { value: reason } = await Swal.fire({
      title: "Approve Leave Request",
      text: "Do you want to approve this Leave Request?",
      input: "textarea",
      inputPlaceholder: "Enter your approval reason...",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, approve it!",
      cancelButtonText: "No, keep it pending",
      inputValidator: (value) => {
        if (!value) {
          return "please mention reason!";
        }
      },
    });

    if (reason) {
      try {
        const url = API_URLS.APPROVE_LEAVE(leaveHistoryId);
        await postRequestWithSecurity(url, { reason });
        Swal.fire(
          "Approved!",
          "The Leave Request has been approved.",
          "success"
        ).then(() => window.location.reload());
      } catch (error) {
        Swal.fire("Error!", error?.response?.data, "error");
      }
    }
  };

  const handleDeny = async (leaveHistoryId) => {
    const { value: reason } = await Swal.fire({
      title: "Deny Leave Application",
      text: "Do you want to deny this Leave Request?",
      input: "textarea",
      inputPlaceholder: "Enter your denial reason...",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, decline it!",
      cancelButtonText: "No, keep it pending",
      inputValidator: (value) => {
        if (!value) {
          return "please mention reason!";
        }
      },
    });

    if (reason) {
      try {
        const url = API_URLS.DENY_LEAVE(leaveHistoryId);
        const response = await postRequestWithSecurity(url, { reason });
        Swal.fire(
          "Denied!",
          "The leave application has been denied.",
          "success"
        ).then(() => window.location.reload());
      } catch (error) {
        Swal.fire("Error!", error?.response?.data, "error");
      }
    }
  };
  const handleDownload = async (file) => {
    try {
      const fileName = file.split("/").pop();
      const response = await fileDownload(
        `${API_URLS?.Leave_Download}${file}`,
        true
      );

      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      const blob = await response.blob();
      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.target = "_blank";
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();
      showSuccessAlert(`File "${fileName}" downloaded successfully.`);
    } catch (error) {
      showErrorAlert("Error downloading file:", error.message);
    }
  };
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  function formatDate(date) {
    const options = { day: "2-digit", month: "2-digit", year: "numeric" };
    const formattedDate = new Date(date).toLocaleDateString("en-GB", options);
    return formattedDate;
  }

  const handleDetail = (row) => {
    const isHistory = window.location.pathname.includes("viewHistory");
    const initials = getInitials(row.empName);
    const S3URL = process.env.NEXT_PUBLIC_S3_URL;

    Swal.fire({
      html: `
        <div style="font-size: 18px; line-height: 1.6; color: #333; display: flex; flex-direction: column; gap: 20px; max-width: 700px;">
      
          <!-- Employee Info Section -->
          <div style="display: flex; justify-content: center; align-items: center; gap: 20px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0;">
            <img 
             src="${S3URL}${row.imageUrl}"
              alt="${initials}" 
                 style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover; border: 2px solid #ddd;"
                 onerror="this.onerror=null; this.src=''; this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div style="display: none; align-items: center; justify-content: center; width: 60px; height: 60px; background-color: #ccc; color: #fff; font-weight: bold; border-radius: 50%;">
              ${initials}
            </div>
            <div style="display: flex; flex-direction: column; align-items: center;">
              <span style="font-size: 22px; font-weight: bold; color: #1a1a1a;">${
                row.empName
              }</span>
              <span style="font-size: 14px; color: #777;">${
                row.designationName
              }</span>
            </div>
          </div>
      
          <div style="display: flex; flex-direction: column; gap: 15px; padding-bottom: 15px;">
            <div style="display: flex; justify-content: space-between; padding-bottom: 10px; margin-bottom: -14px;width: 718px;">
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px; ">Designation:</strong>
                <span>${row.designationName}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">Reason:</strong>
                <span>${row.reason}</span>
              </div>
            </div>
      <div style="display: flex; justify-content: space-between; padding-bottom: 10px; margin-bottom: -11px;width: 718px;">
  <div style="flex: 1; display: flex; justify-content: flex-start;">
    <strong style="font-weight: bold; margin-right: 8px;">Leave Start Date:</strong>
    <span>${formatDate(row.startDate)}</span>
  </div>
  <div style="margin: 0 20px;"></div>
  <div style="flex: 1; display: flex; justify-content: flex-start;">
    <strong style="font-weight: bold; margin-right: 8px;">Leave End Date:</strong>
    <span>${formatDate(row.endDate)}</span>
  </div>
</div>

 <div style="display: flex; justify-content: space-between; padding-bottom: 10px; margin-bottom: -30px;width: 718px;">
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">EmpCode:</strong>
              <span>${row.empCode || "N/A"}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">Leave Type:</strong>
              <span>${row.leaveTypeName || "N/A"}</span>
              </div>
            </div>
          </div>


      
            <div style="display: flex; justify-content: space-between; padding-bottom: 10px; margin-bottom: 9px;width: 718px;">
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">Leave Days:</strong>
                <span>${row.appliedLeaveCount}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">Status:</strong>
                <span>${row.approvalStatus}</span>
              </div>
            </div>
          </div>

          <div style="display: flex; flex-direction: column; gap: 15px; padding-bottom: 15px;">
            <div style="display: flex; justify-content: space-between; padding-bottom: 10px; margin-bottom: -4px;width: 718px;">
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">Created By:</strong>
                <span>${row.createdByName || "N/A"}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 3px;">Created Time:</strong>
                <span>${
                  new Date(row.createdTime).toLocaleString() || "N/A"
                }</span>
              </div>
            </div>

      
          
      
                  ${
                    row.filePath
                      ? `
          <div style="display: flex; justify-content: flex-start; align-items: center; gap: 10px; margin-top: 10px;">
            <strong style="font-weight: bold;">File Attachment:</strong>
            <img src="/image/downarroww.png" alt="Download" 
                 style="cursor: pointer; width: 30px; height: 30px;" />
          </div>
        `
                      : ""
                  }

       

          <div style="display: flex; justify-content: center; gap: 10px;">
            ${
              !isHistory && row.approvalStatus === "PENDING"
                ? ` 
              <button id="approveBtn" class="approve-button">Approve</button>
              <button id="denyBtn" class="deny-button">Decline</button>
            `
                : ""
            }
            ${
              (!isHistory && row.approvalStatus === "APPROVED") ||
              (isHistory && row.approvalStatus === "PENDING")
                ? `
              <button id="cancelBtn" class="deny-butto">Cancel Leave</button>
            `
                : ""
            }
           
          </div>
        </div>
      `,
      icon: "",
      showCloseButton: true,
      showCancelButton: false,
      showConfirmButton: false,
      customClass: {
        container: "custom-swal-container",
        popup: "custom-swal-popup",
        title: "custom-swal-title",
        content: "custom-swal-content",
      },
      didOpen: () => {
        const downloadImage = document.querySelector('img[alt="Download"]');
        if (downloadImage) {
          downloadImage.addEventListener("click", () =>
            handleDownload(row.filePath)
          );
        }

        const approveBtn = document.getElementById("approveBtn");
        const denyBtn = document.getElementById("denyBtn");
        const cancelBtn = document.getElementById("cancelBtn");

        if (approveBtn) {
          approveBtn.addEventListener("click", () =>
            handleAccept(row.leaveHistoryId)
          );
        }
        if (denyBtn) {
          denyBtn.addEventListener("click", () =>
            handleDeny(row.leaveHistoryId)
          );
        }
        if (cancelBtn) {
          cancelBtn.addEventListener("click", () =>
            handlePullBack(row.leaveHistoryId)
          );
        }
      },
      allowOutsideClick: true,
    });
  };

  const columns = [
    { field: "id", headerName: "Sr.no", width: 70 },
    {
      field: "empName",
      headerName: "Name",
      width: 200,
      renderCell: (params) => {
        const imageUrl = params.row.imageUrl;
        const name = params.row.empName;
        const designation = params.row.designationName;

        return (
          <div style={{ display: "flex", alignItems: "center" }}>
            {!imageUrl || imageErrors[params.row.id] ? (
              <div
                className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                style={{
                  width: "50px",
                  height: "50px",
                  backgroundColor: "#ccc",
                  color: "#fff",
                  fontWeight: "bold",
                  marginBottom: "22px",
                }}
              >
                {getInitials(name)}
              </div>
            ) : (
              <img
                src={`${S3URL}${imageUrl}`}
                alt={name}
                className="rounded-circle me-2"
                style={{ width: "50px", height: "50px" }}
                onError={() => handleImageError(params.row.id)}
              />
            )}

            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
              }}
            >
              <span
                style={{
                  fontWeight: "bold",
                  fontSize: "11px",
                  marginRight: "-92px",
                  marginBottom: "23px",
                }}
              >
                {name}
              </span>
              <span
                style={{
                  fontSize: "12px",
                  color: "grey",
                  marginTop: "11px",
                  marginLeft: "23px",
                }}
              >
                {designation}
              </span>
            </div>
          </div>
        );
      },
    },

    { field: "startDate", headerName: "Start Date", width: 150 },
    { field: "endDate", headerName: "End Date", width: 150 },
    { field: "appliedLeaveCount", headerName: "Leave Days", width: 100 },
    { field: "approvalStatus", headerName: "Status", width: 100 },
    // { field: 'reason', headerName: 'Reason', width: 200 }
  ];

  // columns.push({
  //   field: 'action',
  //   headerName: 'Action',
  //   width: 200,
  //   renderCell: (params) => {
  //     const isHistory = window.location.pathname.includes("viewHistory");

  //     if (isHistory && params.row.approvalStatus === 'PENDING') {
  //       return (
  //         <button
  //           className="deny-button" style={{ width: "97px" }}
  //           onClick={() => handlePullBack(params.row.leaveHistoryId)}
  //         >
  //           Cancel Leave
  //         </button>
  //       );
  //     } else if (params.row.approvalStatus === 'PENDING') {
  //       return (
  //         <div>
  //           <button
  //             className="approve-button"
  //             onClick={() => handleAccept(params.row.leaveHistoryId)}
  //           >
  //             Approve
  //           </button>
  //           <button
  //             className="deny-button"
  //             onClick={() => handleDeny(params.row.leaveHistoryId)}
  //           >
  //             Decline
  //           </button>
  //         </div>
  //       );
  //     } else if (params.row.approvalStatus === 'APPROVED') {
  //       return <span className="approvedTxt">Approved by {params.row.updatedBy || "Supervisor"}</span>;
  //     } else if (params.row.approvalStatus === 'DENIED') {
  //       return <span className="rejectedTxt">Declined by {params.row.updatedBy || "Supervisor"}</span>;
  //     }
  //     return null;
  //   }
  // });

  columns.push({
    field: "View",
    headerName: "View",
    width: 100,
    renderCell: (params) => (
      <button
        className="view-button"
        onClick={() => handleDetail(params.row)}
        style={{ border: "none", background: "transparent", cursor: "pointer" }}
      >
        <VisibilityIcon
          style={{ cursor: "pointer", color: "bl", marginBottom: "5px" }}
        />
      </button>
    ),
  });

  return (
    <div id="tableWrapper" style={{ marginTop: -24 }}>
      <TableFilter columns={columns} rows={rows} />
    </div>
  );
};

export default ApprovalTable;
