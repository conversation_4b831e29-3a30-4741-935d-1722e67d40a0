import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from "react-bootstrap";
import {
  postRequest,
  getRequest,
  postRequestWithSecurity,
} from "../../../services/apiService";
import { API_URLS } from "../../../constants/apiConstants";
import { showSuccessAlert2 } from "@/services/alertService";
import Select from "react-select";
import "../ViewTicket/SupportPersonForm.css";

const SupportPersonForm = ({ show, handleClose, ticketId, empId }) => {
  const [formData, setFormData] = useState({
    type: "",
    reviewTicket: "",
    selectedEmpId: "",
    selectedEmpName: "",
    departmentId: "",
  });
  const [empNameOptions, setEmpNameOptions] = useState([]);
  const [deptNames, setDeptNames] = useState([]);
  const [errorMessages, setErrorMessages] = useState({
    type: "",
    reviewTicket: "",
    selectedEmpId: "",
    departmentId: "",
  });
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // const fetchDeptNames = () => {
  //   try {
  //     const data = getRequest(API_URLS.DEPT_LOOKUP);
  //     if (data && Array.isArray(data)) {
  //       setDeptNames(data);
  //     } else {
  //     }
  //   } catch (error) {
  //   }
  // };

  const fetchDeptNames = () => {
    getRequest(API_URLS.DEPT_LOOKUP)
      .then((data) => {
        if (data && Array.isArray(data)) {
          setDeptNames(data);
        }
      })
      .catch((error) => {
        console.error("Error fetching department names:", error);
      });
  };

  const fetchEmpNames = async (selectedType, selectedDeptId = null) => {
    try {
      setLoading(true);
      const payload = {
        type: selectedType,
        offset: 0,
        limit: 10,
      };

      if (selectedType === "internal" && selectedDeptId) {
        payload.departmentId = selectedDeptId;
      }

      const empVendorResponse = await postRequest(
        API_URLS.empVendorList,
        payload
      );

      if (empVendorResponse && Array.isArray(empVendorResponse.content)) {
        const empNamesArray = empVendorResponse.content;
        
        setEmpNameOptions(
          empNamesArray.map((emp) => ({
            value: emp.id,
            label: emp.name, // Get empName directly from the first-level array
          }))
        );
        
      } else {
        setEmpNameOptions([]);
      }
    } catch (error) {
      setEmpNameOptions([]);
      console.error("Error fetching employee names:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleTypeChange = (e) => {
    const selectedType = e.target.value.trim().toLowerCase(); // Ensure correct format

    if (selectedType !== "internal" && selectedType !== "external") {
      console.error("Invalid type selected:", selectedType);
      setErrorMessages((prev) => ({
        ...prev,
        type: "Invalid type. Use 'internal' or 'external'.",
      }));
      return;
    }

    setFormData({
      ...formData,
      type: selectedType,
      departmentId: selectedType === "internal" ? "" : null,
      selectedEmpId: "",
    });

    if (selectedType === "external") {
      fetchEmpNames(selectedType);
    }
  };

  const handleDepartmentSelect = (selectedOption) => {
    const selectedDeptId = selectedOption
      ? selectedOption.value.toString()
      : "";
    setFormData({ ...formData, departmentId: selectedDeptId });

    if (formData.type === "internal") {
      fetchEmpNames(formData.type, selectedDeptId);
    }
  };

  const handleEmployeeSelect = (selectedOption) => {
    setFormData({
      ...formData,
      selectedEmpId: selectedOption ? selectedOption.value : "",
      selectedEmpName: selectedOption ? selectedOption.label : "",
    });
  };

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setErrorMessages({ ...errorMessages, [e.target.name]: "" });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessages({
      type: "",
      reviewTicket: "",
      selectedEmpId: "",
      departmentId: "",
    });

    const { type, reviewTicket, selectedEmpId, departmentId } = formData;
    let hasError = false;

    if (!type) {
      setErrorMessages((prev) => ({ ...prev, type: "*Type is required." }));
      hasError = true;
    }
    if (!selectedEmpId) {
      setErrorMessages((prev) => ({
        ...prev,
        selectedEmpId: "*Employee selection is required.",
      }));
      hasError = true;
    }
    if (!reviewTicket) {
      setErrorMessages((prev) => ({
        ...prev,
        reviewTicket: "*Remarks are required.",
      }));
      hasError = true;
    }
    if (type === "internal" && !departmentId) {
      setErrorMessages((prev) => ({
        ...prev,
        departmentId: "*Department selection is required.",
      }));
      hasError = true;
    }

    if (hasError) return;

    setIsSubmitting(true);

    try {
      const payload = {
        ticketId,
        type,
        remark: reviewTicket,
      };

      if (type === "internal") {
        payload.empId = selectedEmpId;
        payload.departmentId = departmentId;
      } else if (type === "external") {
        payload.vendorId = selectedEmpId;
      }

      const response = await postRequestWithSecurity(
        API_URLS.Assign_Update,
        payload
      );
      if (response) {
        setFormData({
          type: "",
          reviewTicket: "",
          selectedEmpId: "",
          selectedEmpName: "",
          departmentId: "",
        });

        // Call handleClose to update the parent component with the new assignment details
        showSuccessAlert2(response).then(() =>
          handleClose(reviewTicket, formData?.selectedEmpName, selectedEmpId)
        );
      } else {
      }
    } catch (error) {
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    fetchDeptNames();
  }, []);

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>Assign Employee</Modal.Title>
      </Modal.Header>
      <label
        htmlFor="ticketSubject"
        className="ticket-text-primary"
        style={{ marginTop: "10px", marginLeft: "14px" }}
      >
        <span className="text-danger">
          The fields with * marks are mandatory
        </span>
      </label>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group controlId="formType">
            <Form.Label>
              Type <span className="text-danger">*</span>
            </Form.Label>
            <Form.Control
              as="select"
              name="type"
              value={formData.type}
              onChange={handleTypeChange}
            >
              <option value="">Select Type</option>
              <option value="internal">Internal</option>
              <option value="external">External</option>
            </Form.Control>
            {errorMessages.type && (
              <div className="text-danger">{errorMessages.type}</div>
            )}
          </Form.Group>

          {formData.type === "internal" && (
            <Form.Group controlId="formDepartment" className="mt-3">
              <Form.Label>
                Department <span className="text-danger">*</span>
              </Form.Label>
              <Select
                options={deptNames.map((dept) => ({
                  value: dept.departmentId,
                  label: dept.departmentName,
                }))}
                value={
                  deptNames.find(
                    (dept) =>
                      dept.departmentId === parseInt(formData.departmentId)
                  )
                    ? {
                        value: deptNames.find(
                          (dept) =>
                            dept.departmentId ===
                            parseInt(formData.departmentId)
                        )?.departmentId,
                        label: deptNames.find(
                          (dept) =>
                            dept.departmentId ===
                            parseInt(formData.departmentId)
                        )?.departmentName,
                      }
                    : null
                }
                onChange={handleDepartmentSelect}
                placeholder="Select Department"
                isClearable
                classNamePrefix="react-select"
              />
              {errorMessages.departmentId && (
                <div className="text-danger">{errorMessages.departmentId}</div>
              )}
            </Form.Group>
          )}

          <Form.Group controlId="formSelectEmpName" className="mt-3">
            <Form.Label>
              Employee <span className="text-danger">*</span>
            </Form.Label>
            {/* <Select
              name="selectedEmpId"
              value={empNameOptions.find(
                (emp) => emp.value === formData.selectedEmpId
              )}
              onChange={handleEmployeeSelect}
              options={empNameOptions}
              getOptionLabel={(option) => option.label}
              getOptionValue={(option) => option.value}
              placeholder="Select Employee"
              isLoading={loading}
            /> */}

            <Select
              name="selectedEmpId"
              value={empNameOptions.find(
                (emp) => emp.value === formData.selectedEmpId
              )}
              onChange={handleEmployeeSelect}
              options={empNameOptions}
              placeholder="Select Employee"
              isLoading={loading}
            />

            {errorMessages.selectedEmpId && (
              <div className="text-danger">{errorMessages.selectedEmpId}</div>
            )}
          </Form.Group>

          <Form.Group controlId="formRemarkTicket" className="mt-3">
            <Form.Label>
              Remark <span className="text-danger">*</span>
            </Form.Label>
            <Form.Control
              type="text"
              name="reviewTicket"
              value={formData.reviewTicket}
              onChange={handleChange}
              placeholder="Enter your remark"
            />
            {errorMessages.reviewTicket && (
              <div className="text-danger">{errorMessages.reviewTicket}</div>
            )}
          </Form.Group>

          <div className="d-flex justify-content-end mt-4">
            <Button variant="secondary" onClick={handleClose}>
              Close
            </Button>
            <Button
              variant="primary"
              type="submit"
              disabled={isSubmitting}
              className="ms-2"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default SupportPersonForm;
