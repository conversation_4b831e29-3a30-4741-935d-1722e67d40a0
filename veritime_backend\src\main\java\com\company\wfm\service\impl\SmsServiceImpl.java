package com.company.wfm.service.impl;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.company.wfm.annotation.TrackExecutionTime;
import com.company.wfm.dto.SmsDataRequest;
import com.company.wfm.dto.SmsResponse;
import com.company.wfm.entity.SmsServiceStatus;
import com.company.wfm.repository.SmsServiceStatusRepository;
import com.company.wfm.service.SmsService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@DependsOn("appConfigurations")
public class SmsServiceImpl implements SmsService {

    private final WebClient.Builder webClientBuilder;
    private final SmsServiceStatusRepository smsServiceStatusRepository;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${sms.base.url}")
    private String smsURL;

    @Value("${sms.username}")
    private String smsUsername;

    @Value("${sms.password}")
    private String smsPassword;

    public SmsServiceImpl(WebClient.Builder webClientBuilder, SmsServiceStatusRepository smsServiceStatusRepository) {
        this.webClientBuilder = webClientBuilder;
        this.smsServiceStatusRepository = smsServiceStatusRepository;
    }

    @TrackExecutionTime
    @Override
    public void sendMessage(SmsDataRequest smsRequest) throws JsonProcessingException {
        log.info("Processing SMS request: {}", smsRequest);

        getSmsDeliveryStatus(smsRequest).subscribe(
            this::handleSmsResponse,
            error -> log.error("Failed to send SMS: {}", error.getMessage(), error)
        );
    }

    private void handleSmsResponse(String jsonResponse) {
        try {
            log.info("Received SMS response: {}", jsonResponse);

            List<SmsResponse> responses = parseSmsResponse(jsonResponse);
            saveSmsResponses(responses);
        } catch (Exception e) {
            log.error("Error processing SMS response: {}", e.getMessage(), e);
        }
    }

    private List<SmsResponse> parseSmsResponse(String jsonResponse) throws JsonProcessingException {
        if (jsonResponse.startsWith("[")) {
            return Arrays.asList(objectMapper.readValue(jsonResponse, SmsResponse[].class));
        } else {
            return List.of(objectMapper.readValue(jsonResponse, SmsResponse.class));
        }
    }

    private void saveSmsResponses(List<SmsResponse> responses) {
        List<SmsServiceStatus> serviceStatusList = responses.stream().map(response -> {
            SmsServiceStatus serviceStatus = new SmsServiceStatus();
            BeanUtils.copyProperties(response, serviceStatus);
            log.info("Mapped SMS Response: {}", serviceStatus);
            return serviceStatus;
        }).toList();

        try {
            smsServiceStatusRepository.saveAll(serviceStatusList);
            log.info("SMS responses saved successfully");
        } catch (Exception e) {
            log.error("Database persistence failed: {}", e.getMessage(), e);
        }
    }

    private Mono<String> getSmsDeliveryStatus(SmsDataRequest smsRequest) {
        log.info("Sending SMS request...");

        String basicAuth = "Basic " + Base64.getEncoder()
                .encodeToString((smsUsername + ":" + smsPassword).getBytes(StandardCharsets.UTF_8));

        return webClientBuilder.baseUrl(smsURL)
                .build()
                .post()
                .uri("/send/sms")
                .header(HttpHeaders.AUTHORIZATION, basicAuth)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(smsRequest)
                .retrieve()
                .onStatus(HttpStatusCode::is4xxClientError, this::handleClientError)
                .onStatus(HttpStatusCode::is5xxServerError, this::handleServerError)
                .bodyToMono(String.class)
                .doOnError(WebClientResponseException.class, this::handleHttpException);
    }

    private Mono<? extends Throwable> handleClientError(org.springframework.web.reactive.function.client.ClientResponse response) {
        return response.bodyToMono(String.class).flatMap(errorBody -> {
            log.error("Client Error [{}]: {}", response.statusCode(), errorBody);
            return Mono.error(new RuntimeException("Client Error: " + response.statusCode() + " - " + errorBody));
        });
    }

    private Mono<? extends Throwable> handleServerError(org.springframework.web.reactive.function.client.ClientResponse response) {
        return response.bodyToMono(String.class).flatMap(errorBody -> {
            log.error("Server Error [{}]: {}", response.statusCode(), errorBody);
            return Mono.error(new RuntimeException("Server Error: " + response.statusCode() + " - " + errorBody));
        });
    }

    private void handleHttpException(WebClientResponseException ex) {
        log.error("HTTP Request Failed [{}]: {}", ex.getStatusCode().value(), ex.getResponseBodyAsString());
    }
}
