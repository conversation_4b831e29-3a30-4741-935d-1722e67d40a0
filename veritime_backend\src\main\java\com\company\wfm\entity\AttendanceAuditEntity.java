package com.company.wfm.entity;

import java.io.Serializable;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "t_attendance_audit")
public class AttendanceAuditEntity implements Serializable {

  /**
	 *
	 */
	private static final long serialVersionUID = 1L;

@Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ATTENDANCE_AUDIT_ID")
  private Long id;

  @ManyToOne
  @JoinColumn(name = "EMP_ID")
  private Employee empId;

  @Column(name = "PUNCH_IN_TIME")
  private Time punchInTime;

  @Column(name = "DEVICE_TYPE")
  private String deviceType;

  @Column(name = "DEVICE_DUMP")
  private String deviceDump;
  @Column(name = "CREATED_BY")
  private Long createdBy;

  @Column(name = "CREATED_DATE")
  private LocalDateTime createdDate;

  @Column(name = "DATE")
  private LocalDate date;

}
