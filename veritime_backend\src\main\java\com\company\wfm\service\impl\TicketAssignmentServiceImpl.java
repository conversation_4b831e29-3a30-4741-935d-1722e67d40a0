package com.company.wfm.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.AccessDeniedException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.EmailData;
import com.company.wfm.dto.NotificationDTO;
import com.company.wfm.dto.TicketAssignmentDTO;
import com.company.wfm.dto.TicketEscalationRequestDTO;
import com.company.wfm.entity.AssignedTickets;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.Ticket;
import com.company.wfm.entity.TicketCategoryMaster;
import com.company.wfm.entity.TicketEscalation;
import com.company.wfm.entity.Vendor;
import com.company.wfm.entity.VendorApiConfig;
import com.company.wfm.repository.AssignedTicketRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.TicketCategoryMasterRepository;
import com.company.wfm.repository.TicketEscalationRepository;
import com.company.wfm.repository.TicketRepository;
import com.company.wfm.repository.VendorApiConfigRepository;
import com.company.wfm.repository.VendorRepository;
import com.company.wfm.service.AmazonS3Service;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.DepartmentService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.TicketAssignmentService;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.service.VendorApiService;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;

@Service
public class TicketAssignmentServiceImpl implements TicketAssignmentService {

    private static final Logger logger = LoggerFactory.getLogger(TicketAssignmentServiceImpl.class);

    @Autowired
    private AssignedTicketRepository assignedTicketRepository;

    @Autowired
    private VendorApiService vendorApiService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private TicketRepository ticketRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private VendorRepository vendorRepository;

    @Autowired
    private VendorApiConfigRepository vendorApiConfigRepository;

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private CommonNotificationService notificationService;


    @Autowired
    AmazonS3Service s3Service;

    @Autowired
     TicketEscalationRepository ticketEscalationRepository;

    @Autowired
    TicketCategoryMasterRepository ticketCategoryMasterRepository;

    private static final String BASE_UPLOAD_DIRECTORY = "uploads/";

    @Override
    public void assignTicket(TicketAssignmentDTO ticketAssignmentDTO) throws Exception{
        logger.info("Received TicketAssignmentDTO: {}", ticketAssignmentDTO);
        Long ticketId = ticketAssignmentDTO.getTicketId();
        String type = ticketAssignmentDTO.getType();
        String remark = ticketAssignmentDTO.getRemark();

        if ("internal".equalsIgnoreCase(type)) {
            assignInternalTicket(ticketAssignmentDTO.getEmpId(), ticketId,remark,type);
        } else if ("external".equalsIgnoreCase(type)) {
            assignExternalTicket(ticketAssignmentDTO.getVendorId(), ticketId,ticketAssignmentDTO.getProductCode());
        } else {
            throw new IllegalArgumentException("Invalid ticket type: " + type);
        }
    }

    private void assignInternalTicket(Long empId, Long ticketId,String remark,String type) {

        Long employeeIdFromToken = tokenService.getEmployeeIdFromToken();

        AssignedTickets assignedTicket = new AssignedTickets();
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found"));

        Ticket ticket = ticketRepository.findById(ticketId)
                .orElseThrow(() -> new EntityNotFoundException("Ticket not found"));

        assignedTicket.setEmployee(employee);
        assignedTicket.setTicket(ticket);
        assignedTicket.setType(type);
        assignedTicket.setReferenceId(null);

        // Generate custom remark based on the presence of the remark in DTO
        String employeeName = employee.getEmpName();
        String customRemark = "A ticket has been assigned to " + employeeName;
        if (remark != null && !remark.isEmpty()) {
            customRemark += " With Remark: " + remark;
        }

        // Save the remark in the assigned ticket
        assignedTicket.setRemark(customRemark);
        assignedTicket.setPushRemark(false);
        assignedTicket.setCreatedBy(employeeIdFromToken);
        assignedTicket.setCreatedAt(LocalDateTime.now());

        // Save to the assigned tickets table
        assignedTicketRepository.save(assignedTicket);

        updateTicketStatus(ticketId, "assigned", "assigned",empId,employeeIdFromToken,customRemark,type);

        // Send email notification to employee
        String emailId = employeeRepository.getReferenceById(empId).getEmail();

        EmailData data = new EmailData();
        data.setEmailId(emailId);
        data.setSubject("Ticket Assignment");
        data.setMessage("Ticket number: " + ticketId + " has been assigned to you.");
        emailService.sendEmail(data);

        // Send push notification
        sendTicketAssignmentNotification(empId, ticketId, remark, employeeIdFromToken);
    }

    private void sendTicketAssignmentNotification(Long empId, Long ticketId, String remark, Long assignedById) {
        // Define notification title and message
        String notificationTitle = "Ticket Assigned";
        String notificationMessage = "Ticket number: " + ticketId + " has been assigned to you."
                + (remark != null ? " Remark: " + remark : "");

        // Create NotificationDTO with required details
        NotificationDTO notificationDTO = new NotificationDTO(
                notificationTitle,
                notificationMessage,
                "ticket/details/" + ticketId,
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        // Send the notification using notificationService
        notificationService.sendNotificationToEmployee(
                empId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "ticket/details/" + ticketId,
                String.valueOf(assignedById)
        );
    }


    //update ticket status and internal Status

    public void updateTicketStatus(Long ticketId, String status, String internalTicketStatus,Long empId,Long employeeIdFromToken,String customRemark,String type) {
        Ticket ticket = ticketRepository.findById(ticketId)
                .orElseThrow(() -> new EntityNotFoundException("Ticket not found"));

        // Set the new status values
        ticket.setStatus(status);
        ticket.setInternal_ticket_status(internalTicketStatus);
        ticket.setAssigned_to(empId);
        ticket.setCurrent_type(type);
        ticket.setAssigned_by(employeeIdFromToken);
        ticket.setLatest_remark(customRemark);

        // Optionally set updatedBy and updatedTime fields
        ticket.setUpdatedBy(tokenService.getEmployeeIdFromToken());
        ticket.setUpdatedTime(LocalDateTime.now());

        // Save the updated ticket
        ticketRepository.save(ticket);
    }


    @Transactional
    private void assignExternalTicket(Long vendorId, Long ticketId,String productCode) throws Exception {
        AssignedTickets assignedTicket = new AssignedTickets();
        Vendor vendor = vendorRepository.findById(vendorId)
                .orElseThrow(() -> new EntityNotFoundException("Vendor not found"));

        try {
            VendorApiConfig vendorApiConfig =vendorApiConfigRepository.findByVendorId(vendorId)
                    .orElseThrow(() -> new EntityNotFoundException("Vendor not found"));

            String vendorApiEndpoint=vendorApiConfig.getEndPointUrl();
            String userName=vendorApiConfig.getUsername();
            String password=vendorApiConfig.getPassword();
            boolean isPushed = vendorApiService.pushTicketToVendorApi(vendorApiEndpoint, ticketId, userName,password );

            if (isPushed) {
                logger.info("Ticket ID {} successfully pushed to Vendor API for Vendor ID {}.", ticketId, vendorId);
                String emailId  = vendorRepository.findPrimaryEmailById(vendorId);
                EmailData data = new EmailData();
                data.setEmailId(emailId);
                data.setSubject("Ticket Assignment");
                data.setMessage("Ticket number: " + ticketId + " has been assigned to you.");
                emailService.sendEmail(data);

                // Send push notification to the vendor
                sendVendorAssignmentNotification(vendorId, ticketId);

            }else {
                logger.error("Failed to push ticket data to Vendor API for Vendor ID {}", vendorId);
                throw new Exception("Failed to push ticket data to Vendor API.");
            }
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid vendor ID or endpoint not found for Vendor ID {}: {}", vendorId, e.getMessage());
        } catch (Exception e) {
            logger.error("Error pushing ticket data for Vendor ID {}: {}", vendorId, e.getMessage());
        }

    }

    //notification work
    private void sendVendorAssignmentNotification(Long vendorId, Long ticketId) {
        String notificationTitle = "Ticket Assigned";
        String notificationMessage = "Ticket number: " + ticketId + " has been assigned to you.";

        NotificationDTO notificationDTO = new NotificationDTO(
                notificationTitle,
                notificationMessage,
                "vendor/ticket/details/" + ticketId,  // Link to vendor ticket details
                String.valueOf(vendorId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        //vendor ===sendNotificationToVendor

        notificationService.sendNotificationToEmployee(
                vendorId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "vendor/ticket/details/" + ticketId, // Notification type or link
                String.valueOf(vendorId)
        );
    }


    @Override
	public String respondAndCloseInternalTicket(Long ticketId, String responseRemark, String status,boolean notifyCreator, MultipartFile[] files,boolean escalate,HttpServletRequest httpRequest) throws Exception {

        logger.info("Processing ticket with ID: {}", ticketId);
        logger.info("Response remark: {}", responseRemark);
        logger.info("Received status: {}", status);

        // Find the ticket by ID
        Ticket ticket = ticketRepository.findById(ticketId)
                .orElseThrow(() -> new EntityNotFoundException("Ticket not found"));

        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        // Define who can close the ticket
        boolean isCreator = ticket.getCreatedBy().equals(loggedInEmpId);
        boolean isAssignedTo = ticket.getAssigned_to() != null && ticket.getAssigned_to().equals(loggedInEmpId);
        boolean isAssignedBy = ticket.getAssigned_by() != null && ticket.getAssigned_by().equals(loggedInEmpId);
        boolean isDepartmentUser = ticket.getDepartmentId() != null && departmentService.isDepartmentUser(loggedInEmpId, ticket.getDepartmentId());

        // Condition 1: Close the ticket
        if ("close".equalsIgnoreCase(status)) {
            if (isCreator || isAssignedTo || isAssignedBy || isDepartmentUser) {
                // Case 1.1: Creator closes the ticket permanently
                if (isCreator) {
                    StringBuilder filePath = new StringBuilder();
                    if (files != null && files.length > 0) {
                        for (MultipartFile file : files) {
                            if (file != null && !file.isEmpty()) {
                              //  String savedFilePath = saveUploadedFile(file, String.valueOf(loggedInEmpId));
                             //   filePath += savedFilePath + ";";
                                String fileName = file.getOriginalFilename();
                                String uniqueFileName = "assignTicket/" + loggedInEmpId + "/" + System.currentTimeMillis() + "_" + fileName;

                                // Upload the file to S3
                                s3Service.uploadFile(file, uniqueFileName);

                                // Append the file path to the StringBuilder
                                if (filePath.length() > 0) {
                                    filePath.append(";");
                                }
                                filePath.append(uniqueFileName);

                            }
                        }
                    }
                    String filePathString = filePath.length() > 0 ? filePath.toString() : null;
                      //added  new logic if true than send notification and email also .
                        if (notifyCreator) {
                            Long creatorEmpId = ticket.getCreatedBy();
                            Employee creatorEmployee = employeeRepository.findById(creatorEmpId)
                                    .orElseThrow(() -> new EntityNotFoundException("Creator not found"));
                            sendEmailNotification(creatorEmployee.getEmail(), "Internal Ticket Closure",
                                    "Ticket number: " + ticket.getTicketId() + " has been closed internally.");

                            // Send text notification
                            sendTextNotification(creatorEmpId,
                                    "Ticket Closed",
                                    "Ticket number: " + ticket.getTicketId() + " has been closed internally.",
                                    loggedInEmpId,
                                    ticket);

                        }
                    closeTicketAndNotifyAssigned(ticket, responseRemark, loggedInEmpId,filePathString);
                  //  return "Ticket closed permanently.";
                    return "Ticket resolved successfully.";
                }

                // Case 1.2: Department user, assigned-to, or assigned-by user closes ticket internally
                closeTicketInternally(ticket, responseRemark, loggedInEmpId, isCreator);
                return "Ticket resolved internally.";
            } else {
                throw new AccessDeniedException("Only the assigned-to, assigned-by, creator, or department users can close the ticket.");
            }
        }
        // Condition 2: Update ticket's latest message and log if the status is open or hold
        else if ("open".equalsIgnoreCase(status) || "hold".equalsIgnoreCase(status)) {
            ticket.setLatest_remark(responseRemark);
            ticket.setStatus(status);
            ticket.setUpdatedBy(loggedInEmpId);
            ticket.setUpdatedTime(LocalDateTime.now());
            ticketRepository.save(ticket);


            if (notifyCreator) {
                notifyCreator(ticket, responseRemark,loggedInEmpId);
            }
      // handle file
            StringBuilder filePath = new StringBuilder();
            if (files != null && files.length > 0) {
                for (MultipartFile file : files) {
                    if (file != null && !file.isEmpty()) {
                      //  String savedFilePath = saveUploadedFile(file, String.valueOf(loggedInEmpId));
                      //  filePath += savedFilePath + ";";
                        String fileName = file.getOriginalFilename();
                        String uniqueFileName = "assignTicket/" + loggedInEmpId + "/" + System.currentTimeMillis() + "_" + fileName;

                        // Upload the file to S3
                        s3Service.uploadFile(file, uniqueFileName);

                        // Append the file path to the StringBuilder
                        if (!filePath.isEmpty()) {
                            filePath.append(";");
                        }
                        filePath.append(uniqueFileName);


                    }
                }
            }
            logTicketStatusUpdate(ticket, responseRemark, loggedInEmpId, String.valueOf(filePath));
          //  logTicketStatusUpdate(ticket, responseRemark, loggedInEmpId,!filePath.isEmpty() ? filePath.toString() : null);
            return "Ticket status updated with latest message and log entry created.";
        }

        //if status is not coming
        // Condition 3: Status not provided - Update latest remark, create log, and notify assigned users
        else if (status == null || status.isEmpty()) {
            ticket.setLatest_remark(responseRemark);
            ticket.setUpdatedBy(loggedInEmpId);
            ticket.setUpdatedTime(LocalDateTime.now());
            ticketRepository.save(ticket);

         String type=ticket.getCurrent_type();

            // Create new entry in t_assigned_tickets table
            AssignedTickets assignedTicket = new AssignedTickets();
            assignedTicket.setTicket(ticket);
            assignedTicket.setEmployee(null);
            assignedTicket.setType(type);
            assignedTicket.setReferenceId(ticketId);
            assignedTicket.setCreatedBy(loggedInEmpId);
            assignedTicket.setCreatedAt(LocalDateTime.now());
            assignedTicket.setUpdatedBy(loggedInEmpId);
            assignedTicket.setUpdatedAt(LocalDateTime.now());
            assignedTicket.setRemark(responseRemark);
            assignedTicket.setUpdatedRemark(responseRemark);
            assignedTicket.setPushRemark(false);

            //file upload
            StringBuilder filePath = new StringBuilder();
            if (files != null && files.length > 0) {
                // Validate and save the uploaded file if present
                Arrays.asList(files).stream().forEach(file->{
                    if (file != null && !file.isEmpty()) {
                        // Save the file
                        // filePath += saveUploadedFile(file, String.valueOf(loggedInEmpId)) + ";";
                        //  ticket.setFilePath(filePath);  // Save the file path in the database
                        String fileName = file.getOriginalFilename();
                        String uniqueFileName = "assignTicket/" + loggedInEmpId + "/" + System.currentTimeMillis() + "_" + fileName;

                        // Upload the file to S3
                        s3Service.uploadFile(file, uniqueFileName);

                        // Append the file path to the StringBuilder
                        if (!filePath.isEmpty()) {
                            filePath.append(";");
                        }
                        filePath.append(uniqueFileName);

                    }
                });

            }
           assignedTicket.setFiles(!filePath.isEmpty() ? filePath.toString() : null);
           assignedTicketRepository.save(assignedTicket);

            // Send email notifications to assigned_by and assigned_to
            if (ticket.getAssigned_by() != null) {
                Employee assignedByEmployee = employeeRepository.findById(ticket.getAssigned_by())
                        .orElseThrow(() -> new EntityNotFoundException("Assigned by employee not found"));
                sendEmailNotification(assignedByEmployee.getEmail(), "Ticket Update Notification",
                        "Ticket #" + ticket.getTicketId() + " has received a new update.");
               //send notification
                sendTextNotification(assignedByEmployee.getEmpId(),
                        "Ticket Update",
                        "Ticket #" + ticket.getTicketId() + " has received a new update.",
                        loggedInEmpId,
                        ticket);
            }

            if (ticket.getAssigned_to() != null) {
                Employee assignedToEmployee = employeeRepository.findById(ticket.getAssigned_to())
                        .orElseThrow(() -> new EntityNotFoundException("Assigned to employee not found"));
                sendEmailNotification(assignedToEmployee.getEmail(), "Ticket Update Notification",
                        "Ticket #" + ticket.getTicketId() + " has received a new update.");
           //send notification
                sendTextNotification(assignedToEmployee.getEmpId(),
                        "Ticket Update",
                        "Ticket #" + ticket.getTicketId() + " has received a new update.",
                        loggedInEmpId,
                        ticket);

            }

            return "Ticket updated with latest remark.";
        }

        //reopen case

        // Condition: Reopen the ticket
        if ("reopen".equalsIgnoreCase(status)) {
            if (isCreator || isAssignedTo || isAssignedBy || isDepartmentUser) {
                ticket.setStatus("Reopen");
                ticket.setLatest_remark(responseRemark);
                ticket.setUpdatedBy(loggedInEmpId);
                ticket.setUpdatedTime(LocalDateTime.now());
                ticketRepository.save(ticket);

                // Handle file upload if present
                StringBuilder filePath = new StringBuilder();
                if (files != null && files.length > 0) {
                    for (MultipartFile file : files) {
                        if (file != null && !file.isEmpty()) {
                            String fileName = file.getOriginalFilename();
                            String uniqueFileName = "assignTicket/" + loggedInEmpId + "/" + System.currentTimeMillis() + "_" + fileName;

                            // Upload the file to S3
                            s3Service.uploadFile(file, uniqueFileName);

                            // Append the file path to the StringBuilder
                            if (filePath.length() > 0) {
                                filePath.append(";");
                            }
                            filePath.append(uniqueFileName);
                        }
                    }
                }
                logTicketStatusUpdate(ticket, responseRemark, loggedInEmpId, String.valueOf(filePath));

                sendNotificationAndEmailIfStatusIsReopen(ticket, responseRemark, loggedInEmpId);

                if(escalate){
                    TicketEscalationRequestDTO escalationRequest = new TicketEscalationRequestDTO();
                    escalationRequest.setTicketId(ticket.getTicketId());
                    escalationRequest.setEscalationRemark(responseRemark);

                    escalateTicket(escalationRequest, httpRequest);
                }

                // Notify assigned users and creator
                if (notifyCreator) {
                    notifyCreator(ticket, "Ticket reopened: " + responseRemark, loggedInEmpId);
                }

                return "Ticket successfully reopened.";
            } else {
                throw new AccessDeniedException("Only the assigned-to, assigned-by, creator, or department users can reopen the ticket.");
            }
        }
        return "Invalid status provided.";
    }
    
    /**
     * 
     * @param ticket
     * @param responseRemark
     * @param loggedInEmpId
     */
    public void sendNotificationAndEmailIfStatusIsReopen(Ticket ticket, String responseRemark, Long loggedInEmpId){
            Long ticketId= ticket.getTicketId();
            Long assignedToEmpId = ticket.getAssigned_to();
            Long assignedByEmpId = ticket.getAssigned_by();
            Long createdByEmpId=ticket.getCreatedBy();


            String notificationMessage = "Ticket number: " + ticketId + " has been reopened.";
            String emailSubject = "Ticket Reopen Notification";

            // Notify the assigned employee
            if (assignedToEmpId != null) {
                Employee assignedEmployee = employeeRepository.findById(assignedToEmpId)
                        .orElseThrow(() -> new EntityNotFoundException("Assigned employee not found"));
                sendEmailNotification(assignedEmployee.getEmail(), emailSubject, notificationMessage);
                sendTextNotification(assignedToEmpId, "Ticket Reopen Notification", notificationMessage, loggedInEmpId, ticket);
            }

            // Notify the employee who assigned the ticket or the creator if they are the same
            if (assignedByEmpId != null && createdByEmpId != null && assignedByEmpId.equals(createdByEmpId)) {
                Employee createdByEmployee = employeeRepository.findById(createdByEmpId)
                        .orElseThrow(() -> new EntityNotFoundException("Creator employee not found"));
                sendEmailNotification(createdByEmployee.getEmail(), emailSubject, notificationMessage);
                sendTextNotification(createdByEmpId, "Ticket Reopen Notification", notificationMessage, loggedInEmpId, ticket);
            } else {
                // Notify the employee who assigned the ticket
                if (assignedByEmpId != null) {
                    Employee assignedByEmployee = employeeRepository.findById(assignedByEmpId)
                            .orElseThrow(() -> new EntityNotFoundException("Assigned by employee not found"));
                    sendEmailNotification(assignedByEmployee.getEmail(), emailSubject, notificationMessage);
                    sendTextNotification(assignedByEmpId, "Ticket Reopen Notification", notificationMessage, loggedInEmpId, ticket);
                }

                // Notify the creator of the ticket
                if (createdByEmpId != null) {
                    Employee createdByEmployee = employeeRepository.findById(createdByEmpId)
                            .orElseThrow(() -> new EntityNotFoundException("Creator employee not found"));
                    sendEmailNotification(createdByEmployee.getEmail(), emailSubject, notificationMessage);
                    sendTextNotification(createdByEmpId, "Ticket Reopen Notification", notificationMessage, loggedInEmpId, ticket);
                }
            }

        }


    //upload file name

    public String saveUploadedFile(MultipartFile file, String empId) throws IOException {
        // Create the directory if it doesn't exist
        Path folderPath = Paths.get(BASE_UPLOAD_DIRECTORY, empId);  // Using comma instead of string concatenation for better path management
        if (!Files.exists(folderPath)) {
            Files.createDirectories(folderPath);
        }

        // Save the file
        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.isEmpty()) {
            throw new IllegalArgumentException("File name cannot be empty.");
        }

        Path filePath = folderPath.resolve(fileName);
        Files.write(filePath, file.getBytes());

        // Return the file URL or path
        return "/api/v1/tickets/files/" + empId + "/" + fileName;
    }

    private void closeTicketAndNotifyAssigned(Ticket ticket, String responseRemark, Long loggedInEmpId,String filePath) {
        ticket.setStatus("close");
        ticket.setInternal_ticket_status("close");
        ticket.setUpdatedBy(loggedInEmpId);
        ticket.setUpdatedTime(LocalDateTime.now());
        ticket.setLatest_remark(responseRemark);
        ticketRepository.save(ticket);

        //new entry in log table than notify to that person
        logTicketStatusUpdate(ticket, responseRemark, loggedInEmpId,filePath);
        Long assignedToEmpId = ticket.getAssigned_to();
        if (assignedToEmpId != null) {
            Employee assignedEmployee = employeeRepository.findById(assignedToEmpId)
                    .orElseThrow(() -> new EntityNotFoundException("Assigned employee not found"));
            sendEmailNotification(assignedEmployee.getEmail(), "Ticket Closed Notification",
                    "Ticket number: " + ticket.getTicketId() + " has been closed by the creator.");

            // Send the text notification
            sendTextNotification(assignedToEmpId, // Send to assigned employee's ID
                    "Ticket Closed Notification",
                    "Ticket number: " + ticket.getTicketId() + " has been closed by the creator.",
                    loggedInEmpId,
                    ticket
            );
        }
    }

    private void closeTicketInternally(Ticket ticket, String responseRemark, Long loggedInEmpId, boolean notifyCreator) {
       // ticket.setStatus("internally_closed");
        ticket.setInternal_ticket_status("close");
        ticket.setUpdatedBy(loggedInEmpId);
        ticket.setUpdatedTime(LocalDateTime.now());
        ticket.setLatest_remark(responseRemark);
        ticketRepository.save(ticket);

        Long assignedToEmpId = ticket.getAssigned_to();
        Long assignedByEmpId = ticket.getAssigned_by();

        if (assignedToEmpId != null) {
            Employee assignedToEmployee = employeeRepository.findById(assignedToEmpId)
                    .orElseThrow(() -> new EntityNotFoundException("Assigned-to employee not found"));
            sendEmailNotification(assignedToEmployee.getEmail(), "Internal Ticket Closure",
                    "Ticket number: " + ticket.getTicketId() + " has been closed internally.");

            // Send text notification
            sendTextNotification(assignedToEmpId,
                    "Ticket Closed",
                    "Ticket number: " + ticket.getTicketId() + " has been closed internally.",
                    loggedInEmpId,
                    ticket);
        }

        if (assignedByEmpId != null) {
            Employee assignedByEmployee = employeeRepository.findById(assignedByEmpId)
                    .orElseThrow(() -> new EntityNotFoundException("Assigned-by employee not found"));
            sendEmailNotification(assignedByEmployee.getEmail(), "Internal Ticket Closure",
                    "Ticket number: " + ticket.getTicketId() + " has been closed internally.");
            // Send text notification
            sendTextNotification(assignedByEmpId,
                    "Ticket Closed",
                    "Ticket number: " + ticket.getTicketId() + " has been closed internally.",
                    loggedInEmpId,
                    ticket);
        }

        if (notifyCreator) {
            Long creatorEmpId = ticket.getCreatedBy();
            Employee creatorEmployee = employeeRepository.findById(creatorEmpId)
                    .orElseThrow(() -> new EntityNotFoundException("Creator not found"));
            sendEmailNotification(creatorEmployee.getEmail(), "Internal Ticket Closure",
                    "Ticket number: " + ticket.getTicketId() + " has been closed internally.");

            // Send text notification
            sendTextNotification(creatorEmpId,
                    "Ticket Closed",
                    "Ticket number: " + ticket.getTicketId() + " has been closed internally.",
                    loggedInEmpId,
                    ticket);

        }
    }

    private void logTicketStatusUpdate(Ticket ticket, String responseRemark, Long updatedBy,String files) {
        AssignedTickets logEntry = new AssignedTickets();
        logEntry.setTicket(ticket);
        Employee employee = employeeRepository.findById(updatedBy)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for emp_id: " + updatedBy));

        logEntry.setEmployee(null);
        logEntry.setRemark(responseRemark);
        String type1=ticket.getCurrent_type();
        logEntry.setType(type1);
        logEntry.setReferenceId(null); // No reference ID needed for logs
        logEntry.setCreatedBy(updatedBy);
        logEntry.setCreatedAt(LocalDateTime.now());

        //file handle
        logEntry.setFiles((files != null && !files.isEmpty()) ? files : null);

        assignedTicketRepository.save(logEntry);
    }

    private void sendEmailNotification(String email, String subject, String message) {
        EmailData emailData = new EmailData();
        emailData.setEmailId(email);
        emailData.setSubject(subject);
        emailData.setMessage(message);
        emailService.sendEmail(emailData);

        logger.info("Email notification sent to: {}", email);
    }

    private void notifyCreator(Ticket ticket, String message,Long loggedInEmpId) {
        Long creatorEmpId = ticket.getCreatedBy();
        Employee creator = employeeRepository.findById(creatorEmpId)
                .orElseThrow(() -> new EntityNotFoundException("Creator not found"));
        sendEmailNotification(creator.getEmail(), "Ticket Update",
                "Ticket number: " + ticket.getTicketId() + " has been updated: " + message);

        sendTextNotification(creatorEmpId,
                "Ticket Update",
                "Ticket number: " + ticket.getTicketId() + " has been updated: " + message,
                loggedInEmpId, // assuming `updatedBy` is the employee performing the update
                ticket);


    }



    // Method to send text notification
    private void sendTextNotification(Long employeeId, String title, String body, Long loggedInEmpId, Ticket ticket) {
        String ticketDetailsUrl = "ticket/details/" + ticket.getTicketId(); // Link to ticket details page (adjust as needed)

        // Create the NotificationDTO object
        NotificationDTO notificationDTO = new NotificationDTO(
                title,
                body,
                ticketDetailsUrl,
                String.valueOf(loggedInEmpId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        // Send the notification to the employee
        notificationService.sendNotificationToEmployee(
                employeeId,  // Send to the employee's ID
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                ticketDetailsUrl,
                String.valueOf(loggedInEmpId)
        );
    }




    //close ticket

    @Override
	public void closeTicketByHR(Long ticketId, String finalRemark) throws Exception {
        List<AssignedTickets> assignedTickets = assignedTicketRepository.findByTicketId(ticketId);

        if (assignedTickets.isEmpty()) {
            throw new EntityNotFoundException("No assigned tickets found for ticketId " + ticketId);
        }

        Ticket ticket = ticketRepository.findById(ticketId)
                .orElseThrow(() -> new EntityNotFoundException("Ticket not found"));

        ticket.setStatus("close");
        ticketRepository.save(ticket);

        String emailId = null;
        for (AssignedTickets assignedTicket : assignedTickets) {
            assignedTicket.setRemark(finalRemark);
            assignedTicket.setUpdatedAt(LocalDateTime.now());
            assignedTicket.setUpdatedBy(tokenService.getEmployeeIdFromToken());

            // Save each assigned ticket with the updated details
            assignedTicketRepository.save(assignedTicket);
            if (emailId == null && assignedTicket.getEmployee() != null) {
                emailId = assignedTicket.getEmployee().getEmail();
            }
        }
        if (emailId != null) {
            EmailData data = new EmailData();
            data.setEmailId(emailId);
            data.setSubject("Ticket Closed");
            data.setMessage("Ticket number: " + ticketId + " has been closed with final remark: " + finalRemark);

            emailService.sendEmail(data);
        } else {
            throw new EntityNotFoundException("No valid email found for assigned employees.");
        }
    }

    //ticket esclation work

    public void escalateTicket(TicketEscalationRequestDTO request, HttpServletRequest request1) {

        try{ //url
            String frontendUrl = (String) request1.getAttribute("frontendUrl");

            // Construct the reset URL with frontend URL, falling back to server URL if needed
            String resetUrl;
            if (frontendUrl != null) {
                resetUrl = frontendUrl + "ticket/details/";
            } else {
                resetUrl = request1.getRequestURL().toString().replace(request1.getRequestURI(), "") + "ticket/details/";
            }
            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

            Ticket ticket = ticketRepository.findById(request.getTicketId())
                    .orElseThrow(() -> new EntityNotFoundException("Ticket not found with ID: " + request.getTicketId()));

            String type1=ticket.getCurrent_type();
            System.out.println("type ID: " +type1);

            if(type1==null || type1.isEmpty()){
                throw new IllegalArgumentException("Ticket type is not available.");
            }


            // Validate escalation conditions
            if (ticket.getStatus().equalsIgnoreCase("close")) {
                throw new IllegalArgumentException("Cannot escalate a closed ticket.");
            }

            //ticket table update
            ticket.setLatest_remark(request.getEscalationRemark());
            ticket.setInternal_ticket_status("assigned");
            Ticket saveTicket= ticketRepository.save(ticket);

            //creating log
            AssignedTickets logEntry = new AssignedTickets();
            logEntry.setTicket(ticket);
            logEntry.setEmployee(null);
            logEntry.setRemark(request.getEscalationRemark());

            logEntry.setType(type1);
            logEntry.setReferenceId(null);
            logEntry.setCreatedBy(loggedInEmpId);
            logEntry.setCreatedAt(LocalDateTime.now());
            assignedTicketRepository.save(logEntry);
            //change
            // Check if this is the first escalation or subsequent escalations
            if (ticket.getLastEscalationTime() == null) {
                //ticket update
                ticket.setLastEscalationTime(LocalDateTime.now());
                Ticket saveTicket1= ticketRepository.save(ticket);

                // First escalation, send notification and email
                sendEscalationNotificationAndEmail(ticket, request, loggedInEmpId,resetUrl);
            } else {
                // Get the ticket category ID
                Long ticketCategoryId = Long.valueOf(ticket.getCategory());
                // Fetch the ticket category from the database
                Optional<TicketCategoryMaster> ticketCategoryMaster = ticketCategoryMasterRepository.findById(ticketCategoryId);

                // Check if the ticket category exists
                if (ticketCategoryMaster.isPresent()) {

                    BigDecimal etaValueInBigDecimal = ticketCategoryMaster.get().getEta();
                    double etaValueInHours = etaValueInBigDecimal.doubleValue();
                    // Convert ETA from hours to minutes
                    long etaValueInMinutes = (long) (etaValueInHours * 60);
                    // Calculate the allowed escalation time by adding the ETA to the last escalation time
                    LocalDateTime allowedEscalationTime = ticket.getLastEscalationTime().plusMinutes(etaValueInMinutes);

                    // Check if the current time is after the allowed escalation time
                    if (LocalDateTime.now().isAfter(allowedEscalationTime)) {

                        // Check the number of escalations for the ticket
                        int escalationCount = ticketEscalationRepository.countByTicketId(ticket.getTicketId());

                        //checking the latest entry and finding supervisorId
                        TicketEscalation latestEscalation = getLatestEscalationForTicket(ticket.getTicketId());
                        Long latestEmpId = latestEscalation.getEmpId();
                        System.out.println("Latest escalation details: " + latestEscalation);
                        Long upperOfUpperID = employeeRepository.findUpperIdByEmpId(latestEmpId);

                        if(upperOfUpperID != null && upperOfUpperID != 0){
                            sendEscalationNotificationAndEmailSecondTime(upperOfUpperID,ticket, request, loggedInEmpId,resetUrl);
                        }else{
                            sendFinalNotificationAndEmail(ticket,request, latestEmpId,resetUrl) ;
                        }


                    } else {
                        // If escalation is not allowed yet, throw an exception with a message
                        throw new IllegalArgumentException("Escalation is not allowed yet. Please wait until " + allowedEscalationTime);
                    }
                } else {
                    // If the ticket category doesn't exist, throw an exception
                    throw new EntityNotFoundException("Ticket category not found for ID: " + ticketCategoryId);
                }
            }

        }catch (EntityNotFoundException ex) {
            logger.error("Ticket not found", ex);
            throw ex; // Re-throw to be caught in controller
        } catch (IllegalArgumentException ex) {
            logger.error("Invalid argument error", ex);
            throw ex; // Re-throw to be caught in controller
        } catch (Exception ex) {
            logger.error("Unexpected error while escalating ticket", ex);
            throw new RuntimeException("An unexpected error occurred during ticket escalation.", ex);
        }
    }

    public TicketEscalation getLatestEscalationForTicket(Long ticketId) {
        Pageable pageable = PageRequest.of(0, 1); // Fetch only the first result
        Page<TicketEscalation> latestEscalationPage = ticketEscalationRepository.findLatestEscalationByTicketId(ticketId, pageable);

        if (!latestEscalationPage.isEmpty()) {
            TicketEscalation latestEscalation = latestEscalationPage.getContent().get(0);
            logger.info("Latest escalation for ticket ID {}: {}, empId: {}, createdAt: {}",
                    ticketId, latestEscalation, latestEscalation.getEmpId(), latestEscalation.getCreatedAt());
            return latestEscalation;
        } else {
            System.out.println("No escalation found for ticket ID: " + ticketId);
            throw new EntityNotFoundException("No escalation entry found for ticket ID: " + ticketId);
        }
    }

    //update notification logic
    private void sendEscalationNotificationAndEmail(Ticket ticket, TicketEscalationRequestDTO request, Long loggedInEmpId, String resetUrl) {

        Long upperId = employeeRepository.findUpperIdByEmpId(loggedInEmpId);
        // First time escalation: Notify direct supervisor
        if (upperId != null) {
            sendNotificationToSupervisor(upperId, ticket, request, loggedInEmpId,resetUrl);
        }

    }

    private  void sendEscalationNotificationAndEmailSecondTime(Long upperOfUpperID,Ticket ticket, TicketEscalationRequestDTO request, Long loggedInEmpId, String resetUrl){
        logger.info("Sending escalation notification to supervisorOfSupervisor ID: {}", upperOfUpperID);
        // Create and save ticket escalation record
        TicketEscalation ticketEscalation = new TicketEscalation();
        ticketEscalation.setTicketId(ticket.getTicketId());
        ticketEscalation.setEmpId(upperOfUpperID);
        ticketEscalation.setCreatedBy(loggedInEmpId);
        ticketEscalation.setCreatedAt(LocalDateTime.now());
        ticketEscalationRepository.save(ticketEscalation);


        String emailSubject = "Ticket Escalation: " + ticket.getTicketSubject();
       /* String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated.\n\nDetails:\n" +
                "Reason: " + request.getEscalationRemark();*/
        String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated.\n\n" +
                "Details:\n" +
                "Reason: " + request.getEscalationRemark() + "\n\n" +
                "You can view the escalation details by clicking the link below:\n" +
                resetUrl + ticket.getTicketId();

        String emailId = employeeRepository.getReferenceById(upperOfUpperID).getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject(emailSubject);
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        // Sending notification to supervisor
        String notificationTitle = "Ticket Escalated";
        String notificationBody = "Ticket ID " + ticket.getTicketId() + " has been escalated.\n" +
                "Reason: " + request.getEscalationRemark();
        String ticketDetailsUrl = "ticket/details/" + ticket.getTicketId();

        notificationService.sendNotificationToEmployee(
                upperOfUpperID,
                notificationTitle,
                notificationBody,
                ticketDetailsUrl,
                String.valueOf(loggedInEmpId)
        );

    }


    private void sendNotificationToSupervisor(Long supervisorId, Ticket ticket, TicketEscalationRequestDTO request, Long loggedInEmpId,String resetUrl) {
        logger.info("Sending escalation notification to supervisor ID: {}", supervisorId);

        // Create and save ticket escalation record
        TicketEscalation ticketEscalation = new TicketEscalation();
        ticketEscalation.setTicketId(ticket.getTicketId());
        ticketEscalation.setEmpId(supervisorId);
        ticketEscalation.setCreatedBy(loggedInEmpId);
        ticketEscalation.setCreatedAt(LocalDateTime.now());
        ticketEscalationRepository.save(ticketEscalation);

        String emailSubject = "Ticket Escalation: " + ticket.getTicketSubject();
       /* String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated.\n\nDetails:\n" +
                "Reason: " + request.getEscalationRemark();*/
        String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated.\n\n" +
                "Details:\n" +
                "Reason: " + request.getEscalationRemark() + "\n\n" +
                "You can view the escalation details by clicking the link below:\n" +
                resetUrl + ticket.getTicketId();

        String emailId = employeeRepository.getReferenceById(supervisorId).getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject(emailSubject);
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        // Sending notification to supervisor
        String notificationTitle = "Ticket Escalated";
        String notificationBody = "Ticket ID " + ticket.getTicketId() + " has been escalated.\n" +
                "Reason: " + request.getEscalationRemark();
        String ticketDetailsUrl = "ticket/details/" + ticket.getTicketId();

        notificationService.sendNotificationToEmployee(
                supervisorId,
                notificationTitle,
                notificationBody,
                ticketDetailsUrl,
                String.valueOf(loggedInEmpId)
        );
    }

    private void sendFinalNotificationAndEmail(Ticket ticket, TicketEscalationRequestDTO request, Long lastSupervisorId,String resetUrl ) {
        // Final notification and email if no upper supervisor found
        logger.info("No further escalation possible. Sending final notification to supervisor ID: {}", lastSupervisorId);
        System.out.println("inside this final mesage send =========");
        String emailSubject = "Final Escalation Notice: " + ticket.getTicketSubject();
        String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated to its final level.\n\nDetails:\n" +
                "Reason: " + request.getEscalationRemark()+ "\n\n" +
                "You can view the escalation details by clicking the link below:\n" +
                resetUrl + ticket.getTicketId();

        String emailId = employeeRepository.getReferenceById(lastSupervisorId).getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject(emailSubject);
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        // Final notification
        String notificationTitle = "Ticket Escalated - Final Level";
        String notificationBody = "Ticket ID " + ticket.getTicketId() + " has been escalated to the final level.\n" +
                "Reason: " + request.getEscalationRemark();
        String ticketDetailsUrl = "ticket/details/" + ticket.getTicketId();

        notificationService.sendNotificationToEmployee(
                lastSupervisorId,
                notificationTitle,
                notificationBody,
                ticketDetailsUrl,
                String.valueOf(ticket.getCreatedBy())
        );
    }


}
