package com.company.wfm.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
@DependsOn("appConfigurations")
public class WebClientConfig {

	@Value("${api.base.url}")
	private String baseURL;


    @Bean
    public WebClient webClient(WebClient.Builder builder) {
        return builder.baseUrl(baseURL).build();
    }

    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }
}
