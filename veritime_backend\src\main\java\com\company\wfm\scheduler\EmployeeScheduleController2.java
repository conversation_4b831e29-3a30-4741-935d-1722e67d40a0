package com.company.wfm.scheduler;

import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/scheduler")
@CrossOrigin(origins = "*")
public class EmployeeScheduleController2 {

    private static final Logger logger = LoggerFactory.getLogger(EmployeeScheduleController2.class);
    @Autowired
    private EmployeeScheduleService employeeScheduleService;

    @GetMapping("/generateSchedule")
    public String generateSchedule(@RequestParam(required = false, defaultValue = "scheduled") String type) {
        logger.info("Received request to generate schedule with type: {}", type);
        if ("now".equalsIgnoreCase(type)) {
            logger.info("Generating schedule instantly as type is 'now'");
            employeeScheduleService.generateScheduleInstantly();
            return "Schedule generated";
        } else {
            logger.warn("Invalid request received with type: {}", type);
            return "invalid request";
        }
    }

    //with emp code to generate schedule post method
    @PostMapping("/generateSchedule")
    public String generateSchedule(@RequestBody List<String> employeeCodes) {
        logger.info("Received request to generate schedule for employees: {}", employeeCodes);

        if (employeeCodes == null || employeeCodes.isEmpty()) {
            return "No employee codes provided";
        }

        List<Long> employeeIds = employeeScheduleService.getEmployeeIdsByCodes(employeeCodes);
        if (employeeIds.isEmpty()) {
            return "No employees found for provided codes";
        }

        employeeScheduleService.generateScheduleForEmployees(employeeIds);
        return "Schedule generated for selected employees";
    }

}

@Service("employeeSchedulerService")
class EmployeeScheduleService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

//    @Scheduled(cron = "0 * * * * ?")
    @Scheduled(cron = "0 0 0 * * ?")
    public void scheduledGenerateSchedule() {
        LocalDate today = LocalDate.now();
        generateSchedule(today, false);
    }

    //new method to generate schedule of code
    public List<Long> getEmployeeIdsByCodes(List<String> employeeCodes) {
        String sql = "SELECT EMP_ID FROM t_employee WHERE EMP_CODE IN (:codes)";

        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("codes", employeeCodes);

        return namedParameterJdbcTemplate.query(
                sql, parameters,
                (rs, rowNum) -> rs.getLong("EMP_ID")
        );
    }

    public void generateScheduleForEmployees(List<Long> employeeIds) {
        LocalDate today = LocalDate.now();
        List<Employee> employees = fetchEmployeesByIds(employeeIds);
        insertSchedulesForEmployees1(employees, today);
    }

    private List<Employee> fetchEmployeesByIds(List<Long> employeeIds) {
        if (employeeIds.isEmpty()) {
            return Collections.emptyList();
        }

        String sql = "SELECT EMP_ID, DEFAULT_TIME_SLOT_ID, LEAVE_ON_DAYS FROM t_employee WHERE EMP_ID IN (:ids)";

        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("ids", employeeIds);

        return namedParameterJdbcTemplate.query(
                sql, parameters,
                (rs, rowNum) -> new Employee(
                        rs.getLong("EMP_ID"),
                        rs.getLong("DEFAULT_TIME_SLOT_ID"),
                        rs.getString("LEAVE_ON_DAYS")
                )
        );
    }


   /* private void insertSchedulesForEmployees1(List<Employee> employees, LocalDate startDate) {
        LocalDate endDate = startDate.plusDays(30); // Define a proper scheduling period
        String insertSql = "INSERT INTO t_employee_schedule (EMP_ID, [date], actual_shift, created_at, updated_at) " +
                "VALUES (?, ?, ?, ?, ?)";

        List<Object[]> batchArgs = new ArrayList<>();

        for (Employee employee : employees) {
            Set<String> leaveDays = parseLeaveDays(employee.getLeaveOnDays());

            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                if (!leaveDays.contains(currentDate.getDayOfWeek().name().toLowerCase())) {
                    if (!scheduleEntryExists(employee.getEmpId(), currentDate)) {
                        batchArgs.add(new Object[]{
                                employee.getEmpId(),
                                Date.valueOf(currentDate),
                                employee.getDefaultTimeSlotId(),
                                Timestamp.valueOf(LocalDateTime.now()),
                                Timestamp.valueOf(LocalDateTime.now())
                        });
                    }
                }
                currentDate = currentDate.plusDays(1);
            }
        }

        if (!batchArgs.isEmpty()) {
            jdbcTemplate.batchUpdate(insertSql, batchArgs);
        }
    }*/

    private void insertSchedulesForEmployees1(List<Employee> employees, LocalDate startDate) {
        LocalDate endDate = startDate.plusDays(30); // Define a proper scheduling period
        String insertSql = "INSERT INTO t_employee_schedule (EMP_ID, [date], actual_shift, created_at, updated_at) " +
                "VALUES (?, ?, ?, ?, ?)";
        String updateSql = "UPDATE t_employee_schedule SET actual_shift = ?, updated_at = ? WHERE EMP_ID = ? AND [date] = ?";

        List<Object[]> batchInsertArgs = new ArrayList<>();
        List<Object[]> batchUpdateArgs = new ArrayList<>();

        for (Employee employee : employees) {
            Set<String> leaveDays = parseLeaveDays(employee.getLeaveOnDays());

            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                if (!leaveDays.contains(currentDate.getDayOfWeek().name().toLowerCase())) {
                    // Check if the schedule entry exists
                    if (scheduleEntryExists(employee.getEmpId(), currentDate)) {
                        // If it exists, update the schedule
                        batchUpdateArgs.add(new Object[]{
                                employee.getDefaultTimeSlotId(),
                                Timestamp.valueOf(LocalDateTime.now()),
                                employee.getEmpId(),
                                Date.valueOf(currentDate)
                        });
                    } else {
                        // If it doesn't exist, insert a new schedule
                        batchInsertArgs.add(new Object[]{
                                employee.getEmpId(),
                                Date.valueOf(currentDate),
                                employee.getDefaultTimeSlotId(),
                                Timestamp.valueOf(LocalDateTime.now()),
                                Timestamp.valueOf(LocalDateTime.now())
                        });
                    }
                }
                currentDate = currentDate.plusDays(1);
            }
        }

        // Batch update for existing schedules
        if (!batchUpdateArgs.isEmpty()) {
            jdbcTemplate.batchUpdate(updateSql, batchUpdateArgs);
        }

        // Batch insert for new schedules
        if (!batchInsertArgs.isEmpty()) {
            jdbcTemplate.batchUpdate(insertSql, batchInsertArgs);
        }
    }

    //new post method work done

    public void generateScheduleInstantly() {
        LocalDate today = LocalDate.now();
        generateSchedule(today, true);
    }

    public void generateSchedule(LocalDate today, boolean runInstantly) {
        List<ScheduleConfig> scheduleConfigs = fetchActiveScheduleConfigFromDB();

        for (ScheduleConfig config : scheduleConfigs) {
            if (runInstantly || today.getDayOfMonth() == config.getCronTriggerDay()) {
                LocalDate startDate = calculateStartDate(config, today);
                LocalDate endDate = calculateEndDate(config, today);
                processSchedule(startDate, endDate);
            }
        }
    }

    private List<ScheduleConfig> fetchActiveScheduleConfigFromDB() {
        String sql = "SELECT cron_trigger_day, start_day, end_day FROM schedule_config WHERE active = 1 and type='shift'";
        return jdbcTemplate.query(sql, (rs, rowNum) -> {
            int cronTriggerDay = rs.getInt("cron_trigger_day");
            int startDay = rs.getInt("start_day");
            int endDay = rs.getInt("end_day");
            return new ScheduleConfig(cronTriggerDay, startDay, endDay);
        });
    }

    private LocalDate calculateStartDate(ScheduleConfig config, LocalDate today) {
        if (today.getDayOfMonth() >= config.getCronTriggerDay()) {
            return today.plusMonths(1).withDayOfMonth(config.getStartDay());
        }
        return today.withDayOfMonth(config.getStartDay());
    }

    private LocalDate calculateEndDate(ScheduleConfig config, LocalDate today) {
        if (config.getEndDay() == -1) {
            if (today.getDayOfMonth() >= config.getCronTriggerDay()) {
                return today.plusMonths(1).withDayOfMonth(today.plusMonths(1).lengthOfMonth()); // Last day of next month
            } else {
                return today.withDayOfMonth(today.lengthOfMonth());
            }
        }
        if (today.getDayOfMonth() >= config.getCronTriggerDay()) {
            return today.plusMonths(1).withDayOfMonth(config.getEndDay());
        }
        return today.withDayOfMonth(config.getEndDay());
    }

    private void processSchedule(LocalDate startDate, LocalDate endDate) {
        List<Employee> employees = fetchActiveEmployees();
        insertSchedulesForEmployees(employees, startDate, endDate);
    }

    private List<Employee> fetchActiveEmployees() {
        String sql = "SELECT EMP_ID, DEFAULT_TIME_SLOT_ID, LEAVE_ON_DAYS FROM t_employee WHERE IN_SERVICE = 1 and DEFAULT_TIME_SLOT_ID != ''";
        return jdbcTemplate.query(sql, (rs, rowNum) -> {
            long empId = rs.getLong("EMP_ID");
            long defaultTimeSlotId = rs.getLong("DEFAULT_TIME_SLOT_ID");
            String leaveOnDays = rs.getString("LEAVE_ON_DAYS");

            return new Employee(empId, defaultTimeSlotId, leaveOnDays);
        });
    }

   /* private void insertSchedulesForEmployees(List<Employee> employees, LocalDate startDate, LocalDate endDate) {
        String insertSql = "INSERT INTO t_employee_schedule (EMP_ID, [date], actual_shift, created_at, updated_at) " +
                "VALUES (?, ?, ?, ?, ?)";

        List<Object[]> batchArgs = new ArrayList<>();

        for (Employee employee : employees) {
            Set<String> leaveDays = parseLeaveDays(employee.getLeaveOnDays());

            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                if (!leaveDays.contains(currentDate.getDayOfWeek().name().toLowerCase())) {
                    if (!scheduleEntryExists(employee.getEmpId(), currentDate)) {
                        batchArgs.add(new Object[]{
                                employee.getEmpId(),
                                Date.valueOf(currentDate),
                                employee.getDefaultTimeSlotId(),
                                Timestamp.valueOf(LocalDateTime.now()),
                                Timestamp.valueOf(LocalDateTime.now())
                        });
                    }
                }
                currentDate = currentDate.plusDays(1);
            }
        }

        if (!batchArgs.isEmpty()) {
            jdbcTemplate.batchUpdate(insertSql, batchArgs);
        }
    }*/


    private void insertSchedulesForEmployees(List<Employee> employees, LocalDate startDate, LocalDate endDate) {
        String insertSql = "INSERT INTO t_employee_schedule (EMP_ID, [date], actual_shift, created_at, updated_at) " +
                "VALUES (?, ?, ?, ?, ?)";

        String updateSql = "UPDATE t_employee_schedule SET actual_shift = ?, updated_at = ? " +
                "WHERE EMP_ID = ? AND [date] = ?";

        List<Object[]> batchArgsInsert = new ArrayList<>();
        List<Object[]> batchArgsUpdate = new ArrayList<>();

        for (Employee employee : employees) {
            Set<String> leaveDays = parseLeaveDays(employee.getLeaveOnDays());

            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                if (!leaveDays.contains(currentDate.getDayOfWeek().name().toLowerCase())) {
                    if (!scheduleEntryExists(employee.getEmpId(), currentDate)) {
                        // No entry exists, insert a new one
                        batchArgsInsert.add(new Object[]{
                                employee.getEmpId(),
                                Date.valueOf(currentDate),
                                employee.getDefaultTimeSlotId(),
                                Timestamp.valueOf(LocalDateTime.now()),
                                Timestamp.valueOf(LocalDateTime.now())
                        });
                    } else {
                        // Entry exists, update it
                        batchArgsUpdate.add(new Object[]{
                                employee.getDefaultTimeSlotId(),
                                Timestamp.valueOf(LocalDateTime.now()),
                                employee.getEmpId(),
                                Date.valueOf(currentDate)
                        });
                    }
                }
                currentDate = currentDate.plusDays(1);
            }
        }

        // Perform batch insert if there are new schedules to add
        if (!batchArgsInsert.isEmpty()) {
            jdbcTemplate.batchUpdate(insertSql, batchArgsInsert);
        }

        // Perform batch update if there are existing schedules to update
        if (!batchArgsUpdate.isEmpty()) {
            jdbcTemplate.batchUpdate(updateSql, batchArgsUpdate);
        }
    }


    private boolean scheduleEntryExists(long empId, LocalDate date) {
        String checkSql = "SELECT COUNT(*) FROM t_employee_schedule WHERE EMP_ID = ? AND [date] = ?";
        Integer count = jdbcTemplate.queryForObject(checkSql, new Object[]{empId, Date.valueOf(date)}, Integer.class);
        return count != null && count > 0;
    }

    private Set<String> parseLeaveDays(String leaveOnDays) {
        Set<String> leaveDays = new HashSet<>();
        if (leaveOnDays != null && !leaveOnDays.isEmpty()) {
            String[] days = leaveOnDays.split(",");
            for (String day : days) {
                leaveDays.add(day.trim().toLowerCase());
            }
        }
        return leaveDays;
    }

    static class ScheduleConfig {
        private int cronTriggerDay;
        private int startDay;
        private int endDay;

        public ScheduleConfig(int cronTriggerDay, int startDay, int endDay) {
            this.cronTriggerDay = cronTriggerDay;
            this.startDay = startDay;
            this.endDay = endDay;
        }

        public int getCronTriggerDay() {
            return cronTriggerDay;
        }

        public int getStartDay() {
            return startDay;
        }

        public int getEndDay() {
            return endDay;
        }
    }

    static class Employee {
        private long empId;
        private long defaultTimeSlotId;
        private String leaveOnDays;

        public Employee(long empId, long defaultTimeSlotId, String leaveOnDays) {
            this.empId = empId;
            this.defaultTimeSlotId = defaultTimeSlotId;
            this.leaveOnDays = leaveOnDays;
        }

        public long getEmpId() {
            return empId;
        }

        public long getDefaultTimeSlotId() {
            return defaultTimeSlotId;
        }

        public String getLeaveOnDays() {
            return leaveOnDays;
        }
    }

}
