package com.company.wfm.dto;


import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserBranchDepartmentDTO {
    private Long userId;
    private String username;
    private String branchName;
    private String departmentName;


    public UserBranchDepartmentDTO(Long userId, String username, String branchName, String departmentName) {
        this.userId = userId;
        this.username = username;
        this.branchName = branchName;
        this.departmentName = departmentName;
    }
}
