package com.company.wfm.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TicketResponseDTO {

    private Long ticketId;
    private String ticketCode;
    private String ticketSubject;
    private String ticketMessage;
    private String status;
    private String filePath;
    private Long createdBy;
    private Long updatedBy;
    private Long departmentId;
    private Long branchId;
    // New field for category details
    private TicketCategoryResponseDTO categoryDetails;


    public TicketResponseDTO(String message) {
        this.ticketMessage = message; // Store the error message
    }


    public TicketResponseDTO(Object o, Object o1, String s, Object o2, Object o3, Object o4, Object o5, Object o6) {
    }
}
