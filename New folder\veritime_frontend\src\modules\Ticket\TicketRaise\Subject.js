import React, { useRef, useState, useEffect } from "react";
import "../TicketRaise/Subject.css";

const TicketSubject = ({ subjectData, setSubjectData }) => {
  const editorRef = useRef(null);

  const execCommand = (command) => {
    document.execCommand(command, false, null);
  };

  const handleInputChange = (e, type) => {
    if (type === "ticketSubject") {
      const { id, value } = e.target;
      setSubjectData({ ...subjectData, [id]: value });
    } else {
      const message = editorRef.current.innerHTML;
      setSubjectData({ ...subjectData, ticketMessage: message });
    }
  };

  const handleBlur = () => {
    const message = editorRef.current.innerHTML;
    setSubjectData({ ...subjectData, ticketMessage: message });
  };

  useEffect(() => {
    if (editorRef.current && subjectData.ticketMessage !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = subjectData.ticketMessage || "";
    }
  }, [subjectData.ticketMessage]);

  return (
    <div className="container mt-3 custom-container-Subject">
      <form>
        <div className="form-group">
          <label htmlFor="ticketSubject" className="ticket-text-primary">
            Subject <span className="text-danger">*</span>
          </label>
          <input
            type="text"
            className="form-control"
            id="ticketSubject"
            placeholder="Subject"
            value={subjectData.ticketSubject}
            onChange={(e) => handleInputChange(e, "ticketSubject")}
          />
        </div>
        <div className="form-group">
          <label htmlFor="ticketMessage" className="ticket-text-primary">
            Message <span className="text-danger">*</span>
          </label>
          <div className="rich-editor">
            <div className="editor-toolbar">
              <button
                type="button"
                className="btn btn-link"
                onClick={() => execCommand("bold")}
              >
                <b>B</b>
              </button>
              <button
                type="button"
                className="btn btn-link"
                onClick={() => execCommand("italic")}
              >
                <i>I</i>
              </button>
              <button
                type="button"
                className="btn btn-link"
                onClick={() => execCommand("underline")}
              >
                <u>U</u>
              </button>
              <button
                type="button"
                className="btn btn-link"
                onClick={() => execCommand("insertUnorderedList")}
              >
                •
              </button>
              <button
                type="button"
                className="btn btn-link"
                onClick={() =>
                  execCommand("createLink", prompt("Enter a URL:", "http://"))
                }
              >
                <i className="fas fa-link"></i>
              </button>
            </div>
            <div
              className="editor-content"
              contentEditable="true"
              ref={editorRef}
              id="ticketMessage"
              suppressContentEditableWarning={true}
              onInput={(e) => handleInputChange(e, "msg")} 
              onBlur={handleBlur} 
            ></div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TicketSubject;
