import React, { useState, useEffect } from "react";
import Select, { components } from "react-select";
import { Checkbox } from "@material-ui/core";
import { Modal } from "react-bootstrap";

const CustomOption = (props: any) => {
  return (
    <components.Option {...props}>
      <input type="checkbox" checked={props?.isSelected} />
      <label style={{ marginLeft: "10px" }}>{props?.label}</label>
    </components.Option>
  );
};

const BranchDropdown = ({
  branches,
  onChange,
  isMulti = true,
  value,
  width,
}: {
  branches: any[];
  onChange: (selected: any) => void;
  isMulti?: boolean;
  value: any[] | null;
  width?: any;
}) => {
  const [allSelected, setAllSelected] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [optionsWithSelectAll, setOptionsWithSelectAll] = useState<any>([]);

  const toggleModal = () => setShowModal(!showModal);

  const handleChange = (selectedOptions: any) => {
    if (!isMulti) {
      const selectedValue = selectedOptions ? selectedOptions.value : null;
      onChange(selectedValue);
    } else {
      if (selectedOptions.some((option: any) => option.value === "selectAll")) {
        if (allSelected) {
          onChange([]);
          setAllSelected(false);
        } else {
          const allValues = branches.map((branch) => branch.branchId);
          onChange(allValues);
          setAllSelected(true);
        }
      } else {
        const selectedValues = selectedOptions
          ? selectedOptions.map((option: any) => option.value)
          : [];
        onChange(selectedValues);
        setAllSelected(selectedValues.length === branches.length);
      }
    }
  };

  const getSelectedBranches: any = () => {
    if (isMulti) {
      return value
        ? value
            .map((id: string) => {
              const branch = branches.find((b) => b.branchId === id);
              return branch
                ? { label: branch.branchName, value: branch.branchId }
                : null;
            })
            .filter(Boolean)
        : [];
    } else {
      const branch = branches.find((b) => b.branchId === value);
      return branch
        ? { label: branch.branchName, value: branch.branchId }
        : null;
      return branch
        ? { label: branch.branchName, value: branch.branchId }
        : null;
    }
  };
  const MultiValue = (props: any) => {
    const values = props.getValue();
    const isLastVisible = props.index === 1;
    const extraCount = values.length - 1;

    return isLastVisible && extraCount > 0 ? (
      <div onClick={toggleModal} style={{ cursor: "pointer", color: "grey" }}>
        +{extraCount}
      </div>
    ) : (
      props.index < 2 && (
        <components.MultiValue {...props}>
          <span>{props.data.label}</span>
        </components.MultiValue>
      )
    );
  };

  useEffect(() => {
    if (isMulti && value && value.length === branches.length) {
      setAllSelected(true);
    } else {
      setAllSelected(false);
    }
    try {
      const optionsWithSelectAll = [
        {
          label: allSelected ? "Deselect All" : "Select All",
          value: "selectAll",
        },
        ...branches?.map((branch) => ({
          label: branch.branchName,
          value: branch.branchId,
        })),
      ];
      setOptionsWithSelectAll(optionsWithSelectAll);
    } catch (error) {}
  }, [value, branches.length, isMulti]);

  const customStyles = {
    menu: (provided: any) => ({
      ...provided,
      overflowY: "auto",
      width: "240px", // Match the width of the select box
      "@media (min-width: 769px) and (max-width: 1024px)": {
        marginLeft: "-90px", // Adjust for tablet views
        width: "200px",
      },
      "@media (max-width: 768px)": {
        marginLeft: "-90px",
        width: "180px",
      },
    }),

    multiValue: (styles: any) => ({
      ...styles,
      backgroundColor: "#e2e2e2",
      borderRadius: "2px",
      marginRight: "5px",
      padding: "3px",
      display: "flex",
      alignItems: "center",
    }),
    menuPortal: (styles: any) => ({
      ...styles,
      zIndex: 9999, // Ensure the dropdown appears above other elements
    }),
    multiValueLabel: (styles: any) => ({
      ...styles,
      whiteSpace: "nowrap",
    }),
    multiValueRemove: (styles: any) => ({
      ...styles,
      cursor: "pointer",
      ":hover": {
        backgroundColor: "#ff6b6b",
        color: "white",
      },
    }),
    control: (provided: any, state: any) => ({
      ...provided,
      minHeight: "40px",
      height: "40px",
      display: "flex",
      width: width || "25%",
      "@media (min-width: 768px) and (max-width: 1024px)": {
        width: width || "80%",
      },
      "@media (min-width: 1024px) and (max-width: 1200px)": {
        width: width || "200px",
      },
      "@media (min-width: 1200px) and (max-width: 1300px)": {
        width: width || "230px",
      },
      "@media (min-width: 1300px) and (max-width: 1600px)": {
        width: width || "240px",
      },
      "@media (min-width: 1600px)": {
        width: width || "260px",
      },
    }),
    indicatorsContainer: (styles: any) => ({
      ...styles,
      marginLeft: "auto",
    }),
    valueContainer: (styles: any) => ({
      ...styles,
      display: "flex",
      flexWrap: "wrap",
      maxHeight: "40px",
      overflowY: value && value.length > 2 ? "auto" : "hidden",
    }),
    option: (styles: any, { isSelected }: any) => ({
      ...styles,
      backgroundColor: isSelected ? "transparent" : "transparent",
      color: "black",
      ":hover": {
        backgroundColor: "transparent",
      },
      fontSize: "14px",
      "@media (min-width: 1025px) and (max-width: 1600px)": {
        fontSize: "12px",
      },
      "@media (min-width: 769px) and (max-width: 1024px)": {
        fontSize: "10px",
      },
      "@media (min-width: 481px) and (max-width: 768px)": {
        fontSize: "8px",
      },
      "@media (max-width: 480px)": {
        fontSize: "6px",
      },
    }),
    placeholder: (styles: any) => ({
      ...styles,
      fontSize: "16px",
      "@media (min-width: 1201px) and (max-width: 1600px)": {
        fontSize: "14px",
      },
      "@media (min-width: 1025px) and (max-width: 1200px)": {
        fontSize: "12px",
      },
      "@media (min-width: 769px) and (max-width: 1024px)": {
        fontSize: "11px",
      },
      "@media (max-width: 768px)": {
        fontSize: "8px",
      },
    }),
  };

  return (
    <>
      <Select
        isMulti={isMulti}
        closeMenuOnSelect={!isMulti}
        isClearable={!isMulti}
        hideSelectedOptions={false}
        components={isMulti ? { Option: CustomOption, MultiValue } : undefined}
        options={optionsWithSelectAll}
        value={getSelectedBranches()}
        onChange={handleChange}
        styles={customStyles}
        placeholder="Select Facility"
        isSearchable={true}
      />
      <Modal show={showModal} onHide={toggleModal}>
        <Modal.Body>
          {getSelectedBranches().map((item: any) => (
            <div
              key={item.value}
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "5px",
              }}
            >
              <span style={{ marginRight: "auto" }}>{item.label}</span>
              <button
                onClick={() =>
                  onChange(value?.filter((val: string) => val !== item.value))
                }
                style={{
                  border: "none",
                  background: "none",
                  color: "red",
                  cursor: "pointer",
                }}
              >
                ✕
              </button>
            </div>
          ))}
        </Modal.Body>
      </Modal>
    </>
  );
};

export default BranchDropdown;
