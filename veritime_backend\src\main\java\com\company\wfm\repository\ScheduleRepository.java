package com.company.wfm.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.entity.Schedule;
import com.company.wfm.entity.TimeSlot;

@Repository
public interface ScheduleRepository extends JpaRepository<Schedule, Long> {
    @Query(value = "SELECT " +
            "s.schedule_id AS schedule_id, " +
            "s.branch_id, " +
            "s.department_id, " +
            "ts.start_time, " +
            "ts.end_time " +
            "FROM t_schedule s " +
            "LEFT JOIN t_time_slot ts ON s.schedule_id = ts.schedule_id",
            nativeQuery = true)
    	List<Map<String, Object>> findRawSchedulesWithTimeSlots();

    	@Query("Select d from Schedule d where d.branch.id = ?1")
    	List<Schedule> getScheduledBasedOnBranch(int branchId);

    	@Query("Select d from Schedule d where d.department.id = ?1")
    	List<Schedule> getScheduledBasedOnDepartment(int departmentId);

    	@Query("Select d from Schedule d where d.branch.id = :branchId and d.department.id = :departmentId")
    	Schedule getScheduledBasedOnBranchDepartment(@Param("branchId") long branchId, @Param("departmentId") long departmentId);

		// Native query to insert Schedule and return the generated ID
		@Modifying
		@Transactional
		@Query(value = "INSERT INTO schedule (branch_id, department_id, created_by, created_at) " +
				"VALUES (:branchId, :departmentId, :createdBy, :createdAt) RETURNING id", nativeQuery = true)
		Long insertSchedule(@Param("branchId") Long branchId,
		@Param("departmentId") Long departmentId,
		@Param("createdBy") Long createdBy,
		@Param("createdAt") LocalDateTime createdAt);


	// Native query to insert TimeSlot and associate with the Schedule
	@Modifying
	@Transactional
	@Query(value = "INSERT INTO time_slot (start_time, end_time, is_default, schedule_id) " +
			"VALUES (:startTime, :endTime, :isDefault, :scheduleId)", nativeQuery = true)
	void insertTimeSlot(@Param("startTime") LocalDateTime startTime,
						@Param("endTime") LocalDateTime endTime,
						@Param("isDefault") Boolean isDefault,
						@Param("scheduleId") Long scheduleId);

	// Native query to fetch TimeSlot details after insertion
	@Query(value = "SELECT id, start_time, end_time, is_active, is_default, schedule_id " +
			"FROM time_slot WHERE schedule_id = :scheduleId", nativeQuery = true)
	List<TimeSlot> findTimeSlotsByScheduleId(@Param("scheduleId") Long scheduleId);
}
