package com.company.wfm.controller;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.ApproveLeaveRequestDTO;
import com.company.wfm.dto.EncryptedRequest;
import com.company.wfm.dto.EncryptedResponse;
import com.company.wfm.dto.LeaveApplicationDTO;
import com.company.wfm.dto.LeaveCountDTO;
import com.company.wfm.dto.LeaveHistoryResponseDTO;
import com.company.wfm.dto.LeaveTypeDTO;
import com.company.wfm.service.AmazonS3Service;
import com.company.wfm.service.impl.LeaveApplicationService;
import com.company.wfm.util.EncryptionUtil;
import com.google.gson.Gson;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/api/v1/leave-applications")
@CrossOrigin(origins = "*")
public class ListingLeaveRequest {

    Logger logger = LoggerFactory.getLogger(ListingLeaveRequest.class);

    @Autowired
    private LeaveApplicationService leaveApplicationService;

    @Autowired
    AmazonS3Service s3Service;

	@Autowired
	private EncryptionUtil encryptionUtil;


    @GetMapping("/types")
    public ResponseEntity<List<LeaveTypeDTO>> getAllLeaveTypes() {
        List<LeaveTypeDTO> leaveTypes = leaveApplicationService.getAllLeaveTypes();
        return ResponseEntity.ok(leaveTypes);
    }

    @GetMapping("/employeetypes")
    public ResponseEntity<List<LeaveTypeDTO>> getAllLeaveTypesByEmpId() {
        List<LeaveTypeDTO> leaveTypes = leaveApplicationService.getAllLeaveTypesByEmpId();
        return ResponseEntity.ok(leaveTypes);
    }
    /**
     *
     * @param type
     * @return
     */
    @GetMapping("/history")
    public ResponseEntity<EncryptedResponse> getLeaveHistory(
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(required = false) LocalDate createdDate,
            @RequestParam(required = false) String status) {
        try {
            // Retrieve leave history based on the request type
            List<LeaveHistoryResponseDTO> leaveHistory;

            if ("history".equalsIgnoreCase(type)) {
                leaveHistory = leaveApplicationService.getSelfLeaveHistory(createdDate,status);
            } else {
                leaveHistory = leaveApplicationService.getAllLeaveHistoryWithEmployeeDetails(createdDate,status);
            }

            // Convert the leave history to JSON
            Gson gson = encryptionUtil.createGson();
            String jsonResponse = gson.toJson(leaveHistory);

            // Encrypt the JSON response
            String encryptedResponse = encryptionUtil.encrypt(jsonResponse,encryptionUtil.generateKey());

            // Return the encrypted response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));
        } catch (Exception e) {
            // Handle errors during encryption or data retrieval
			logger.error("Error encrypting response: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse("Error encrypting response: " + e.getMessage()));
        }
    }

    //old listing method

    // listing modification
   /* @PostMapping("/{leaveHistoryId}/approve")
    public ResponseEntity<?> approveLeave(@PathVariable Long leaveHistoryId, @RequestBody String comment) {
        try {
            leaveApplicationService.approveLeave(leaveHistoryId, comment);
            return ResponseEntity.ok("Leave request approved successfully");
        } catch (EntityNotFoundException | IllegalStateException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }*/

    @PostMapping("/{leaveHistoryId}/approve")
    public ResponseEntity<?> approveLeave(
            @PathVariable Long leaveHistoryId,
            @RequestBody EncryptedRequest encryptedRequest) {
        try {
            // Decrypt the incoming request data
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

            // Parse the decrypted data to extract the comment
            Gson gson = encryptionUtil.createGson();
            ApproveLeaveRequestDTO approveLeaveRequest = gson.fromJson(decryptedData, ApproveLeaveRequestDTO.class);

            // Approve the leave using the leave history ID and the decrypted comment
            leaveApplicationService.approveLeave(leaveHistoryId, approveLeaveRequest.getReason());

            // Prepare the success response
            String responseMessage = "Leave request approved successfully";

            // Encrypt the response message
            String encryptedResponse = encryptionUtil.encrypt(responseMessage,encryptionUtil.generateKey());

            // Return success response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));
        } catch (EntityNotFoundException | IllegalStateException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred: " + e.getMessage());
        }
    }


    @PostMapping("/{leaveHistoryId}/deny")
    public ResponseEntity<?> denyLeave(@PathVariable Long leaveHistoryId, @RequestBody EncryptedRequest encryptedRequest) {
        try {
            // Decrypt the incoming encrypted request data
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

            // Parse the decrypted data to extract the reason
            Gson gson = encryptionUtil.createGson();
            ApproveLeaveRequestDTO approveLeaveRequest = gson.fromJson(decryptedData, ApproveLeaveRequestDTO.class);

            // Call the service method to deny the leave with the extracted reason
            leaveApplicationService.denyLeave(leaveHistoryId, approveLeaveRequest.getReason());

            // Prepare and encrypt the success response
            String responseMessage = "Leave request denied successfully";
            String encryptedResponse = encryptionUtil.encrypt(responseMessage,encryptionUtil.generateKey());

            // Return the encrypted response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));

        } catch (EntityNotFoundException | IllegalStateException e) {
            try {
                // Encrypt the error message
                String encryptedErrorMessage = encryptionUtil.encrypt(e.getMessage(),encryptionUtil.generateKey());
                return ResponseEntity.badRequest().body(new EncryptedResponse(encryptedErrorMessage));
            } catch (Exception encryptionException) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Error while encrypting response: " + encryptionException.getMessage());
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred: " + e.getMessage());
        }
    }


    @PostMapping("/{leaveHistoryId}/pullback")
    public ResponseEntity<?> pullbackLeave(@PathVariable Long leaveHistoryId,@RequestBody LeaveApplicationDTO LeaveApplicationDTO) {
        try {
            String comment=LeaveApplicationDTO.getReason();
            Long empId=LeaveApplicationDTO.getEmpId();
            leaveApplicationService.pullbackLeave(leaveHistoryId,comment,empId);
            return ResponseEntity.ok("Leave request pulled back successfully");
        } catch (EntityNotFoundException | IllegalStateException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
    @GetMapping("/counts")
    public ResponseEntity<LeaveCountDTO> getLeaveRequestCounts() {
        LeaveCountDTO counts = leaveApplicationService.getLeaveRequestCounts();
        return ResponseEntity.ok(counts);
    }
    @GetMapping("/bydate/counts")
    public ResponseEntity<LeaveCountDTO> getLeaveRequestCounts(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        LeaveCountDTO counts = leaveApplicationService.getLeaveRequestCounts(startDate, endDate);
        return ResponseEntity.ok(counts);
    }


    @GetMapping("/download/leaveuploadfile")
    public ResponseEntity<String> downloadFile(@RequestParam("name") String fileName, HttpServletResponse response) {

        try {
            // Set response headers for file download
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // Stream the file directly to the response output stream
            s3Service.downloadFile(fileName, response.getOutputStream());

            return ResponseEntity.ok("File downloaded successfully: " + fileName);
        } catch (IOException e) {
            logger.error("Error while downloading file: {}", fileName, e);
            throw new RuntimeException("Error writing file to response output stream", e);

        }
    }


}
