package com.company.wfm.repository;


import java.time.LocalDate;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.LeaveCountDTO;
import com.company.wfm.dto.LeaveHistoryResponseDTO;
import com.company.wfm.entity.EmployeeLeaveHistory;

@Repository
public interface EmployeeLeaveHistoryRepository extends JpaRepository<EmployeeLeaveHistory, Long> {

  @Query("SELECT new com.company.wfm.dto.LeaveCountDTO( " +
      "COUNT(elh), " +
      "SUM(CASE WHEN elh.approvalStatus = 'APPROVED' THEN 1 ELSE 0 END), " +
      "SUM(CASE WHEN elh.approvalStatus = 'DENIED' THEN 1 ELSE 0 END)) " +
      "FROM EmployeeLeaveHistory elh " +
      "WHERE elh.startDate BETWEEN DATEADD(YEAR, -1, CURRENT_DATE) AND CURRENT_DATE")
  LeaveCountDTO getLeaveRequestCounts();

  @Query("SELECT new com.company.wfm.dto.LeaveCountDTO(" +
      "COUNT(elh), " +
      "SUM(CASE WHEN elh.approvalStatus = 'APPROVED' THEN 1 ELSE 0 END), " +
      "SUM(CASE WHEN elh.approvalStatus = 'REJECTED' THEN 1 ELSE 0 END)) " +
      "FROM EmployeeLeaveHistory elh " +
      "WHERE (:startDate IS NULL OR elh.startDate >= :startDate) " +
      "AND (:endDate IS NULL OR elh.endDate <= :endDate)")
  LeaveCountDTO getLeaveRequestCounts(@Param("startDate") LocalDate startDate,
      @Param("endDate") LocalDate endDate);


  //ritesh

  List<EmployeeLeaveHistory> findByEmpId(Long empId);

  List<EmployeeLeaveHistory> findByEmpIdIn(List<Long> empIds);



  // img employee name
 @Query("SELECT new com.company.wfm.dto.LeaveHistoryResponseDTO( " +
          "elh.id, e.empName, d.name, e.imgUre, elh.startDate, elh.endDate, elh.approvalStatus) " +
          "FROM EmployeeLeaveHistory elh " +
          "JOIN Employee e ON e.empId = elh.empId " +
          "JOIN Designation d ON e.designation.id = d.id")
  List<LeaveHistoryResponseDTO> getLeaveHistoryWithEmployeeDetails();

  /*@Query("SELECT new com.company.wfm.dto.LeaveHistoryResponseDTO( " +
          "elh.empLeaveId, " +
          "e.empName, " +
          "d.name, " +
          "e.imgUrl, " +
          "elh.startDate, " +
          "elh.endDate, " +
          "elh.approvalStatus, " +
          "elh.reason, " +
          "createdByEmp.empName, " +
          "elh.createdTime, " +
          "elh.updatedBy, " +
          "updatedByEmp.empName, " +
          "elh.updatedTime, " +
          "elh.comment) " +
          "FROM EmployeeLeaveHistory elh " +
          "JOIN Employee e ON e.empId = elh.empId " +
          "JOIN Designation d ON e.designation.id = d.id " +
          "LEFT JOIN Employee createdByEmp ON createdByEmp.empId = elh.createdBy " +
          "LEFT JOIN Employee updatedByEmp ON updatedByEmp.empId = elh.updatedBy")
  List<LeaveHistoryResponseDTO> getLeaveHistoryWithEmployeeDetails();*/



    List<EmployeeLeaveHistory> findByEmpIdOrderByIdDesc(Long empId);
    List<EmployeeLeaveHistory> findAllByOrderByIdDesc();

 // List<EmployeeLeaveHistory> findAllByEmpIdIn(List<Long> empIds);
 @Query("SELECT e FROM EmployeeLeaveHistory e WHERE e.empId IN :empIds " +
         "ORDER BY CASE " +
         "WHEN e.approvalStatus = 'pending' THEN 1 " +
         "WHEN e.approvalStatus = 'approved' THEN 2 " +
         "WHEN e.approvalStatus = 'DENIED' THEN 3 " +
         "WHEN e.approvalStatus = 'PULLED_BACK' THEN 4 " +
         "ELSE 5 END, e.id ASC")
 List<EmployeeLeaveHistory> findAllByEmpIdInOrderByIdDesc(@Param("empIds") List<Long> empIds);




   /* @Query("SELECT e FROM EmployeeLeaveHistory e WHERE e.empId = :empId AND FUNCTION('DATE', e.createdTime) = :createdDate AND e.approvalStatus = :status")
    List<EmployeeLeaveHistory> findByEmpIdAndCreatedDateAndApprovalStatus(@Param("empId") Long empId, @Param("createdDate") LocalDate createdDate, @Param("status") String status);*/

    @Query("SELECT e FROM EmployeeLeaveHistory e WHERE e.empId = :empId AND CAST(e.createdTime AS DATE) = :createdDate AND e.approvalStatus = :status")
    List<EmployeeLeaveHistory> findByEmpIdAndCreatedDateAndApprovalStatus(
            @Param("empId") Long empId,
            @Param("createdDate") LocalDate createdDate,
            @Param("status") String status);


    @Query("SELECT e FROM EmployeeLeaveHistory e WHERE e.empId = :empId AND e.approvalStatus = :status")
    List<EmployeeLeaveHistory> findByEmpIdAndApprovalStatus(@Param("empId") Long empId, @Param("status") String status);


    @Query("SELECT e FROM EmployeeLeaveHistory e WHERE e.empId IN :empIds AND FUNCTION('DATE', e.createdTime) = :createdDate AND e.approvalStatus = :status")
    List<EmployeeLeaveHistory> findByEmpIdInAndCreatedTimeAndApprovalStatus(
            @Param("empIds") List<Long> empIds,
            @Param("createdDate") LocalDate createdDate,
            @Param("status") String status);

    @Query("SELECT e FROM EmployeeLeaveHistory e WHERE e.empId IN :empIds AND FUNCTION('DATE', e.createdTime) = :createdDate")
    List<EmployeeLeaveHistory> findByEmpIdInAndCreatedTime(
            @Param("empIds") List<Long> empIds,
            @Param("createdDate") LocalDate createdDate);

    @Query("SELECT e FROM EmployeeLeaveHistory e WHERE e.empId IN :empIds AND e.approvalStatus = :status")
    List<EmployeeLeaveHistory> findByEmpIdInAndApprovalStatus(
            @Param("empIds") List<Long> empIds,
            @Param("status") String status);




}
