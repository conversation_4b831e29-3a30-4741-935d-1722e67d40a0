package com.company.wfm.controller;


import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.DocumentMasterDTO;
import com.company.wfm.dto.ErrorResponse;
import com.company.wfm.entity.DocumentMaster;
import com.company.wfm.service.DocumentMasterService;

@RestController
@RequestMapping("/api/v1/documentMaster")
@CrossOrigin(origins = "*")
public class DocumentMasterController {
    Logger logger = LoggerFactory.getLogger(DocumentMasterController.class);

    @Autowired
    private DocumentMasterService documentMasterService;

    @PostMapping("/save")
    public ResponseEntity<?> saveDocument(@RequestBody DocumentMasterDTO documentMasterDTO) {
        try {
            logger.info("Received request to save document: {}", documentMasterDTO);
            // Save the document
            DocumentMaster savedDocument = documentMasterService.saveDocument(documentMasterDTO);

            logger.info("Document saved successfully with ID: {}", savedDocument.getDocumentId());
            // Return the saved document
            return ResponseEntity.ok(savedDocument);
        } catch (Exception e) {
            // Log the error for debugging
            logger.error("Error occurred while saving document: {}", e.getMessage(), e);
            // Return a meaningful error response
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while saving the document: " + e.getMessage());
        }
    }


    @PostMapping("/list")
    public ResponseEntity<?> getDocuments(@RequestBody Map<String, Integer> requestBody) {
        try {
            int offset = requestBody.getOrDefault("offset", 0); // Default page 0
            int limit = requestBody.getOrDefault("limit", 10); // Default size 10

            // Log the request details
            logger.info("Received request for document list with pagination: offset={} limit={}", offset, limit);


            // Create a Pageable object with sorting by documentId in descending order
            Pageable pageable = PageRequest.of(offset, limit, Sort.by(Sort.Order.desc("documentId")));

            // Call the service to fetch the documents
            Page<DocumentMaster> documentPage = documentMasterService.getDocuments(pageable);

            // Return the paginated result
            return ResponseEntity.ok(documentPage);

        } catch (Exception e) {
            // Log the error with the full stack trace for debugging
            logger.error("Error occurred while retrieving document list: {}", e.getMessage(), e);

            // Return an error response with the error message
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while retrieving the document list: " + e.getMessage());
        }
    }

    //update

    @PutMapping("/update/{documentId}")
    public ResponseEntity<?> updateDocument(@PathVariable Long documentId, @RequestBody DocumentMasterDTO updateDTO) {
        try {
            logger.info("Received request to update document with ID: {}", documentId);
            // Call the service layer to update the document
            DocumentMaster updatedDocument = documentMasterService.updateDocument(documentId, updateDTO);

            // Check if the document was found and updated
            if (updatedDocument == null) {
                logger.warn("Document with ID {} not found", documentId);
                ErrorResponse errorResponse = new ErrorResponse("Document not found with ID: " + documentId);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
            }

            logger.info("Document with ID {} updated successfully", documentId);
            return ResponseEntity.ok(updatedDocument);
        } catch (Exception e) {
            // Log the error for debugging
            logger.error("Error occurred while updating document: {}", e.getMessage(), e);

            // Return an error response with message
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while updating the document: " + e.getMessage());
        }
    }





}
