package com.company.wfm.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.EmployeeHikvisionData;

@Repository
public interface EmployeeHikvisionDataRepository extends JpaRepository<EmployeeHikvisionData, Long> {
	Optional<EmployeeHikvisionData> findByPersonId(String personId);
}
