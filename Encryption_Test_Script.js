// Veritime Encryption Test Script
// Use this in a separate Postman request to test encryption methods

const CryptoJS = require('crypto-js');

// Your credentials
const aesPassword = "uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabqAU=";
const aesSalt = "uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabBA==";

// Test payload
const testPayload = {
    username: "<EMAIL>",
    password: "admin123"
};

const jsonString = JSON.stringify(testPayload);
console.log("Original payload:", jsonString);

// Method 1: Direct AES encryption with base64 password (CryptoJS default)
try {
    const encrypted1 = CryptoJS.AES.encrypt(jsonString, aesPassword).toString();
    console.log("Method 1 - Direct base64 password:");
    console.log("Encrypted:", encrypted1);
    console.log("Length:", encrypted1.length);
    console.log("Starts with 'U2FsdGVkX1':", encrypted1.startsWith('U2FsdGVkX1'));
    
    // Test decryption
    const decrypted1 = CryptoJS.AES.decrypt(encrypted1, aesPassword).toString(CryptoJS.enc.Utf8);
    console.log("Decrypted:", decrypted1);
    console.log("Round trip success:", decrypted1 === jsonString);
    console.log("---");
} catch (error) {
    console.log("Method 1 failed:", error.message);
}

// Method 2: PBKDF2 key derivation
try {
    const passwordWordArray = CryptoJS.enc.Base64.parse(aesPassword);
    const saltWordArray = CryptoJS.enc.Base64.parse(aesSalt);
    
    const key = CryptoJS.PBKDF2(passwordWordArray, saltWordArray, {
        keySize: 256/32,
        iterations: 1000
    });
    
    const encrypted2 = CryptoJS.AES.encrypt(jsonString, key).toString();
    console.log("Method 2 - PBKDF2 key derivation:");
    console.log("Encrypted:", encrypted2);
    console.log("Length:", encrypted2.length);
    console.log("Starts with 'U2FsdGVkX1':", encrypted2.startsWith('U2FsdGVkX1'));
    
    // Test decryption
    const decrypted2 = CryptoJS.AES.decrypt(encrypted2, key).toString(CryptoJS.enc.Utf8);
    console.log("Decrypted:", decrypted2);
    console.log("Round trip success:", decrypted2 === jsonString);
    console.log("---");
} catch (error) {
    console.log("Method 2 failed:", error.message);
}

// Method 3: Using password as UTF8 string
try {
    const passwordUtf8 = CryptoJS.enc.Base64.parse(aesPassword).toString(CryptoJS.enc.Utf8);
    const encrypted3 = CryptoJS.AES.encrypt(jsonString, passwordUtf8).toString();
    console.log("Method 3 - Password as UTF8 string:");
    console.log("Encrypted:", encrypted3);
    console.log("Length:", encrypted3.length);
    console.log("Starts with 'U2FsdGVkX1':", encrypted3.startsWith('U2FsdGVkX1'));
    
    // Test decryption
    const decrypted3 = CryptoJS.AES.decrypt(encrypted3, passwordUtf8).toString(CryptoJS.enc.Utf8);
    console.log("Decrypted:", decrypted3);
    console.log("Round trip success:", decrypted3 === jsonString);
    console.log("---");
} catch (error) {
    console.log("Method 3 failed:", error.message);
}

// Method 4: Manual salt handling (like your expected format)
try {
    // Convert base64 to word arrays
    const passwordWordArray = CryptoJS.enc.Base64.parse(aesPassword);
    const saltWordArray = CryptoJS.enc.Base64.parse(aesSalt);
    
    // Create key from password + salt
    const keyWithSalt = passwordWordArray.concat(saltWordArray);
    
    const encrypted4 = CryptoJS.AES.encrypt(jsonString, keyWithSalt).toString();
    console.log("Method 4 - Manual salt concatenation:");
    console.log("Encrypted:", encrypted4);
    console.log("Length:", encrypted4.length);
    console.log("Starts with 'U2FsdGVkX1':", encrypted4.startsWith('U2FsdGVkX1'));
    console.log("---");
} catch (error) {
    console.log("Method 4 failed:", error.message);
}

// Method 5: Try to match your expected format exactly
try {
    // Your expected format starts with "U2FsdGVkX1" which is "Salted__" in base64
    // This suggests CryptoJS default format with random salt
    
    const encrypted5 = CryptoJS.AES.encrypt(jsonString, aesPassword, {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
        format: CryptoJS.format.OpenSSL
    }).toString();
    
    console.log("Method 5 - OpenSSL format:");
    console.log("Encrypted:", encrypted5);
    console.log("Length:", encrypted5.length);
    console.log("Starts with 'U2FsdGVkX1':", encrypted5.startsWith('U2FsdGVkX1'));
    
    // Test decryption
    const decrypted5 = CryptoJS.AES.decrypt(encrypted5, aesPassword, {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
        format: CryptoJS.format.OpenSSL
    }).toString(CryptoJS.enc.Utf8);
    console.log("Decrypted:", decrypted5);
    console.log("Round trip success:", decrypted5 === jsonString);
    console.log("---");
} catch (error) {
    console.log("Method 5 failed:", error.message);
}

// Compare with your expected output
console.log("=== COMPARISON ===");
console.log("Your expected format:");
console.log("U2FsdGVkX19fy/n2MrqR8I0xCHgETYgbEUK/pZVUvQ/6s8GRnKtY5hhpfkpVyAn3GWlC4UPCPUSO/sP0VISILe4DeWc4H7Q4hgD/n9VcayX5ksHAJBB5R6xINldqd2D3");
console.log("Length:", "U2FsdGVkX19fy/n2MrqR8I0xCHgETYgbEUK/pZVUvQ/6s8GRnKtY5hhpfkpVyAn3GWlC4UPCPUSO/sP0VISILe4DeWc4H7Q4hgD/n9VcayX5ksHAJBB5R6xINldqd2D3".length);

// Test decryption of your expected format
try {
    const yourEncrypted = "U2FsdGVkX19fy/n2MrqR8I0xCHgETYgbEUK/pZVUvQ/6s8GRnKtY5hhpfkpVyAn3GWlC4UPCPUSO/sP0VISILe4DeWc4H7Q4hgD/n9VcayX5ksHAJBB5R6xINldqd2D3";
    
    // Try to decrypt with different methods
    console.log("Trying to decrypt your expected format...");
    
    // Method 1: Direct with base64 password
    try {
        const decrypted = CryptoJS.AES.decrypt(yourEncrypted, aesPassword).toString(CryptoJS.enc.Utf8);
        if (decrypted) {
            console.log("✅ Decrypted with Method 1:", decrypted);
        }
    } catch (e) {
        console.log("❌ Method 1 failed");
    }
    
    // Method 2: With UTF8 password
    try {
        const passwordUtf8 = CryptoJS.enc.Base64.parse(aesPassword).toString(CryptoJS.enc.Utf8);
        const decrypted = CryptoJS.AES.decrypt(yourEncrypted, passwordUtf8).toString(CryptoJS.enc.Utf8);
        if (decrypted) {
            console.log("✅ Decrypted with Method 2:", decrypted);
        }
    } catch (e) {
        console.log("❌ Method 2 failed");
    }
    
} catch (error) {
    console.log("Failed to decrypt your expected format:", error.message);
}

console.log("=== END TEST ===");
