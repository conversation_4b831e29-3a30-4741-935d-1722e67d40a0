package com.company.wfm.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.exception.S3ServiceException;
import com.company.wfm.service.AmazonS3Service;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

@Slf4j
@Service
public class AmazonS3ServiceImpl implements AmazonS3Service {

	@Value("${aws.s3.bucket.name}")
	private String bucketName;

	@Autowired
	private S3Client s3Client;

	@Override
	public void uploadFile(MultipartFile file,String uniqueFileName) {
		try {
			log.info("Uploading file to S3: bucket={}, key={},key={}", bucketName, file.getOriginalFilename(),uniqueFileName);
			s3Client.putObject(PutObjectRequest.builder()
			        .bucket(bucketName)
			        .key(uniqueFileName)
			        .contentType(file.getContentType())
			        .build(),
			RequestBody.fromBytes(file.getBytes()));
			log.info("File uploaded successfully: key={}", file);
		} catch (AwsServiceException | SdkClientException | IOException e) {
			log.error("Error uploading file to S3: {}", file.getOriginalFilename(), e);
			throw new S3ServiceException("AWS S3 error: ", e);
		}

	}

	@Override
    public void downloadFile(String key, OutputStream outputStream) {
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(key)
                .build();
        try {
            s3Client.getObject(getObjectRequest, ResponseTransformer.toOutputStream(outputStream));
        } catch (S3Exception e) {
        	log.error("Error downloading file from S3: {}", key, e);
            throw new S3ServiceException("AWS S3 error: " + e.awsErrorDetails().errorMessage(), e);
        }
    }


	@Override
	public void downloadFileToLocal(String key, String localFilePath) {
		GetObjectRequest getObjectRequest = GetObjectRequest.builder()
				.bucket(bucketName)
				.key(key)
				.build();

		File file = new File(localFilePath, key.substring(key.lastIndexOf("/") + 1));
		try (FileOutputStream fileOutputStream = new FileOutputStream(localFilePath)) {
			// Download the file from S3 and write it to the local file system
			s3Client.getObject(getObjectRequest, ResponseTransformer.toOutputStream(fileOutputStream));
			log.info("File downloaded successfully to {}", localFilePath);
		} catch (S3Exception e) {
			log.error("Error downloading file from S3: {}", key, e);
			throw new S3ServiceException("AWS S3 error: " + e.awsErrorDetails().errorMessage(), e);
		} catch (IOException e) {
			log.error("Error writing file to local path: {}", localFilePath, e);
			throw new RuntimeException("Error writing file locally: " + e.getMessage(), e);
		}
	}

}
