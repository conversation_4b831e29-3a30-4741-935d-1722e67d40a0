
.custom-card-links {
  border-radius: 8px;
  padding: 0 20px;
  max-width: 400px;
  width: 100%;
  background-color: #ffffff;
  transition: all 0.3s ease;
  height:270px;
}

.card-title-quick {
  font-size: 1.25rem;
  font-weight: bold;
  color: #333333;
  margin: 0;
  /* padding-left: 35px; */

}

.quick-link {
  font-size: 1rem; 
  color: #007bff;
  text-decoration: none;
  display: flex;
  /* align-items: center; */
  justify-content :center;
  margin-bottom: -4px;
}

.quick-link:hover {
  text-decoration: underline;
}


@media (max-width: 576px) {
  .custom-card-links {
    padding: 0 15px;
  }

  .card-title-quick {
    font-size: 1rem; 
  }

  .quick-link {
    font-size: 0.875rem; 
  }
}


@media (min-width: 577px) and (max-width: 768px) {
  .custom-card-links {
    padding: 0 18px;
  }

  .card-title-quick {
    font-size: 1.125rem; 
  }

  .quick-link {
    font-size: 1rem; 
  }
}
