package com.company.wfm.service.impl;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.dto.BranchUpdateResponseDTO;
import com.company.wfm.dto.CreateBranchDTO;
import com.company.wfm.dto.FeederHospitalDTO;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.DepartmentBranch;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.FeederHospital;
import com.company.wfm.entity.User;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentBranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.FeederHospitalRepository;
import com.company.wfm.service.BranchService;
import com.company.wfm.service.UserTokenService;

import jakarta.persistence.EntityNotFoundException;

@Service
public class BranchServiceImpl implements BranchService {
    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private UserTokenService userTokenService;

    @Autowired
    private FeederHospitalRepository feederHospitalRepository;

    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    DepartmentBranchRepository departmentBranchRepository;

    @Autowired
    private UserTokenService tokenService;

    @Override
	@Transactional
    public Branch createBranch(CreateBranchDTO branchDTO) {

        validateBranchCode(branchDTO);

        // Find department by ID
        Department department = departmentRepository.findById(branchDTO.getDepartmentId())
                .orElseThrow(() -> new EntityNotFoundException("Department not found"));

        // Fetch the branch head (employee) by EMP_ID
        Employee branchHead = employeeRepository.findById(branchDTO.getBranchHeadId())
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for the given EMP_ID"));

        // Create and save the branch entity
        Branch branch = new Branch();
        branch.setBranchName(branchDTO.getBranchName());
        branch.setBranchCode(branchDTO.getBranchCode());
        branch.setBranchHead(branchHead);  // Set Employee entity
        branch.setBranchHeadName(branchHead.getEmpName());  // Set Employee name separately
        //branch.setDepartment(department);
        //branch.setCompany(department.getCompany());

        return branchRepository.saveAndFlush(branch);
    }

    private void validateBranchCode(CreateBranchDTO branchDTO) {
        // Check if branch code already exists
        if (branchRepository.existsByBranchCode(branchDTO.getBranchCode())) {
            throw new IllegalArgumentException("Branch code already exists");
        }
    }

    @Override
	public void deleteDepartment(Long id) {
        departmentRepository.deleteById(id);
    }
//    public List<Branch> getBranchIdAndName() {
//        List<Object[]> branches = branchRepository.findBranchIdAndName();
//        return branches.stream()
//                .map(branch -> Map.of(
//                        "name", branch.getClass(),
//                        "id", branch.getId()
//                ))
//                .collect(Collectors.toList());
//    }


    @Transactional
    @Override
    public void createBranchAndDepartment(CreateBranchDTO branchDTO) {
        Long empId = tokenService.getEmployeeIdFromToken();
        Employee branchHead = null;

        if(null != branchDTO.getBranchHeadId()) {
            branchHead = employeeRepository.findById(branchDTO.getBranchHeadId())
                    .orElseThrow(() -> new EntityNotFoundException("Employee not found for the given EMP_ID"));
        }


        User employee = userTokenService.getEmployeeFromToken();
       // Long hospitalId = employee.getEmployee().getCompanyId();
        Long hospitalId = branchDTO.getHospitalId();


        if (hospitalId == null) {
            hospitalId = employee.getEmployee().getCompanyId();
        }


        FeederHospital feederHospital = feederHospitalRepository.findById(hospitalId)
                .orElseThrow(() -> new EntityNotFoundException("Hospital not found for the given HOSPITAL_ID"));


        Branch branch = new Branch();
        branch.setBranchName(branchDTO.getBranchName());
        branch.setBranchCode(branchDTO.getBranchCode());
		branch.setBranchHead(null != branchHead ? branchHead : null);
       // branch.setCompany(hospitalId);
        branch.setCompany(feederHospital);
        branch.setBranchHeadName(null != branchHead ? branchHead.getEmpName() : null);
        branch.setBranchType(branchDTO.getBranchType());
        branch.setActive(true);
        branch.setCreatedBy(empId);
        branch.setCreatedAt(LocalDateTime.now());
        branch.setUpdatedAt(null);
        branch.setUpdatedBy(null);
        branch.setCluster(branchDTO.getCluster());
        branch.setLeaveCreditDay(branchDTO.getLeaveCreditDay());
        branch.setTimeZone(branchDTO.getTimeZone());


        Branch savedBranch = branchRepository.saveAndFlush(branch);
        Long branchId = savedBranch.getId();

        for (Long departmentId : branchDTO.getDepartmentIds()) {
            DepartmentBranch departmentBranch = new DepartmentBranch();
            departmentBranch.setBranchId(branchId);
            departmentBranch.setDepartmentId(departmentId);
            departmentBranchRepository.saveAndFlush(departmentBranch);
        }
    }

/*
    @Transactional
    @Override
    public Branch updateBranch(Long id, CreateBranchDTO branchDTO) {
        // Validate branch code
        validateBranchCode(branchDTO);

        // Find existing branch by ID
        Branch existingBranch = branchRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Branch not found"));

        // Update branch fields
        existingBranch.setBranchName(branchDTO.getBranchName());
        existingBranch.setBranchCode(branchDTO.getBranchCode());

        // Fetch the branch head (employee) by EMP_ID
        Employee branchHead = employeeRepository.findById(branchDTO.getBranchHeadId())
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for the given EMP_ID"));

        existingBranch.setBranchHead(branchHead);
        existingBranch.setBranchHeadName(branchHead.getEmpName());

        // Update the department if necessary
        if (branchDTO.getDepartmentId() != null) {
            Department department = departmentRepository.findById(branchDTO.getDepartmentId())
                    .orElseThrow(() -> new EntityNotFoundException("Department not found"));
            //existingBranch.setDepartment(department);
          //  existingBranch.setCompany(department.getCompany());
        }

        return branchRepository.saveAndFlush(existingBranch);
    }
*/

    //new update


    @Transactional
    @Override
    public BranchUpdateResponseDTO updateBranch(Long id, CreateBranchDTO branchDTO) {
        // Find existing branch by ID
        Long empId = tokenService.getEmployeeIdFromToken();
        Branch existingBranch = branchRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Branch not found"));

        // Update basic branch fields
        existingBranch.setBranchName(branchDTO.getBranchName());
        existingBranch.setBranchCode(branchDTO.getBranchCode());
        existingBranch.setBranchType(branchDTO.getBranchType());
        existingBranch.setLeaveCreditDay(branchDTO.getLeaveCreditDay()); // new field add
        existingBranch.setTimeZone(branchDTO.getTimeZone()); //new field add

        // Find and update the branch head (employee)
        Employee branchHead = employeeRepository.findById(branchDTO.getBranchHeadId())
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for the given EMP_ID"));
        existingBranch.setBranchHead(branchHead);
        existingBranch.setBranchHeadName(branchHead.getEmpName());

        Long hospitalId = branchDTO.getHospitalId();  // Get the hospital ID from branchDTO
        if (hospitalId != null) {
            // Fetch FeederHospitalDTO containing id and name
            FeederHospitalDTO feederHospitalDTO = feederHospitalRepository.findFeederHospitalById(hospitalId);

            if (feederHospitalDTO != null) {
                FeederHospital feederHospital = new FeederHospital();
                feederHospital.setId(feederHospitalDTO.getId());  // Set the id
                feederHospital.setName(feederHospitalDTO.getName());  // Set the name

                existingBranch.setCompany(feederHospital);
            } else {
                throw new EntityNotFoundException("Hospital not found for the given HOSPITAL_ID");
            }
        } else {
            throw new IllegalArgumentException("Hospital ID cannot be null");
        }



        // Set the `isActive` status if needed
        existingBranch.setActive(true);
        existingBranch.setCluster(branchDTO.getCluster());
        existingBranch.setUpdatedBy(empId);
        existingBranch.setUpdatedAt(LocalDateTime.now());

        // Save the updated branch
        Branch updatedBranch = branchRepository.saveAndFlush(existingBranch);

        // Handle updating departments: clear old ones and save the new ones
        departmentBranchRepository.deleteByBranchId(id); // Delete old associations
        for (Long departmentId : branchDTO.getDepartmentIds()) {
            DepartmentBranch departmentBranch = new DepartmentBranch();
            departmentBranch.setBranchId(id);
            departmentBranch.setDepartmentId(departmentId);
            departmentBranchRepository.saveAndFlush(departmentBranch);
        }

        String createdByName = employeeRepository.findById(existingBranch.getCreatedBy())
                .map(Employee::getEmpName)
                .orElse("Unknown");
        String updatedByName = employeeRepository.findById(existingBranch.getUpdatedBy())
                .map(Employee::getEmpName)
                .orElse("Unknown");

       // return updatedBranch;

        return new BranchUpdateResponseDTO(
                existingBranch.getId(),
                existingBranch.getBranchName(),
                existingBranch.getBranchCode(),
                existingBranch.getBranchHeadName(),
                existingBranch.getBranchType(),
                existingBranch.getCompany().getId(),
                existingBranch.getCompany().getName(),
                existingBranch.getCreatedAt(),
                existingBranch.getUpdatedAt(),
                existingBranch.getCreatedBy(),
                createdByName,
                existingBranch.getUpdatedBy(),
                updatedByName,
                existingBranch.getIsActive(),
                existingBranch.getCluster(),
                existingBranch.isActive(),
                existingBranch.getLeaveCreditDay(),
                existingBranch.getTimeZone()

        );
    }


    @Override
    public void deleteBranch(Long id) {
        Branch branch = branchRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Branch not found for ID: " + id));

        // Set isActive to false (soft delete)
          branch.setActive(false);
        branchRepository.saveAndFlush(branch);
    }

}