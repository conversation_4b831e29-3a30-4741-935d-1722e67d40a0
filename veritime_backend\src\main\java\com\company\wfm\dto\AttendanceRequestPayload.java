package com.company.wfm.dto;

import java.util.List;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class AttendanceRequestPayload {

	// Body parameters
	private Integer pageIndex; // Page index (Optional)
	private Integer pageSize; // Page size (Optional)
	private String beginTime; // Start time in ISO format (Required)
	private String endTime; // End time in ISO format (Required)
	private String personName; // Person name for fuzzy search (Optional)
	private String personCode; // Employee number for fuzzy search (Optional)
	private List<String> personGroupIds; // List of person group IDs (Optional)
	private String dateFormat; // Date format (Optional)
	private String timeFormat; // Time format (Optional)
	private String durationFormat; // Duration format (Optional)
}
