package com.company.wfm.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateBranchDTO {
    private String branchName;
    private String branchCode;
    private String branchType;
    private String cluster;
    private Long branchHeadId; // EMP_ID of the branch head
    private Long departmentId;
    private Long hospitalId;
    private String hospitalName;
    private Integer leaveCreditDay; // new Field
    private String timeZone; // new Field

    private List<Long> departmentIds;
}
