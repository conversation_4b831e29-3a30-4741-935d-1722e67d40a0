package com.company.wfm.controller;
import java.util.List;

import com.company.wfm.dto.BranchUpdateResponseDTO;
import jakarta.persistence.EntityNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.web.bind.annotation.*;

import com.company.wfm.dto.CreateBranchDTO;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.service.BranchService;

@RestController
@RequestMapping("/api/v1/branches")
@CrossOrigin(origins = "*")
public class BranchController {

    private static final Logger logger = LoggerFactory.getLogger(BranchController.class);
    @Autowired
    private BranchService branchService;
    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private BranchRepository branchRepository;
    @PostMapping("/create")
    public ResponseEntity<?> createBranch(@RequestBody CreateBranchDTO branchDTO) {
        try {
            Branch createdBranch = branchService.createBranch(branchDTO);
            return new ResponseEntity<>(createdBranch, HttpStatus.CREATED);
        }
        catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Specific error occurred: " + e.getMessage());
        }
        catch (Exception e) {
            // Custom exception handling for branch creation errors
            return new ResponseEntity<>("Error creating branch: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }
    @PostMapping("/create-branch-dept")
    public ResponseEntity<String> createBranchDept(@RequestBody CreateBranchDTO branchDTO) {
        try {
            branchService.createBranchAndDepartment(branchDTO);
            return new ResponseEntity<>("Branch and department records created successfully.", HttpStatus.CREATED);
        }
        catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Specific error occurred: " + e.getMessage());
        }
        catch (Exception e) {
            // Custom exception handling for branch creation errors
            return new ResponseEntity<>("Error creating branch: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/department")
    public ResponseEntity<List<Department>> getAllDepartments() {
        List<Department> departments = departmentRepository.findAll();
        return ResponseEntity.ok(departments);
    }
    //Lookup for department
    @GetMapping("/names")
    public ResponseEntity<List<String>> getAllBranchNames() {
        List<String> departmentNames = branchRepository.findAllBranchNames();
        return ResponseEntity.ok(departmentNames);
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateBranch(@PathVariable Long id, @RequestBody CreateBranchDTO branchDTO) {
        try {
            // Update the branch and return BranchUpdateResponseDTO
            BranchUpdateResponseDTO updatedBranch = branchService.updateBranch(id, branchDTO);
            return ResponseEntity.ok(updatedBranch);  // Return updatedBranch if successful
        } catch (EntityNotFoundException e) {
            // Log the exception and return a 404 response
            logger.error("Error: Branch not found", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Branch not found: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            // Log the exception and return a 400 response
            logger.error("Invalid argument", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid argument: " + e.getMessage());
        } catch (Exception e) {
            // Catch all other exceptions
            logger.error("An unexpected error occurred while updating the branch", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred while updating the branch: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> deleteBranch(@PathVariable Long id) {
        try {
            branchService.deleteBranch(id);
            return ResponseEntity.ok("Branch soft deleted successfully.");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Branch not found: " + e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error deleting branch: " + e.getMessage());
        }
    }
}
