"use client";
import React, { useState, useEffect } from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import "./Profiledetails.css";
import Layout from "../../components/Layout";
import { Modal, Button, Form } from "react-bootstrap";

import { colors } from "@constants/colors";
import {
  fileDownload,
  getRequest,
  getRequestWithSecurity,
} from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { appConstants } from "@/constants/appConstants";
import Avatar from "@/components/Avatar";
import { showSuccessAlert2, showErrorAlert } from "@/services/alertService";
import axios from "axios";

const ProfileDetail = () => {
  const [data, setData] = useState<any>({});
  const [imgUrl, setImgUrl] = useState<string>("");
  const [imageErrors, setImageErrors] = useState<any>({});
  const [activeTab, setActiveTab] = useState(0);

  // Get initials from employee's name
  const getInitials = (name: any) => name?.charAt(0)?.toUpperCase() || "U";
  const handleImageError = (index: any) => {
    setImageErrors((prevErrors: any) => ({
      ...prevErrors,
      [index]: true,
    }));
  };

  const handleTabChange = (newValue: number) => setActiveTab(newValue);

  // const handleTabChange = (newValue: number) => {
  //   setActiveTab(newValue);
  //   if (newValue === 4) {
  //     handleOpenResignationModal();
  //   }
  // };

  const fetchEmpDetails = async () => {
    try {
      const id = window.location.search.substring(4);
      let empData;

      if (id) {
        try {
          empData = await getRequestWithSecurity(
            `${API_URLS.EMPLOYEE_LIST}/${atob(id)}`
          );
          console.log(empData, "employeedetail");
          if (Object.keys(empData).length > 0) {
            setData(empData);

            setImgUrl(empData?.imgUre);
            return;
          } else {
            alert("No user found.");
          }
        } catch (error) {
          alert("No user found.");
        }
      } else {
        // const img =
        //   localStorage.getItem(appConstants.role) === 'admin' ? "/image/avatar.png" :
        //   localStorage.getItem(appConstants.role) === 'supervisor' ? "/image/person1.png" :
        //   "/image/profile1.png";
        const currentUserData = await getRequestWithSecurity(
          `${API_URLS.PROFILE_DETAILS}`
        );
        setImgUrl(currentUserData?.imgUre);
        setData(currentUserData);
      }
    } catch (error) {}
  };

  useEffect(() => {
    fetchEmpDetails();
  }, []);

  // console.log("download function is called");
  const handleDownload = async (filePath: any) => {
    try {
      const fileName = filePath.split("/").pop();

      const response: any = await fileDownload(
        `${API_URLS.TICKET_DOWNLOAD}${filePath}`,
        true
      );

      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      const blob = await response.blob();
      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.target = "_blank";
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();
    } catch (error) {}
  };
  const [isWorkoffFormOpen, setIsWorkoffFormOpen] = useState(false);

  const [isResignationModalOpen, setIsResignationModalOpen] = useState(false);
  const [reason, setReason] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleOpenResignationModal = () => setIsResignationModalOpen(true);
  const handleCloseResignationModal = () => setIsResignationModalOpen(false);

  const handleReasonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setReason(e.target.value);
  };

  const handleResignation = async () => {
    if (!reason.trim()) {
      alert("Please provide a reason for termination.");
      return;
    }

    console.log("Submitting Resignation Request - Reason:", reason);

    const token = localStorage.getItem("accessToken");

    if (!token) {
      alert("Authentication token missing. Please log in again.");
      return;
    }

    setIsLoading(true);

    try {
      const response = await axios.post(
        API_URLS.RESIGNATION,
        { terminationReason: reason },

        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log("API Payload:", { terminationReason: reason });

      if (response?.status === 200 || response?.status === 201) {
        showSuccessAlert2(
          response.data.message || "Resignation submitted successfully."
        );
        handleCloseResignationModal(); // ✅ Correct
      } else {
        showErrorAlert(response.data.message || "Something went wrong.");
      }
    } catch (error: any) {
      console.error("Error submitting resignation:", error);

      const errorMessage =
        error.response?.data?.message ||
        error.response?.data ||
        "An unexpected error occurred.";

      showErrorAlert(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    console.log("Documents Data:", data);
  }, [data]);

  if (!isMounted) return null;
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  return (
    <Layout>
      <div className="pt-1" style={{ backgroundColor: colors.blue12 }}>
        <div className="container-fluid my-4">
          {/* Header */}
          <div className="row justify-content-center mx-3">
            <div className="col-12">
              <div className="card shadow-sm">
                <div className="card-body d-flex flex-column flex-md-row align-items-center">
                  {/* <Avatar
                    intial={getInitials(data.empName)}
                    profileImg={imgUrl ? `${baseURL}${imgUrl}` : ""}
                    // profileImg={`${baseURL}${imgUrl}`}
                    className={"rounded-circle mb-3 mb-md-0 me-md-3"}
                    styleInital={{
                      width: "90px",
                      height: "90px",
                      backgroundColor: "#AABFD5",
                      color: "#000",
                      fontSize: 38,
                    }}
                    styleImg={{ width: "100px", height: "100px" }}
                  /> */}

                  {/* Conditionally display image or initials */}
                  {!imgUrl || imageErrors[0] ? (
                    <div
                      className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                      style={{
                        width: "90px",
                        height: "90px",
                        backgroundColor: "#AABFD5",
                        color: "#000",
                        fontSize: 38,
                      }}
                    >
                      {getInitials(data.empName)}
                    </div>
                  ) : (
                    <img
                      // src={`${baseURL}${imgUrl}`}
                      src={`${S3URL}${imgUrl}`}
                      alt={data.empName}
                      className="rounded-circle me-2"
                      style={{ width: "90px", height: "90px" }}
                      onError={() => handleImageError(0)}
                    />
                  )}
                  <div className="flex-grow-1 text-center text-md-start">
                    {/* <h4 className="text-primary mb-1">ID - {data.empId}</h4> */}
                    <p className="mb-1">Name: {data.empName || ""}</p>
                    <p className="mb-1">Email ID: {data.email || ""}</p>
                  </div>
                  <button className="btn btn-outline-success ms-md-auto mt-3 mt-md-0">
                    {data?.inService ? "ACTIVE" : "INACTIVE"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="row">
          <div
            className="col-12 col-md-6 profile-section1"
            style={{ width: "20%" }}
          >
            <button
              className={`btn w-100 mb-1 ${
                activeTab === 0 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={() => handleTabChange(0)}
            >
              Personal Details
            </button>
            <button
              className={`btn w-100 mb-1 ${
                activeTab === 1 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={() => handleTabChange(1)}
            >
              Work Details
            </button>
            <button
              className={`btn w-100 mb-1 ${
                activeTab === 2 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={() => handleTabChange(2)}
            >
              Documents
            </button>
            <button
              className={`btn w-100 mb-1 ${
                activeTab === 3 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={() => handleTabChange(3)}
            >
              Leave
            </button>
            <button
              className={`btn w-100 mb-1 ${
                activeTab === 4 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={handleOpenResignationModal}
            >
              Resignation
            </button>
          </div>

          <div className="col-12 col-md-8 profile-section2">
            {" "}
            {activeTab === 0 && (
              <div className="container-box ">
                <div className="row">
                  <div className="col-12">
                    <h3
                      className="section-header"
                      style={{ fontWeight: "bold" }}
                    >
                      Personal Details
                    </h3>
                    <hr className="section-divider" />
                  </div>
                  <div className="col-md-6 ">
                    <p>
                      Date Of Birth:{" "}
                      {data.birthday
                        ? new Date(data.birthday).toLocaleDateString()
                        : "/"}
                    </p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Ethnicity: {data.ethnicity || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>Gender: {data.gender || "/"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Marital Status: {data?.married ? "Yes" : "No"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <p>Phone Number: {data.mobileNo || "/"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Email: {data.email || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <p>Alternate Number: {data?.alternateNumber || "/"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Alternate Email: {data?.alternateEmail || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>Country: {data?.nation || "South Africa"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Province:: {data?.provinceName || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <p>
                      District:{" "}
                      {data?.districtName === "Unknown District"
                        ? "N/A"
                        : data?.districtName || "N/A"}
                    </p>
                  </div>
                  <div className="col-md-6">
                    <p>SubDistrict: {data?.subDistrictName || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>
                      {" "}
                      Emergency Contact Name 1:{" "}
                      {data?.emergencyContactname1 || "/"}
                    </p>
                  </div>
                  <div className="col-md-6 ">
                    <p>
                      {" "}
                      Emergency Contact Name 2:{" "}
                      {data?.emergencyContactname2 || "/"}
                    </p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>
                      {" "}
                      Emergency Contact 1: {data?.emergencyContact1 || "/"}
                    </p>
                  </div>
                  <div className="col-md-6">
                    <p>Emergency Contact 2: {data?.emergencyContact2 || "/"}</p>
                  </div>
                </div>

                {/* <div className="row">
                  <div className="col-md-6 ">
                    <p>District: {data?.districtName || "/"}</p>
                  </div>
                  <div className="col-md-6">
                    <p>SubDistrict: {data?.subDistrictName || "/"}</p>
                  </div>
                </div> */}

                <div className="row">
                  <div className="col-md-6 ">
                    {/* <p>Zip Code: {data?.zipCode || "/"}</p> */}
                    <p>
                      Zip Code: {data?.zipCode ? parseInt(data.zipCode) : "/"}
                    </p>
                  </div>
                  <div className="col-md-6 ">
                    <p>National Id: {data?.nationalId || "/"}</p>
                  </div>
                </div>
              </div>
            )}
            {activeTab === 1 && (
              <div className="container-box ">
                <div className="row">
                  <div className="col-12">
                    <h3
                      className="section-header"
                      style={{ fontWeight: "bold" }}
                    >
                      Work Details
                    </h3>
                    <hr className="section-divider" />
                  </div>

                  <div className="col-md-6 ">
                    <p> Reporting Person: {data?.upperName || "/"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Hospital Name: {data?.companyName || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>Role: {data?.role || "/"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Facility: {data?.branchName || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>UID: {data?.uid || "/"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>
                      Department: {data?.deptName || data?.department || "/"}
                    </p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>
                      Joining Date:{" "}
                      {data?.hireDate
                        ? new Date(data?.hireDate).toLocaleDateString()
                        : "/"}
                    </p>
                  </div>
                  <div className="col-md-6 ">
                    <p>
                      Designation:{" "}
                      {data?.designationName || data?.designation || "/"}
                    </p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>Employee Code: {data?.empCode || "/"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Leave On Days: {data?.leaveOnDays || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p> Street: {data?.street || "/"}</p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Unit Number: {data?.unitNumber || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <p>
                      Default Shift:{" "}
                      {data?.startTime && data?.endTime
                        ? `${data.startTime}  -  ${data.endTime}`
                        : "/"}
                    </p>
                  </div>
                  <div className="col-md-6 ">
                    <p>Work Zone: {data?.workZone || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    {/* <p>Probation Status: {data?.probationPeriod || ""}</p> */}
                    <p>
                      Probation Status:{" "}
                      {data?.isInProbation === "Yes"
                        ? "Under Probation"
                        : "Completed"}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {activeTab === 2 && (
              <div className="container-box">
                <h3 className="section-header" style={{ fontWeight: "bold" }}>
                  Documents
                </h3>
                <hr className="section-divider" />
                <div className="table-responsive">
                  <table className="table  ">
                    <thead>
                      <tr
                        className="border-bottom pt-1  fontsize-row"
                        style={{ textAlign: "left", lineHeight: "2" }}
                      >
                        <th scope="col" style={{ width: "1%" }}>
                          Sr
                        </th>
                        <th scope="col" style={{ width: "30%" }}>
                          Name
                        </th>
                        <th scope="col" style={{ width: "3%" }}>
                          Type
                        </th>
                        <th
                          scope="col"
                          style={{ width: "3%", textAlign: "center" }}
                        >
                          Download
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.documents.map((doc: any, index: number) => (
                        <tr
                          key={doc.id}
                          className="border-bottom pt-1 fontsize-row"
                          style={{ textAlign: "left", lineHeight: "1.5" }}
                        >
                          <td className="pt-1">{index + 1}</td>
                          <td className="pt-1">{doc.documentName}</td>
                          <td className="pt-1">{doc.documentType}</td>
                          <td className="pt-1">
                            <a
                              className="text-primary p-3"
                              onClick={() => handleDownload(doc.filePath)}
                              style={{
                                cursor: "pointer",
                                marginLeft: "50px",
                              }}
                            >
                              <img
                                src="/image/icons8-download-24.png"
                                style={{ width: "20px" }}
                              />
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
            {activeTab === 3 && (
              <div className="container-box">
                <h3 className="section-header" style={{ fontWeight: "bold" }}>
                  Leave Balances
                </h3>
                <hr className="section-divider" />
                <div className="table-responsive">
                  <table className="table " style={{ width: "80%" }}>
                    <thead>
                      <tr
                        className="border-bottom pt-1  fontsize-row"
                        style={{ textAlign: "left", lineHeight: "2" }}
                      >
                        <th scope="col" style={{ width: "1%" }}>
                          Sr
                        </th>
                        <th scope="col" style={{ width: "30%" }}>
                          Leave Name
                        </th>
                        <th
                          scope="col"
                          style={{ textAlign: "center", width: "10%" }}
                        >
                          Assigned
                        </th>
                        <th
                          scope="col"
                          style={{ textAlign: "center", width: "10%" }}
                        >
                          Balance
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.leaveBalances.map((leave: any, index: number) => (
                        <tr
                          key={leave.leaveId}
                          className="border-bottom pt-1 fontsize-row"
                          style={{ textAlign: "left", lineHeight: "1.5" }}
                        >
                          <td className="pt-1">{index + 1}</td>
                          <td className="pt-1">{leave.leaveName}</td>
                          <td style={{ textAlign: "center" }} className="pt-1">
                            {leave.assignedLeave}
                          </td>
                          <td style={{ textAlign: "center" }} className="pt-1">
                            {leave.balanceLeave}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
            {/* {activeTab === 4 && (
              <div className="container-box">
                <div className="row">
                  <div className="col-12">
                    <button
                      className="btn btn-danger"
                      onClick={handleOpenResignationModal}
                    >
                      Submit Resignation
                    </button>
                  </div>
                </div>
              </div>
            )} */}
            {isResignationModalOpen && (
              <Modal
                show={isResignationModalOpen}
                onHide={handleCloseResignationModal}
              >
                <Modal.Header closeButton>
                  <Modal.Title>Resignation</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                  <p style={{ marginBottom: "10px" }}>
                    Are you sure you want to Resign?
                  </p>
                  <Form.Group controlId="terminationReason">
                    <Form.Label style={{ marginTop: "10px" }}>
                      Reason for Resignation
                    </Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      value={reason}
                      onChange={handleReasonChange}
                      placeholder="Please provide a reason for termination."
                    />
                  </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                  <Button
                    variant="secondary"
                    onClick={handleCloseResignationModal}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="success"
                    onClick={handleResignation}
                    disabled={isLoading}
                  >
                    {isLoading ? "Resignation..." : "Resignation"}
                  </Button>
                </Modal.Footer>
              </Modal>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ProfileDetail;
