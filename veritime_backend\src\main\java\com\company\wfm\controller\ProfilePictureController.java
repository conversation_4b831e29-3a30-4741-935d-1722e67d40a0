package com.company.wfm.controller;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.entity.Employee;
import com.company.wfm.repository.EmployeeRepository;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/profile")
public class ProfilePictureController {

    private static final String BASE_UPLOAD_DIRECTORY = "uploads/";
    private static final List<String> ALLOWED_FILE_TYPES = Arrays.asList("image/jpeg", "image/png");
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB size limit

    @Autowired private EmployeeRepository empRepository;

    @PostMapping("/upload")
	public ResponseEntity<String> uploadProfilePicture(@RequestParam("file") MultipartFile file,
			@RequestParam("employeeId") Long employeeId, @RequestParam("hospitalId") Long hospitalId, HttpServletRequest request) {

		if (file.isEmpty()) {
			return new ResponseEntity<>("Please select a file to upload.", HttpStatus.BAD_REQUEST);
		}

		// Validate file type
		if (!ALLOWED_FILE_TYPES.contains(file.getContentType())) {
			return new ResponseEntity<>("Invalid file type. Only JPEG and PNG files are allowed.",
					HttpStatus.UNSUPPORTED_MEDIA_TYPE);
		}

		// Validate file size
		if (file.getSize() > MAX_FILE_SIZE) {
			return new ResponseEntity<>("File size exceeds the maximum limit of 5 MB.", HttpStatus.PAYLOAD_TOO_LARGE);
		}

		try {

			Path folderPath = Paths.get(BASE_UPLOAD_DIRECTORY + String.valueOf(hospitalId));
			if (!Files.exists(folderPath)) {
				Files.createDirectories(folderPath);
			}
			Path filePath = folderPath.resolve(file.getOriginalFilename());
			Files.copy(file.getInputStream(), filePath);
			String baseUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath();
			String imageUrl = "/api/profile/images/" + hospitalId + "/" + file.getOriginalFilename();
			Employee emp = new Employee();
			emp.setEmpId(employeeId);
			emp.setImgUre(imageUrl);
			empRepository.save(emp);
			return new ResponseEntity<>("File uploaded successfully. Access it at: " + baseUrl+imageUrl, HttpStatus.OK);

		} catch (IOException e) {
			log.error("Failed to upload the image {}", file.getOriginalFilename());
			log.error("Exception occured while uploadProfilePicture", e);
			return new ResponseEntity<>("File upload failed", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

    @GetMapping("/images/{hospitalId}/{filename}")
    public ResponseEntity<?> getProfilePictureUrl(@PathVariable("filename") String filename, @PathVariable("hospitalId") String hospitalId, HttpServletRequest request) throws IOException {
        Path filePath = Paths.get(BASE_UPLOAD_DIRECTORY + String.valueOf(hospitalId)).resolve(filename).normalize();
        if (Files.exists(filePath)) {
            // Load the file as a resource
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                // Return the image content with appropriate headers
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                        .contentType(MediaType.parseMediaType(Files.probeContentType(filePath))) // Change this to dynamic based on the file type
                        .body(resource);
            } else {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }
        } else {
            return new ResponseEntity<>("Image not found.", HttpStatus.NOT_FOUND);
        }
    }
}
