import React, { useEffect, useState } from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import {
  fileDownload,
  postRequest,
  postRequestWithSecurity,
} from "../../../services/apiService";
import {
  showErrorAlert,
  showSuccessAlert,
  showSuccessAlert2,
} from "@/services/alertService";
import { API_URLS } from "../../../constants/apiConstants";
import moment from "moment";
import { convertToSouthAfricaTime } from "@/services/utils";
import SupportPersonForm from "./SupportPersonForm";
import Link from "next/link";
import { textAlign } from "@mui/system";
import "../TicketRaise/Subject.css";

// const truncateMessage = (message, maxLength = 20) => {
//   if (!message) return "No message";
//   return message.length > maxLength
//     ? `${message.substring(0, maxLength)}...`
//     : message;
// };

const truncateMessage = (message, maxLength = 20) => {
  if (!message) return "No message";
  const plainText = message.replace(/<\/?[^>]+(>|$)/g, "");
  return plainText.length > maxLength
    ? `${plainText.substring(0, maxLength)}...`
    : plainText;
};

// const truncateMessage = (message, maxLength = 5) => {
//   if (!message) return "No message";
//   const plainText = message.replace(/<\/?[^>]+(>|$)/g, ""); // Remove HTML tags
//   const words = plainText.split(" ");

//   if (words.length > maxLength) {
//     const truncatedWords = words.slice(0, maxLength).join(" ");
//     return `${truncatedWords}...`;
//   } else {
//     return plainText;
//   }
// };

const handleDownload = async (file) => {
  try {
    const fileName = file.split("/").pop();
    const response = await fileDownload(
      `${API_URLS?.TICKET_DOWNLOAD}${file}`,
      true
    );

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const blob = await response.blob();
    const urlBlob = window.URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = urlBlob;
    a.target = "_blank";
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    a.remove();
    showSuccessAlert(`File "${fileName}" downloaded successfully.`);
  } catch (error) {
    showErrorAlert("Error downloading file:", error.message);
  }
};

const TableRow = ({
  srNo,
  date,
  subject,
  message,
  createdBy,
  status,
  ageing,
  onEyeClick,
  ticketId,
  ticketCode,
  file,
  categoryName,
  assignedTo,
  currentUser,
  assigned_to,
}) => {
  const [showSupportForm, setShowSupportForm] = useState(false);

  const handleSupportClick = () => setShowSupportForm(true);

  const handleCloseSupportForm = () => setShowSupportForm(false);

  const handleEyeClick = () => {
    onEyeClick(ticketId);
    const ticketData = {
      srNo,
      date,
      subject,
      message,
      createdBy,
      status,
      ageing,
      ticketId,
      ticketCode,
    };
  };

  const isFileAvailable = file && file.trim() !== "";

  const canDownload =
    (currentUser?.empId === createdBy || currentUser?.empId === assigned_to) &&
    isFileAvailable;

  console.log("currentUser.empId:", currentUser?.empId);
  console.log("createdBy:", createdBy);
  console.log("assigned_to:", assigned_to);
  console.log("file:", file);
  console.log("isFileAvailable:", isFileAvailable);
  console.log("canDownload:", canDownload);

  // Log for debugging

  return (
    <>
      <tr
        className="border-bottom pt-1  fontsize-row"
        style={{ textAlign: "center", textAlign: "left" }}
      >
        {/* <td className="pt-1" style={{ maxWidth: "30px" }}>
          {srNo}
        </td> */}
        {/* <td className="pt-3">{date}</td>
        <td className="pt-3">{ticketId || "N/A"}</td> */}
        <td className="pt-1">
          <Link
            className="text-primary p-3"
            href={`/ticket/details/${btoa(ticketId)}`}
            style={{ cursor: "pointer" }}
          >
            #{ticketId}
          </Link>
        </td>
        <td className="pt-1" title={subject}>
          {truncateMessage(subject, 20)}
        </td>
        {/* <td className="pt-1">{subject || "No subject"}</td> */}
        {/* <td
          className="pt-1"
          title={message}
          dangerouslySetInnerHTML={{ __html: message || "No message" }}
        /> */}
        <td
          className="pt-1"
          title={message}
          dangerouslySetInnerHTML={{ __html: truncateMessage(message, 20) }}
        />

        <td className="pt-1">{categoryName || "N/A"}</td>

        {/* <td
          className="pt-3 text-truncate"
          title={message}
          dangerouslySetInnerHTML={{ __html: truncateMessage(message, 30) }}
        ></td> */}

        {/* <td
          className="pt-5 text-truncate"
          title={message}
          style={{
            maxWidth: "200px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {truncateMessage(message, 30)}
        </td> */}
        {/* <td className="pt-5">{createdBy || "Unknown"}</td> */}
        {/* <td className="pt-3">{status || "Unknown"}</td> */}
        <td className="pt-1">{ageing}</td>
        {/* <td className="pt-3">
          <Link
            className="text-primary p-3"
            href={`/ticket/details/${btoa(ticketId)}`}
            style={{ cursor: "pointer" }}
          >
            <img
              src="/image/eyebutton.png"
              style={{ width: "20px" }}
              alt="View Details"
            />
          </Link>
        </td> */}
        <td className="pt-1">
          {canDownload ? (
            <a
              className="text-primary p-3"
              onClick={() => handleDownload(file)}
              style={{ cursor: "pointer" }}
            >
              <img
                src="/image/icons8-download-24.png"
                style={{ width: "20px" }}
              />
            </a>
          ) : (
            <span style={{ marginLeft: "22px", width: "10px" }}></span>
          )}
        </td>
      </tr>
      <SupportPersonForm
        show={showSupportForm}
        handleClose={handleCloseSupportForm}
        ticketId={ticketId}
      />
    </>
  );
};

const Table = ({
  onEyeClick,
  departmentId,
  status,
  assignedTo,
  createdBy,
  myDepartmentTicket,
  query,
  currentUser,
}) => {
  const [tableData, setTableData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalCount, settotalCount] = useState(0);
  const rowsPerPage = 10;

  const fetchTickets = async (page = 0) => {
    try {
      const response = await postRequestWithSecurity(
        API_URLS.FILTER_TICKETS,
        {
          departmentId,
          status,
          assignedTo,
          createdBy,
          myDepartmentTicket,
          offset: page,
          limit: rowsPerPage,
          query, // Use the query to filter tickets
        },
        true
      );

      if (response && response.content) {
        setTableData(response.content);
        settotalCount(response.totalCount);
      } else {
        setTableData([]);
      }
    } catch (error) {}
  };

  useEffect(() => {
    fetchTickets(currentPage);
  }, [
    departmentId,
    status,
    assignedTo,
    createdBy,
    myDepartmentTicket,
    query,
    currentPage,
  ]);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber - 1);
  };

  const calculateAgeing = (createdTime) => {
    const now = moment();
    const createdDate = moment(createdTime);
    const duration = moment.duration(now.diff(createdDate));
    const days = duration.asDays().toFixed(0);
    return `${days} day${days > 1 ? "s" : ""}`;
  };

  return (
    <div className="container mt-1">
      <div
        className="table-responsive"
        style={{ maxWidth: "100%", display: "flex" }}
      >
        <table className="table">
          <thead>
            <tr>
              {/* <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Sr No
              </th> */}
              {/* <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "center",
                }}
              >
                Date
              </th> */}
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Ticket No
              </th>
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Subject
              </th>
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Description
              </th>

              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Issue Type
              </th>
              {/* <th scope="col">Created By</th> */}
              {/* <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "center",
                }}
              >
                Status
              </th> */}
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Ageing
              </th>
              {/* <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "center",
                }}
              >
                Action
              </th> */}
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Download
              </th>
            </tr>
          </thead>
          <tbody>
            {tableData.length > 0 ? (
              tableData.map((row, index) => (
                <TableRow
                  key={row.ticketId}
                  // srNo={index + 1 + currentPage * rowsPerPage}
                  // date={convertToSouthAfricaTime(row.createdTime)}
                  subject={row.ticketSubject ?? "No subject"}
                  message={row.ticketMessage ?? "No message"}
                  categoryName={row.categoryName}
                  ticketCode={row.ticketCode ?? "N/A"}
                  createdBy={`emp ${row.createdBy}`}
                  // status={row.status ?? "Unknown"}
                  ageing={calculateAgeing(row.createdTime)}
                  // onEyeClick={onEyeClick}
                  assigned_to={row.assigned_to}
                  ticketId={row.ticketId}
                  file={row.filePath}
                  currentUser={currentUser}
                />
              ))
            ) : (
              <tr>
                <td colSpan="9" className="text-center">
                  No tickets found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <nav aria-label="Page navigation example">
        <ul className="pagination justify-content-center">
          {Array.from({ length: totalCount }, (_, index) => (
            <li
              key={index + 1}
              className={`page-item ${currentPage === index ? "active" : ""}`}
            >
              <a
                className="page-link"
                onClick={() => handlePageChange(index + 1)}
              >
                {index + 1}
              </a>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default Table;
