package com.company.wfm.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 *
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationFilter authenticationFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration configuration) throws Exception {
        return configuration.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.cors(Customizer.withDefaults()).csrf(csrf -> csrf.disable()) // CSRF is disabled because we use JWT authentication (state-less)
                .authorizeHttpRequests((authorize) -> {
                    authorize.requestMatchers("/api/auth/**").permitAll();
                    authorize.requestMatchers("/error").permitAll();
                    authorize.requestMatchers("/api/v1/users").permitAll(); // Allow access to user creation end-point
                    authorize.requestMatchers("/api/v1/users/forgot-password").permitAll();
                    authorize.requestMatchers("/api/v1/users/validate-reset-token").permitAll();
                    authorize.requestMatchers("/api/v1/users/reset-password").permitAll();
                    authorize.requestMatchers("/api/v1/resetDefault-password").permitAll();
                    authorize.requestMatchers("/api/v1/users/createNew").permitAll();
                    authorize.requestMatchers("/api/v1/hikvision/generateCountryTargetData").permitAll();
                    authorize.requestMatchers("/api/v1/hikvision/uploadProfilePictureToHikVision").permitAll();
                    authorize.requestMatchers("/api/v1/hikvision/getScannedData").permitAll();
                    authorize.requestMatchers("/api/v1/hikvision/markDailyAttendance").permitAll();
                    authorize.requestMatchers("/api/v1/hikvision/createPastAttendance").permitAll();
                    authorize.requestMatchers("/api/v1/sms/webhook").permitAll();
                    authorize.requestMatchers("/api/v1/encryption/encrypt").permitAll();
                    authorize.requestMatchers("/api/v1/encryption/decrypt").permitAll();
                    authorize.requestMatchers("/api/v1/attendance/process-daily").permitAll();
                    authorize.requestMatchers("/api/v1/credit/job").permitAll();
                    authorize.requestMatchers(HttpMethod.OPTIONS, "/**").permitAll();
                    authorize.anyRequest().authenticated();
                })
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }
}