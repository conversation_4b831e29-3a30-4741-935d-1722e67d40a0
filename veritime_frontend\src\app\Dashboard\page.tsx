"use client";
import React, { useEffect, useState } from "react";
import TotalDepartmentsChart from "../../modules/Dashboard/TotalDepartmentsChart";
import DepartmentWiseReportChart from "../../modules/Dashboard/pieChartComponent";
import "./Main.css";
import "../../css/statuscard.css";
import DepartmentWiseTicketChart from "../../modules/Dashboard/DepartmentWiseTicketChart";
import Calenderr from "../../components/Calender/Calenderr";
import { Card } from "react-bootstrap";
import Layout from "../../components/Layout";
import { appConstants } from "../../constants/appConstants";
import useLocalStorage from "@/services/localstorage";
import TicketCreated from "../../modules/Dashboard/TicketCreated";
import TicketAssigned from "../../modules/Dashboard/TicketAssigned";
import { getRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import Link from "next/link";

const Main = () => {
  const [username, setusername] = useLocalStorage(appConstants?.username, "");
  const [isMounted, setIsMounted] = useState(false);

  const [blocksData, setBlocksData] = useState({
    totalEmployeesPresent: {
      totalCount: 0,
      percentage: 0.0,
    },
    ticketCreated: 0,
    totalEmployeesClockedIn: {
      totalCount: 0,
      percentage: 0.0,
    },
    teams: [],
    departmentWiseReport: [],
    teamsCount: 0,
    totalDepartments: {
      axisLbl: [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sept",
        "Oct",
        "Nov",
        "Dec",
      ],
    },
    ticketAssigned: 0,
    totalEmployees: {
      totalCount: 0,
      percentage: 0,
    },
    totalEmployeesOnLeave: {
      totalCount: 0,
      percentage: 0.0,
    },
    scheduleWiseReport: [],
    ticketWiseReport: [],
  });

  useEffect(() => {
    getDashboardData();
  }, []);

  const getDashboardData = async () => {
    try {
      const data = await getRequest(API_URLS?.GET_SUPERVISOR_DASHBOARD);
      setBlocksData(data);
    } catch (error) {}
  };

  const [teams, setTeams] = useState([]);
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        const response = await getRequest(API_URLS.EMPLOYEE_LIST);
        const mappedTeams = response.map((employee: any) => ({
          id: employee.empId,
          name: employee.empName || "Unknown",
          dept: employee.deptName || "Unknown",
          totalLeaves: 0,
          pendingLeaves: 0,
          overtime: 0,
          profImg:
            process.env.NEXT_PUBLIC_S3_URL + employee.imgUre ||
            "/image/person3.png",
        }));
        setTeams(mappedTeams);
      } catch (error) {}
    };

    fetchTeams();
  }, []);
  useEffect(() => {}, []);

  const handleEmployee = () => {
    window.location.href = "./adminDashboard/employee";
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;
  return (
    <Layout>
      <div className="container-fluid bg-custom p-4 pt-5">
        <div className="row mb-3">
          <div className="col-12 col-md-9">
            <h1 className="h3">Dashboard</h1>
            {/* <p className="small" style={{ textTransform: 'capitalize' }}>Hi, {username ? username?.toLowerCase() : ""}. Welcome back to Veritime Workforce Management System!</p> */}
          </div>
          <Calenderr />
        </div>
        {/* <PendingActionCard /> */}
        <div className="row">
          <Link href={"./adminDashboard/employee"}>
            <div className="col-12 col-md-6 col-lg-3 mb-3">
              <Card className="shadow-sm">
                <Card.Body
                  style={{ height: 150, cursor: "pointer" }}
                  className="d-flex flex-column justify-content-center align-items-center"
                >
                  <div className="d-flex align-items-center">
                    <div
                      style={{
                        backgroundColor: "#EFE9DA",
                        padding: 15,
                        borderRadius: 60,
                        marginRight: 15,
                      }}
                    >
                      <img
                        src="/image/human.png"
                        alt="Foreground Icon"
                        className="foreground-icon"
                      />
                    </div>
                    <div className="text-center mt-3">
                      <div className="h2">
                        {blocksData.totalEmployees.totalCount}
                      </div>
                      <div className="small text-muted">Total Employees</div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </div>
          </Link>
          <div
            className="col-12 col-md-6 col-lg-3 mb-3"
            onClick={handleEmployee}
          >
            <Card className="shadow-sm">
              <Card.Body
                style={{ height: 150, cursor: "pointer" }}
                className="d-flex flex-column justify-content-center align-items-center"
              >
                <div className="d-flex align-items-center">
                  <div
                    style={{
                      backgroundColor: "#A8F3FC",
                      padding: 15,
                      borderRadius: 60,
                      marginRight: 15,
                    }}
                  >
                    <img
                      src="/image/present.png"
                      alt="Foreground Icon"
                      className="foreground-icon"
                    />
                  </div>
                  <div className="text-center mt-3">
                    <div className="h2">
                      {blocksData.totalEmployeesPresent.totalCount}
                    </div>
                    <div className="small text-muted">
                      Total Employee Present
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>
          <div
            className="col-12 col-md-6 col-lg-3 mb-3"
            onClick={handleEmployee}
          >
            <Card className="shadow-sm">
              <Card.Body
                style={{ height: 150, cursor: "pointer" }}
                className="d-flex flex-column justify-content-center align-items-center"
              >
                <div className="d-flex align-items-center">
                  <div
                    style={{
                      backgroundColor: "#FDD9DA",
                      padding: 15,
                      borderRadius: 60,
                      marginRight: 15,
                    }}
                  >
                    <img
                      src="/image/Icon.png"
                      alt="Foreground Icon"
                      className="foreground-icon"
                    />
                  </div>
                  <div className="text-center mt-3">
                    <div className="h2">
                      {blocksData.totalEmployeesOnLeave.totalCount}
                    </div>
                    <div className="small text-muted">
                      Total Employee On Leave
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>
          <div
            className="col-12 col-md-6 col-lg-3 mb-3"
            onClick={handleEmployee}
          >
            <Card className="shadow-sm">
              <Card.Body
                style={{ height: 150, cursor: "pointer" }}
                className="d-flex flex-column justify-content-center align-items-center"
              >
                <div className="d-flex align-items-center">
                  <div
                    style={{
                      backgroundColor: "#EFE9DA",
                      padding: 15,
                      borderRadius: 60,
                      marginRight: 15,
                    }}
                  >
                    <img
                      src="/image/clock.png"
                      alt="Foreground Icon"
                      className="foreground-icon"
                    />
                  </div>
                  <div className="text-center mt-3">
                    <div className="h2">
                      {blocksData.totalEmployeesClockedIn.totalCount}
                    </div>
                    <div className="small text-muted">
                      Total Employee Clocked In
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </div>
        </div>
        <div className="row">
          <div className="col-12 col-lg-6 col-xl-6 col-md-12 mb-3">
            <div className="card shadow-sm">
              <div className="card-body">
                <h2 className="h5">Schedule Specific Report</h2>
                <div className="d-flex justify-content-around">
                  {blocksData?.scheduleWiseReport.map((item: any) => (
                    <div className="text-center">
                      <div className="pie-chart" data-percentage="81%">
                        <div className="pie-chart-center">{item?.value}%</div>
                      </div>
                      <p className="small">{item?.name}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-3">
            <div className="card shadow-sm">
              <div className="card-body">
                <p className="h6">Department Specific Report</p>
                {/* <DepartmentWiseReportChart data={blocksData.departmentWiseReport}/> */}
              </div>
            </div>
          </div>
          <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-3">
            <div className="card shadow-sm">
              <div className="card-body">
                <p className="h6">Facility Based Ticket Overview</p>
                <DepartmentWiseTicketChart data={blocksData.ticketWiseReport} />
              </div>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-12 col-lg-6 col-xl-6 col-md-12 mb-3">
            <div className="card shadow-sm card-department">
              <div className="card-body">
                <TotalDepartmentsChart data={blocksData.totalDepartments} />
              </div>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-12 col-lg-3 col-xl-3 col-md-12">
            <div
              className="card shadow-sm"
              style={{ cursor: "pointer", height: "250px" }}
            >
              <div className="card-body">
                <p className="text fw-bold">Tickets Created By You</p>
                <TicketCreated count={blocksData.ticketCreated} />
              </div>
            </div>
          </div>
          <div className="col-12 col-lg-3 col-xl-3 col-md-12">
            <div
              className="card shadow-sm"
              style={{ cursor: "pointer", height: "250px" }}
            >
              <div className="card-body">
                <p className="text text-green fw-bold">
                  Ticket Assigned To You
                </p>
                <TicketAssigned count={blocksData.ticketAssigned} />
              </div>
            </div>
          </div>
        </div>

        {/* <Team teamsCount={blocksData.teams.length} teamsList={blocksData.teams}/> */}
        {/* {teams.length >0?<Team teamsCount={teams.length} teamsList={teams}/>:''} */}
      </div>
    </Layout>
  );
};

export default Main;
