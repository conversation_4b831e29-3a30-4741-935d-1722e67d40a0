package com.company.wfm.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.company.wfm.dto.*;
import com.company.wfm.entity.*;
import com.company.wfm.repository.*;
import com.company.wfm.service.SmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.UserTokenService;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AttendanceRegularizationService {

    private final AttendanceRegularizationRepository repository;
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    AttendanceRepository attendanceRepository;

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private CommonNotificationService notificationService;

    @Autowired
    private EmployeeScheduleRepository employeeScheduleRepository;

    @Autowired
    private TimeSlotRepository timeSlotRepository;

    @Autowired
    private SmsService smsService;

    @Autowired
    private SmsProcessingService smsProcessingService;


    @Autowired
    public AttendanceRegularizationService(AttendanceRegularizationRepository repository) {
        this.repository = repository;
    }

    private static final Logger logger = LoggerFactory.getLogger(AttendanceRegularizationService.class);
    @Transactional
    public AttendanceRegularization submitRegularization(AttendanceRegularizationRequest request) {
        // Get the currently authenticated user's username
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();

        // Fetch the user details and eagerly load the associated Employee using findByUsername
        User user = userRepository.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("User not found");
        }

        // Fetch the associated Employee object from the User entity
        Employee employee = user.getEmployee();
        if (employee == null) {
            throw new RuntimeException("Employee details not found for user");
        }

        Long empId = employee.getEmpId();  // Get the Employee's ID
        String empName = employee.getEmpName();  // Get the Employee's Name

        // Create a new AttendanceRegularization object and set fields
        AttendanceRegularization regularization = new AttendanceRegularization();
        regularization.setDate(request.getDate());
        regularization.setTimeRange(request.getTimeRange());
        regularization.setReason(request.getReason());

        // Set EMP_ID and EMP_NAME from the Employee object
        regularization.setEmployeeId(empId.toString());
        regularization.setEmployeeName(empName);

        // Save the regularization record
        return repository.saveAndFlush(regularization);
    }

    @Transactional
    public AttendanceRegularization updateApprovalStatus(ApprovalRequest request) {

        try{
        AttendanceRegularization regularization = repository.findById(request.getRegularizationId())
            .orElseThrow(() -> new RuntimeException("Regularization not found"));

      /*  if (ApprovalStatus.APPROVED == request.getApprovalStatus()) {
            insertAttendanceRecords(regularization);
        }*/

        // Determine the action taken (Approved or Denied)
            boolean isApproved = ApprovalStatus.APPROVED == request.getApprovalStatus();
        // Handle the approval action
        if (isApproved) {
            insertAttendanceRecords(regularization);
            sendApprovalNotificationAndEmail(regularization, request.getActionReason());
        } else {
            sendDenialNotificationAndEmail(regularization, request.getActionReason());
        }
        regularization.setApprovalStatus(request.getApprovalStatus());
        regularization.setActionReason(request.getActionReason());
        return repository.saveAndFlush(regularization);
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("An unexpected error occurred while updating approval status", e);
        }

    }
    // email and notification work for regulization
    // Send Approval Notification and Email to Supervisor
    private void sendApprovalNotificationAndEmail(AttendanceRegularization regularization, String actionReason) {
        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();
        Long empId = Long.valueOf(regularization.getEmployeeId());
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));

        List<String> formattedDates = Arrays.stream(regularization.getMultiDate().split(","))
                .map(dateStr -> LocalDate.parse(dateStr.trim()).format(DateTimeFormatter.ofPattern("dd MMM yyyy")))
                .collect(Collectors.toList());

        String formattedDateString = String.join(", ", formattedDates);

        // Construct the notification message
        String notificationMessage = "Attendance Modification request for Employee " + regularization.getEmployeeName() +
                " has been approved. Dates: " + formattedDateString +
                ". Time Range: " + regularization.getTimeRange() + ". Reason: " + regularization.getReason() +
                ". Action Reason: " + actionReason;

        // Construct the email message
        String emailMessage ="Attendance Modification request for Employee " + regularization.getEmployeeName() +
                " has been approved. Dates: " + formattedDateString +
                ". Time Range: " + regularization.getTimeRange() + ". Reason: " + regularization.getReason() +
                ". Action Reason: " + actionReason;

        // Send notification to the supervisor
        NotificationDTO notificationDTO = new NotificationDTO(
                "Attendance Modification Request Approved",
                notificationMessage,
                "ATTENDANCE_REGULARIZATION_APPROVED",
                String.valueOf(LoggedInEmpId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        //type ======= attendance_regularization_approved
        notificationService.sendNotificationToEmployee(Long.valueOf(regularization.getEmployeeId()), notificationDTO.getTitle(), notificationDTO.getBody(), "biometric", String.valueOf(LoggedInEmpId));

        // Send email notification to the employee
        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Attendance Modification Request Approved");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);



        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            log.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                log.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                log.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            log.warn("Employee phone number is missing. SMS not sent.");
        }

    }

    // Send Denial Notification and Email to Supervisor
    private void sendDenialNotificationAndEmail(AttendanceRegularization regularization, String actionReason) {
        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();

        Long empId = Long.valueOf(regularization.getEmployeeId());
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));

        List<String> formattedDates = Arrays.stream(regularization.getMultiDate().split(","))
                .map(dateStr -> LocalDate.parse(dateStr.trim()).format(DateTimeFormatter.ofPattern("dd MMM yyyy")))
                .collect(Collectors.toList());

        String formattedDateString = String.join(", ", formattedDates);

        // Construct the notification message
        String notificationMessage = "Attendance Modification request for Employee " + regularization.getEmployeeName() +
                " has been denied. Dates: " + formattedDateString +
                ". Time Range: " + regularization.getTimeRange() + ". Reason: " + regularization.getReason() +
                ". Action Reason: " + actionReason;

        // Construct the email message
        String emailMessage ="Attendance Modification request for Employee " + regularization.getEmployeeName() +
                " has been denied. Dates: " + formattedDateString +
                ". Time Range: " + regularization.getTimeRange() + ". Reason: " + regularization.getReason() +
                ". Action Reason: " + actionReason;

        // Send notification to the supervisor
        NotificationDTO notificationDTO = new NotificationDTO(
                "Attendance Modification Request Denied",
                notificationMessage,
                "ATTENDANCE_REGULARIZATION_DENIED",
                String.valueOf(LoggedInEmpId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

      //  type=========attendance_regularization_denied
        notificationService.sendNotificationToEmployee(Long.valueOf(regularization.getEmployeeId()), notificationDTO.getTitle(), notificationDTO.getBody(), "biometric", String.valueOf(LoggedInEmpId));

        // Send email notification to the employee
        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Attendance Modification Denied");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);


        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            log.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                log.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                log.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            log.warn("Employee phone number is missing. SMS not sent.");
        }
    }

    private void insertAttendanceRecords(AttendanceRegularization regularization) {
        try {
        List<LocalDate> regularizedDates = Arrays.stream(regularization.getMultiDate().split(","))
            .map(String::trim)
            .map(LocalDate::parse)
            .toList();

        regularizedDates.forEach(regularizedDate -> {
                if (!isAttendanceRecordExists(regularization.getEmployeeId(), regularizedDate)) {
                    insertAttendance(regularization, regularizedDate);
                } else {
                    log.info("Attendance record already exists for Employee ID: {} on {}",
                        regularization.getEmployeeId(), regularizedDate);
                }
            }
        );

        } catch (RuntimeException e) {
            log.error("Runtime exception in insertAttendanceRecords: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in insertAttendanceRecords: {}", e.getMessage(), e);
            throw new RuntimeException("Error while inserting attendance records: " + e.getMessage(), e);
        }
    }

//    private void insertAttendance(AttendanceRegularization regularization,
//        LocalDate regularizedDate) {
//        String[] timeRange = regularization.getTimeRange().split("-");
//        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
//
//        Employee employee = employeeRepository.findById(
//                Long.valueOf(regularization.getEmployeeId()))
//            .orElseThrow(() -> new RuntimeException(
//                "Employee not found with ID: " + regularization.getEmployeeId()));
//
//        try {
//            Date checkInDate = timeFormat.parse(convertTo24HourFormat(timeRange[0].trim()));
//            Date checkOutDate = timeFormat.parse(convertTo24HourFormat(timeRange[1].trim()));
//
//            Time checkInTime = new Time(checkInDate.getTime());
//            Time checkOutTime = new Time(checkOutDate.getTime());
//            Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();
//
//            // overtime logic
//
//            BigDecimal overtimeHours = BigDecimal.ZERO;
//            // Fetch employee schedule
//            Optional<EmployeeSchedule> scheduleOpt = employeeScheduleRepository.findByEmpIdAndDate(employee.getEmpId(), regularizedDate);
//            if (scheduleOpt.isPresent()) {
//                EmployeeSchedule schedule = scheduleOpt.get();
//                Long shiftTimeSlotId = Long.valueOf((schedule.getModifiedShift() != null) ? schedule.getModifiedShift() : schedule.getActualShift());
//
//                // Fetch shift start and end time
//                Optional<TimeSlot> timeSlotOpt = timeSlotRepository.findById(shiftTimeSlotId);
//                if (timeSlotOpt.isEmpty()) {
//                    throw new RuntimeException("No time slot found for Shift ID: " + shiftTimeSlotId);
//                }
//                TimeSlot timeSlot = timeSlotOpt.get();
//
//                Time shiftStartTime = Time.valueOf(timeSlot.getStartTime());
//                Time shiftEndTime = Time.valueOf(timeSlot.getEndTime());
//
//                // Handle case when punch-in & punch-out exactly match shift times
//                if (checkInTime.equals(shiftStartTime) && checkOutTime.equals(shiftEndTime)) {
//                    overtimeHours = BigDecimal.ZERO;
//                    log.info("Employee ID: {} worked exactly as per shift timing, setting overtime to 0.", employee.getEmpId());
//                }else if (checkOutTime.after(shiftEndTime)) {
//                    // Calculate overtime if check-out time is after shift end time
//                    long overtimeMillis = checkOutTime.getTime() - shiftEndTime.getTime();
//                    double overtimeInHours = overtimeMillis / (60.0 * 60 * 1000);
//                    overtimeHours = BigDecimal.valueOf(overtimeInHours).setScale(2, RoundingMode.HALF_UP);
//                }
//            } else {
//                // No schedule found, consider total working time as overtime
//                long totalWorkedMillis = checkOutTime.getTime() - checkInTime.getTime();
//                double totalWorkedHours = totalWorkedMillis / (60.0 * 60 * 1000);
//                overtimeHours = BigDecimal.valueOf(totalWorkedHours).setScale(2, RoundingMode.HALF_UP);
//                log.warn("No schedule found for Employee ID: {}, considering total worked time as overtime.", employee.getEmpId());
//            }
//
//            AttendanceEntity attendance = AttendanceEntity.builder()
//                .empId(employee)
//                .checkInTime(checkInTime)
//                .checkOutTime(checkOutTime)
//                .createdBy(LoggedInEmpId)
//                .createdTime(LocalDateTime.now())
//                .updatedBy(null)
//                .updatedTime(null)
//                .modeType("Manual")
//                .date(regularizedDate)
//                    .overtime(overtimeHours)
//                .build();
//
//            attendanceRepository.saveAndFlush(attendance);
//            log.info("Inserted new attendance record for Employee ID: {} on {}",
//                regularization.getEmployeeId(),
//                regularizedDate);
//        }catch (RuntimeException e) {
//            log.error("Runtime exception occurred: {}", e.getMessage(), e);
//            throw e;
//        }  catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }

    private void insertAttendance(AttendanceRegularization regularization,
                                  LocalDate regularizedDate) {
        String[] timeRange = regularization.getTimeRange().split("-");
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");

        Employee employee = employeeRepository.findById(
                        Long.valueOf(regularization.getEmployeeId()))
                .orElseThrow(() -> new RuntimeException(
                        "Employee not found with ID: " + regularization.getEmployeeId()));

        try {
            Date checkInDate = timeFormat.parse(convertTo24HourFormat(timeRange[0].trim()));
            Date checkOutDate = timeFormat.parse(convertTo24HourFormat(timeRange[1].trim()));

            Time checkInTime = new Time(checkInDate.getTime());
            logger.info("Check-In Time: {}", checkInTime);
            Time checkOutTime = new Time(checkOutDate.getTime());
            logger.info("Check-Out Time: {}", checkOutTime);

            // Calculate employee working hours
            long employeeMillis = checkOutTime.getTime() - checkInTime.getTime();

            // Handle next day checkout
            if (employeeMillis < 0) {
                logger.info("Check-Out time is next day. Adding 24 hours to checkout time.");
                employeeMillis = (checkOutTime.getTime() + (24 * 60 * 60 * 1000)) - checkInTime.getTime();
            }

            long employeeMinutes = employeeMillis / (60 * 1000);
            BigDecimal employeeHours = BigDecimal.valueOf(employeeMinutes)
                    .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
            logger.info("Employee Working Hours: {}", employeeHours);

            Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();

            // overtime logic

            BigDecimal overtimeHours = BigDecimal.ZERO;
            // Fetch employee schedule
            Optional<EmployeeSchedule> scheduleOpt = employeeScheduleRepository.findByEmpIdAndDate(employee.getEmpId(), regularizedDate);
            Time actualShiftStartTime = null;
            Time actualShiftEndTime = null;
            if (scheduleOpt.isPresent()) {
                EmployeeSchedule schedule = scheduleOpt.get();
                Long shiftTimeSlotId = Long.valueOf((schedule.getModifiedShift() != null) ? schedule.getModifiedShift() : schedule.getActualShift());

                // Fetch shift start and end time
                Optional<TimeSlot> timeSlotOpt = timeSlotRepository.findById(shiftTimeSlotId);
                if (timeSlotOpt.isEmpty()) {
                    throw new RuntimeException("No time slot found for Shift ID: " + shiftTimeSlotId);
                }
                TimeSlot timeSlot = timeSlotOpt.get();

                Time shiftStartTime = Time.valueOf(timeSlot.getStartTime());
                logger.info("Shift Start Time: {}", shiftStartTime);
                Time shiftEndTime = Time.valueOf(timeSlot.getEndTime());
                logger.info("Shift End Time: {}", shiftEndTime);

                actualShiftStartTime = shiftStartTime;
                actualShiftEndTime = shiftEndTime;

                //calculating total shift working hour
                // Calculate total shift working hours
                long shiftMillis = shiftEndTime.getTime() - shiftStartTime.getTime();
                long shiftMinutes = shiftMillis / (60 * 1000);
                BigDecimal shiftHours = BigDecimal.valueOf(shiftMinutes)
                        .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                logger.info("Shift Hours: {}", shiftHours);

                overtimeHours = employeeHours.subtract(shiftHours);

                if (overtimeHours.compareTo(BigDecimal.ZERO) > 0) {
                    logger.info("Positive Overtime Hours: {}", overtimeHours);
                } else if (overtimeHours.compareTo(BigDecimal.ZERO) < 0) {
                    logger.info("Negative Overtime Hours: {}", overtimeHours);
                } else {
                    overtimeHours = BigDecimal.ZERO;
                    logger.info("No Overtime. Overtime Hours: {}", overtimeHours);
                }
            } else {
                // No schedule found, consider total working time as overtime
                long totalWorkedMillis = checkOutTime.getTime() - checkInTime.getTime();
                double totalWorkedHours = totalWorkedMillis / (60.0 * 60 * 1000);
                overtimeHours = BigDecimal.valueOf(totalWorkedHours).setScale(2, RoundingMode.HALF_UP);
                log.warn("No schedule found for Employee ID: {}, considering total worked time as overtime.", employee.getEmpId());

                actualShiftStartTime = Time.valueOf("00:00:00");
                actualShiftEndTime = Time.valueOf("00:00:00");
                logger.warn("No schedule found for Employee ID: {}, setting shift times to 00:00:00.", employee.getEmpId());
            }
            logger.info("Calculated Overtime Hours: {}", overtimeHours);

            // Check if attendance already exists for the given date
            Optional<AttendanceEntity> existingAttendanceOpt = attendanceRepository.findByEmpIdAndDate1(employee.getEmpId(), regularizedDate);
            if (existingAttendanceOpt.isPresent()) {
                // Update existing attendance record
                AttendanceEntity existingAttendance = existingAttendanceOpt.get();
                existingAttendance.setCheckInTime(checkInTime);
                existingAttendance.setCheckOutTime(checkOutTime);
                existingAttendance.setOvertime(overtimeHours);
                existingAttendance.setUpdatedBy(LoggedInEmpId);
                existingAttendance.setUpdatedTime(LocalDateTime.now());
                existingAttendance.setActualShiftStartTime(actualShiftStartTime);  // Set actual shift start time
                existingAttendance.setActualShiftEndTime(actualShiftEndTime);
                existingAttendance.setModeType("Manual");  // Ensure modeType is set to "Manual"
                attendanceRepository.saveAndFlush(existingAttendance);
                log.info("Updated existing attendance record for Employee ID: {} on {}", regularization.getEmployeeId(), regularizedDate);
            } else {
                // Insert new attendance record
                AttendanceEntity attendance = AttendanceEntity.builder()
                        .empId(employee)
                        .checkInTime(checkInTime)
                        .checkOutTime(checkOutTime)
                        .createdBy(LoggedInEmpId)
                        .createdTime(LocalDateTime.now())
                        .actualShiftStartTime(actualShiftStartTime)  // Set actual shift start time
                        .actualShiftEndTime(actualShiftEndTime)
                        .modeType("Manual") // Set modeType to "Manual"
                        .date(regularizedDate)
                        .overtime(overtimeHours)
                        .build();
                attendanceRepository.saveAndFlush(attendance);
                log.info("Inserted new attendance record for Employee ID: {} on {}", regularization.getEmployeeId(), regularizedDate);
            }



        }catch (RuntimeException e) {
            log.error("Runtime exception occurred: {}", e.getMessage(), e);
            throw e;
        }  catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private boolean isAttendanceRecordExists(String employeeId, LocalDate regularizedDate) {
        return attendanceRepository.findByEmpIdAndDate(Long.getLong(employeeId), regularizedDate)
            .isPresent();
    }

//    private String convertTo24HourFormat(String time) throws Exception {
//        SimpleDateFormat inputFormat = new SimpleDateFormat("hh:mma");
//        SimpleDateFormat outputFormat = new SimpleDateFormat("HH:mm:ss");
//        Date date = inputFormat.parse(time);
//        return outputFormat.format(date);
//    }

   /* public static String convertTo24HourFormat(String time) throws Exception {

        if (time.length() == 7 && time.charAt(5) != ' ') {
            time = time.substring(0, 5) + " " + time.substring(5);
        }

        // 12-hour format (with space before AM/PM)
        SimpleDateFormat inputFormat = new SimpleDateFormat("hh:mm a");
        // 24-hour format (HH:mm:ss)
        SimpleDateFormat outputFormat = new SimpleDateFormat("HH:mm:ss");

        // Parse the input time string into a Date object
        Date date = inputFormat.parse(time);

        // Format the Date object into the 24-hour time format
        return outputFormat.format(date);
    }*/

    public static String convertTo24HourFormat(String time) throws Exception {
        // If time has no space between time and AM/PM, insert a space
        if (time.length() == 7 && time.charAt(5) != ' ') {
            time = time.substring(0, 5) + " " + time.substring(5);
        }

        // 12-hour format (with space before AM/PM)
        SimpleDateFormat inputFormat = new SimpleDateFormat("hh:mm a");
        // 24-hour format (HH:mm:ss)
        SimpleDateFormat outputFormat = new SimpleDateFormat("HH:mm:ss");

        // Try parsing the time string
        Date date = inputFormat.parse(time);

        // Return the time in 24-hour format
        return outputFormat.format(date);
    }



    /*public List<AttendanceRegularizationResponse> getAllRegularizations() {
        List<AttendanceRegularization> regularizations = repository.findAll();
        return regularizations.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private AttendanceRegularizationResponse convertToDto(AttendanceRegularization regularization) {

        AttendanceRegularizationResponse dto = new AttendanceRegularizationResponse();
        dto.setId(regularization.getId());

        dto.setEmployeeId(regularization.getEmployeeId());
        dto.setEmployeeName(regularization.getEmployeeName());
        dto.setDate(regularization.getDate());
        dto.setTimeRange(regularization.getTimeRange());
        dto.setReason(regularization.getReason());
        dto.setApprovalStatus(regularization.getApprovalStatus());
        dto.setActionReason(regularization.getActionReason());

        return dto;
    }*/

    //  modifed requlation data
   /* public List<AttendanceRegularizationResponse> getAllRegularizations() {
        // Fetch all attendance regularizations
        List<AttendanceRegularization> regularizations = repository.findAll();

        // Fetch all employees and map them by employee ID for quick access
        List<Employee> employees = employeeRepository.findAll();
        Map<String, Employee> employeeMap = employees.stream() // Use String for employeeId map key
                .collect(Collectors.toMap(emp -> String.valueOf(emp.getEmpId()), Function.identity())); // Ensure employeeId is converted to String

        // Convert regularizations to DTOs and enrich with employee details
        return regularizations.stream()
                .map(regularization -> convertToDto(regularization, employeeMap))
                .collect(Collectors.toList());
    }*/

    /*private AttendanceRegularizationResponse convertToDto(AttendanceRegularization regularization, Map<String, Employee> employeeMap) {
        AttendanceRegularizationResponse dto = new AttendanceRegularizationResponse();

        // Populate regularization details
        dto.setId(regularization.getId());
        dto.setEmployeeId(regularization.getEmployeeId());
        dto.setDate(regularization.getDate());
        dto.setTimeRange(regularization.getTimeRange());
        dto.setReason(regularization.getReason());
        dto.setApprovalStatus(regularization.getApprovalStatus());
        dto.setActionReason(regularization.getActionReason());

        // Get employee details from employeeMap
        Employee employee = employeeMap.get(regularization.getEmployeeId()); // Match String employeeId
        if (employee != null) {
            dto.setEmployeeName(employee.getEmpName()); // Set employee name
            dto.setEmployeeImage(employee.getImgUre()); // Set employee image

            // Assuming Designation has a getTitle() or getName() method for the designation
            dto.setEmployeeDesignation(employee.getDesignation().getName()); // Set employee designation
        }

        return dto;
    }
*/

    public List<AttendanceRegularizationResponse> getAllRegularizations() {

        Long loggedInEmpId = getCurrentAuthenticatedEmployeeId();

        // Fetch all employees whose upperId matches the logged-in employee ID
        List<Employee> employeesUnderLoggedIn = employeeRepository.findByUpperId(loggedInEmpId);

        // Extract the employee IDs from the list of employees under the logged-in employee
        List<Long> employeeIdsUnderLoggedIn = employeesUnderLoggedIn.stream()
                .map(Employee::getEmpId)
                .collect(Collectors.toList());

      //  List<AttendanceRegularization> regularizations = repository.findAll(loggedInEmpId);

        List<AttendanceRegularization> regularizations = repository.findByEmployeeIdIn(employeeIdsUnderLoggedIn);

       List<Employee> employees = employeeRepository.findAll();
        Map<String, Employee> employeeMap = employees.stream() // Use String for employeeId map key
                .collect(Collectors.toMap(emp -> String.valueOf(emp.getEmpId()), Function.identity())); // Ensure employeeId is converted to String

       /* return regularizations.stream()
                .map(regularization -> convertToDto(regularization, employeeMap))
                .sorted(Comparator.comparing(AttendanceRegularizationResponse::getEmployeeId).reversed()) // Sort by employeeId in descending order
                .collect(Collectors.toList());*/

        return regularizations.stream()
                .map(regularization -> convertToDto(regularization, employeeMap))
                .sorted(Comparator.comparing(AttendanceRegularizationResponse::getId, Comparator.reverseOrder())) // Sort by id in descending order
                .collect(Collectors.toList());
    }

    public List<AttendanceRegularizationResponse> getRegularizationsForLoggedInEmployee() {
        // Retrieve the logged-in employee ID
        Long currentEmpId = getCurrentAuthenticatedEmployeeId();

        // Fetch regularizations only for the logged-in employee
        List<AttendanceRegularization> regularizations = repository.findByEmployeeId(String.valueOf(currentEmpId));

        // Fetch employee details
        List<Employee> employees = employeeRepository.findAll();
        Map<String, Employee> employeeMap = employees.stream()
                .collect(Collectors.toMap(emp -> String.valueOf(emp.getEmpId()), Function.identity()));

       /* return regularizations.stream()
                .map(regularization -> convertToDto(regularization, employeeMap))
                .sorted(Comparator.comparing(AttendanceRegularizationResponse::getEmployeeId).reversed())
                .collect(Collectors.toList());*/
        return regularizations.stream()
                .map(regularization -> convertToDto(regularization, employeeMap))
                .sorted(Comparator.comparing(AttendanceRegularizationResponse::getId, Comparator.reverseOrder())) // Sort by id in descending order
                .collect(Collectors.toList());
    }

    private Long getCurrentAuthenticatedEmployeeId() {
        // Assuming you use Spring Security to get the authenticated username
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        return userRepository.findByUsername(username).getEmployee().getEmpId();
    }

    private AttendanceRegularizationResponse convertToDto(AttendanceRegularization regularization, Map<String, Employee> employeeMap) {
        AttendanceRegularizationResponse dto = new AttendanceRegularizationResponse();


        dto.setId(regularization.getId());
        dto.setEmployeeId(regularization.getEmployeeId());
       // dto.setDate(regularization.getDate());
        dto.setTimeRange(regularization.getTimeRange());
        dto.setReason(regularization.getReason());
        dto.setApprovalStatus(regularization.getApprovalStatus());
        dto.setActionReason(regularization.getActionReason());

        Employee employee = employeeMap.get(regularization.getEmployeeId());
        if (employee != null) {
            dto.setEmployeeName(employee.getEmpName());
            dto.setEmployeeImage(employee.getImgUre());
            dto.setEmployeeDesignation(employee.getDesignation().getName());
        }


        dto.setMultiDates(parseMultiDate(regularization.getMultiDate()));

        return dto;
    }

    private List<LocalDate> parseMultiDate(String multiDateString) {

        if (multiDateString == null || multiDateString.isEmpty()) {
            return Collections.emptyList();
        }

        return Arrays.stream(multiDateString.split(","))
                .map(LocalDate::parse)  // Parse each date string to LocalDate
                .collect(Collectors.toList());
    }





    public AttendanceRegulationCountDTO getAttendanceRegulationCounts() {
        return repository.getAttendanceRegulationCounts();
    }
    public AttendanceRegulationCountDTO getAttendanceRegulationCounts(LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);
        return repository.getAttendanceRegulationCounts(startDateTime, endDateTime);
    }

    @Transactional
    public AttendanceRegularization submitRegularization2(AttendanceRegularizationRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();

        User user = userRepository.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("User not found");
        }

        Employee employee = user.getEmployee();
        if (employee == null) {
            throw new RuntimeException("Employee details not found for user");
        }

        Long empId = employee.getEmpId();
        String empName = employee.getEmpName();

        AttendanceRegularization regularization = new AttendanceRegularization();
        regularization.setDate(request.getDate());
        regularization.setTimeRange(request.getTimeRange());
        regularization.setReason(request.getReason());

        regularization.setEmployeeId(empId.toString());
        regularization.setEmployeeName(empName);

        regularization.setMultiDate(request.getMultiDate());
        //return repository.saveAndFlush(regularization);

        AttendanceRegularization savedRegularization = repository.saveAndFlush(regularization);

        // Notify supervisor or HR about the regularization request
        Long supervisorId = employeeRepository.findUpperIdByEmpId(employee.getEmpId());
        if (supervisorId != null && supervisorId != 0) {
            notifyOnRegularizationRequest(employee, request);
        }

        return savedRegularization;
    }

    // email and notification work
    private void notifyOnRegularizationRequest(Employee employee, AttendanceRegularizationRequest request) {

        // Retrieve supervisor for notification and email
        Long supervisorId = employeeRepository.findUpperIdByEmpId(employee.getEmpId());
        if (supervisorId == null) {
            logger.error("No supervisor found for employee ID: "+employee.getEmpId()+" to send notification");
           // throw new IllegalStateException("No supervisor found for employee ID: " + employee.getEmpId());
            return;
        }
        Employee supervisor = employeeRepository.findById(supervisorId)
                .orElseThrow(() -> new EntityNotFoundException("Supervisor not found"));

        String supervisorEmail = supervisor.getEmail();

        // Construct the notification and email messages
        // Process the multiDate field: Split, parse, and format the dates
        List<String> formattedDates = Arrays.stream(request.getMultiDate().split(","))
                .map(dateStr -> LocalDate.parse(dateStr).format(DateTimeFormatter.ofPattern("dd MMM yyyy"))) // Format each date
                .collect(Collectors.toList());

        // Construct the notification and email messages
        String formattedDateString = String.join(", ", formattedDates); // Join the formatted dates with a comma
        String notificationMessage = "Employee " + employee.getEmpName() + " submitted an attendance modification request for the following dates: " + formattedDateString +
                ". Time Range: " + request.getTimeRange() + ". Reason: " + request.getReason() + ".";

        String emailMessage = "Employee " + employee.getEmpName() + " submitted an attendance modification request for the following dates: " + formattedDateString +
                ". Time Range: " + request.getTimeRange() + ". Reason: " + request.getReason() + ".";


        // Send notification to the supervisor
        NotificationDTO notificationDTO = new NotificationDTO(
                "Attendance Modification Request",
                notificationMessage,
                "ATTENDANCE_REGULARIZATION_REQUEST",
                String.valueOf(supervisorId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );
        // type ===attendance_regularization

        notificationService.sendNotificationToEmployee(supervisorId, notificationDTO.getTitle(), notificationDTO.getBody(), "biometric", String.valueOf(employee.getEmpId()));

        // Send email notification to the supervisor
        EmailData emailData = new EmailData();
        emailData.setEmailId(supervisorEmail);
        emailData.setSubject("Attendance Modification Request Submitted");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);
    }



    public Map<String, Object> getAttendancePaginated(List<LocalDate> dates, Long empId, int offset, int limit) {
        try {
            // If empId is not provided, use the logged-in user's empId
            if (empId == null) {
                empId = tokenService.getEmployeeIdFromToken();
            }

            // Create Pageable object for pagination
            Pageable pageable = PageRequest.of(offset / limit, limit);

            // Fetch paginated results
            Page<AttendanceEntity> page = attendanceRepository.findByEmpIdAndDates(empId, dates, pageable);

            // Convert entities to DTOs
            List<AttendanceResponseDTO> data = page.getContent().stream()
                    .map(entity -> AttendanceResponseDTO.builder()
                            .attendanceId(entity.getId())
                            .empId(entity.getEmpId().getEmpId()) // Assuming the Employee entity has an `empId` field
                            .empName(entity.getEmpId().getEmpName()) // Assuming `empName` is available from `empId`
                            .checkInTime(entity.getCheckInTime())
                            .checkOutTime(entity.getCheckOutTime())
                            .date(entity.getDate())
                            .multiDate(entity.getMultiDate()) // Assuming `multiDate` is available
                            .modeType(entity.getModeType()) // Assuming `modeType` is available
                            .createdBy(entity.getCreatedBy()) // Assuming `createdBy` is available
                            .createdTime(entity.getCreatedTime()) // Assuming `createdTime` is available
                            .updatedBy(entity.getUpdatedBy()) // Assuming `updatedBy` is available
                            .updatedTime(entity.getUpdatedTime()) // Assuming `updatedTime` is available
                            .overtime(entity.getOvertime()) // Assuming `overtime` is available
                            .actualShiftStartTime(entity.getActualShiftStartTime())
                            .actualShiftEndTime(entity.getActualShiftEndTime())
                            .build()
                    )
                    .collect(Collectors.toList());


            // Return paginated response
            return Map.of(
                    "status", "success",
                    "offset", offset,
                    "limit", limit,
                    "totalRecords", page.getTotalElements(),
                    "data", data
            );
        } catch (Exception e) {
            return Map.of(
                    "status", "error",
                    "message", "Something went wrong: " + e.getMessage()
            );
        }
    }

}
