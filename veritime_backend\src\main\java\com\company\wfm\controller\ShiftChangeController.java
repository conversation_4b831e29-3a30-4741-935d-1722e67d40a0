package com.company.wfm.controller;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.EmployeeRecommendation;
import com.company.wfm.dto.EncryptedRequest;
import com.company.wfm.dto.EncryptedResponse;
import com.company.wfm.dto.ScheduleRecommendationDTO;
import com.company.wfm.dto.ShiftChangeRequestDTO;
import com.company.wfm.dto.ShiftSwapResponseDTO;
import com.company.wfm.entity.ShiftSwap;
import com.company.wfm.service.impl.ShiftChangeService;
import com.company.wfm.util.EncryptionUtil;
import com.google.gson.Gson;

import jakarta.persistence.EntityNotFoundException;
@RestController
@RequestMapping("/api/v1/shift-change")
@CrossOrigin(origins = "*")
public class ShiftChangeController {
    private final ShiftChangeService shiftChangeService;

	@Autowired
	private EncryptionUtil encryptionUtil;

    @Autowired
    public ShiftChangeController(ShiftChangeService shiftChangeService) {
        this.shiftChangeService = shiftChangeService;
    }

    @PostMapping("/recommendations")
    public ResponseEntity<List<EmployeeRecommendation>> getRecommendations(@RequestBody ScheduleRecommendationDTO request) {
        List<EmployeeRecommendation> recommendations = shiftChangeService.getRecommendations(request);
        return ResponseEntity.ok(recommendations);
    }

    @PostMapping("/request")
    public ResponseEntity<EncryptedResponse> createShiftChangeRequest(@RequestBody EncryptedRequest encryptedRequest) throws Exception {
        try {
            // Decrypt the incoming encrypted data
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

            // Deserialize the decrypted data into the ShiftChangeRequestDTO object
            Gson gson = encryptionUtil.createGson(); // Use the custom Gson with LocalDateTimeAdapter
            ShiftChangeRequestDTO request = gson.fromJson(decryptedData, ShiftChangeRequestDTO.class);

            // Process the shift change request and create the ShiftSwap entity
            ShiftSwap createdRequest = shiftChangeService.createShiftChangeRequest(request);

            // Serialize the created ShiftSwap entity into JSON
            String responseJson = gson.toJson(createdRequest);

            // Encrypt the response data
            String encryptedResponseData = encryptionUtil.encrypt(responseJson,encryptionUtil.generateKey());

            // Wrap the encrypted response into an EncryptedResponse object
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponseData));
        } catch (RuntimeException e) {
            // Handle application-specific exceptions
            String encryptedError = encryptionUtil.encrypt("Error processing the request: " + e.getMessage(),encryptionUtil.generateKey());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new EncryptedResponse(encryptedError));
        } catch (Exception e) {
            // Handle general exceptions
            String encryptedError = encryptionUtil.encrypt("An unexpected error occurred: " + e.getMessage(),encryptionUtil.generateKey());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse(encryptedError));
        }
    }

    @PostMapping("/{swapRequestId}/approve")
    public ResponseEntity<?> approveShiftSwap(@PathVariable Long swapRequestId, @RequestBody EncryptedRequest encryptedRequest) {
        try {
            // Decrypt the incoming encrypted request data
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

            // Parse the decrypted data to extract the shift change request
            Gson gson = encryptionUtil.createGson();
            ShiftChangeRequestDTO shiftChangeRequestDTO = gson.fromJson(decryptedData, ShiftChangeRequestDTO.class);

            // Process the shift swap approval
            shiftChangeService.approveShiftSwap(swapRequestId, shiftChangeRequestDTO);

            // Prepare and encrypt the success response
            String successMessage = "Shift swap approved and schedules updated";
            String encryptedResponse = encryptionUtil.encrypt(successMessage,encryptionUtil.generateKey());

            // Return the encrypted success response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));

        } catch (RuntimeException ex) {
            try {
                // Encrypt the error message
                String encryptedErrorMessage = encryptionUtil.encrypt(ex.getMessage(),encryptionUtil.generateKey());
                return ResponseEntity.badRequest().body(new EncryptedResponse(encryptedErrorMessage));
            } catch (Exception encryptionException) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Error while encrypting response: " + encryptionException.getMessage());
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred: " + e.getMessage());
        }
    }

    @PostMapping("/{swapRequestId}/deny")
    public ResponseEntity<?> denyLeave(@PathVariable Long swapRequestId, @RequestBody EncryptedRequest encryptedRequest) {
        try {
            // Decrypt the incoming encrypted request data
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

            // Parse the decrypted data to extract the shift change request
            Gson gson = encryptionUtil.createGson();
            ShiftChangeRequestDTO shiftChangeRequestDTO = gson.fromJson(decryptedData, ShiftChangeRequestDTO.class);

            // Process the denial request
            shiftChangeService.denyRequest(swapRequestId, shiftChangeRequestDTO.getActionReason());

            // Prepare and encrypt the success response
            String successMessage = "Swap request denied successfully";
            String encryptedResponse = encryptionUtil.encrypt(successMessage,encryptionUtil.generateKey());

            // Return the encrypted success response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));

        } catch (EntityNotFoundException | IllegalStateException e) {
            try {
                // Encrypt the error message
                String encryptedErrorMessage = encryptionUtil.encrypt(e.getMessage(),encryptionUtil.generateKey());
                return ResponseEntity.badRequest().body(new EncryptedResponse(encryptedErrorMessage));
            } catch (Exception encryptionException) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Error while encrypting response: " + encryptionException.getMessage());
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An unexpected error occurred: " + e.getMessage());
        }
    }

    /**
     *
     * @param type
     * @return
     */
    @GetMapping("/data")
    public ResponseEntity<EncryptedResponse> getShiftSwaps(
            @RequestParam(value = "type", required = false) String type) {
        try {
            // Retrieve shift swap data based on the request type
            List<ShiftSwapResponseDTO> shiftSwaps;

            if ("history".equalsIgnoreCase(type)) {
                // Fetch shift swaps only for the logged-in employee
                shiftSwaps = shiftChangeService.getAllShiftSwapDataForLoggedInEmployee();
            } else {
                // Fetch shift swaps for the team (employees under the logged-in employee)
                shiftSwaps = shiftChangeService.getAllShiftSwapData();
            }

            // Convert the shift swap list to JSON
            Gson gson = encryptionUtil.createGson();
            String jsonResponse = gson.toJson(shiftSwaps);

            // Encrypt the JSON response
            String encryptedResponse = encryptionUtil.encrypt(jsonResponse,encryptionUtil.generateKey());

            // Return the encrypted response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));
        } catch (Exception e) {
            // Handle errors during encryption or data retrieval
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse("Error encrypting response: " + e.getMessage()));
        }
    }


}
