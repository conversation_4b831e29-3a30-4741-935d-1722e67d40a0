package com.company.wfm.repository;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.AttendanceAuditDetailsListDTO;
import com.company.wfm.entity.AttendanceAuditEntity;

@Repository
public interface AttendanceAuditRepository extends JpaRepository<AttendanceAuditEntity, Long> {

  @Query(value =
      "SELECT EMP_ID, MIN(PUNCH_IN_TIME) as first_punch, MAX(PUNCH_IN_TIME) as last_punch, date as punch_date "
          + " FROM t_attendance_audit "
          + " WHERE CREATED_DATE BETWEEN :startDate AND :endDate "
          + " GROUP BY EMP_ID, date", nativeQuery = true)
  List<Object[]> findFirstAndLastPunches(@Param("startDate") LocalDateTime startDate,@Param("endDate") LocalDateTime endDate);

  @Query("SELECT a FROM AttendanceAuditEntity a WHERE a.empId.empId = :empId AND a.date >= :fromDate and a.date <=:toDate")
  List<AttendanceAuditEntity> findEmployeeFirstAndLastPunches(@Param("empId") Long empId, @Param("fromDate") LocalDate fromDate, @Param("toDate") LocalDate toDate);

  @Query("SELECT a FROM AttendanceAuditEntity a WHERE a.empId.empId in (:empIds) AND a.createdDate >= :fromDate and a.createdDate <=:toDate")
  List<AttendanceAuditEntity> findByMultipleEmpIdAndDateRange(@Param("empIds") List<Long> empIds,
      @Param("fromDate") LocalDateTime fromDate, @Param("toDate") LocalDateTime toDate);

  @Query(value =
          "SELECT EMP_ID, MIN(PUNCH_IN_TIME) as first_punch, MAX(PUNCH_IN_TIME) as last_punch, DATE as punch_date "
                  + " FROM t_attendance_audit "
                  + " WHERE DATE < :startDate "
                  + " GROUP BY EMP_ID, DATE", nativeQuery = true)
  List<Object[]> findPastFirstAndLastPunches(@Param("startDate") Date startDate);



  @Query("SELECT new com.company.wfm.dto.AttendanceAuditDetailsListDTO(" +
          "a.id, a.empId.empId, a.empId.empName, a.punchInTime, a.deviceType, a.deviceDump, a.createdBy, a.createdDate, a.date) " +
          "FROM AttendanceAuditEntity a WHERE a.empId.empId = :empId AND a.date = :date")
  List<AttendanceAuditDetailsListDTO> retrieveByEmpIdandDate(@Param("empId") Long empId, @Param("date") LocalDate date);

  @Query(value = """
		    SELECT 
		        emp_id AS empId,
		        MIN(punch_in_time) AS firstCheckIn,
		        MIN([DATE]) AS firstCheckInDate,
		        MAX(punch_in_time) AS lastCheckOut,
		        MAX([DATE]) AS lastCheckOutDate,
		        CASE 
		            WHEN DATEADD(SECOND, DATEDIFF(SECOND, '00:00:00', MAX(punch_in_time)), CAST(MAX([DATE]) AS DATETIME)) > 
		                 DATEADD(SECOND, DATEDIFF(SECOND, '00:00:00', CAST(:endTime AS TIME)), CAST(:calculatedEndDate AS DATETIME)) 
		            THEN 
		                 DATEDIFF(HOUR, 
		                          DATEADD(SECOND, DATEDIFF(SECOND, '00:00:00', CAST(:endTime AS TIME)), CAST(:calculatedEndDate AS DATETIME)), 
		                          DATEADD(SECOND, DATEDIFF(SECOND, '00:00:00', MAX(punch_in_time)), CAST(MAX([DATE]) AS DATETIME))
		                 ) 
		            ELSE 0 
		        END AS overtimeHours
		    FROM t_attendance_audit 
		    WHERE emp_id IN :empIds 
		    AND DATEADD(SECOND, DATEDIFF(SECOND, '00:00:00', punch_in_time), CAST([DATE] AS DATETIME)) 
		        BETWEEN DATEADD(SECOND, DATEDIFF(SECOND, '00:00:00', CAST(:startTime AS TIME)), CAST(:calculatedStartDate AS DATETIME)) 
		        AND DATEADD(SECOND, DATEDIFF(SECOND, '00:00:00', CAST(:endTime AS TIME)), CAST(:calculatedEndDate AS DATETIME))
		    GROUP BY emp_id
		""", nativeQuery = true)
  		List<Object[]> getEmployeePunchDetails(
		    @Param("empIds") List<Long> empIds,
		    @Param("calculatedStartDate") String calculatedStartDate,
		    @Param("startTime") String startTime,  // Format: 'HH:MI:SS'
		    @Param("calculatedEndDate") String calculatedEndDate,
		    @Param("endTime") String endTime  // Format: 'HH:MI:SS'
	);
}