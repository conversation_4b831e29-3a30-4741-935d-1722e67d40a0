"use client";
import React, { useEffect, useState } from "react";
import { appConstants } from "../../constants/appConstants";
import { API_URLS, BASE_URL } from "@/constants/apiConstants";
import { getRequest, postRequest } from "@/services/apiService";
import Layout from "../../components/Layout";

const Sequence = () => {
  const [isMounted, setIsMounted] = useState(false);
  const [roles, setRoles] = useState([]); 
  const [selectedRole, setSelectedRole] = useState("");
  const [widgets, setWidgets] = useState([]); 
  const [draggedWidgetIndex, setDraggedWidgetIndex] = useState<any>(null);


  const fetchRoles = async () => {
    try {
      const rolesData = await getRequest(`${BASE_URL}/widget-sequence/roles`); 
      setRoles(rolesData);
    } catch (error) {
    }
  };


  const fetchWidgets = async (role:string) => {
    try {
      const widgetsData = await getRequest(`${BASE_URL}/widget-sequence/widgets?role=${role}`);
      setWidgets(widgetsData);
    } catch (error) {
    }
  };

  const handleRoleChange = (e:any) => {
    const selectedRole = e.target.value;
    setSelectedRole(selectedRole);
    fetchWidgets(selectedRole); 
  };


  const handleDragStart = (index:any) => {
    setDraggedWidgetIndex(index);
  };

  const handleDrop = (index:any) => {
    const reorderedWidgets:any = [...widgets];
    const draggedWidget = reorderedWidgets[draggedWidgetIndex];
    reorderedWidgets.splice(draggedWidgetIndex, 1); 
    reorderedWidgets.splice(index, 0, draggedWidget); 
    setWidgets(reorderedWidgets); 
    setDraggedWidgetIndex(null);
  };


  const handleSaveSequence = async () => {
    const updatedSequence = widgets.map((widget:any, index) => ({
      widget_name: widget.widget_name,
      sequence: index + 1,
    }));
    try {
      await postRequest(`${BASE_URL}/widget-sequence/updateSequence`, { roleName: selectedRole,widgets: updatedSequence });
      alert("Widget sequence updated successfully!");
    } catch (error) {
    }
  };

  useEffect(() => {
    setIsMounted(true);
    fetchRoles(); 
  }, []);

  if (!isMounted) return null;

  return (
    <Layout>
      <div className="container-fluid p-4">
        <div className="row mb-3">
          {/* Dropdown to select role */}
          <div className="col-12 mb-4">
            <label htmlFor="roleSelect">Select Role:</label>
            <select
              id="roleSelect"
              className="form-select"
              value={selectedRole}
              onChange={handleRoleChange}
            >
              <option value="" disabled>
                -- Select Role --
              </option>
              {roles.map((role:any) => (
                <option key={role.role_name} value={role.role_name}>
                  {role.role_name}
                </option>
              ))}
            </select>
          </div>
        </div>

   
        <div className="row">
          {widgets.map((widget:any, index) => (
            <div
              key={widget.widget_name}
              className="col-3 mb-4"
              draggable
              onDragStart={() => handleDragStart(index)}
              onDrop={() => handleDrop(index)}
              onDragOver={(e) => e.preventDefault()} 
              style={{ backgroundColor: widget.backgroundColor || "#fff", padding: "10px", border: "1px solid #ccc", cursor: "move" }}
            >
              <h5>{widget?.description}</h5>
              <p>{widget?.componentType || "-"}</p>

            </div>
          ))}
        </div>

    
        <div className="row">
          <div className="col-12 text-center">
            <button className="btn btn-primary" onClick={handleSaveSequence}>
              Save Widget Sequence
            </button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Sequence;
