package com.company.wfm.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HospitalFilterRequest {

    private List<Long> provinceIds;
    private List<Long> districtIds;
    private List<Long> subDistrictIds;
    private String type;
    private String query;
  //  private int offset = 0;
    private int limit = 10;
    @JsonProperty("offset")
    private int offset = 0;
}
