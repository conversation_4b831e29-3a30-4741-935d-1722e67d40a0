
.home-container {
    display: flex;
    height: 100vh; 
    flex-direction: column;
  }
  
  .main-container {
    display: flex;
    width: 100%;
    margin-top: 32px; 
    flex-grow: 1;
    height: 90%; 
    position: relative; 
    background-color: #BDCDD6;
  }
  
  .sidemenu-container {
    position: fixed;
    left: 0;
    width: 100px;
    z-index: 1000;
    background-color: var(--red14);
    transition: left 0.3s;
    overflow: hidden; 
  }
  
  .sidemenu-container.expanded {
    left: 0; 
    z-index: 1000; 
    width: 250px;
  }
  
  .content-container {
    flex: 1;
    /* margin-left: 100px; */
    overflow-y: auto;
    transition: margin-left 0.3s;
    z-index: 1000;
    
  }
  
  .sidemenu-container.expanded ~ .content-container {
    /* margin-left: 250px; */
    
  }

  div#alert-container {
    position: absolute;
    z-index: 1000000000000000000000;
    right: 0;
    top: 0;
}
  
  @media (max-width: 768px) {
    .sidemenu-container {
      left: -250px; 
    }
  
    .sidemenu-container.expanded {
      left: 0;
      width: 100%;
    }
  
    .menu-container {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
    }
  
    .menu-item {
      width: 100%;
      justify-content: center;
    }
  
    .menu-item-text {
      display: inline;
    }
  
    .sidemenu-container.expanded .menu-item-text {
      display: inline;
    }
  
    .center-image {
      opacity: 0; 
      transition: opacity 0.3s; 
      position: fixed;
      left: 10px;
      bottom: 20px;
      width: 55px;
      height: auto;

    }
    .sidemenu-container:hover + .content-container .center-image {
      opacity: 1;
    }
  
    .content-container {
      margin-left: -100px;
    }
  
    .sidemenu-container.expanded ~ .content-container {
      margin-left: 0;
    }
  }
  
  .dashboardHeader{
    font-size: 20px !important;
    font-weight: 500 !important;
  }
  .cardHeaderTxt{
    font-size: 18px !important;
    font-weight: 500 !important;
  }
  .ch1{
    font-size: 16px !important;
  }

  .btnTxt1{
    font-size: 16px !important;
  }

  @media (max-width: 1024px) {
    .btnTxt1{
      font-size: 12px !important;
    }
  }

  .image-container {
    position: relative;
  }
  
  .center-image {
    transition: transform 0.3s ease, opacity 0.3s ease;
  }
  
  .hidden-on-hover {
    opacity: 0; 
    transform: translateX(-100%); 
  }
  
  .image-container:hover .hidden-on-hover {
    opacity: 1; 
    transform: translateX(0); 
  }
  

  .loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: #4193FF;
    overflow: hidden;
    z-index: 9999;
    pointer-events: none;
  }
  
  .loader-line {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(189, 204, 214, 0),
      #BDCCD6,
      rgba(189, 204, 214, 0)
    );
    animation: loading 1.0s infinite linear;
  }
  
  @keyframes loading {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  