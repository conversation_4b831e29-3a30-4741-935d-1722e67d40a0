import React, { useEffect, useState } from "react";
import {
  Mo<PERSON>,
  Button,
  Form,
  Alert,
  Dropdown,
  Row,
  Col,
} from "react-bootstrap";
import Select from "react-select";
import "../../../css/style.css";
import { getRequest, postRequest, putRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showSuccessAlert2 } from "@/services/alertService";
import { decryptData } from "@/utils/encryption.js";
import Hospital from "../hospitalList/page";
import useLocalStorage from "@/services/localstorage";
import { borderRadius } from "@mui/system";
import "../modals/CreateBranchModal.css";

const CreateBranchModal = ({
  show,
  handleClose,
  rows,
  setRows,
  rowToEdit,
}: any) => {
  const [deptList, setDeptList] = useState<any[]>([]);
  const [branchList, setBranchList] = useState<any[]>([]);
  const [hospitalList, setHospitalList] = useState<any>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [branchTypeList, setBranchTypeList] = useState<any[]>([
    { branchTypeId: 2, branchTypeName: "CHC" },
    { branchTypeId: 3, branchTypeName: "Clinic" },
    { branchTypeId: 4, branchTypeName: "College" },
    { branchTypeId: 5, branchTypeName: "EMS" },
  ]);

  const [role, setRole] = useLocalStorage("role", "");

  const [data, setData] = useState<any>({
    code: "",
    branch: "",
    branchHeadId: "",
    departmentIds: [],
    branchType: "",
    hospital: "",
    cluster: "",
  });

  const [errors, setErrors] = useState<any>({});
  const [showAlert, setShowAlert] = useState(false);
  const [searchTerm, setSearchTerm] = useState<any>("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false); // Dropdown state for departments

  // useEffect(() => {
  //   async () => {
  //     (async () => {
  //       const userData: any = await await decryptData(
  //         localStorage.getItem("userData")
  //       );

  //       if (userData) {
  //         setData((prevData: any) => ({
  //           ...prevData,
  //           hospital: userData.companyId,
  //         }));
  //       }
  //     })();

  //     const fetchDeptList = async () => {
  //       const urlParams = new URLSearchParams(window.location.search);
  //       const userData: any = await await decryptData(
  //         localStorage.getItem("userData")
  //       );
  //       const payload = {
  //         hospitalId: urlParams.get("hospitalId")
  //           ? urlParams.get("hospitalId")
  //           : userData.companyId,
  //         branchId: urlParams.get("branchId")
  //           ? urlParams.get("branchId")
  //           : userData.branchId,
  //         type: "select",
  //       };

  //       const data = await postRequest(API_URLS.DEPT_LIST, payload);

  //       const selectAllOption = {
  //         departmentId: 0,
  //         departmentName: "Select All",
  //       };
  //       const updatedDeptList = [selectAllOption, ...data.content];
  //       setDeptList(updatedDeptList);
  //     };
  //     fetchDeptList();
  //   };
  // }, []);

  useEffect(() => {
    const fetchDeptList = async () => {
      // Function declared
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const userData: any = await decryptData(
          localStorage.getItem("userData")
        );

        if (userData) {
          setData((prevData: any) => ({
            ...prevData,
            hospital: userData.companyId,
          }));
        }

        const payload = {
          hospitalId: urlParams.get("hospitalId") || userData.companyId,
          branchId: urlParams.get("branchId") || userData.branchId,
          type: "select",
        };

        const data = await postRequest(API_URLS.DEPT_LIST, payload);
        console.log("Department List API Response:", data);

        if (data?.content) {
          const selectAllOption = {
            departmentId: 0,
            departmentName: "Select All",
          };
          const updatedDeptList = [selectAllOption, ...data.content];
          setDeptList(updatedDeptList);
          console.log("Full Department List:", updatedDeptList);
        }
      } catch (error) {
        console.error("Error fetching department list:", error);
      }
    };

    fetchDeptList(); // Function is now being executed properly
  }, []);

  useEffect(() => {
    const fetchHospitalList = async () => {
      const response = {
        offset: 0,
        limit: 10000,
        type: "select",
      };
      const data = await postRequest(API_URLS.GET_HOSPITAL_LIST, response);
      setHospitalList(data.content);
    };
    fetchHospitalList();
  }, []);

  useEffect(() => {
    const fetchBranchList = async () => {
      const branchlist = await getRequest(API_URLS.EMPLOYEE_NAME);
      setBranchList(branchlist);
      if (rowToEdit) {
        setData({
          code: rowToEdit.code || "",
          cluster: rowToEdit.cluster || "",
          branch: rowToEdit?.branch || "",
          hospital: rowToEdit?.hospitalId || "",
          department: rowToEdit?.department || "",
          departmentIds: rowToEdit?.departmentIds || [],
          branchHeadId: rowToEdit?.branchHeadId || "",
          branchType: rowToEdit?.branchType || "",
          leaveCreditDay: rowToEdit?.leaveCreditDay || "",
          timeZone: rowToEdit?.timeZone || "",
        });
      }
    };
    fetchBranchList();
  }, []);

  const handleChange = (e: any) => {
    setData({
      ...data,
      [e.target.name]: e.target.value,
    });
  };

  const validateForm = () => {
    let formErrors: any = {};
    if (!data.hospital) formErrors.hospital = "Hospital Name is required";
    if (!data.branch) formErrors.branch = "Branch Name is required";
    if (!data.code) formErrors.code = "Branch Code is required";
    if (!data.cluster) formErrors.cluster = "Cluster is required";
    // if (!data.branchHeadId) formErrors.branchHeadId = "Branch Head is required";
    // if (data.departmentIds.length === 0)
    //   formErrors.department = "At least one department must be selected";
    if (!data.branchType)
      formErrors.branchType = "At least one valid branch type must be selected";
    return formErrors;
  };

  const handleSave = async () => {
    const formErrors = validateForm();
    if (Object.keys(formErrors).length === 0) {
      try {
        const requestData = {
          branchName: data.branch,
          branchCode: data.code,
          cluster: data.cluster,
          branchHeadId: parseInt(data.branchHeadId),
          departmentIds: data.departmentIds,
          branchType: data.branchType,
          hospitalId: parseInt(data.hospital),
          leaveCreditDay: parseInt(data.leaveCreditDay),
          timeZone: data.timeZone,
        };

        let response;
        let updatedRows = [...rows];

        if (rowToEdit) {
          response = await putRequest(
            `${API_URLS.UPDATE_BRANCH}/${rowToEdit.id}`,
            requestData
          );
          showSuccessAlert2("Facility updated successfully!");
          console.log(response, "branch update");

          updatedRows = updatedRows.map((row) =>
            row.id === rowToEdit.id ? { ...row, ...data } : row
          );
        } else {
          response = await postRequest(API_URLS.CREATE_BRANCH, requestData);
          showSuccessAlert2("Facility created successfully!");
          console.log(response, "branch create");
          // updatedRows.push({ id: response.id, ...data });
          const newId = response.id || new Date().getTime();
          updatedRows.push({ id: newId, ...data });
        }

        if (response) {
          setRows(updatedRows);

          handleClose();
        }
      } catch (error) {}
    } else {
      setErrors(formErrors);
      setShowAlert(true);
    }
  };

  const selectedDepartmentNames = deptList
    .filter((dept: any) => data.departmentIds.includes(dept.departmentId))
    .map((dept: any) => dept.departmentName)
    .join(", ");

  const filteredDeptList = deptList.filter((dept: any) =>
    dept.departmentName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredHospitalList = hospitalList.filter((hospital: any) =>
    hospital.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  const isSelectAllChecked = data.departmentIds.length === deptList.length;

  // Function to handle checkbox changes for departments
  const handleCheckboxChangeDept = (
    e: React.ChangeEvent<HTMLInputElement>,
    id: number
  ) => {
    if (id === 0) {
      // Handle "Select All" option
      if (e.target.checked) {
        // Select all department IDs
        const allDeptIds = deptList.map((dept) => dept.departmentId);
        setData((prevData: any) => ({
          ...prevData,
          departmentIds: allDeptIds,
        }));
      } else {
        // Deselect all departments
        setData((prevData: any) => ({
          ...prevData,
          departmentIds: [],
        }));
      }
    } else {
      // Handle individual department selection
      if (e.target.checked) {
        setData((prevData: any) => ({
          ...prevData,
          departmentIds: [...prevData.departmentIds, id],
        }));
      } else {
        setData((prevData: any) => ({
          ...prevData,
          departmentIds: prevData.departmentIds.filter(
            (deptId: any) => deptId !== id
          ),
        }));
      }
    }
  };
  const handleSelectChange = (selectedOption: any) => {
    setData({
      ...data,
      hospital: selectedOption ? selectedOption.value.toString() : "", // Update hospital ID
    });
    setErrors({
      ...errors,
      hospital: "", // Clear the hospital error
    });
  };

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>
          {rowToEdit ? "Edit Facility" : "Create Facility"}
        </Modal.Title>
      </Modal.Header>
      <label
        htmlFor="ticketSubject"
        className="ticket-text-primary"
        style={{ marginTop: "10px", marginLeft: "14px" }}
      >
        <span className="text-danger">
          The fields with * marks are mandatory
        </span>
      </label>
      <Modal.Body>
        {/* {showAlert && (
          <Alert
            variant="danger"
            onClose={() => setShowAlert(false)}
            dismissible
          >
            Please correct the errors in the form.
          </Alert>
        )} */}
        <Form>
          {role === "ceo" && (
            <Row>
              {/* Select Hospital */}
              <Col md={6}>
                <Form.Group className="mb-3" controlId="hospitalSelect">
                  <Form.Label className="ticket-text-primary">
                    Select Hospital<span className="text-danger">*</span>
                  </Form.Label>
                  <Select
                    options={hospitalList.map(
                      (hospital: { id: any; name: any }) => ({
                        value: hospital.id,
                        label: hospital.name,
                      })
                    )}
                    value={
                      hospitalList.find(
                        (hospital: { id: number }) =>
                          hospital.id == parseInt(data.hospital)
                      )
                        ? {
                            value: hospitalList.find(
                              (hospital: { id: number }) =>
                                hospital.id == parseInt(data.hospital)
                            )?.id,
                            label: hospitalList.find(
                              (hospital: { id: number }) =>
                                hospital.id == parseInt(data.hospital)
                            )?.name,
                          }
                        : null
                    }
                    onChange={handleSelectChange}
                    placeholder="Select Hospital Name"
                    isClearable
                    classNamePrefix="react-select"
                    className={errors.hospital ? "is-invalid" : ""}
                  />
                  {errors.hospital && (
                    <div className="invalid-feedback d-block">
                      {errors.hospital}
                    </div>
                  )}
                </Form.Group>
              </Col>

              {/* Facility Name */}
              <Col md={6}>
                <Form.Group className="mb-3" controlId="FacilityName">
                  <Form.Label className="ticket-text-primary">
                    Facility Name<span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Control
                    type="text"
                    placeholder="Facility Name"
                    value={data.branch}
                    name="branch"
                    onChange={handleChange}
                    isInvalid={!!errors.branch}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors.branch}
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
            </Row>
          )}
          <Row>
            {/* Facility Code */}
            <Col md={6}>
              <Form.Group className="mb-3" controlId="designationCode">
                <Form.Label className="ticket-text-primary">
                  Facility Code<span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Facility Code"
                  value={data.code}
                  name="code"
                  onChange={handleChange}
                  isInvalid={!!errors.code}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.code}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>

            {/* Cluster */}
            <Col md={6}>
              <Form.Group className="mb-3" controlId="cluster">
                <Form.Label className="ticket-text-primary">
                  Cluster<span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Cluster"
                  value={data.cluster}
                  name="cluster"
                  onChange={handleChange}
                  isInvalid={!!errors.cluster}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.cluster}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Row>
            {/* Facility Head */}
            <Col md={6}>
              <Form.Group className="mb-3" controlId="empId">
                <Form.Label className="ticket-text-primary">
                  Facility Head
                </Form.Label>
                <Select
                  options={branchList.map((emp) => ({
                    value: emp.empId,
                    label: emp.empName, // Display employee name
                  }))}
                  onChange={(option) =>
                    setData((prevData: any) => ({
                      ...prevData,
                      branchHeadId: option?.value, // Set branchHeadId to selected empId
                    }))
                  }
                  placeholder="Select Facility Head"
                  value={
                    branchList.find((emp) => emp.empId === data.branchHeadId)
                      ? {
                          value: data.branchHeadId,
                          label: branchList.find(
                            (emp) => emp.empId === data.branchHeadId
                          ).empName,
                        }
                      : null
                  }
                />
                {errors.branchHeadId && (
                  <Form.Control.Feedback type="invalid">
                    {errors.branchHeadId}
                  </Form.Control.Feedback>
                )}
              </Form.Group>
            </Col>

            {/* Facility Type */}
            <Col md={6}>
              <Form.Group className="mb-3" controlId="branchType">
                <Form.Label className="ticket-text-primary">
                  Facility Type<span className="text-danger">*</span>
                </Form.Label>
                <Form.Select
                  name="branchType"
                  value={data.branchType}
                  onChange={handleChange}
                  isInvalid={!!errors.branchType}
                >
                  <option value="">Select Facility Type</option>
                  {branchTypeList.map((branchType) => (
                    <option
                      key={branchType.branchTypeId}
                      value={branchType.branchTypeName}
                    >
                      {branchType.branchTypeName}
                    </option>
                  ))}
                </Form.Select>
                <Form.Control.Feedback type="invalid">
                  {errors.branchType}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-3" controlId="department">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Departments<span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Dropdown
              show={isDropdownOpen}
              onToggle={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              <Dropdown.Toggle
                as={Button}
                variant="outline-secondary"
                style={{
                  width: "100%",
                  textAlign: "left",
                  overflow: "hidden",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                }}
                title={selectedDepartmentNames || "Select Departments"}
              >
                {selectedDepartmentNames || "Select Departments"}
              </Dropdown.Toggle>
              <Dropdown.Menu
                style={{ maxHeight: "200px", overflowY: "auto", width: "100%" }}
              >
                <Form.Control
                  type="text"
                  placeholder="Search Departments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ width: "90%", margin: "0 10px 10px 10px" }}
                />

                {filteredDeptList.map((dept: any) => (
                  <Form.Check
                    type="checkbox"
                    id={`dept-${dept.departmentId}`} // Unique ID for checkboxes
                    key={dept.departmentId}
                    label={dept.departmentName}
                    value={dept.departmentId}
                    checked={data.departmentIds.includes(dept.departmentId)}
                    onChange={(e) =>
                      handleCheckboxChangeDept(e, dept.departmentId)
                    }
                  />
                ))}
              </Dropdown.Menu>
            </Dropdown>
            {errors.department && (
              <Form.Control.Feedback
                type="invalid"
                style={{ display: "block" }}
              >
                {errors.department}
              </Form.Control.Feedback>
            )}
          </Form.Group>

          <Row>
            {/* Leave Credit Day */}
            <Col md={6}>
              <Form.Group className="mb-3" controlId="leaveCreditDay">
                <Form.Label>
                  Leave Credit Day<span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  type="number"
                  min="1"
                  max="31"
                  value={data.leaveCreditDay}
                  name="leaveCreditDay"
                  onChange={(e) => {
                    const value = parseInt(e.target.value, 10);
                    if (value >= 1 && value <= 31) {
                      handleChange(e);
                    }
                  }}
                  isInvalid={!!errors.leaveCreditDay}
                />
                <Form.Control.Feedback type="invalid">
                  {errors.leaveCreditDay}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>

            {/* Time Zone */}
            <Col md={6}>
              <Form.Group className="mb-3" controlId="timeZone">
                <Form.Label>
                  Time Zone<span className="text-danger">*</span>
                </Form.Label>
                <Form.Select
                  name="timeZone"
                  value={data.timeZone}
                  onChange={handleChange}
                  isInvalid={!!errors.timeZone}
                >
                  <option value="">Select Time Zone</option>
                  <option value="SAST">South Africa S.T (UTC+02:00)</option>
                  <option value="CAT">Central Africa (UTC+02:00)</option>
                  <option value="EAT">East Africa (UTC+03:00)</option>
                  <option value="WAT">West Africa (UTC+01:00)</option>
                </Form.Select>
                <Form.Control.Feedback type="invalid">
                  {errors.timeZone}
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
          </Row>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>
          Close
        </Button>
        <Button variant="primary" onClick={handleSave}>
          {rowToEdit ? "Update" : "Create"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateBranchModal;
