"use client"
import React, { useEffect, useState } from 'react'
import Layout from "../../../components/Layout";
import ApprovalsDashboardBootStrap from '../../../components/Approvalsdashboard/ApprovalsDashboardBootStrap';

const viewHistory = ({ }) => {
    const [isMounted, setIsMounted] = useState(false);
    useEffect(() => {
    setIsMounted(true);
    }, []);
      
    if (!isMounted) return null;

    return (
        <Layout>
            <ApprovalsDashboardBootStrap toggleMenu={undefined} expanded={undefined} isFromHistory={true} />
        </Layout>
    );
}

export default viewHistory;
