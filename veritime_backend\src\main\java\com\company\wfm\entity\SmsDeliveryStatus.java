package com.company.wfm.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "t_sms_delivery_status")
@Getter
@Setter
@NoArgsConstructor
public class SmsDeliveryStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "fn")
    private Integer fromNumber;

    @Column(name = "tn")
    private Integer toNumber;

    @Column(name = "sc")
    private Integer success;

    @Column(name = "st", length = 100)
    private String smscStatus;

    @Column(name = "rf")
    private Integer referenceNumber;

    @Column(name = "ts")
    private LocalDateTime timestamp;
}

