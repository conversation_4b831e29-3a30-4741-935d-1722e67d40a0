import React, { useEffect, useState } from "react";
import TableFilter from "../../common-components/TableFilter";
import { postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { decryptData } from "@/utils/encryption";
import "../../components/AttendanceList/teamAttendance.css";
import { Modal, Button, ModalFooter } from "react-bootstrap";
import VisibilityIcon from "@mui/icons-material/Visibility";

const AttendanceRegularization = ({ fromDate, toDate, goButton, selectedEmployee, statusFilter }) => {
  const [filteredRows, setFilteredRows] = useState([]);

  useEffect(() => {
    if (goButton) {
      fetchAttendanceData();
    }
  }, [goButton, fromDate, toDate, selectedEmployee, statusFilter]);

  const fetchAttendanceData = () => {
    
    try {
      const encryptedUserData = localStorage.getItem("userData");
      const userData = decryptData(encryptedUserData).then(() => {
        if (!userData) {
          return;
        }


        const myEmpId = userData.empId;

        if (!myEmpId) {
          return;
        }
      });


      const payload = {
        userid: selectedEmployee ? [selectedEmployee.value] : [],
        fromDate: fromDate,
        toDate: toDate,
      };
      

      const response = postRequest(API_URLS.TEAM_ATTENDANCE, payload)
      response.then((res) => {
        if (res && Array.isArray(res)) {
          const filteredData = res.filter((item) => {
            if (statusFilter === "All") {
              return true;
            }
            return item.status === statusFilter;
          });
          const formattedRows = filteredData.map((item, index) => ({
            srno: index + 1,
            id: index + 1,
            empId: item.empidç,
            Name: item.empname,
            Date: item.date,
            "Start Time": item.checkin|| "00:00",
            "End Time": item.checkout|| "00:00",
            "Entry mode": item.modetype===null?item.status:item.modetype,
            mode: item.modetype,
            status: item.status,
            overtime: item.overtime || "0", // Add this line
          }));
          console.log(formattedRows)
          setFilteredRows(formattedRows);
        }
      });

      response.catch(err=>setFilteredRows([]))    //04.04.25


    } catch (error) {
    }
  };

  const handleViewClick = async (empId, date) => {
    console.log("Clicked Employee ID:", empId);
    console.log("Clicked Date:", date, "Type:", typeof date);

    if (!empId) {
      console.error("empId is undefined! Check your table data.");
      return;
    }

    if (typeof date !== "string") {
      console.error("Date is not a string! Check data format.");
      return;
    }

    setSelectedEmpId(empId);
    setSelectedDate(date);
    setModalOpen(true);

    const formattedDate = new Date(date).toISOString().split("T")[0];
    const payload = { empId, date: formattedDate };

    console.log("Sending Payload:", payload);

    try {
      const response = await postRequest(
        API_URLS.ATTENDANCE_AUDIT_DETAILS,
        payload
      );
      console.log("API Response:", JSON.stringify(response, null, 2));

      if (Array.isArray(response) && response.length > 0) {
        setAuditDetails(response);
      } else {
        console.warn("No data returned from API.");
        setAuditDetails([]);
      }
    } catch (error) {
      console.error("Error fetching audit details:", error);
    }
  };

  const columns = [
    { field: "srno", headerName: "Sr.no", width: 80 },
    { field: "Name", headerName: "Name", width: 250 },
    { field: "Entry mode", headerName: "Entry mode", width: 150 },

    { field: "Date", headerName: "Date", width: 180 },
    { field: "Start Time", headerName: "Start Time", width: 100 },
    { field: "End Time", headerName: "End Time", width: 100 },
    { field: "overtime", headerName: "over Time", width: 120 },

    {
      field: "Action",
      headerName: "View",
      width: 80,
      renderCell: (params) =>
        
       
          <Button
            onClick={() => handleViewClick(params.row.empId, params.row.Date)}
          >
            <VisibilityIcon style={{ cursor: "pointer", color: "black" }} />
          </Button>
        ,
    },
  ];

  return (
    <div id="tableWrapper">
      <TableFilter columns={columns} rows={filteredRows} />
    </div>
  );
};

export default AttendanceRegularization;
