
import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';

// import '../Tickets/TicketCard.css'

import TicketCard from '@/components/Tickets/TicketCard';
import Link from 'next/link';


const TicketAssigned = ({count}) => {
  return (
    <Link href={"/ticket/viewTickets"}>

    <div  style={{ position: 'relative', width: '100%',height: "180px" }}>
      <TicketCard count={count} countColor="text-green"/>
    </div>
    </Link>
  );
};

export default TicketAssigned;

