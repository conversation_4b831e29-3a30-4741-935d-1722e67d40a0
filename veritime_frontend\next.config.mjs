/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    API_BASE_URL: process.env.NEXT_PUBLIC_API_URL
  },
  // output: "export",
  // webpack: (config, { isServer }) => {
  //   if (isServer) {
  //     config.externals = [...(config.externals || []), ({ request }, callback) => {
  //       const firebaseRegex = /^@?firebase(\/(.+))?/;
  //       if (firebaseRegex.test(request)) {
  //         return callback(null, `commonjs ${request}`);
  //       }
  //       callback();
  //     }];
  //   }
  //   return config;
  // }
};

export default nextConfig;
