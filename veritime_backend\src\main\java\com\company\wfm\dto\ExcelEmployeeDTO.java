package com.company.wfm.dto;


import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExcelEmployeeDTO {




    private String empCode;             // Employee Code
    private String empName;             // Employee Name
    private String departmentName;      // Department Name
    private String designationName;     // Designation Name
    private String uid;                 // Unique Identifier
    private String idNo;                // ID Number
    private String orgId;               // Organization ID
    private String upperId;             // Upper Management ID
    private String regionId;            // Region ID
    private String countryId;           // Country ID
   // private String provinceId;          // Province ID
    private String provinceName;
    private String city;                // City
    private String upperName;           // Upper Management Name
    private Date hireDate;              // Hire Date
    private String gender;              // Gender
    private Date birthday;              // Date of Birth
    private String nation;              // Nationality
    private Boolean married;             // Marital Status
    private String phoneNo;             // Phone Number
    private String mobileNo;            // Mobile Number
    private String email;               // Email Address
    private String nativePlace;         // Native Place
    private String zipCode;             // Zip Code
    private Boolean isHistory;           // History Status
    private Boolean inService;          // In Service Status
    private String remark;              // Remarks
    private String createdBy;           // Created By
    private Date createdTime;           // Creation Time
    private String updatedBy;           // Updated By
    private Date updatedTime;           // Update Time
    private Integer version;            // Version Number
    private String nativeLanguage;      // Native Language
    private String foreignLanguages;    // Foreign Languages
    private Integer workYears;          // Years of Work Experience
    private String graduateSchool;      // Graduate School
    private Date graduateTime;          // Graduation Date
    private String highestDegree;       // Highest Degree
    private String imgUre;              // Image URL
    private String defaultTimeSlotId;   // Default Time Slot ID
    private String leaveOnDays;
    private String hospitalName;
}
