.custom-container-Subject {
    background-color: var(--red14); 
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    /* width:1080px; */
  }
  
  .text-primary {
    color: var(--blue12) !important; 
  }
  
  .form-control {
    border-radius: 4px;
    margin-top: 5px;
  }

  .disabled {
    color: gray; /* or any color to indicate it's disabled */
    opacity: 0.5;
  }
  
  .form-group {
    margin-bottom: 1.5rem; 
  }
  
  .rich-editor {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px;
  }
  .value.with-margin {
    margin-top: 15px;
  }
  
  .editor-toolbar {
    display: flex;
    border-bottom: 1px solid #ced4da;
    margin-bottom: 10px;
    padding-bottom: 5px;
  }
  
  .editor-toolbar .btn-link {
    color: var(--blue12); 
    text-decoration: none;
    font-weight: bold;
    padding: 0 10px;
  }
  
  .editor-content {
    min-height: 150px;
    border: none;
    outline: none;
    padding-top: 5px;
  }

  @media (max-width: 768px) {
    .custom-container-Subject {
      padding: 15px;
      width: 100%; 
      max-width: 600px; 
      margin-left: 10px; 
      margin-right: auto; 
    }
  
    .form-group {
      margin-bottom: 1rem; 
      width: 100%; 
    }
  }
  @media (min-width: 768px) and (max-width: 1024px) {
    .custom-container-Subject {
      padding: 15px;
      width: 100%; 
      max-width: 600px; 
      margin-left: 15px; 
      margin-right: auto; 
    }
  
    .form-group {
      margin-bottom: 1rem; 
      width: 100%; 
    }
  }
  

  .ticket-detail-container {
    text-align: left;
    background-color: white;
    border-radius: 7px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    min-height: 350px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 10px;
    margin-right: 25px;
    font-family: Barlow, sans-serif;
    font-size: 16px;
  }
  
  .ticket-title2 {
    font-weight: bold;
    color: #333;
    padding-bottom: 1px;
    margin-bottom: 1px;
    border-bottom: 1px solid #d3d3d3;
  }
  
  .ticket-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    padding-bottom: 2px;  
    border-bottom: 1px solid #d3d3d3;
  }
  

  .ticket-field {
    display: flex;
    align-items: center;
    gap: 10px; 
    flex: 1;
  }
  
  .ticketlabel {
    font-size: 16px;
    align-items: center;
    font-weight: bold;
    white-space: nowrap;
  }
  
  .value {
    font-size: 16px;
    align-items: center;
    word-wrap: break-word;
    flex: 1; 
    margin-left: 1px;
    display: inline-block;
  text-align: left; 
  text-decoration: none;
  }
  
 



.ticket-field.attachment,
.ticket-field.assignedto,
.ticket-field.CreatedAt {
  display: flex;
  align-items: center;
  justify-content: flex-end; 
  margin-left: auto; 
  padding-bottom: 2px ; 
  text-align: right;
  gap: 10px;
  font-size: 16px ;
}

.ticket-field.attachment .label,
.ticket-field.assignedto .label,
.ticket-field.CreatedAt .label {
  font-weight: bold ;
  white-space: nowrap;
  text-align: right;
}

.ticket-field.attachment .value,
.ticket-field.assignedto .value,
.ticket-field.CreatedAt .value {
  flex: 1;
  text-align: right; 
  
}




  .attachment .text-primary {
    cursor: pointer;
  }
  
  .download-icon {
    width: 30px;
  }
  
  .no-attachment {
    margin-left: 22px;
    width: 10px;
  }
  
  .priority {
    border-radius: 15px;
    margin-right: 30px;
    font-size: 14px;
    display: inline-block; 
  }
  
  
  


/* Small devices (up to 768px) */
@media (min-width: 768px) {
  .ticket-detail-container {
    padding: 15px;
    font-size: 14px;
  }

  .ticket-title2 {
    font-size: 18px;
  }

  .ticket-row {
    font-size: 16px;
  }

  .ticketlabel, .value {
    font-size: 14px;
  }

  .ticket-field.attachment,
.ticket-field.assignedto,
.ticket-field.CreatedAt {
  justify-content: flex-end;
  font-size: 12px ;
  
}


  .ticket-field.attachment .value,
  .ticket-field.assignedto .value,
  .ticket-field.CreatedAt .value {
    text-align: right;
    font-size: 12px;
   
  }
  
}
.priority-badge {
  font-size: 10px; 
  padding: 3px 8px;
  border-radius: 10px;
}



/* Small devices (up to 768px) */
@media (max-width: 768px) {
  .ticket-detail-container {
    padding: 15px;
    font-size: 14px;
  }

  .ticket-title2 {
    font-size: 18px;
  }

  .ticket-row {
    font-size: 16px;
  }

  .ticketlabel {
    font-size: 14px;
  }
  .value{
    font-size: 12px;
  }
  
  .priority-badge {
    font-size: 12px;  /* Adjust the font size for larger screens */
    padding: 5px 10px;
    border-radius: 12px;
  }
  .ticket-field.attachment,
  .ticket-field.assignedto,
  .ticket-field.CreatedAt {
    justify-content: flex-end;
    font-size: 12px;
    
  }

  .ticket-field.attachment .label,
  .ticket-field.assignedto .label,
  .ticket-field.CreatedAt .label {
    font-weight: bold;
    text-align: right;
    font-size: 14px;
  }

  .ticket-field.attachment .value,
  .ticket-field.assignedto .value,
  .ticket-field.CreatedAt .value {
    text-align: right;
    font-size: 14px;
  }
}


/* Medium devices (769px to 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .ticket-detail-container {
    padding: 15px;
    font-size: 14px;
  }

  .ticket-title2 {
    font-size: 18px;
  }

  .ticket-row {
    font-size: 16px;
  }

  .ticketlabel {
    font-size: 14px;
  }
  .value{
    font-size: 12px;
  }
  
  .priority-badge {
    font-size: 12px;  /* Adjust the font size for larger screens */
    padding: 5px 10px;
    border-radius: 12px;
  }
  .ticket-field.attachment,
  .ticket-field.assignedto,
  .ticket-field.CreatedAt {
    justify-content: flex-end;
    font-size: 12px;
    
  }

  .ticket-field.attachment .label,
  .ticket-field.assignedto .label,
  .ticket-field.CreatedAt .label {
    font-weight: bold;
    text-align: right;
    font-size: 14px;
  }

  .ticket-field.attachment .value,
  .ticket-field.assignedto .value,
  .ticket-field.CreatedAt .value {
    text-align: right;
    font-size: 12px;
  }
}
/* Medium devices (769px to 1024px) */
@media (min-width: 1025px) and (max-width: 1100px) {
  .ticket-detail-container {
    padding: 15px;
    font-size: 14px;
  }

  .ticket-title2 {
    font-size: 18px;
  }

  .ticket-row {
    font-size: 16px;
  }

  .ticketlabel {
    font-size: 14px;
  }
  .value{
    font-size: 12px;
  }
  
  .priority-badge {
    font-size: 12px;  /* Adjust the font size for larger screens */
    padding: 5px 10px;
    border-radius: 12px;
  }
  .ticket-field.attachment,
  .ticket-field.assignedto,
  .ticket-field.CreatedAt {
    justify-content: flex-end;
    font-size: 14px;
    
  }

  .ticket-field.attachment .label,
  .ticket-field.assignedto .label,
  .ticket-field.CreatedAt .label {
    font-weight: bold;
    text-align: right;
    font-size: 14px;
  }

  .ticket-field.attachment .value,
  .ticket-field.assignedto .value,
  .ticket-field.CreatedAt .value {
    text-align: right;
    font-size: 12px;
  }
}

  /* For 1101px to 1240px */
  @media (min-width: 1101px) and (max-width: 1240px) {
    .ticket-detail-container {
      padding: 15px;
      font-size: 14px;
    }
  
    .ticket-title2 {
      font-size: 18px;
    }
  
    .ticket-row {
      font-size: 16px;
    }
  
    .ticketlabel {
      font-size: 14px;
    }
    .value{
      font-size: 12px;
    }
    
    .priority-badge {
      font-size: 12px; 
      padding: 5px 10px;
      border-radius: 12px;
    }
    .ticket-field.attachment,
    .ticket-field.assignedto,
    .ticket-field.CreatedAt {
      justify-content: flex-end;
      font-size: 14px;
      
    }
  
    .ticket-field.attachment .label,
    .ticket-field.assignedto .label,
    .ticket-field.CreatedAt .label {
      font-weight: bold;
      text-align: right;
      font-size: 14px;
    }
  
    .ticket-field.attachment .value,
    .ticket-field.assignedto .value,
    .ticket-field.CreatedAt .value {
      text-align: right;
      font-size: 12px;
    }
  }


@media (min-width: 1241px) and (max-width: 1450px) {
  .ticket-detail-container {
    padding: 15px;
    font-size: 16px;
  }

  .ticket-title2 {
    font-size: 18px;
  }

  .ticket-row {
    font-size: 16px;
  }

  .ticketlabel {
    font-size: 16px;
  }
  .value{
    font-size: 16px;
  }
  
  .priority-badge {
    font-size: 12px;  /* Adjust the font size for larger screens */
    padding: 5px 10px;
    border-radius: 12px;
  }
  .ticket-field.attachment,
  .ticket-field.assignedto,
  .ticket-field.CreatedAt {
    justify-content: flex-end;
    font-size: 16px;
    
  }

  .ticket-field.attachment .label,
  .ticket-field.assignedto .label,
  .ticket-field.CreatedAt .label {
    font-weight: bold;
    text-align: right;
    font-size: 16px;
  }

  .ticket-field.attachment .value,
  .ticket-field.assignedto .value,
  .ticket-field.CreatedAt .value {
    text-align: right;
    font-size: 16px;
  }
}
@media  (min-width: 1450px) {

  .priority-badge {
    font-size: 14px;  /* Adjust the font size for larger screens */
    padding: 5px 10px;
    border-radius: 14px;
  }
  .ticket-detail-container {
    padding: 20px;
    font-size: 18px;
  }

  .ticket-title2 {
    font-size: 22px;
  }

  .ticket-row {
    font-size: 18px;
  }

  .label, .value {
    font-size: 18px;
  }

  .ticket-field.attachment,
  .ticket-field.assignedto,
  .ticket-field.CreatedAt {
    justify-content: flex-end;
    font-size: 18px;
  }

  .ticket-field.attachment .label,
  .ticket-field.assignedto .label,
  .ticket-field.CreatedAt .label {
    text-align: right;
    
  }
  .ticket-field.attachment .value,
  .ticket-field.assignedto .value,
  .ticket-field.CreatedAt .value {
    text-align: right;
    font-size: 18px;
  }
}


/* Escalate media query is coded here */
@media (min-width: 769px) and (max-width: 1024px) {
  
.fontsize-row{
  font-size: 12px;
}
}

@media (min-width: 1025px) and (max-width: 1240px) {
  
  .fontsize-row{
    font-size: 12px;
  }
  }

  @media (min-width: 1241px) and (max-width: 1400px) {
  
    .fontsize-row{
      font-size: 14px;
    }
    }

    @media (min-width: 1401px) and (max-width: 1600px) {
  
      .fontsize-row{
        font-size: 16px;
      }
      }