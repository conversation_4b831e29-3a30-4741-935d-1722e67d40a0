import React, { useEffect, useRef, useState } from "react";
import { Card } from "react-bootstrap";
import "./Team.css";
import { API_URLS } from "@/constants/apiConstants";
import { postRequest } from "@/services/apiService";
import Link from "next/link";

const Team = ({ data }) => {
  const sliderRef = useRef(null);
  const [teams, setTeams] = useState([]);
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);
  const [totalElements, setTotalElements] = useState(0); // Added totalElements state

  const limitSequence = [10, 20, 50, 100];
  const [imageErrors, setImageErrors] = useState({});

  const getInitials = (name) => {
    return name?.charAt(0)?.toUpperCase() || "U";
  };

  const handleImageError = (index) => {
    setImageErrors((prevErrors) => ({
      ...prevErrors,
      [index]: true,
    }));
  };

  // const scrollLeft = () => {
  //   if (sliderRef.current) {
  //     sliderRef.current.scrollBy({
  //       left: -sliderRef.current.clientWidth,
  //       behavior: "smooth",
  //     });
  //   }
  // };

  // const scrollRight = () => {
  //   if (sliderRef.current) {
  //     sliderRef.current.scrollBy({
  //       left: sliderRef.current.clientWidth,
  //       behavior: "smooth",
  //     });
  //   }
  // };

  const incrementLimit = () => {
    const currentIndex = limitSequence.indexOf(limit);
    if (currentIndex < limitSequence.length - 1) {
      setLimit(limitSequence[currentIndex + 1]);
      setOffset(0);
    }
  };

  const decrementLimit = () => {
    const currentIndex = limitSequence.indexOf(limit);
    if (currentIndex > 0) {
      setLimit(limitSequence[currentIndex - 1]);
      setOffset(0);
    }
  };

  useEffect(() => {
    const fetchTeams = async () => {
      try {
        const request = { limit, offset };
        const response = await postRequest(API_URLS.MY_TEAMS, request);

        setTotalElements(response?.totalElements || 0); // Set total elements from API

        const mappedTeams = response?.content?.map((employee) => ({
          id: employee?.empId,
          name: employee?.empName,
          dept: employee?.department,
          totalLeaves: employee?.totalLeaves,
          pendingLeaves: employee?.pendingLeaves,
          overtime: employee?.overTimeHours,
          profImg: process.env.NEXT_PUBLIC_S3_URL + employee?.imgUre,
        }));

        setTeams(mappedTeams);
      } catch (error) {
        console.error("Error fetching team members:", error);
      }
    };

    fetchTeams();
  }, [limit, offset]);

  const getPercentage = (totalLeaves, pendingLeaves) => {
    const percentage = ((pendingLeaves / totalLeaves) * 100).toFixed(0);
    return isNaN(percentage) ? 0 : percentage;
  };

  const currentPage = Math.floor(offset / limit) + 1;

  return (
    <div className="team-container">
      <div className="row">
        <div className="col-12 mb-3">
          <div className="d-flex justify-content-between align-items-center">
            <div className="pl-3 pl-md-5 font-size-lg">
              <h4 className="team-text">
                Your Team ({totalElements} members ) {/* Display total team count */}
              </h4>
              {/* <p>Page {currentPage}: {limit} items per page</p> */}
            </div>
            <div className="slider-buttons">
              <img
                src="/image/rightArrow.png"
                alt="Decrease Limit"
                className="slider-button left"
                onClick={decrementLimit}
              />
              <img
                src="/image/leftArrow.png"
                alt="Increase Limit"
                className="slider-button right"
                onClick={incrementLimit}
              />
            </div>
          </div>
          <div className="slider-container">
            <div className="slider" ref={sliderRef}>
              {teams.map((item, index) => (
                <div key={index} className="team-card">
                  <Link href={`./ProfileDetails?id=${btoa(item.id)}`}>
                    <Card className="h-100 shadow-sm">
                      <Card.Body className="container-team">
                        <div className="flex-grow-1">
                          <div className="d-flex align-items-center">
                            {!item.profImg || imageErrors[index] ? (
                              <div
                                className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                                style={{
                                  width: "50px",
                                  height: "50px",
                                  backgroundColor: "#ccc",
                                  color: "#fff",
                                  fontWeight: "bold",
                                }}
                              >
                                {getInitials(item.name)}
                              </div>
                            ) : (
                              <img
                                src={item.profImg}
                                alt={item.profImg}
                                className="rounded-circle me-2"
                                style={{ width: "50px", height: "50px" }}
                                onError={() => handleImageError(index)}
                              />
                            )}
                            <div>
                              <p className="mb-1 font-weight-bold">{item.name}</p>
                              <p className="text-muted mb-1">{item.dept}</p>
                            </div>
                          </div>
                          <div className="mt-3">
                            <p className="mb-1">
                              <strong>Total Leaves:</strong> {item.totalLeaves}
                            </p>
                            <p className="mb-1">
                              <strong>Pending Leaves:</strong> {item.pendingLeaves}
                            </p>
                            <p className="mb-1">
                              <strong>Overtime:</strong> {item.overtime}
                            </p>
                          </div>
                          <div className="d-flex justify-content-start mt-3">
                            <img src="/image/email.png" alt="Email" className="mr-3" />
                            <img src="/image/phone.png" alt="Phone" />
                          </div>
                        </div>
                        <div className="ml-3">
                          <div
                            className="pie-chart-team"
                            style={{
                              "--percentage": `${getPercentage(
                                item?.totalLeaves,
                                item?.pendingLeaves
                              )}%`,
                            }}
                          >
                            <div className="pie-chart-team-center">
                              {getPercentage(item?.totalLeaves, item?.pendingLeaves)}%
                            </div>
                          </div>
                          <p className="mt-3 small font-weight-bold">Pending Leaves</p>
                        </div>
                      </Card.Body>
                    </Card>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Team;
