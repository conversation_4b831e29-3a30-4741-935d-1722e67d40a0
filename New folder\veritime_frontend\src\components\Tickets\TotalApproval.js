
import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import TicketCard from './TicketCard';
import '../Tickets/TicketCard.css'


const TicketApproval = ({ count }) => {
  return (
    <>
      <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-3">
        <Link  href={"/approvals"}>
        <div
          className="card shadow-sm"
          style={{ height: "270px", cursor: "pointer" }}
        >
          <div className="card-body">
            <p className="text text-green fw-bold">Total Approved</p>

            <div style={{ position: 'relative', width: '100%', height: "250px" }}>
              <TicketCard count={count} countColor="text-green" />
            </div>
          </div>
        </div>
        </Link>
      </div>
    </>
  );
};

export default TicketApproval;

