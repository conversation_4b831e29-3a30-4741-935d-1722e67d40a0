package com.company.wfm.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.HospitalDTO;
import com.company.wfm.dto.HospitalFilterRequest;
import com.company.wfm.dto.HospitalSaveDto;
import com.company.wfm.dto.HospitalUpdateDetailsDTO;
import com.company.wfm.service.impl.HospitalService;

import jakarta.persistence.EntityNotFoundException;

@RestController
@RequestMapping("/api/v1/hospitals")
@CrossOrigin(origins = "*")
public class HospitalController {

	private static final Logger logger = LoggerFactory.getLogger(HospitalController.class);

	@Autowired
	private HospitalService hospitalService;

	@PostMapping("/getHospitals")
	public ResponseEntity<Page<HospitalDTO>> filterHospitals(@RequestBody HospitalFilterRequest filterRequest) {

		// Validate limit (size)
		int validatedLimit = Math.max(1, Math.min(filterRequest.getLimit(), 10000)); // Ensure limit is between 1 and
																						// 10000

		// Create PageRequest with validated offset and limit
		PageRequest pageRequest = PageRequest.of(filterRequest.getOffset(), validatedLimit);

		// Fetch filtered hospitals from service
		Page<HospitalDTO> result = hospitalService.filterHospitals(filterRequest.getProvinceIds(),
				filterRequest.getDistrictIds(), filterRequest.getSubDistrictIds(), filterRequest.getQuery(), pageRequest // Pass
																															// PageRequest
																															// here
		);

		return new ResponseEntity<>(result, HttpStatus.OK);
	}

	// delete

	@DeleteMapping("/delete/{id}")
	public ResponseEntity<String> softDeleteHospital(@PathVariable("id") Long id) {
		try {
			hospitalService.deleteHospital(id);
			return ResponseEntity.ok("Hospital deleted successfully.");
		} catch (EntityNotFoundException e) {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Hospital not found: " + e.getMessage());
		} catch (Exception e) {
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
					.body("Error deleting branch: " + e.getMessage());
		}
	}

	// update api
	@PutMapping("/update/{id}")
	public ResponseEntity<String> updateHospital(@PathVariable("id") Long id,
			@RequestBody HospitalUpdateDetailsDTO updatedHospitalDTO) {
		// Log the IDs received
		System.out.println("Updating Hospital: District ID = " + updatedHospitalDTO.getDistrictId() + ", Province ID = "
				+ updatedHospitalDTO.getProvinceId() + ", Sub-District ID = " + updatedHospitalDTO.getSubDistrictId());

		// Attempt to update the hospital directly with a DTO conversion
		boolean isUpdated = hospitalService.updateHospital(id, updatedHospitalDTO);

		// Check if the update was successful
		if (isUpdated) {
			return ResponseEntity.ok("Hospital updated successfully.");
		} else {
			return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Hospital not found.");
		}
	}

	@PostMapping("/save")
	public ResponseEntity<String> saveHospitalFromForm(@RequestBody HospitalSaveDto hospitalSaveDto) {
		try {
			hospitalService.saveHospitalFromForm(hospitalSaveDto);
			return new ResponseEntity<>("Hospital and Branch saved successfully.", HttpStatus.CREATED);
		} catch (Exception e) {
			return new ResponseEntity<>("Failed to save hospital: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping("/fromnew") // Ensure this matches your request
	public ResponseEntity<?> saveHospitalFrom(@RequestBody HospitalSaveDto hospitalSaveDto) {
		try {
			// hospitalService.saveHospitalFromForm(hospitalSaveDto);
			return new ResponseEntity<>("Hospital and Branch saved successfully.", HttpStatus.CREATED);
		} catch (Exception e) {
			return new ResponseEntity<>("Failed to save hospital: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@PostMapping("/upload")
	public ResponseEntity<String> uploadHospitalData(@RequestParam("file") MultipartFile file) {
		if (file.isEmpty()) {
			return new ResponseEntity<>("Please upload a valid Excel file.", HttpStatus.BAD_REQUEST);
		}

		try {
			hospitalService.uploadAndSaveHospitals(file);
			return new ResponseEntity<>("File uploaded and processed successfully!", HttpStatus.ACCEPTED);

		} catch (Exception e) {
			// Log error and return a failure response
			return new ResponseEntity<>("An error occurred while processing the file: " + e.getMessage(),
					HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
