import { useState } from "react";
import Swal from 'sweetalert2';
import { FaArrowLeft } from "react-icons/fa";
import { postRequestNoToken } from "../../services/apiService";
import { API_URLS } from "../../constants/apiConstants";
import Link from "next/link";

const SendLink = ({ setLinkSend, isLinkend }) => {
  const [email, setEmail] = useState("");


  const handleSendLink = async (event) => {
    event.preventDefault();
    try {
      const request = { email: email };

      const response = await postRequestNoToken(API_URLS.EMAIL_SENT, request);
      if (response) {
        Swal.fire({
          text: "A password reset link has been sent to your email.",
          icon: "success",
          confirmButtonText: "OK"
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href="/login"
          }
        });
      } else {
        throw new Error("API did not return a valid response.");
      }
    } catch (error) {
      // console.error("Error sending reset password link:", error);
      Swal.fire({
        title: "Error",
        text: "Failed to send reset link. Please try again.",
        icon: "error",
        confirmButtonText: "OK"
      });
    }
  };

 

  return (
    <div className="send-link-container">
      <form className="login-form">
        <h3>Forgot Your Password?</h3>

        <input
          type="email"
          className="form-control"
          id="email"
          placeholder="Enter email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          style={{
            borderRadius: '10px',
            // fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            color: '#4A4A4A',
            border: '1px solid #CCCCCC',
            padding: '10px',
            backgroundColor: "#F0F0F0",
            marginBottom:"30px"
          }}
        />

        <div className="d-flex flex-column gap-2">
          <button
            className="btn btn-primary"
            onClick={handleSendLink}
            style={{
              backgroundColor: "#0056b3",
              borderColor: "#0056b3",
              borderRadius: "8px",
              fontSize: "14px",
              // fontFamily: "Arial, sans-serif",
              padding: "10px 0",
            }}
          >
            Send link
          </button>
        </div>
        <Link href={"/login"}>
        <div className="back-button-container">
          <button className="back-button" type="button">
            <FaArrowLeft className="back-icon" />
          </button>
        </div>
        </Link>
      </form>
    </div>
  );
};

export default SendLink;
