package com.company.wfm.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

import org.hibernate.annotations.CreationTimestamp;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;


@Entity
@Data
@Table(name = "t_branch")
public class Branch implements Serializable {


    /**
	 *
	 */
	private static final long serialVersionUID = 1L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BRANCH_ID")
    private Long id;

    @Column(name = "BRANCH_NAME")
    private String branchName;

    @Column(name = "<PERSON><PERSON>CH_CODE")
    private String branchCode;

    @ManyToOne()
    @JoinColumn(name = "BRANCH_HEAD_ID", referencedColumnName = "EMP_ID", nullable = true)
    @JsonIgnore
    private Employee branchHead;

    @Column(name = "BRANCH_HEAD_NAME")
    private String branchHeadName;

    @Column(name = "branch_type")
    private String branchType;

    //@JsonIgnore
    //private Department department;

    @ManyToOne
    @JoinColumn(name = "COMPANY_ID", referencedColumnName = "id", nullable = true)
   // @JsonIgnore
    private FeederHospital company;

    @Column(name = "CREATED_AT")
    @CreationTimestamp
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_AT")
   // @UpdateTimestamp
    private LocalDateTime updatedAt;

    @Column(name = "CREATED_BY")
    private Long createdBy;

    @Column(name = "UPDATED_BY")
    private Long updatedBy;

    @Column(name = "IS_ACTIVE")
    private Integer isActive;

    @Column(name="clustor_no")
    private String cluster;

    @Column(name = "leave_credit_day")
    private  Integer leaveCreditDay;

    @Column(name = "timezone")
    private String timeZone;


    public boolean isActive() {
        return this.isActive != null && this.isActive == 1;
    }

    // Setter for boolean isActive
    public void setActive(boolean active) {
        this.isActive = active ? 1 : 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id); // Use only the primary key or a unique field
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Branch branch = (Branch) obj;
        return Objects.equals(this.id, branch.id); // Compare only by primary key
    }


}