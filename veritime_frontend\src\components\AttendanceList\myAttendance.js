import React, { useEffect, useState } from "react";
import TableFilter from "../../common-components/TableFilter";
import { API_URLS } from "@/constants/apiConstants";
import { getRequest } from "@/services/apiService";

const MyAttendance = ({ fromDate, toDate, goButton }) => {
  const [filteredRows, setFilteredRows] = useState([]);

  useEffect(() => {
    fetchAttendance();
  }, [goButton]);

  const fetchAttendance = async () => {
    try {
      const response = await getRequest(
        API_URLS.BIOMETRIC_LIST(fromDate, toDate)
      );
      if (response) {
        const formattedRows = response.map((item, index) => ({
          srno: index + 1,
          id: index + 1,
          Name: item.empName,
          Date: item.date,
          "Punch Time": item.punchInTime,
          "Created At": item.createdDate,
        }));

        setFilteredRows(formattedRows);
      }
    } catch (error) {}
  };
  const columns = [
    { field: "srno", headerName: "Sr.no", width: 120 },
    { field: "Name", headerName: "Name", width: 350 },
    { field: "Date", headerName: "Date", width: 300 },
    { field: "Punch Time", headerName: "Punch Time", width: 120 },
    { field: "Created At", headerName: "Created On", width: 300 },
  ];

  return (
    <div id="tableWrapper">
      <TableFilter columns={columns} rows={filteredRows} />
    </div>
  );
};

export default MyAttendance;
