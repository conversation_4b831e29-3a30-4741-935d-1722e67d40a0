package com.company.wfm.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.UserTokenService;

@RestController
@RequestMapping("/api/v1/dashboard/supervisor")
@CrossOrigin(origins = "*")
public class SupervisorDashboardController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserTokenService tokenService;

    @GetMapping
    public Map<String, Object> getSupervisorSummary() {

        Long supervisorEmpId = tokenService.getEmployeeIdFromToken();

        Long companyId = jdbcTemplate.queryForObject(
                "SELECT COMPANY_ID FROM t_employee WHERE EMP_ID = ?",
                Long.class, supervisorEmpId);

        Map<String, Object> response = new HashMap<>();

        List<Long> supervisedEmpIds = jdbcTemplate.queryForList(
                "SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?",
                Long.class, supervisorEmpId
        );

        if (supervisedEmpIds.isEmpty()) {
            response.put("totalEmployees", Map.of("totalCount", 0, "percentage", 0));
            response.put("totalEmployeesPresent", Map.of("totalCount", 0, "percentage", 0));
            response.put("totalEmployeesOnLeave", Map.of("totalCount", 0, "percentage", 0));
            response.put("totalEmployeesClockedIn", Map.of("totalCount", 0, "percentage", 0));
            response.put("scheduleWiseReport", new ArrayList<>());
            response.put("departmentWiseReport", new HashMap<>());
            response.put("ticketWiseReport", new HashMap<>());
            response.put("totalDepartments", new HashMap<>());
            response.put("ticketCreated", 0);
            response.put("ticketAssigned", 0);
            response.put("teamsCount", 0);
            return response;
        }

        Integer totalEmployees = supervisedEmpIds.size();
        Integer totalEmployeesInCompany = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee WHERE company_id = ?", Integer.class, companyId);

        Double employeePercentage = (totalEmployeesInCompany > 0)
                ? (totalEmployees * 100.0) / totalEmployeesInCompany : 0.0;

        // SQL Queries with parameterized IN clause
        String sqlPresent = "SELECT COUNT(DISTINCT EMP_ID) FROM t_attendance WHERE EMP_ID IN (?) AND DATE = CAST(GETDATE() AS DATE)";
        Integer totalEmployeesPresent = Optional.ofNullable(jdbcTemplate.queryForObject(
                sqlPresent, Integer.class, supervisedEmpIds.toArray()
        )).orElse(0);

        Double employeesPresentPercentage = (totalEmployees > 0)
                ? (totalEmployeesPresent * 100.0) / totalEmployees : 0.0;

        String sqlLeave = "SELECT COUNT(DISTINCT EMP_ID) FROM t_employee_leave_history WHERE EMP_ID IN (?) AND start_date <= GETDATE() AND end_date >= GETDATE()";
        Integer totalEmployeesOnLeave = Optional.ofNullable(jdbcTemplate.queryForObject(
                sqlLeave, Integer.class, supervisedEmpIds.toArray()
        )).orElse(0);

        Double employeesOnLeavePercentage = (totalEmployees > 0)
                ? (totalEmployeesOnLeave * 100.0) / totalEmployees : 0.0;

        String sqlClockedIn = "SELECT COUNT(DISTINCT EMP_ID) FROM t_attendance WHERE EMP_ID IN (?) AND CHECK_IN_TIME IS NOT NULL AND DATE = CAST(GETDATE() AS DATE)";
        Integer totalEmployeesClockedIn = Optional.ofNullable( jdbcTemplate.queryForObject(
                sqlClockedIn, Integer.class, supervisedEmpIds.toArray()
        )).orElse(0);

        Double employeesClockedInPercentage = (totalEmployees > 0)
                ? (totalEmployeesClockedIn * 100.0) / totalEmployees : 0.0;

        String sqlScheduleWise = "SELECT CONCAT(CONVERT(VARCHAR(8), ts.start_time, 108), '-', CONVERT(VARCHAR(8), ts.end_time, 108)) AS name, COUNT(es.EMP_ID) AS value " +
                "FROM t_time_slot ts " +
                "JOIN t_employee_schedule es ON ts.time_slot_id = es.actual_shift " +
                "WHERE es.EMP_ID IN (?) " +
                "GROUP BY ts.start_time, ts.end_time";
        List<Map<String, Object>> scheduleWiseReport = jdbcTemplate.queryForList(
                sqlScheduleWise, supervisedEmpIds.toArray()
        );

        String sqlDepartmentWise = "SELECT d.department_name AS name, COUNT(e.EMP_ID) AS value " +
                "FROM t_department d JOIN t_employee e ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
                "WHERE e.EMP_ID IN (?) GROUP BY d.department_name";
        List<Map<String, Object>> departmentWiseReport = jdbcTemplate.queryForList(
                sqlDepartmentWise, supervisedEmpIds.toArray()
        );

        String sqlTicketWise = "SELECT b.branch_name AS name, COUNT(t.TICKET_ID) AS value " +
                "FROM t_branch b JOIN t_employee e ON b.branch_id = e.BRANCH_ID " +
                "JOIN t_ticket t ON e.EMP_ID = t.EMP_ID WHERE e.EMP_ID IN (?) " +
                "GROUP BY b.branch_name";
        List<Map<String, Object>> ticketWiseReport = jdbcTemplate.queryForList(
                sqlTicketWise, supervisedEmpIds.toArray()
        );

        String sqlTotalDepartments = "SELECT TOP 5 d.DEPARTMENT_NAME AS name, " +
                "COUNT(DISTINCT a.EMP_ID) AS department_count " +
                "FROM t_employee e " +
                "JOIN t_department d ON e.DEPARTMENT_ID = d.DEPARTMENT_ID " +
                "JOIN t_attendance a ON e.EMP_ID = a.EMP_ID " +
                "WHERE e.EMP_ID IN (?) " +
                "AND a.DATE = CAST(GETDATE() AS DATE) " +
                "GROUP BY d.DEPARTMENT_NAME " +
                "HAVING COUNT(DISTINCT a.EMP_ID) > 0 " +
                "ORDER BY department_count DESC";

        List<Map<String, Object>> departmentsData = jdbcTemplate.queryForList(
                sqlTotalDepartments, supervisedEmpIds.toArray()
        );

        Map<String, Object> totalDepartments = new HashMap<>();
        List<String> axisLabels = new ArrayList<>();
        List<List<Integer>> departmentAxes = new ArrayList<>();

        int deptIndex = 1;
        for (Map<String, Object> department : departmentsData) {
            String departmentName = (String) department.get("name");

            axisLabels.add(departmentName);

            List<Integer> axisValues = Arrays.asList(0, 10, 5, 2, 10, 30, 45, 35, 25, 10, 5, 0);
            departmentAxes.add(axisValues);

            totalDepartments.put("axis" + deptIndex, axisValues);
            deptIndex++;
        }

        for (int i = 0; i < axisLabels.size(); i++) {
            totalDepartments.put("label" + (i + 1), Collections.singletonList(axisLabels.get(i)));
        }

        totalDepartments.put("axisLbl", Arrays.asList("Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"));

        response.put("totalDepartments", totalDepartments);

        response.put("totalEmployees", Map.of("totalCount", totalEmployees, "percentage", employeePercentage));
        response.put("totalEmployeesPresent", Map.of("totalCount", totalEmployeesPresent, "percentage", employeesPresentPercentage));
        response.put("totalEmployeesOnLeave", Map.of("totalCount", totalEmployeesOnLeave, "percentage", employeesOnLeavePercentage));
        response.put("totalEmployeesClockedIn", Map.of("totalCount", totalEmployeesClockedIn, "percentage", employeesClockedInPercentage));
        response.put("scheduleWiseReport", scheduleWiseReport);
        response.put("departmentWiseReport", departmentWiseReport);
        response.put("ticketWiseReport", ticketWiseReport);
        response.put("ticketCreated", 23);
        response.put("ticketAssigned", 29);
        response.put("teamsCount", 3);

        return response;
    }

}