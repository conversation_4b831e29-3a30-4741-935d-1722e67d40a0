import { convertAndValidateTimeRange } from '@/services/utils'
import React from 'react'

function ScheduleReport({data}) {
    
    return (
        <div className="col-12 col-lg-6 col-xl-6 col-md-12 mb-3">
            <div className="card shadow-sm">
                <div className="card-body">
                    <h2 className="h5">{data.widgetName}</h2>
                    
                    <div className="d-flex justify-content-around">
                        {data.value.map((item,index) => (
                            <div  key ={index} className="text-center">
                                <div className="pie-chart" 
                                style={{
                                    '--percentage': `${item.value.toFixed(2)}%`
                                  }}
                                  data-percentage={`${item.value.toFixed(2)}%`}
                                >
                                    <div className="pie-chart-center">{item?.value}%</div>
                                </div>
                                <p className="small">{convertAndValidateTimeRange(item?.name)}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ScheduleReport