import React, { useState, useCallback } from "react";
import { <PERSON><PERSON>, Button, Form, Col, Alert } from "react-bootstrap";
import { postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showSuccessAlert2, showErrorAlert } from "@/services/alertService";
import { FaDownload } from "react-icons/fa";
import "./CreateExcelModals.css";

const CreateExcelModalHospital = ({
  show,
  handleClose,
  fetchItems,
  fetchHolidays,
  downloadType,
}: any) => {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState("");
  const fileMap: { [key: string]: string } = {
    holiday: "/Holiday_Format.xlsx",
    hospital: "/Hospital_Format.xlsx",
    Employee: "/Internaldemo_Empoyee1.xlsx",
    leave: "/employee-leave-data-upload.xlsx", // Add this line
  };
  const handleFileUpload = (event: any) => {
    const selectedFile = event.target.files[0];
    if (selectedFile && selectedFile.type == "xls") {
      setError("Please upload a valid file!");
      setFile(null);
    } else {
      setError("");
      setFile(selectedFile);
    }
  };

  const uploadExcelFile = async () => {
    if (!file) {
      setError("Please select a file first!");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    setIsUploading(true);
    try {
      const response = await postRequest(
        API_URLS.UPLOAD_EXCEL_EMPLOYEE,
        formData
      );
      if (response?.status === 200 || response?.status === 201) {
        showSuccessAlert2(
          response.data.message || " Employee Created successfully."
        );
        handleClose();
        setFile(null);
        fetchItems();
        fetchHolidays();
      } else {
        showErrorAlert(
          response.data.message || "Something went wrong while uploading excel."
        );
      }
    } catch (error: any) {
      console.error("Error submitting Excel file:", error);

      const errorMessage =
        error.response?.data?.message ||
        error.response?.data ||
        "An unexpected error occurred.";

      showErrorAlert(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDownload = useCallback(() => {
    const fileUrl = fileMap[downloadType];
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = `${
      downloadType.charAt(0).toUpperCase() + downloadType.slice(1)
    }_Format.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [downloadType]);

  return (
    <Modal show={show} onHide={handleClose} className="create-excel-modal">
      <Modal.Header closeButton>
        <Modal.Title>Employee Upload</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}
        <Col md={3}>
          <Form.Group controlId="uploadExcel" className="mb-3">
            <Form.Control
              type="file"
              onChange={handleFileUpload}
              className="upload-input"
            />
            <div style={{ marginTop: "15px" }}>
              <Button
                variant="primary"
                onClick={uploadExcelFile}
                disabled={isUploading}
                className="upload-button"
                style={{ width: "190px" }}
              >
                {isUploading ? "Uploading..." : "Upload Employee"}
              </Button>
            </div>
          </Form.Group>
        </Col>

        <div className="note-container">
          <h1 className="note-title">Note: Download the sample file.</h1>

          <div onClick={handleDownload} className="download-container">
            <FaDownload size={30} className="download-icon" />
            <span className="download-text">
              Download{" "}
              {downloadType.charAt(0).toUpperCase() + downloadType.slice(1)}{" "}
              Format
            </span>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default CreateExcelModalHospital;
