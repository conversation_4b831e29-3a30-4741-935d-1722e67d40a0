package com.company.wfm.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.EmployeeCalendarService;
import com.company.wfm.service.UserTokenService;

@RestController
@RequestMapping("/api/v1/employee")
@CrossOrigin(origins = "*")
public class EmployeeCalendarController {

    @Autowired
    private EmployeeCalendarService employeeCalendarService;

    @Autowired
    private UserTokenService userTokenService;

    @PostMapping("/calendar-month")
    public ResponseEntity<List<Map<String, Object>>> getEmployeeCalendar(@RequestBody Map<String, Integer> payload) {
        Long empId = userTokenService.getEmployeeIdFromToken();
        int year = payload.get("year");
        int month = payload.get("month");

        List<Map<String, Object>> calendarData = employeeCalendarService.getEmployeeCalendar(empId, year, month);
        return ResponseEntity.ok(calendarData);
    }
}
