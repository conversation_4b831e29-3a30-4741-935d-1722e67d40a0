import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Badge } from "react-bootstrap";
import { getRequest, postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";

interface Department {
  departmentId: string;
  departmentName: string;
}

interface Branch {
  branchId: string;
  branchName: string;
}

const ScheduleDetailModal = ({ show, handleClose, rowData }: any) => {
  const [deptList, setDeptList] = useState<Department[]>([]);
  const [branchList, setBranchList] = useState<Branch[]>([]);
  const [data, setData] = useState({ branch: "", department: "" });
  const [timeSlots, setTimeSlots] = useState<any[]>([]);

  useEffect(() => {
    const fetchDeptList = async () => {
      const data = await getRequest(API_URLS.DEPT_LOOKUP);
      if (data) setDeptList(data);
    };
    const fetchBranchList = async () => {
      const payload = { type: "select" };
      const data = await postRequest(API_URLS.GET_BRANCH, payload);
      if (data) setBranchList(data);
    };

    fetchDeptList();
    fetchBranchList();
  }, []);

  useEffect(() => {
    if (rowData) {
      setData({
        branch: rowData?.branchId || "",
        department: rowData?.departmentId || "",
      });
      setTimeSlots(rowData.timeSlots || []);
    }
  }, [rowData]);

  const labelStyle = {
    fontWeight: "bold",
    whiteSpace: "nowrap" as "nowrap",
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Schedule Details</Modal.Title>
      </Modal.Header>
      <Modal.Body
        style={{ overflowY: "auto", maxHeight: "400px", padding: "10px" }}
      >
        {/* DEPARTMENT NAME */}
        <div style={{ marginBottom: "15px" }}>
          <div style={labelStyle}>Department</div>
          <div>
            {deptList.find((dept) => dept.departmentId === data.department)
              ?.departmentName || "N/A"}
          </div>
        </div>
        {/* GREY BORDER */}
        <hr style={{ borderColor: "#a0a0a0" }} />

        {/* FACILITY NAME */}
        <div style={{ marginBottom: "15px" }}>
          <div style={labelStyle}>Facility</div>
          <div>
            {branchList.find((branch) => branch.branchId === data.branch)
              ?.branchName || "N/A"}
          </div>
        </div>
        {/* GREY BORDER */}
        <hr style={{ borderColor: "#a0a0a0" }} />
        {/* TIME SLOT SECTION */}
        <div style={{ marginBottom: "15px" }}>
          <div style={labelStyle}>Slots</div>
          <div style={{ marginBottom: "10px" }}></div>{" "}
          {/* This will add space */}
          <div style={{ display: "flex", flexWrap: "wrap", gap: "10px" }}>
            {timeSlots.map((slot: any, index: number) => {
              // Determine badge color based on isDefault

              //const badgeVariant = slot.isDefault === 1 ? "#e0e0e0" : "success"; // dark grey for default, green for non-default

              // Determine badge color based on isDefault
              const isDefault = slot.isDefault === 1;
              // const badgeVariant = isDefault ? "#e0e0e0" : "success";
              const badgeVariant = isDefault ? "success" : "#e0e0e0";

              return (
                <Badge
                  key={index}
                  pill
                  bg={badgeVariant} //  Bootstrap variant
                  style={{
                    backgroundColor:
                      //  slot.isDefault === 1 ? "#e0e0e0" : "#d4edda", // Light grey for default, light green for non-default
                      slot.isDefault === 1 ? "#d4edda" : "#e0e0e0", // green for default, light grey for non-default

                    //color: isDefault ? "#333" : "#fff", // Dark text for grey, white text for green
                    color: isDefault ? "#fff" : "#333", // Dark text for grey, white text for green

                    // color: "#333", // Text color black
                    borderRadius: "8px", // Rounded corners
                    padding: "5px 10px", // Padding similar
                    fontSize: "14px",
                    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)", // Subtle shadow to match
                    border:
                      slot.isDefault === 1
                        ? "1px solid #28a745" // Border for default
                        : "1px solid #bcbcbc", // Border for non-default
                  }}
                >
                  {`${slot.startTime.slice(0, 5)} - ${slot.endTime.slice(
                    0,
                    5
                  )}`}
                </Badge>
              );
            })}
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={handleClose}>Close</Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ScheduleDetailModal;
