import React, { useState, useRef } from 'react';
//import 'bootstrap/dist/css/bootstrap.min.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Form, InputGroup, Button } from 'react-bootstrap';
import { BsClock } from 'react-icons/bs';
import { postRequest, postRequestWithSecurity } from '../../services/apiService';
import { API_URLS } from '../../constants/apiConstants';
import { showSuccessAlert2 } from '../../services/alertService';
import { getFormattedDate } from '@/services/utils';
import CustomCalendar from '../../components/CustomCalender/CustomCalender';

const AttendanceRegularisation = ({ closeApplyModal }) => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedDates, setSelectedDates] = useState([]); // For multiple dates
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [reason, setReason] = useState('');
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const formatDate = (date) => {
    return getFormattedDate(date);
  };

  const handleStartTimeChange = (time) => {
    setStartTime(time);
    if (errors.startTime) {
      setErrors((prevErrors) => ({ ...prevErrors, startTime: '' }));
    }
  };

  const handleEndTimeChange = (time) => {
    setEndTime(time);
    if (errors.endTime) {
      setErrors((prevErrors) => ({ ...prevErrors, endTime: '' }));
    }
  };

  const formatTimeRange = (startTime, endTime) => {
    const formatTime = (time) =>
      time
        .toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true })
        .toUpperCase();

    return `${formatTime(startTime)}-${formatTime(endTime)}`;
  };

  const validateForm = () => {
    const newErrors = {};

    if (!selectedDates.length) {
      newErrors.selectedDate = 'At least one date is required';
    }

    if (!startTime) {
      newErrors.startTime = 'Start Time is required';
    }

    if (!endTime) {
      newErrors.endTime = 'End Time is required';
    }

    if (startTime && endTime) {
      const start = startTime.getHours() * 60 + startTime.getMinutes();
      const end = endTime.getHours() * 60 + endTime.getMinutes();

      if (start >= end) {
        newErrors.endTime = 'End Time must be greater than Start Time';
      }
    }

    if (!reason.trim()) {
      newErrors.reason = 'Reason is required';
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (validateForm()) {
      setLoading(true);
      const regulariseData = {
        multiDate: selectedDates.join(','),
        timeRange: formatTimeRange(startTime, endTime),
        reason: reason.trim(),
      };

      try {
        const data = await postRequestWithSecurity(API_URLS.CREATE_REGULARIZE_ATTENDANCE, regulariseData);
        if (data) showSuccessAlert2('Attendance Modification successful');
        closeApplyModal();
      } catch (error) {
      } finally {
        setLoading(false);
        closeApplyModal();
      }
    } else {
    }
  };

  return (
    <div className="container mt-4" onClick={(e) => e.stopPropagation()}>
      <div className="row justify-content-center">
        <div className="col-12 col-md-8 col-lg-8">
          <div className="card shadow-sm border-0" style={{ borderRadius: '8px' }}>
            <div className="card-body p-4 cldr-modal-body" >
              <h5 className="card-title text-center fw-bold mb-4" style={{ fontSize: '22px' }}>
                Attendance Modification
              </h5>
              <label htmlFor="ticketSubject" className="ticket-text-primary" style={{marginLeft:'33%'}}>
                <span className="text-danger">The fields with * marks are mandatory</span>
          </label>
              <span onClick={closeApplyModal} style={{ position: 'absolute', top: 10, color: 'black', right: 10, cursor: 'pointer' }}>
                X
              </span>

              <div style={{ display: "flex", flexDirection: "row", justifyContent: 'space-evenly' }}>
                <div>
                  <CustomCalendar setSelectedDates={setSelectedDates} />
                </div>
                <div style={{ marginLeft: "10px" }}>
                  <Form.Group className="mb-3" controlId="formStartTime">
                    <Form.Label className="fw-bold" style={{ fontSize: '14px', marginTop: "30px" }}> <label htmlFor="ticketSubject" className="ticket-text-primary">
                    Start Time<span className="text-danger">*</span>
          </label></Form.Label>
                    <InputGroup>
                      <DatePicker
                        selected={startTime}
                        onChange={handleStartTimeChange}
                        className={`form-control ${errors.startTime ? 'is-invalid' : ''}`}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={10}
                        timeCaption="Time"
                        dateFormat="h:mm aa"
                        placeholderText="Select Start Time"
                      />
                      <InputGroup.Text style={{ cursor: 'pointer' }}>
                        <BsClock />
                      </InputGroup.Text>
                      {errors.startTime && <div className="invalid-feedback d-block">{errors.startTime}</div>}
                    </InputGroup>
                  </Form.Group>

                  <Form.Group className="mb-3" controlId="formEndTime">
                    <Form.Label className="fw-bold" style={{ fontSize: '14px' }}> <label htmlFor="ticketSubject" className="ticket-text-primary">
                    End Time<span className="text-danger">*</span>
          </label></Form.Label>
                    <InputGroup>
                      <DatePicker
                        selected={endTime}
                        onChange={handleEndTimeChange}
                        className={`form-control ${errors.endTime ? 'is-invalid' : ''}`}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={10}
                        timeCaption="Time"
                        dateFormat="h:mm aa"
                        placeholderText="Select End Time"
                      />
                      <InputGroup.Text style={{ cursor: 'pointer' }}>
                        <BsClock />
                      </InputGroup.Text>
                      {errors.endTime && <div className="invalid-feedback d-block">{errors.endTime}</div>}
                    </InputGroup>
                  </Form.Group>

                  <Form.Group className="mb-4" controlId="formReason">
                    <Form.Label className="fw-bold" style={{ fontSize: '14px' }}> <label htmlFor="ticketSubject" className="ticket-text-primary">
              Reason<span className="text-danger">*</span>
          </label></Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      placeholder="Reason"
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      className={`${errors.reason ? 'is-invalid' : ''}`}
                    />
                    {errors.reason && <div className="invalid-feedback">{errors.reason}</div>}
                  </Form.Group>

                </div>

              </div>

              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%', marginTop: "-12px" }}>
                <Button
                  onClick={handleSubmit}
                  className="w-20"
                  style={{ backgroundColor: '#2B2E4A', fontSize: '16px', padding: '10px', width: '25%', marginRight: '10px' }}
                  disabled={loading}
                >
                  {loading ? 'Submitting...' : 'Submit'}
                </Button>

                <Button
                  onClick={closeApplyModal}
                  className="btn"
                  style={{ marginTop: 5, backgroundColor: 'white', color: '#2B2E4A', fontSize: '16px', padding: '10px', width: '25%' }}
                >
                  Cancel
                </Button>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AttendanceRegularisation;

