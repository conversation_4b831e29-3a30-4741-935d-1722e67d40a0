import "../css/style.css";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

const ActionButtons = ({ handleChange, value }) => {
  function a11yProps(index) {
    return {
      id: `full-width-tab-${index}`,
      "aria-controls": `full-width-tabpanel-${index}`,
    };
  }

  return (
    <div className="action-buttons">
      <Tabs
        value={value}
        onChange={handleChange}
        indicatorColor="secondary"
        textColor="inherit"
        variant="fullWidth"
        aria-label="full width tabs example"
      >
        <Tab
          label="Leave Request"
          value="leave_request"
          {...a11yProps(0)}
          sx={{
            bgcolor: value === "leave_request" ? "#e0e0e0" : "white", // Grey background when active
            color: value === "leave_request" ? "black" : "inherit", // Change text color if active
            "&:hover": {
              bgcolor: "#d5d5d5", // Slightly darker grey on hover
            },
          }}
        />
        <Tab
          label="Regularisation"
          value="regularisation"
          {...a11yProps(1)}
          sx={{
            bgcolor: value === "regularisation" ? "#e0e0e0" : "white", // Grey background when active
            color: value === "regularisation" ? "black" : "inherit", // Change text color if active
            "&:hover": {
              bgcolor: "#d5d5d5", // Slightly darker grey on hover
            },
          }}
        />
        <Tab
          label="Shift Change"
          value="shift_change"
          {...a11yProps(2)}
          sx={{
            bgcolor: value === "shift_change" ? "#e0e0e0" : "white", // Grey background when active
            color: value === "shift_change" ? "black" : "inherit", // Change text color if active
            "&:hover": {
              bgcolor: "#d5d5d5", // Slightly darker grey on hover
            },
          }}
        />
      </Tabs>
    </div>
  );
};

export default ActionButtons;
