package com.company.wfm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.EncryptionRequestDTO;
import com.company.wfm.util.EncryptionUtil;


@RestController
@RequestMapping("/api/v1/encryption")
@CrossOrigin(origins = "*")
public class EncryptionController {

	@Autowired
	private EncryptionUtil encryptionUtil;

    @PostMapping("/encrypt")
    public ResponseEntity<String> encryptData(@RequestBody EncryptionRequestDTO encryptionRequest) {
        try {
            // Encrypt the data from the EncryptionRequest object
            String encryptedText = encryptionUtil.encrypt(encryptionRequest.getPlainText(),encryptionUtil.generateKey());
            return ResponseEntity.ok(encryptedText); // Return encrypted text
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error occurred while encrypting: " + e.getMessage());
        }
    }

    // Endpoint to decrypt data
    @PostMapping("/decrypt")
    public ResponseEntity<String> decryptData(@RequestBody EncryptionRequestDTO encryptionRequest) {
        try {
            // Decrypt the data from the EncryptionRequest object
            String decryptedText = encryptionUtil.decrypt(encryptionRequest.getEncryptedText(),encryptionUtil.generateKey());
            return ResponseEntity.ok(decryptedText);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error occurred while decrypting: " + e.getMessage());
        }
    }
}
