
@import url('../../css/style.css');

.MuiDataGrid-cell{
  display: flex;
}

.row {
  margin-right: 0;
  margin-left: 0;
  display: flex;
  flex-direction: row;
}

.card-department {
  width: 203%;
}

.col,
.col-auto {
  padding-right: 5px;
  padding-left: 5px;
}

.text-nowrap {
  white-space: nowrap;
}


body {
  /* font-family: 'Barlow', sans-serif !important; */
}

.header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--blue6);
  width: calc(100% - 100px);
}

/* .badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: blue;
  color: white;
  border-radius: 50%;
  padding: 5px 10px;
  font-size: 12px;
} */

.dashboard-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  width: 90%;
}

.dashboard-header {
  display: flex;
  flex-direction: column;
}

.dashboard-header h1 {
  margin: 0;
  font-size: 24px;
}

.dashboard-header p {
  margin: 5px 0 0 0;
  font-size: 16px;
  color: var(--red2);
}

.filter-date {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  flex-direction: row;
}

.filter-date-icon {
  width: 40px;
  height: 35px;
  margin-right: 10px;
}

.filter-date-text {
  display: flex;
  flex-direction: column;
  margin-right: 40px;
}

.filter-date-title {
  font-size: 16px;
  font-weight: bold;
}

.filter-date-subtitle {
  font-size: 14px;
  color: var(--red2);
}

/* .filter-down-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  } */

.calendar-container {
  position: absolute;
  top: 140px;
  z-index: 8;
  padding-right: 30px;
}

.total-card {
  background-color: var(--red14);
  border: 1px solid #ddd;
  border-radius: 10px;
  padding: 30px;
  margin-right: 10px;
  width: 200px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.total-card-content {
  text-align: center;
}

.total-number {
  font-size: 38px;
  font-weight: bold;
  color: var(--grey1);
}

.total-description {
  font-size: 16px;
  color: var(--grey2);
  margin-top: 5px;
}

.total-percentage {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.percentage-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.total-percentage span {
  font-size: 14px;
  color: var(--red2);
}

.dashboard-container {
  display: flex;
  flex-direction: "row";
}


.schedule-report-container {
  width: "80%";
  height: "300px";
  padding: 2%;
  box-sizing: border-box;
  background-color: "white";
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.schedule-report-container h2 {
  text-align: center;
  font-size: 2.5vh;
  color: var(--grey1);
  margin-bottom: 2%;
}

.chart-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 80%;
}

.chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
  height: 100%;
}

.pie-chart {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: conic-gradient(var(--red13) var(--percentage), #ffcc80 0);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5%;
  margin-right: 10px;
  margin-top: 10px;
}

.pie-chart::before {
  content: '';
  position: absolute;
  width: 70%;
  height: 70%;
  background: white;
  border-radius: 50%;
}

.pie-chart-center {
  position: absolute;
  font-size: 2.5vh;
  font-weight: bold;
  color: var(--grey1);
}

.chart p {
  font-size: 2vh;
  color: #555;
  margin-top: 0;
}

.pie-chart[data-percentage="81%"] {
  --percentage: 81%;
}

.pie-chart[data-percentage="90%"] {
  --percentage: 90%;
}

.pie-chart[data-percentage="62%"] {
  --percentage: 62%;
}

.schedule-report-container-department {
  width: "100%";
  height: "300px";
  padding: 2%;
  box-sizing: border-box;
  background-color: "white";
  margin-right: "20px";
}

.pie-chart2 {
  width: 130px;
  height: 130px;
  border-radius: 50%;
  background: conic-gradient(#5a2cd0 0% 20%,
      #bfb4ed 20% 40%,
      #dcd3f4 40% 60%,
      #9c7de1 60% 80%,
      #c7b5ed 80% 100%);
}

.c1 {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.App {
  text-align: center;
  padding: 20px;
}

.App-header {
  margin-bottom: 20px;
}


.chart-title2 {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}

.box-shadow {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.bg-custom {
  background-color: var(--blue6);
}





@media (max-width: 1200px) {
  .pie-chart {
    height: 130px;
    width: 130px;
  }
}

@media (max-width: 720px) {
  .pie-chart {
    height: 100px;
    width: 87px;
  }

  .card-department {
    width: 100%;
  }
}

.pie-chart-team {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: conic-gradient(var(--red13) var(--percentage), #ffcc80 0);
  display: flex;
  align-items: center;
  justify-content: center;
}
.pie-chart-team-center {
  position: absolute;
  font-size: 1.7vh;
  font-weight: bold;
  color: var(--grey1);
}
.pie-chart-team::before {
  content: '';
  position: absolute;
  width: 70%;
  height: 70%;
  background: white;
  border-radius: 50%;
}

.pie-chart-center-team {
  position: absolute;
  font-size: 1rem;
  font-weight: bold;
  color: var(--grey1);
}

.text {
  font-size: 20px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .font-size-lg {
    font-size: 1.2rem;
  }
}

.mr-3 {
  margin-right: 1rem;
}

.ml-3 {
  margin-left: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 1rem;
}

.team-container .slick-slide {
  margin: 0 10px;
}

.team-container .slick-list {
  padding: 0 30px;
}

.slick-dots li button:before {
  font-size: 10px;
}

.slick-dots li.slick-active button:before {
  color: var(--blue2);
}