package com.company.wfm.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.HolidayScheduleDTO;
import com.company.wfm.dto.HolidayScheduleRequestDTO;
import com.company.wfm.entity.HolidaySchedule;
import com.company.wfm.service.impl.HolidayScheduleService;

@RestController
@RequestMapping("/api/v1/holiday-schedule")
@CrossOrigin(origins = "*")
public class HolidayScheduleController {

    @Autowired
    private HolidayScheduleService holidayScheduleService;

    @PostMapping("/save")
    public ResponseEntity<HolidayScheduleDTO> saveHolidaySchedule(@RequestBody HolidayScheduleDTO holidayScheduleDTO) {
        HolidayScheduleDTO savedHolidaySchedule = holidayScheduleService.saveHolidaySchedule(holidayScheduleDTO);
        return ResponseEntity.ok(savedHolidaySchedule);
    }

    @PutMapping("/update/{id}")
    public ResponseEntity<HolidaySchedule> updateHolidaySchedule(
            @PathVariable Long id,
            @RequestBody HolidayScheduleDTO holidayScheduleDTO) {
        HolidaySchedule updatedHolidaySchedule = holidayScheduleService.updateHolidaySchedule(id, holidayScheduleDTO);
        return ResponseEntity.ok(updatedHolidaySchedule);
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> deleteHolidaySchedule(@PathVariable Long id) {
        holidayScheduleService.softDeleteHolidaySchedule(id);
        return ResponseEntity.ok("Your data has been deleted successfully.");
    }

    //convert into post method
    @PostMapping("/list")
    public ResponseEntity<Page<HolidayScheduleDTO>> getAllHolidaySchedules(@RequestBody HolidayScheduleRequestDTO requestDTO) {

        // Call the service method to get holiday schedules with employee names and images
        Page<HolidayScheduleDTO> holidaySchedules = holidayScheduleService.getHolidaySchedules(
                requestDTO.getFromDate(),
                requestDTO.getToDate(),
                requestDTO.getQuery(),
                requestDTO.getOffset(),
                requestDTO.getLimit()
        );

        return ResponseEntity.ok(holidaySchedules);
    }

    @PostMapping("/upload-excel")
    public ResponseEntity<String> uploadExcelFile(@RequestParam("file") MultipartFile file) {
        try {
            holidayScheduleService.uploadExcelFile(file);
            return ResponseEntity.ok("Excel file uploaded successfully.");
        }catch (IllegalArgumentException e) {  // Catch invalid data errors
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Invalid data: " + e.getMessage());
        }catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error uploading file: " + e.getMessage());
        }
    }
}
