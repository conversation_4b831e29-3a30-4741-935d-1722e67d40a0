package com.company.wfm.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.company.wfm.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.entity.Employee;
import com.company.wfm.entity.EmployeeSchedule;
import com.company.wfm.entity.Shift;
import com.company.wfm.entity.ShiftSwap;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.EmployeeScheduleRepository;
import com.company.wfm.repository.ShiftAssignmentRepository;
import com.company.wfm.repository.ShiftRepository;
import com.company.wfm.repository.ShiftSwapRepository;
import com.company.wfm.repository.TimeSlotRepository;
import com.company.wfm.repository.UserRepository;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.UserTokenService;

import jakarta.persistence.EntityNotFoundException;

@Service
@Transactional
public class ShiftChangeService {

    private static final Logger logger = LoggerFactory.getLogger(ShiftChangeService.class);
    @Autowired
    private ShiftSwapRepository shiftSwapRepository;
    @Autowired
    private ShiftRepository shiftRepository;
    @Autowired
    private TimeSlotRepository timeSlotRepository;

    @Autowired
    private EmailService emailService;

    @Autowired
    private CommonNotificationService notificationService;

    @Autowired
    private UserRepository userRepository;


    @Autowired
    private SmsProcessingService smsProcessingService;

    @Autowired
    private EmployeeScheduleRepository employeeScheduleRepository;
    private final EmployeeRepository employeeRepository;  // Assume this exists to fetch employee names

    private UserTokenService tokenService;

    public ShiftChangeService(ShiftSwapRepository shiftSwapRepository, ShiftAssignmentRepository shiftAssignmentRepository, ShiftRepository shiftRepository, EmployeeScheduleRepository employeeScheduleRepository, EmployeeRepository employeeRepository,
                              UserTokenService tokenService) {
        this.shiftSwapRepository = shiftSwapRepository;
        this.shiftRepository = shiftRepository;
        this.employeeScheduleRepository = employeeScheduleRepository;
        this.employeeRepository = employeeRepository;
        this.tokenService = tokenService;
    }

    public List<EmployeeRecommendation> getRecommendations(ScheduleRecommendationDTO request) {
        LocalDateTime startDate = request.getStartDate().atStartOfDay();
        LocalDateTime endDate = request.getEndDate().atTime(LocalTime.MAX);

        String[] times = request.getScheduleTimeRange().split("-");
        LocalDateTime startTime = LocalDateTime.of(request.getStartDate(), LocalTime.parse(times[0]));
        LocalDateTime endTime = LocalDateTime.of(request.getStartDate(), LocalTime.parse(times[1]));

        List<Shift> matchingShifts = shiftRepository.findMatchingShifts(startDate, endDate, startTime, endTime);

        return matchingShifts.stream()
                .map(shift -> {
                    Employee employee = employeeRepository.findById(shift.getEmpId()).orElseThrow();
                    return new EmployeeRecommendation(employee.getEmpId(), employee.getEmpName());
                })
                .distinct()
                .collect(Collectors.toList());
    }

    public ShiftSwap createShiftChangeRequest(ShiftChangeRequestDTO request) {
        ShiftSwap shiftSwap = new ShiftSwap();
        shiftSwap.setSwapRequestDate(LocalDate.now());
        shiftSwap.setStatus("PENDING");
        shiftSwap.setStartDate(request.getStartDate());
        shiftSwap.setEndDate(request.getEndDate());
        shiftSwap.setRequestedSchedule(request.getSchedule());
        shiftSwap.setReason(request.getReason());
        shiftSwap.setSwapRequestedToEmployeeId(request.getRecommendation());
        shiftSwap.setActionReason(request.getActionReason());
        shiftSwap.setCreatedBy(tokenService.getEmployeeIdFromToken());
        shiftSwap.setCreatedAt(LocalDateTime.now());
        // Set ORIGINAL_SHIFT_ASSIGNMENT_ID to null for now
        shiftSwap.setOriginalShiftAssignmentId(null);

        // We'll set SWAP_REQUESTED_BY_EMPLOYEE_ID to null for now
        // This should be set to the actual employee ID when you implement authentication
        shiftSwap.setSwapRequestedByEmployeeId(tokenService.getEmployeeIdFromToken());

       // return shiftSwapRepository.saveAndFlush(shiftSwap);
        ShiftSwap savedShiftSwap = shiftSwapRepository.saveAndFlush(shiftSwap);

        //email and notification work
        // Fetch employee details for sending notification and email
        Employee requestedByEmployee = employeeRepository.findById(shiftSwap.getSwapRequestedByEmployeeId())
                .orElseThrow(() -> new EntityNotFoundException("Requesting employee not found"));

        Long requestedByEmpId=requestedByEmployee.getEmpId();
        Long upperId = employeeRepository.findUpperIdByEmpId(requestedByEmpId);
        if (upperId != 0) {
            sendShiftChangeNotificationAndEmail(requestedByEmployee, request);
        }


        return savedShiftSwap;
    }

    //notification and email work
    private void sendShiftChangeNotificationAndEmail(Employee requestedByEmployee, ShiftChangeRequestDTO request) {
        Long requestedByEmpId=requestedByEmployee.getEmpId();
        Long upperId = employeeRepository.findUpperIdByEmpId(requestedByEmpId);
        if (upperId == null) {
            throw new IllegalStateException("No supervisor found for employee ID: " + requestedByEmpId);
        }
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
        String formattedStartDate = request.getStartDate().format(dateFormatter);
        String formattedEndDate = request.getEndDate().format(dateFormatter);

        // Create the message for both notification and email
        String notificationMessage = "Shift change request from " + requestedByEmployee.getEmpName() +
                " for dates: " + formattedStartDate + " to " + formattedEndDate +
                ". Reason: " + request.getReason() + ".";

        // Sending Notification
        NotificationDTO notificationDTO = new NotificationDTO(
                "New Shift Change Request",
                notificationMessage,
                "SHIFT_CHANGE_REQUEST",
                String.valueOf(upperId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        //type=shift_change
        notificationService.sendNotificationToEmployee(upperId, notificationDTO.getTitle(), notificationDTO.getBody(), "shift", String.valueOf(requestedByEmpId));
        // Sending Email
        String emailMessage ="Shift change request from " + requestedByEmployee.getEmpName() +
                " for dates: " + formattedStartDate + " to " + formattedEndDate +
                ". Reason: " + request.getReason() + ".";

        String emailId = employeeRepository.getReferenceById(upperId).getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Shift Change Request Notification");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);
    }

    private void validateRequest(ShiftChangeRequestDTO request) {
        if (request.getStartDate().isAfter(request.getEndDate())) {
            throw new IllegalArgumentException("Start date must be before or equal to end date");
        }
        // Add more validations as needed
    }

    public void approveShiftSwap(Long swapRequestId, ShiftChangeRequestDTO requestBody) {
        ShiftSwap shiftSwap = shiftSwapRepository.findBySwapRequestId(swapRequestId)
                .orElseThrow(() -> new RuntimeException("Request Not Found"));

        try {
            Long LoggedInEmpId = tokenService.getEmployeeIdFromToken();

            if (null != requestBody.getRecommendation() && requestBody.getRecommendation() > 0) {
                shiftSwap.setSwapRequestedToEmployeeId(requestBody.getRecommendation());
                shiftSwap.setUpdatedBy(LoggedInEmpId);
                shiftSwap.setUpdatedAt(LocalDateTime.now());
                shiftSwapRepository.saveAndFlush(shiftSwap);
            }
            updateEmployeeSchedules(shiftSwap);
           // System.out.println("request Body=====" + requestBody);
            shiftSwap.setStatus("APPROVED");
            shiftSwap.setActionReason(requestBody.getActionReason());
            shiftSwap.setUpdatedBy(LoggedInEmpId);
            shiftSwap.setUpdatedAt(LocalDateTime.now());
            shiftSwapRepository.saveAndFlush(shiftSwap);

           /* if (shiftSwap.getSwapRequestedByEmployeeId() != null && shiftSwap.getSwapRequestedToEmployeeId() != null) {
                // Fetch the requesting employee if the ID is not null
                Optional<Employee> requestingEmployeeOptional = employeeRepository.findById(Long.valueOf(shiftSwap.getSwapRequestedByEmployeeId()));

                // Fetch the recommended employee if the ID is not null
                Optional<Employee> recommendedEmployeeOptional = employeeRepository.findById(Long.valueOf(shiftSwap.getSwapRequestedToEmployeeId()));

                // If both employees exist, proceed with notification
                if (requestingEmployeeOptional.isPresent() && recommendedEmployeeOptional.isPresent()) {
                    Employee requestingEmployee = requestingEmployeeOptional.get();
                    Employee recommendedEmployee = recommendedEmployeeOptional.get();

                    sendShiftSwapApprovalNotificationAndEmail(requestingEmployee, recommendedEmployee, shiftSwap);
                } else {
                    // Handle the case where one or both employees are not found (you can log or take another action)
                    logger.error("One or both employees not found for shift swap. " +
                                    "Requested by employee ID: {}, Requested to employee ID: {}",
                            shiftSwap.getSwapRequestedByEmployeeId(),
                            shiftSwap.getSwapRequestedToEmployeeId());
                }
            } else {
                // Handle the case where employee IDs are null (optional logic)
                logger.error("Employee IDs are null, cannot process shift swap. " +
                                "Swap Requested By Employee ID: {}, Swap Requested To Employee ID: {}",
                        shiftSwap.getSwapRequestedByEmployeeId(),
                        shiftSwap.getSwapRequestedToEmployeeId());
            }*/

            if (shiftSwap.getSwapRequestedByEmployeeId() != null &&
                    shiftSwap.getSwapRequestedByEmployeeId() != 0) {

                Optional<Employee> requestingEmployeeOptional = employeeRepository.findById(Long.valueOf(shiftSwap.getSwapRequestedByEmployeeId()));
                if (requestingEmployeeOptional.isPresent()) {
                    Employee requestingEmployee = requestingEmployeeOptional.get();

                    sendShiftSwapApprovalNotificationAndEmailRequestingEmployee(requestingEmployee, shiftSwap);

                } else {
                    logger.error("Employee IDs are null, cannot process shift swap. " +
                                    "Swap Requested To Employee ID: {}",
                            shiftSwap.getSwapRequestedByEmployeeId()
                    );
                }

            }

            if (shiftSwap.getSwapRequestedToEmployeeId() != null &&
                    shiftSwap.getSwapRequestedToEmployeeId() != 0) {

                Optional<Employee> recommendedEmployeeOptional = employeeRepository.findById(Long.valueOf(shiftSwap.getSwapRequestedToEmployeeId()));
                if (recommendedEmployeeOptional.isPresent()) {
                    Employee recommendedEmployee = recommendedEmployeeOptional.get();
                    sendShiftSwapApprovalNotificationAndEmailRequestedToEmployee(recommendedEmployee, shiftSwap);
                } else {
                    logger.error("Employee IDs are null, cannot process shift swap. " +
                                    "Swap Requested By Employee ID: {}",
                            shiftSwap.getSwapRequestedToEmployeeId()
                    );
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("Failed to approve shift chnge: " + e.getMessage(), e);
        }
    }


    private void sendShiftSwapApprovalNotificationAndEmailRequestingEmployee(Employee requestingEmployee,ShiftSwap shiftSwap){
        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
        String formattedStartDate = shiftSwap.getStartDate().format(dateFormatter);
        String formattedEndDate = shiftSwap.getEndDate().format(dateFormatter);

        // Create the message content for both notification and email
        String messageContent = "Your shift swap request from " + formattedStartDate + " to " + formattedEndDate +
                " has been approved. Your schedules have been updated accordingly.";

        // Send Notifications
        NotificationDTO notificationDTO = new NotificationDTO(
                "Shift Swap Approved",
                messageContent,
                "SHIFT_SWAP_APPROVED",
                String.valueOf(requestingEmployee.getEmpId()),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(requestingEmployee.getEmpId(), notificationDTO.getTitle(), notificationDTO.getBody(), "shift", String.valueOf(LoggedInEmpId));

        String emailSubject = "Shift Swap Approved Notification";
        EmailData emailDataRequestingEmployee = new EmailData();
        emailDataRequestingEmployee.setEmailId(requestingEmployee.getEmail());
        emailDataRequestingEmployee.setSubject(emailSubject);
        emailDataRequestingEmployee.setMessage(messageContent);

        emailService.sendEmail(emailDataRequestingEmployee);

        Employee employee = employeeRepository.findById(requestingEmployee.getEmpId())
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + requestingEmployee.getEmpId()));

        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(messageContent);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }


    }

    private void sendShiftSwapApprovalNotificationAndEmailRequestedToEmployee(Employee recommendedEmployee, ShiftSwap shiftSwap){
        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
        String formattedStartDate = shiftSwap.getStartDate().format(dateFormatter);
        String formattedEndDate = shiftSwap.getEndDate().format(dateFormatter);

      /*  Optional<Employee> requestingEmployeeOptional = employeeRepository.findById(Long.valueOf(shiftSwap.getSwapRequestedByEmployeeId()));
        Employee requestingEmployee = requestingEmployeeOptional.get();
        */
        // Create the message content for both notification and email
        String messageContent = "Your shift has change" +
                " from " + formattedStartDate + " to " + formattedEndDate +
                " Please check your updated schedule.";


        // Send Notifications
        NotificationDTO notificationDTO = new NotificationDTO(
                "Shift Swap Approved",
                messageContent,
                "SHIFT_SWAP_APPROVED",
                String.valueOf(recommendedEmployee.getEmpId()),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );
        notificationService.sendNotificationToEmployee(recommendedEmployee.getEmpId(), notificationDTO.getTitle(), notificationDTO.getBody(), "shift", String.valueOf(LoggedInEmpId));

        String emailSubject = "Shift Swap Approved Notification";
        EmailData emailDataRecommendedEmployee = new EmailData();
        emailDataRecommendedEmployee.setEmailId(recommendedEmployee.getEmail());
        emailDataRecommendedEmployee.setSubject(emailSubject);
        emailDataRecommendedEmployee.setMessage(messageContent);
        emailService.sendEmail(emailDataRecommendedEmployee);




    }



    /*//email and notification sending
    private void sendShiftSwapApprovalNotificationAndEmail(Employee requestingEmployee, Employee recommendedEmployee, ShiftSwap shiftSwap) {

        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
        String formattedStartDate = shiftSwap.getStartDate().format(dateFormatter);
        String formattedEndDate = shiftSwap.getEndDate().format(dateFormatter);

        // Create the message content for both notification and email
        String messageContent = "Your shift swap request from " + formattedStartDate + " to " + formattedEndDate +
                " has been approved. Your schedules have been updated accordingly.";

        // Send Notifications
        NotificationDTO notificationDTO = new NotificationDTO(
                "Shift Swap Approved",
                messageContent,
                "SHIFT_SWAP_APPROVED",
                String.valueOf(requestingEmployee.getEmpId()),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        // Notify both employees about the approval
        notificationService.sendNotificationToEmployee(requestingEmployee.getEmpId(), notificationDTO.getTitle(), notificationDTO.getBody(), "shift", String.valueOf(LoggedInEmpId));
        notificationService.sendNotificationToEmployee(recommendedEmployee.getEmpId(), notificationDTO.getTitle(), notificationDTO.getBody(), "shift", String.valueOf(LoggedInEmpId));

        // Prepare EmailData for both employees
        String emailSubject = "Shift Swap Approved Notification";
        EmailData emailDataRequestingEmployee = new EmailData();
        emailDataRequestingEmployee.setEmailId(requestingEmployee.getEmail());
        emailDataRequestingEmployee.setSubject(emailSubject);
        emailDataRequestingEmployee.setMessage(messageContent);

        EmailData emailDataRecommendedEmployee = new EmailData();
        emailDataRecommendedEmployee.setEmailId(recommendedEmployee.getEmail());
        emailDataRecommendedEmployee.setSubject(emailSubject);
        emailDataRecommendedEmployee.setMessage(messageContent);

        // Send Emails
        emailService.sendEmail(emailDataRequestingEmployee);
        emailService.sendEmail(emailDataRecommendedEmployee);
    }
*/

    private void updateEmployeeSchedules(ShiftSwap shiftSwap) {
        Long employee1Id = shiftSwap.getSwapRequestedByEmployeeId();
        Long employee2IdString = shiftSwap.getSwapRequestedToEmployeeId(); // Keep it as a String for now
        LocalDate startDate = shiftSwap.getStartDate();
        LocalDate endDate = shiftSwap.getEndDate();

        List<EmployeeSchedule> employee1Schedules = employeeScheduleRepository.findByEmployeeEmpIdAndDateBetween(employee1Id, startDate, endDate);

        if (employee2IdString == 0) {
            for (EmployeeSchedule schedule1 : employee1Schedules) {
                schedule1.setModifiedShift(shiftSwap.getRequestedSchedule());
                schedule1.setSwapedWithEmpId(null);  // No swapping, so set this to null
                employeeScheduleRepository.saveAndFlush(schedule1);
            }
        } else {
            Long employee2Id = employee2IdString;
            List<EmployeeSchedule> employee2Schedules = employeeScheduleRepository.findByEmployeeEmpIdAndDateBetween(employee2Id, startDate, endDate);

            if (employee1Schedules.size() == employee2Schedules.size()) {
                for (int i = 0; i < employee1Schedules.size(); i++) {
                    EmployeeSchedule schedule1 = employee1Schedules.get(i);
                    EmployeeSchedule schedule2 = employee2Schedules.get(i);

                    String tempShift = schedule1.getActualShift();
                    schedule1.setModifiedShift(shiftSwap.getRequestedSchedule());
                    schedule2.setModifiedShift(tempShift);

                    schedule1.setSwapedWithEmpId(employee2Id);
                    schedule2.setSwapedWithEmpId(employee1Id);

                    employeeScheduleRepository.saveAndFlush(schedule1);
                    employeeScheduleRepository.saveAndFlush(schedule2);
                }
            } else {
                throw new RuntimeException("Mismatch of working days");
            }
        }
    }

    @Transactional
    public void denyRequest(Long swapRequestId, String actionReason) {
        ShiftSwap shiftSwap = shiftSwapRepository.findById(swapRequestId).orElseThrow(() -> new EntityNotFoundException("Swap request not found"));

        if (!"PENDING".equals(shiftSwap.getStatus())) {
            throw new IllegalStateException("Can only approve pending requests");
        }

        shiftSwap.setStatus("DENIED");
        shiftSwap.setActionReason(actionReason);
        shiftSwapRepository.saveAndFlush(shiftSwap);

        //email and notification work

        Employee requestingEmployee = employeeRepository.findById(Long.valueOf(shiftSwap.getSwapRequestedByEmployeeId()))
                .orElseThrow(() -> new EntityNotFoundException("Requesting employee not found"));
        sendShiftSwapDenialNotificationAndEmail(requestingEmployee, shiftSwap);
    }

    private void sendShiftSwapDenialNotificationAndEmail(Employee requestingEmployee, ShiftSwap shiftSwap) {

        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
        String formattedStartDate = shiftSwap.getStartDate().format(dateFormatter);
        String formattedEndDate = shiftSwap.getEndDate().format(dateFormatter);

        // Create the denial message content for both notification and email
        String messageContent = "Your shift swap request from " + formattedStartDate + " to " + formattedEndDate +
                " has been denied. Reason: " + shiftSwap.getActionReason();

        // Send Notification
        NotificationDTO notificationDTO = new NotificationDTO(
                "Shift Swap Denied",
                messageContent,
                "SHIFT_SWAP_DENIED",
                String.valueOf( requestingEmployee.getEmpId()),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(requestingEmployee.getEmpId(), notificationDTO.getTitle(), notificationDTO.getBody(), "shift", String.valueOf(LoggedInEmpId));

        // Send Email
        EmailData emailData = new EmailData();
        emailData.setEmailId(requestingEmployee.getEmail());
        emailData.setSubject("Shift Swap Denial Notification");
        emailData.setMessage(messageContent);
        emailService.sendEmail(emailData);


        if (requestingEmployee.getMobileNo() != null && !requestingEmployee.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", requestingEmployee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(messageContent);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(requestingEmployee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }

    }



    public List<ShiftSwapResponseDTO> getAllShiftSwapData() {
        Long loggedInEmpId = getCurrentAuthenticatedEmployeeId();

        // Fetch employees who directly report to the logged-in employee
        List<Employee> employeesUnderLoggedIn = employeeRepository.findByUpperId(loggedInEmpId);
        List<Long> employeeIdsUnderLoggedIn = employeesUnderLoggedIn.stream()
                .map(emp -> emp.getEmpId()) // Convert Long to String
                .collect(Collectors.toList());

        // Fetch only shift swaps for employees under the logged-in employee
        List<ShiftSwap> shiftSwaps = shiftSwapRepository.findBySwapRequestedByEmployeeIdIn(employeeIdsUnderLoggedIn);

        // Create a map of employees under the logged-in employee for easy access
        Map<Long, Employee> employeeMap = employeesUnderLoggedIn.stream()
                .collect(Collectors.toMap(Employee::getEmpId, Function.identity()));

        // Convert to DTO and return
        return shiftSwaps.stream()
                .map(shiftSwap -> convertToResponseDTO(shiftSwap, employeeMap))
                .sorted(Comparator.comparing(ShiftSwapResponseDTO::getSwapRequestId, Comparator.reverseOrder())) // Sort by swapRequestId in descending order
                .collect(Collectors.toList());
    }


    public List<ShiftSwapResponseDTO> getAllShiftSwapDataForLoggedInEmployee() {
        // Retrieve the logged-in employee ID
        Long currentEmpId = getCurrentAuthenticatedEmployeeId();
        System.out.println("Logged-in employee ID: " + currentEmpId);

        // Fetch shift swaps only for the logged-in employee
        List<ShiftSwap> shiftSwaps = shiftSwapRepository.findBySwapRequestedByEmployeeId(currentEmpId);

        // Fetch all employee details to map employee ID to employee information
        List<Employee> employees = employeeRepository.findAll();
        Map<Long, Employee> employeeMap = employees.stream()
                .collect(Collectors.toMap(Employee::getEmpId, Function.identity()));

        // Convert each ShiftSwap entity to ShiftSwapResponseDTO and sort by employee ID in descending order
        return shiftSwaps.stream()
                .map(shiftSwap -> convertToResponseDTO(shiftSwap, employeeMap))
                .sorted(Comparator.comparing(ShiftSwapResponseDTO::getSwapRequestId, Comparator.reverseOrder())) // Sort by swapRequestId in descending order
                .collect(Collectors.toList());
    }



    private Long getCurrentAuthenticatedEmployeeId() {
        // Assuming you use Spring Security to get the authenticated username
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        return userRepository.findByUsername(username).getEmployee().getEmpId();
    }




    private ShiftSwapResponseDTO convertToResponseDTO(ShiftSwap shiftSwap, Map<Long, Employee> employeeMap) {
        ShiftSwapResponseDTO dto = new ShiftSwapResponseDTO();
        dto.setSwapRequestId(shiftSwap.getSwapRequestId());
        dto.setOriginalShiftAssignmentId(shiftSwap.getOriginalShiftAssignmentId());

        // Safely convert employee IDs to Long and get employee details from the map
        Long requestedById = shiftSwap.getSwapRequestedByEmployeeId();
        Long requestedToId = shiftSwap.getSwapRequestedToEmployeeId();
        dto.setRequestByEmployeeId(requestedById);
        dto.setSwapWithEmployeeId(requestedToId);

        Employee requestedBy = employeeMap.get(requestedById);
        Employee requestedTo = employeeMap.get(requestedToId);

        if (requestedBy != null) {
            dto.setSwapRequestedByEmployeeName(requestedBy.getEmpName());
            dto.setSwapRequestedByEmployeeImage(requestedBy.getImgUre());
        }

        if (requestedTo != null) {
            dto.setSwapRequestedToEmployeeName(requestedTo.getEmpName());
            dto.setSwapRequestedToEmployeeImage(requestedTo.getImgUre());
        }

        dto.setSwapRequestDate(shiftSwap.getSwapRequestDate());
        dto.setStatus(shiftSwap.getStatus());
        dto.setStartDate(shiftSwap.getStartDate());
        dto.setEndDate(shiftSwap.getEndDate());
        Long requestedScheduleId = parseLongSafely(shiftSwap.getRequestedSchedule());
        if (requestedScheduleId != null) {
            timeSlotRepository.findById(requestedScheduleId).ifPresent(timeSlot -> {
                dto.setRequestedSchedule(String.valueOf(+requestedScheduleId));  // Display schedule ID
                dto.setStartTime(timeSlot.getStartTime());
                dto.setEndTime(timeSlot.getEndTime());
            });
        } else {
            dto.setRequestedSchedule("Schedule not found");
        }

        dto.setReason(shiftSwap.getReason());
        dto.setActionReason(shiftSwap.getActionReason());

        return dto;
    }

    // Helper method to safely parse Long values
    private Long parseLongSafely(String id) {
        if (id == null || id.trim().isEmpty()) {
            return null; // or throw an exception if you prefer
        }
        try {
            return Long.valueOf(id);
        } catch (NumberFormatException e) {
            // Log the exception if necessary
            return null; // or handle it as needed
        }
    }
}