package com.company.wfm.dto;

import java.time.LocalDate;

public class ShiftChangeDto {


    private Long employeeId;
    private LocalDate date;
    private String newShift;
    private Long swapWithEmployeeId;

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getNewShift() {
        return newShift;
    }

    public void setNewShift(String newShift) {
        this.newShift = newShift;
    }

    public Long getSwapWithEmployeeId() {
        return swapWithEmployeeId;
    }

    public void setSwapWithEmployeeId(Long swapWithEmployeeId) {
        this.swapWithEmployeeId = swapWithEmployeeId;
    }
}