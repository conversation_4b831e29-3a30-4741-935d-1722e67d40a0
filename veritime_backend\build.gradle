plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.2'
    id 'io.spring.dependency-management' version '1.1.4'
    id "org.sonarqube" version "6.0.1.5171" // Replace with the latest version
}

sonar {
  properties {
    property "sonar.projectKey", "wfm-manager"
    property "sonar.host.url", "https://sonar.xpertlyte.tech"
    property "sonar.token", System.getenv("SONAR_TOKEN")
    property "sonar.sources", "src/main/java"
    property "sonar.java.binaries", "build/classes/java/main"
    property "sonar.qualitygate.wait", "true"
  }
}

group = 'com.example'
version = '0.0.1-SNAPSHOT'

java {
    sourceCompatibility = '17'
}

repositories {
    mavenCentral()
}

dependencies {

    //롬복 추가
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'


    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-mustache'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-mail:3.3.5'
  	implementation 'org.springframework.boot:spring-boot-starter-thymeleaf:3.3.5'
  	implementation 'org.springframework.boot:spring-boot-starter-aop:3.3.5'
    implementation 'org.springframework.boot:spring-boot-starter-webflux:3.3.5'
  	  	// https://mvnrepository.com/artifact/software.amazon.awssdk/aws-sdk-java
	implementation group: 'software.amazon.awssdk', name: 'aws-sdk-java', version: '2.29.9'
	
  	testImplementation 'org.springframework.boot:spring-boot-starter-test'
  	testImplementation 'org.springframework.security:spring-security-test'
  	
  
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.1.0'
    implementation 'ch.qos.logback:logback-classic'
    runtimeOnly 'com.microsoft.sqlserver:mssql-jdbc'
	// https://mvnrepository.com/artifact/io.jsonwebtoken/jjwt-impl
	implementation group: 'io.jsonwebtoken', name: 'jjwt-impl', version: '0.12.6'
	implementation group: 'org.springframework.security', name: 'spring-security-jwt', version: '1.1.1.RELEASE'
	
	implementation "io.jsonwebtoken:jjwt-api:0.12.6"
	// https://mvnrepository.com/artifact/io.jsonwebtoken/jjwt-jackson
	implementation group: 'io.jsonwebtoken', name: 'jjwt-jackson', version: '0.12.6'


    // Apache POI for Excel file processing
    implementation 'org.apache.poi:poi:5.2.3' // For .xls files
    implementation 'org.apache.poi:poi-ooxml:5.2.3' // For .xlsx files

    // Updated File upload dependencies
    implementation 'commons-fileupload:commons-fileupload:1.4' // Updated version
    implementation 'commons-io:commons-io:2.11.0' // For file handling utilities
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.json:json:20210307'


    implementation 'com.google.firebase:firebase-admin:9.0.0'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

}

bootJar{
enabled = true;
}

tasks.named('test') {
    useJUnitPlatform()
}
