package com.company.wfm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

@Entity
@Table(name = "holiday_schedule")
@Data
public class HolidaySchedule {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "date", nullable = false)
    private LocalDate date;

    @Column(name = "holiday", nullable = false, length = 255)
    private String holiday;

    @Column(name = "is_optional", nullable = false)
    private Boolean isOptional;

    @Column(name = "created_by", nullable = false)
    private Long createdBy;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_by")
    private Long updatedBy;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt; // This should be nullable

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;


    @Transient
    private String createdByName;

    @Transient
    private String updatedByName;

    @Transient
    private String createdByImage;

    @Transient
    private String updatedByImage;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = null; // Set updatedAt to null when creating a new record
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now(); // Set updatedAt when updating a record
    }
}
