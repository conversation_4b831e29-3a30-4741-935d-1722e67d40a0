/* Global styles for the filter container */
.filter-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 20px;
  margin-top: 20px;
  flex-wrap: wrap; /* Allow the container to wrap on smaller screens */
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200px; /* Ensure elements do not shrink too much */
  max-width: 250px; /* Ensure elements do not grow too much */
}

.filter-se {
  border-radius: 4px; /* Rounded corners for select */
  background-color: #ffff; /* Light background */
  width: 200px; /* Fixed width */
  color: #1F5F9A; /* Text color */
  padding: 0; /* Removed padding */
  border: none; /* Removed border */
  font-size: 14px; /* Consistent font size */
  height: 40px; /* Adjusted height to make it visually appealing */
}

.date-picker {
  width: 100%; /* Make it full width of container */
  padding: 10px; /* Padding to make the date picker feel comfortable */
  border: 1px solid #ccc; /* Border to differentiate the date picker */
  border-radius: 8px; /* Rounded corners for date picker */
  background-color: #ffff; /* Light background */
  color: #1F609A; /* Text color for date picker */
  font-size: 14px; /* Consistent font size */
}


.go-button-shift {
  padding: 12px 24px;
  background-color: var(--yellow2); /* Custom yellow color */
  border: none;
  margin-right: 140px;
  border-radius: 8px;
  font-weight: bold;
  color: black;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.go-button-shift:hover {
  background-color: var(--yellow2-dark); /* Add hover effect */
}

.go-button-shift:active {
  background-color: var(--yellow2-light); /* Add active effect */
}


/* @media (max-width: 600px) {
  .filter-container {
    flex-direction: column; 
  }

  .filter-item {
    width: 100%; 
    margin-bottom: 15px; 
  }

  .go-button {
    width: 100%; 
  }
} */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .filter-container {
    display: flex; 
    flex-wrap: nowrap; 
    align-items: center; 
    padding: 20px; 
    margin-top: 30px; 
    margin-left: 20%;
    
  }

  .filter-item {
    min-width: 190px; 
    max-width: 200px; 
   
  }

  .filter-se {
    width: 190px; 
    font-size: 14px; 
    height: 40px; 
  }

  .date-picker {
    width: 190px; 
    padding: 10px; 
    font-size: 14px; 
  }

  .go-button-shift {
    padding: 12px 24px; 
    margin: 0;
    font-size: 14px; 
    margin-right: 70%;
  }

  p{
    align-items: center;
  }
}

@media screen and (min-width: 1024px) and (max-width: 1300px) {
  .filter-container {
    display: flex;
    flex-wrap: nowrap;
   
    align-items: center;
    padding: 20px;
    margin-top: 30px;
    margin-left: 20%;
    
  }

  .filter-item {
    min-width: 230px;
    max-width: 260px;
  }

  .filter-se {
    width: 230px;
    font-size: 14px;
    height: 40px;
  }

  .date-picker {
    width: 230px;
    padding: 10px;
    font-size: 14px;
  }

  .go-button-shift {
    padding: 12px 28px;
    margin: 0;
    font-size: 14px;
    margin-right: 70%;
  }
}

@media screen and (min-width: 1301px) and (max-width: 1500px) {
  .filter-container {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    padding: 20px;
    margin-top: 30px;
    margin-left: 15%;
  }

  .filter-item {
    min-width: 240px;
    max-width: 270px;
  }

  .filter-se {
    width: 240px;
    font-size: 14px;
    height: 40px;
  }

  .date-picker {
    width: 240px;
    padding: 10px;
    font-size: 14px;
  }

  .go-button-shift {
    padding: 12px 28px;
    margin: 0;
    font-size: 14px;
    margin-right: 60%;
  }
}
