import React from 'react';
import { Line } from 'react-chartjs-2';
import 'chart.js/auto';
import { colors } from '@constants/colors';

const AttendanceChart = () => {
  const data = {
    labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    datasets: [
      {
        label: 'Present',
        data: [100, 200, 720, 450, 350, 600, 400],
        borderColor: colors.blue11,
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        pointBackgroundColor: colors.blue11,
        pointBorderColor: colors.red14,
        pointHoverBackgroundColor: colors.red14,
        pointHoverBorderColor: colors.blue11,
        tension: 0.4,
        fill: true,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (tooltipItem) => `${tooltipItem.raw} Present`,
          afterLabel: (tooltipItem) => `JUL 18th, 2024`,
        },
        backgroundColor: colors.red14,
        titleColor: colors.purple2,
        bodyColor: colors.purple2,
        borderColor: colors.blue11,
        borderWidth: 1,
      },title: {
        display: true,
        text: 'Chart Attendance',
      },
    },
    scales: {
      x: {
        title: {
          display: false,
        },
      },
      y: {
        title: {
          display: false,
        },
        beginAtZero: true,
      },
    },
  };

  return (
    <div style={{height:"300px"}}>
      <Line data={data} options={options} />
    </div>
  );
};

export default AttendanceChart;
