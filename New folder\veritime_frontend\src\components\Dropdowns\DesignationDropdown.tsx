import React, { useState } from "react";
import Select, { components } from "react-select";
import { Modal } from "react-bootstrap";

const CustomOption = (props: any) => (
  <components.Option {...props}>
    <input type="checkbox" checked={props.isSelected} />
    <label style={{ marginLeft: "10px" }}>{props.label}</label>
  </components.Option>
);

const DesignationDropdown = ({
  designations,
  onChange,
  isMulti = true,
  value,
}: {
  designations: any;
  onChange: any;
  isMulti?: boolean;
  value: any;
}) => {
  const [showModal, setShowModal] = useState(false);

  const toggleModal = () => setShowModal(!showModal);

  const handleChange = (selectedOptions: any) => {
    const isSelectAll = selectedOptions.some(
      (option: any) => option.value === "select-all"
    );
    if (isSelectAll) {
      const allValues =
        isMulti && value.length === designations.length
          ? []
          : designations.map((d: any) => d.designationId);
      onChange(allValues);
    } else {
      const selectedValues = selectedOptions
        ? selectedOptions.map((option: any) => option.value)
        : [];
      onChange(selectedValues);
    }
  };

  const getSelectedDesignations: any = () => {
    return isMulti
      ? (value || [])
          .map((id: any) => {
            const designation = designations.find(
              (d: any) => d.designationId === id
            );
            return designation
              ? {
                  label: designation.designationName,
                  value: designation.designationId,
                }
              : null;
          })
          .filter(Boolean)
      : designations.find((d: any) => d.designationId === value)
      ? {
          label: designations.find((d: any) => d.designationId === value)
            ?.designationName,
          value,
        }
      : null;
  };

  const MultiValue = (props: any) => {
    const values = props.getValue();
    const isLastVisible = props.index === 1;
    const extraCount = values.length - 1;

    return isLastVisible && extraCount > 0 ? (
      <div onClick={toggleModal} style={{ cursor: "pointer", color: "grey" }}>
        +{extraCount}
      </div>
    ) : (
      props.index < 2 && (
        <components.MultiValue {...props}>
          <span>{props.data.label}</span>
        </components.MultiValue>
      )
    );
  };
  const optionsWithSelectAll = [
    { label: value && value.length === designations.length ? 'Deselect All' : 'Select All', value: 'select-all' },
    ...designations.map((designation:any) => ({
      label: designation.designationName,
      value: designation.designationId,
    })),
  ];

  const customStyles = {
    menu: (provided: any) => ({
      ...provided,
      overflowY: "auto",
      width: "240px",
      zIndex: 9999,
      "@media (min-width: 769px) and (max-width: 1024px)": {
        marginLeft: "-90px",
        width: "200px",
      },
      "@media (max-width: 768px)": {
        marginLeft: "-90px",
        width: "180px",
      },
    }),
    control: (provided: any, state: any) => ({
      ...provided,
      minHeight: '40px',
      height: '40px',
      display: 'flex',
      width: '50%',
      '@media (min-width: 768px)': {
        width: '200px',
      },
      '@media (min-width: 1200px)': {
        width: '200px',
      },
      '@media (min-width: 1600px)': {
        width: '250px',
      },
    }),

    valueContainer: (styles: any) => ({
      ...styles,
      display: "flex",
      overflowX: "auto",
      flexWrap: "nowrap",
      maxHeight: "40px",
    }),
    option: (styles: any, { isSelected }: any) => ({
      ...styles,
      backgroundColor: isSelected ? "#f0f0f0" : "transparent",
      color: "black",
      ":hover": {
        backgroundColor: "#e0e0e0",
      },
      fontSize: "14px", // Default font size
      "@media (min-width: 1025px) and (max-width: 1600px)": {
        fontSize: "12px",
      },
      "@media (min-width: 769px) and (max-width: 1024px)": {
        fontSize: "10px",
      },
      "@media (min-width: 481px) and (max-width: 768px)": {
        fontSize: "8px",
      },
      "@media (max-width: 480px)": {
        fontSize: "6px",
      },
    }),
    placeholder: (styles: any) => ({
      ...styles,
      fontSize: "16px",
      "@media (min-width: 1201px) and (max-width: 1600px)": {
        fontSize: "14px",
      },
      "@media (min-width: 1025px) and (max-width: 1200px)": {
        fontSize: "12px",
      },
      '@media (max-width: 768px)': {
        fontSize: '10px',
      },
    }),
  };

  return (
    <>
      <Select
        isMulti={isMulti}
        closeMenuOnSelect={false}
        hideSelectedOptions={false}
        components={{ Option: CustomOption, MultiValue }}
        // options={designations.map((designation:any) => ({
        //   label: designation.designationName,
        //   value: designation.designationId,
        // }))}
        options={optionsWithSelectAll}
        value={getSelectedDesignations()}
        onChange={handleChange}
        styles={customStyles}
        placeholder="Select Designation"
        isSearchable={true}
      />

      <Modal show={showModal} onHide={toggleModal}>
        <Modal.Body>
          {getSelectedDesignations().map((item: any) => (
            <div
              key={item.value}
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "5px",
              }}
            >
              <span style={{ marginRight: "auto" }}>{item.label}</span>
              <button
                onClick={() =>
                  onChange(value.filter((val: string) => val !== item.value))
                }
                style={{
                  border: "none",
                  background: "none",
                  color: "red",
                  cursor: "pointer",
                }}
              >
                ✕
              </button>
            </div>
          ))}
        </Modal.Body>
      </Modal>
    </>
  );
};

export default DesignationDropdown;
