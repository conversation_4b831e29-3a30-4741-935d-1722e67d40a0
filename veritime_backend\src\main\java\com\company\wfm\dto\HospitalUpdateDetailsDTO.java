package com.company.wfm.dto;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

@Data
public class HospitalUpdateDetailsDTO {
    private String hospitalName;
    private String shortCode;
    private Long provinceId;
    private Long districtId;
    private Long subDistrictId;
    private String addressLine1;
    private String addressLine2;
    private Double lat;
    private Double lng;
    private LocalDateTime validity;
    private String hospitalType;
    private Boolean isActive;
    private String clusterName;
    private List<HospialBranchUpdateDTO> branches;

}
