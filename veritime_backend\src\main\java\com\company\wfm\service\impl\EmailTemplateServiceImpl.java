package com.company.wfm.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import com.company.wfm.dto.EmailTemplateDto;
import com.company.wfm.dto.WelcomeEmailTemplateDto;
import com.company.wfm.service.EmailTemplateService;

@Service
public class EmailTemplateServiceImpl implements EmailTemplateService {

	@Autowired
	private TemplateEngine templateEngine;

	@Override
	public String getTicketCreateTemplate(EmailTemplateDto emailTemplate) {
		Context context = new Context();
		context.setVariable("name", emailTemplate.getName());
		context.setVariable("ticket", emailTemplate.getTicket());
		context.setVariable("date", emailTemplate.getDate());
		return templateEngine.process("ticket-receive", context);

	}

	@Override
	public String getTicketResolveTemplate(EmailTemplateDto emailTemplate) {
		Context context = new Context();
		context.setVariable("name", emailTemplate.getName());
		context.setVariable("ticket", emailTemplate.getTicket());
		context.setVariable("date", emailTemplate.getDate());
		return templateEngine.process("ticket-update", context);

	}

	@Override
	public String getTicketUpdateTemplate(EmailTemplateDto emailTemplate) {
		Context context = new Context();
		context.setVariable("name", emailTemplate.getName());
		context.setVariable("ticket", emailTemplate.getTicket());
		context.setVariable("date", emailTemplate.getDate());
		return templateEngine.process("ticket-closure", context);

	}

	@Override
	public String getPasswordResetTemplate(EmailTemplateDto emailTemplate) {
		Context context = new Context();
		context.setVariable("name", emailTemplate.getName());
		context.setVariable("resetLink", emailTemplate.getResetLink());
		return templateEngine.process("password-reset", context);

	}

	@Override
	public String getWelcomeTemplate(WelcomeEmailTemplateDto welcomeEmailTemplateDto) {

		Context context = new Context();
		// Set variables to be used in the template
		context.setVariable("name", welcomeEmailTemplateDto.getName());
		context.setVariable("defaultPassword", welcomeEmailTemplateDto.getDefaultPassword());
		context.setVariable("instructions", welcomeEmailTemplateDto.getInstructions());
		context.setVariable("resetLink", welcomeEmailTemplateDto.getResetLink());

		// Process the template with the context variables
		return templateEngine.process("welcome-email", context);
	}

	@Override
	public String getTicketAssingedTemplate(EmailTemplateDto emailTemplate) {
		Context context = new Context();
		context.setVariable("name", emailTemplate.getName());
		context.setVariable("historicalTicket", emailTemplate.getTicket());
		context.setVariable("historicalTicketDate", emailTemplate.getDate());
		context.setVariable("ticket", emailTemplate.getTicket());
		context.setVariable("date", emailTemplate.getDate());
		return templateEngine.process("ticket-assign", context);

	}
}