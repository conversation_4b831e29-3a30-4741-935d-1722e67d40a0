package com.company.wfm.annotation;

import java.lang.reflect.Method;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Component;

import com.company.wfm.entity.ScheduleJobTracker;
import com.company.wfm.enumaration.ScheduleJobStatusEnum;
import com.company.wfm.repository.ScheduleJobtrackerRepository;

import lombok.extern.slf4j.Slf4j;

@Aspect
@Component
@Slf4j
@ConditionalOnExpression("${aspect.enabled:true}")
public class ScheduleJobTrackerAdvice {

	@Autowired
	private ScheduleJobtrackerRepository scheduleJobRepository;

	@Around("execution(public * *(..)) && @annotation(com.company.wfm.annotation.TrackSchedularExecution)")
	public Object executionTime(ProceedingJoinPoint point) throws Throwable {

		MethodSignature signature = (MethodSignature) point.getSignature();
		Method method = signature.getMethod();
		TrackSchedularExecution myAnnotation = method.getAnnotation(TrackSchedularExecution.class);

		LocalDateTime startTime = LocalDateTime.now();
		log.info("Starting {} job on {} date", myAnnotation.name(), startTime);
		ScheduleJobTracker job = null;
		try {
			job = scheduleJobRepository
					.saveAndFlush(ScheduleJobTracker.builder().name(myAnnotation.name()).status(ScheduleJobStatusEnum.INPROGRESS.name())
							.executionStartTime(startTime).executionEndTime(null).runDate(LocalDate.now()).build());

			Object object = point.proceed();

			LocalDateTime endTime = LocalDateTime.now();
			job.setExecutionEndTime(endTime);
			job.setStatus(ScheduleJobStatusEnum.SUCCESS.name());
			scheduleJobRepository.saveAndFlush(job);

			log.info("Finished {} job on {} date", myAnnotation.name(), endTime);
			return object;
		} catch(DataIntegrityViolationException excp) {
			log.error("Job with name {} running for {} already exist",myAnnotation.name(),startTime,excp);
		} catch (Exception e) {
			log.error("Error occured while running {} job",myAnnotation.name(),e);
			LocalDateTime endTime = LocalDateTime.now();
			job.setExecutionEndTime(endTime);
			job.setStatus(ScheduleJobStatusEnum.FAILED.name());
			job.setReason(e.getMessage());
			scheduleJobRepository.saveAndFlush(job);
		}
		return null;
	}
	
    @Around("@annotation(TrackExecutionTime)")
    public Object measureExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        Instant start = Instant.now();
        log.info("Execution started for method: {}", joinPoint.getSignature().toShortString());
        Object result = joinPoint.proceed(); // Execute the method

        Instant end = Instant.now();
        Duration executionTime = Duration.between(start, end);
        log.info("Execution ended for method: {}", joinPoint.getSignature().toShortString());
        log.info("Total execution time: {} ms", executionTime.toMillis());

        return result;
    }
}
