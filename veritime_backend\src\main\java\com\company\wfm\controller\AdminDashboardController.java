package com.company.wfm.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.UserTokenService;

@RestController
@RequestMapping("/api/v1/dashboard/admin")
@CrossOrigin(origins = "*")
public class AdminDashboardController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserTokenService tokenService;

    private Random rand = new Random();

    private String getRandomBlueShade() {

        int blue = rand.nextInt(155) + 100;
        return String.format("#0000%02x", blue);
    }

    @GetMapping
    public Map<String, Object> getSummary() {

        Long empId = tokenService.getEmployeeIdFromToken();

        Long companyId = jdbcTemplate.queryForObject(
                "SELECT COMPANY_ID FROM t_employee WHERE EMP_ID = ?", Long.class, empId);

        Integer branchCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(DISTINCT b.branch_id) FROM t_branch b " +
                        "JOIN t_employee e ON b.branch_id = e.branch_id " +
                        "WHERE e.emp_id = ?", Integer.class, empId);

        Integer deptCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_department WHERE DEPARTMENT_ID IN (SELECT DEPARTMENT_ID FROM t_employee WHERE COMPANY_ID = ?)",
                Integer.class, companyId);

        Integer empCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee WHERE COMPANY_ID = ?", Integer.class, companyId);

        Integer totalDevicesCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_user WHERE EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE COMPANY_ID = ?)",
                Integer.class, companyId);

        Integer activeDevicesCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_user WHERE IS_ONLINE = 1 AND EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE COMPANY_ID = ?)",
                Integer.class, companyId);

        Integer totalEnrollmentsCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee WHERE COMPANY_ID = ?", Integer.class, companyId);

        Integer totalApprovals = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee_leave_history WHERE EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE COMPANY_ID = ?)",
                Integer.class, companyId);

        Integer totalApproved = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM t_employee_leave_history WHERE approval_status = 'APPROVED' AND EMP_ID IN (SELECT EMP_ID FROM t_employee WHERE COMPANY_ID = ?)",
                Integer.class, companyId);

        List<Map<String, Object>> top3branches = jdbcTemplate.queryForList(
                "SELECT TOP 3 b.branch_name AS name, COUNT(e.EMP_ID) AS employeeCount " +
                        "FROM t_branch b JOIN t_employee e ON b.branch_id = e.branch_id " +
                        "WHERE e.company_id = ? " +
                        "GROUP BY b.branch_name ORDER BY COUNT(e.EMP_ID) DESC", companyId);

        for (Map<String, Object> branch : top3branches) {
            Integer employeeCount = (Integer) branch.get("employeeCount");
            double percentage = (employeeCount * 100.0) / empCount;
            branch.put("value", percentage);
            branch.remove("employeeCount");
        }

        Map<String, Object> branchTicketList = new HashMap<>();
        List<String> axisLbl = new ArrayList<>();
        List<Integer> axis = new ArrayList<>();
        List<String> colors = new ArrayList<>();

        List<Map<String, Object>> ticketsPerBranch = jdbcTemplate.queryForList(
                "SELECT TOP 5 b.branch_name AS branchName, COALESCE(COUNT(t.TICKET_ID), 0) AS totalTickets " +
                        "FROM t_branch b " +
                        "LEFT JOIN t_employee e ON b.branch_id = e.BRANCH_ID " +
                        "LEFT JOIN t_ticket t ON e.EMP_ID = t.EMP_ID " +
                        "WHERE e.COMPANY_ID = ? " +
                        "GROUP BY b.branch_name ORDER BY COUNT(t.TICKET_ID) DESC", companyId);

        for (Map<String, Object> row : ticketsPerBranch) {
            axisLbl.add((String) row.get("branchName"));
            axis.add((Integer) row.get("totalTickets"));
            colors.add(getRandomBlueShade());
        }

        branchTicketList.put("axis", axis);
        branchTicketList.put("axisLbl", axisLbl);
        branchTicketList.put("colors", colors);

        Map<String, Object> response = new HashMap<>();
        response.put("branchCount", branchCount);
        response.put("deptCount", deptCount);
        response.put("empCount", empCount);
        response.put("devicesCount", totalDevicesCount);
        response.put("activeDevicesCount", activeDevicesCount);
        response.put("totalEnrollmentsCount", totalEnrollmentsCount);
        response.put("totalApprovals", totalApprovals);
        response.put("totalApproved", totalApproved);
        response.put("top3branches", top3branches);
        response.put("branchTicketList", branchTicketList);
        response.put("teamsCount", empCount);
        return response;
    }
}