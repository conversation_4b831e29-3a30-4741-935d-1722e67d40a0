package com.company.wfm.util;

public enum LeaveCreditBasis {
	JOINING_DATE("joiningDate"), IMMEDIATE_AFTER_PROBATION_PERIOD("immediateAfterProbationPeriod"),
	WORKED_AFTER_PROBATION_PERIOD("workedAfterProbationPeriod");

	private final String value;

	LeaveCreditBasis(String value) {
		this.value = value;
	}

	public String getValue() {
		return value;
	}

	public static LeaveCreditBasis fromString(String value) {
		for (LeaveCreditBasis basis : LeaveCreditBasis.values()) {
			if (basis.getValue().equalsIgnoreCase(value)) {
				return basis;
			}
		}
		throw new IllegalArgumentException("Invalid Leave Credit Basis: " + value);
	}
}
