package com.company.wfm.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FirebaseNotificationService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${one.signal.url}")
    private String oneSignalUrl;

    @Value("${app.id}")
    private String appId;

    @Value("${authorization.token}")
    private String authorizationToken;

    public void sendNotification(String title, String messageBody, List<String> tokens) {
        try {
            RestTemplate restTemplate = new RestTemplate();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(MediaType.parseMediaTypes("application/json"));
            headers.setBearerAuth(authorizationToken.replace("Bearer ", "")); // Add Bearer token

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("app_id", appId);
            requestBody.put("target_channel", "push");
            requestBody.put("headings", Map.of("en", title));
            requestBody.put("contents", Map.of("en", messageBody));
            requestBody.put("include_aliases", Map.of("external_id", tokens.toArray(new String[0])));
            requestBody.put("url", "http://localhost:3001/dashboards#notifications");
            requestBody.put("priority", 10);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            String response = restTemplate.postForObject(oneSignalUrl, request, String.class);

            log.info("Notification response {} ",  response);
        } catch (Exception e) {
            log.error("Exception occured while sendNotification", e);
        }
    }

    public boolean updateFirebaseToken(Long empId, String firebaseToken,String logHistoryId) {
        try {
            String updateQuery = "UPDATE t_log_history SET fb_token = ? WHERE id = ?";
            int rowsAffected = jdbcTemplate.update(updateQuery, firebaseToken, logHistoryId);
            return rowsAffected > 0;
        } catch (Exception e) {
            log.error("Exception occured while updateFirebaseToken", e);
            return false;
        }
    }

    public boolean removeToken(String logHistoryId) {
        try {
            String updateQuery = "UPDATE t_log_history SET updated_at=CURRENT_TIMESTAMP, is_active=0 WHERE id=?";
            int rowsAffected = jdbcTemplate.update(updateQuery, logHistoryId);
            return rowsAffected > 0;
        } catch (Exception e) {
            log.error("Exception occured while removeToken", e);
            return false;
        }
    }
}
