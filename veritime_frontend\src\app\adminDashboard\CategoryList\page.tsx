"use client";
import { useState, useEffect } from "react";
import { Con<PERSON>er, Row, Col, Button, Modal } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter.js";
import { useTheme } from "@material-ui/core/styles";
import EditIcon from "@mui/icons-material/Edit";
import { TabPanel } from "../../../common-components/utils.js";
import Layout from "@/components/Layout";
import { appConstants } from "@/constants/appConstants.js";
import EyeIcon from "@mui/icons-material/Visibility";
import { postRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import EditCategoryModal from "../modals/CategoryModal";

const CategoryListPage = ({ toggleMenu, expanded }: any) => {
  const [rows, setRows] = useState<any>([]);
  const [role, setRole] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);

  useEffect(() => {
    const storedRole = localStorage.getItem(appConstants?.role);
    if (storedRole) {
      setRole(storedRole);
    }

    fetchCategoryData();
  }, []);

  const fetchCategoryData = async () => {
    try {
      const response = await postRequest(API_URLS.CATEGORY_LIST, {
        offset: 0,
        limit: 10,
      });

      // console.log("API Response:", response);

      if (Array.isArray(response.content)) {
        const formattedData = response.content.map(
          (category: any, index: any) => ({
            id: category.id,
            srno: index + 1,
            category: category.category || "Others",
            eta: category.eta || "N/A",
            priority: category.priority || "Others",
            createdAt: category.createdAt || "N/A",
            updatedAt: category.updatedAt || "N/A",
            branchId: category.branchId || "Others",
            branchName: category.branchName || "Others",
            departmentId: category.departmentId || "Others",
            departmentName: category.departmentName || "Others",
          })
        );

        // console.log("Formatted Data:", formattedData);
        setRows(formattedData);
      } else {
        // console.error("Response content is not an array", response);
      }
    } catch (error) {
      // console.error("Error fetching category data", error);
    }
  };

  const theme = useTheme();
  const [value, setValue] = useState(0);

  const columns = [
    { field: "srno", headerName: "Sr.no", width: 75 },
    { field: "departmentName", headerName: "Department", width: 250 },
    { field: "category", headerName: "Category", width: 250 },
    { field: "priority", headerName: "Priority", width: 200 },
    { field: "eta", headerName: "ETA[Hour]", width: 150 },
    ...(role == "admin"
      ? [
          {
            field: "actions",
            headerName: "Actions",
            width: 150,
            renderCell: (params: any) => (
              <strong>
                <EyeIcon
                  onClick={() => handleViewDetails(params.row)}
                  style={{ cursor: "pointer", marginRight: 8 }}
                />
                &nbsp; &nbsp;&nbsp;
                <EditIcon
                  onClick={() => handleEdit(params.row)}
                  style={{ cursor: "pointer", marginRight: 2 }}
                />
              </strong>
            ),
          },
        ]
      : []),
  ];

  const handleViewDetails = (category: any) => {
    setSelectedCategory(category);
    setShowModal(true);
  };

  const handleEdit = (category: any) => {
    setSelectedCategory(category);
    setShowEditModal(true);
  };

  const handleAddCategory = () => {
    setSelectedCategory(null);
    setShowEditModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleCategoryUpdate = (updatedCategory: any) => {
    const updatedRows = rows.map((row: any) =>
      row.id === updatedCategory.id ? { ...row, ...updatedCategory } : row
    );
    setRows(updatedRows);
    fetchCategoryData();
  };

  const formatDate = (dateArray: any) => {
    if (!Array.isArray(dateArray) || dateArray.length < 3) return "N/A";
    const [year, month, day] = dateArray; // Extract year, month, day
    return `${year}-${String(month).padStart(2, "0")}-${String(day).padStart(
      2,
      "0"
    )}`;
  };

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <Row className="my-3">
          <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
            <h4>{"Issue Type"}</h4>
          </Col>
          <Col md={8}>
            {role === "admin" && (
              <Row style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  style={{
                    width: "150px",
                    background: "green",
                    marginRight: "15px",
                  }}
                  onClick={handleAddCategory}
                >
                  + Add Issue
                </Button>
              </Row>
            )}
          </Col>
        </Row>

        <Row>
          <Col md={12}>
            <TabPanel value={value} index={0}>
              {console.log("Rows passed to TableFilter:", rows)}
              <TableFilter rows={rows} columns={columns} />
            </TabPanel>
          </Col>
        </Row>

        <EditCategoryModal
          show={showEditModal}
          onClose={() => setShowEditModal(false)}
          category={selectedCategory}
          categories={rows}
          onUpdate={handleCategoryUpdate}
        />

        <Modal show={showModal} onHide={handleCloseModal} centered>
          <Modal.Header closeButton>
            <Modal.Title>Issue Details</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {selectedCategory ? (
              <div>
                <p>
                  <strong>Department:</strong> {selectedCategory.departmentName}
                </p>
                <p>
                  <strong>Issue:</strong> {selectedCategory.category}
                </p>
                <p>
                  <strong>Priority:</strong> {selectedCategory.priority}
                </p>
                <p>
                  <strong>ETA in hour:</strong> {selectedCategory.eta}
                </p>
                <p>
                  <strong>Created At:</strong>{" "}
                  {formatDate(selectedCategory?.createdAt)}
                </p>
                <p>
                  <strong>Updated At:</strong>{" "}
                  {formatDate(selectedCategory?.updatedAt)}
                </p>
              </div>
            ) : (
              <p>Loading...</p>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={handleCloseModal}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
      </Container>
    </Layout>
  );
};

export default CategoryListPage;
