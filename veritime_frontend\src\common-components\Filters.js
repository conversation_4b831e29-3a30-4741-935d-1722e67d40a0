import React, { useState } from 'react';
import '../css/style.css';

import { Unstable_Popup as BasePopup } from '@mui/base/Unstable_Popup';
import { styled } from '@mui/system';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
const Filters = () => {
  const [anchor, setAnchor] = useState(null);

  const handleClick = (event) => {
    setAnchor(anchor ? null : event.currentTarget);
  };

  const open = Boolean(anchor);
  const id = open ? 'simple-popup' : undefined;

    return (
      <div className="filters" placeholder="test">
                <div className='carousal-container'>

         <Button aria-describedby={id} type="button" className={"filter-button"} onClick={handleClick}>
        Filter <FilterAltIcon />
      </Button>
      <BasePopup id={id} open={open} anchor={anchor}>
        <PopupBody>
          <div>
        <select className="filter-select">
          <option disabled selected>Select Department</option>
          <option>Account</option>
          <option>IT</option>
          <option>Pharmacy</option>
          <option>Nursing</option>
        </select>
        </div>
        <hr />
        <div>
        <select className="filter-select">
        <option disabled selected>Select Designation</option>
          <option>Juniour HR</option>
          <option>Pharmist</option>
          <option>Doctor</option>
          <option>Nurse</option>
        </select>
        </div>
        <hr />

        <div>
        <select className="filter-select">
        <option disabled selected>Select Schedule</option>
          <option>10:00Am - 7:00Pm</option>
          <option>7:00 - 3:00Pm</option>
          <option>3:00Am - 1:00Pm</option>
          <option>Other</option>
        </select>
        </div>
        </PopupBody>
      </BasePopup>
      </div>
      <div className='filter-container' style={{display:'inline-block'}}>
        <select className="filter-select">
          <option disabled selected>Select Department</option>
          <option>Account</option>
          <option>IT</option>
          <option>Pharmacy</option>
          <option>Nursing</option>
        </select>
        <select className="filter-select">
        <option disabled selected>Select Designation</option>
          <option>Juniour HR</option>
          <option>Pharmist</option>
          <option>Doctor</option>
          <option>Nurse</option>
        </select>
        <select className="filter-select">
        <option disabled selected>Select Schedule</option>
          <option>10:00AM - 7:00PM</option>
          <option>7:00AM - 3:00PM</option>
          <option>3:00AM - 1:00PM</option>
          <option>Other</option>
        </select>
        </div>
      </div>
    );
  };
  export default Filters;

  const grey = {
    50: '#F3F6F9',
    100: '#E5EAF2',
    200: '#DAE2ED',
    300: '#C7D0DD',
    400: '#B0B8C4',
    500: '#9DA8B7',
    600: '#6B7A90',
    700: '#434D5B',
    800: '#303740',
    900: '#1C2025',
  };
  
  const blue = {
    200: '#99CCFF',
    300: '#66B2FF',
    400: '#3399FF',
    500: '#007FFF',
    600: '#0072E5',
    700: '#0066CC',
  };
  
  const PopupBody = styled('div')(
    ({ theme }) => `
    width: max-content;
    padding: 12px 16px;
    margin: 8px;
    border-radius: 8px;
    border: 1px solid ${theme.palette.mode === 'dark' ? grey[700] : grey[200]};
    background-color: ${theme.palette.mode === 'dark' ? grey[900] : '#fff'};
    box-shadow: ${
      theme.palette.mode === 'dark'
        ? `0px 4px 8px rgb(0 0 0 / 0.7)`
        : `0px 4px 8px rgb(0 0 0 / 0.1)`
    };
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    z-index: 1;
  `,
  );
  
  const Button = styled('button')(
    ({ theme }) => `
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: ${blue[500]};
    padding: 8px 16px;
    border-radius: 8px;
    color: white;
    transition: all 150ms ease;
    cursor: pointer;
    border: 1px solid ${blue[500]};
    box-shadow: 0 2px 4px ${
      theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 127, 255, 0.5)'
    }, inset 0 1.5px 1px ${blue[400]}, inset 0 -2px 1px ${blue[600]};
  
    &:hover {
      background-color: ${blue[600]};
    }
  
    &:active {
      background-color: ${blue[700]};
      box-shadow: none;
    }
  
    &:focus-visible {
      box-shadow: 0 0 0 4px ${theme.palette.mode === 'dark' ? blue[300] : blue[200]};
      outline: none;
    }
  
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      box-shadow: none;
      &:hover {
        background-color: ${blue[500]};
      }
    }
  `,
  );