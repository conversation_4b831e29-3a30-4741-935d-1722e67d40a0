import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";
import { postRequest, putRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showErrorAlert, showSuccessAlert2 } from "@/services/alertService";
import moment from "moment";

const CreateHolidayModal = ({
  show,
  handleClose,
  holiday,
  fetchHolidays,
}: any) => {
  const [formData, setFormData] = useState({
    date: "",
    holiday: "",
    isOptional: false,
    id: "",
    createdByName: "",
  });
  const [errors, setErrors] = useState<any>({});

  useEffect(() => {
    if (holiday) {
      // Convert the date array to YYYY-MM-DD format
      let formattedDate = "";
      if (holiday.date) {
        if (Array.isArray(holiday.date)) {
          formattedDate = `${holiday.date[0]}-${String(
            holiday.date[1]
          ).padStart(2, "0")}-${String(holiday.date[2]).padStart(2, "0")}`;
        } else if (typeof holiday.date === "string") {
          const [day, month, year] = holiday.date.split("-");
          formattedDate = `${year}-${month}-${day}`;
        }
      }

      setFormData({
        date: formattedDate,
        holiday: holiday.holiday,
        isOptional: holiday.isOptional,
        id: "",
        createdByName: holiday.createdByName,
      });
      console.log("Original date:", holiday.date);
      console.log("Formatted date:", formattedDate);
    } else {
      setFormData({
        date: "",
        holiday: "",
        isOptional: false,
        id: "",
        createdByName: "",
      });
    }
    console.log("Updated formData state:", formData);
  }, [holiday]);
  //console.log(1)

  const handleChange = (e: any) => {
    const { name, value, type, checked } = e.target;
    if (name === "date" && moment(value).year().toString().length > 4) {
      showErrorAlert(
        "The maximum allowed year is 4 digits. Please enter a valid year"
      );
    } else {
      setFormData({
        ...formData,
        [name]: type === "checkbox" ? checked : value,
      });
    }
  };
  const validateForm = () => {
    let formErrors: any = {};
    if (!formData.date) formErrors.date = "Feild is required";
    if (!formData.holiday) formErrors.holiday = "Feild required";
    return formErrors;
  };

  const handleSave = async () => {
    const formErrors = validateForm();
    if (Object.keys(formErrors).length === 0) {
      try {
        let response: any;
        if (holiday) {
          response = await putRequest(
            `${API_URLS.UPDATE_HOLIDAY(holiday?.id)}`,
            formData
          );
          console.log("API Response for Update:", response);
          showSuccessAlert2("Holiday updated successfully!");
        } else {
          response = await postRequest(API_URLS.CREATE_HOLIDAY, formData);
          console.log("API Response for Create:", response);
          showSuccessAlert2("Holiday created successfully!");
        }
        if (response) {
          fetchHolidays();
          handleClose(response);
        }
      } catch (error) {}
    }
  };

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>{holiday ? "Edit Holiday" : "Create Holiday"}</Modal.Title>
      </Modal.Header>
      <label
        htmlFor="ticketSubject"
        className="ticket-text-primary"
        style={{ marginTop: "10px", marginLeft: "14px" }}
      >
        <span className="text-danger">
          The fields with * marks are mandatory
        </span>
      </label>
      <Modal.Body>
        <Form>
          <Form.Group controlId="holidayDate">
            <Form.Label>
              {" "}
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Date <span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Form.Control
              type="date"
              name="date"
              value={formData.date}
              onChange={handleChange}
              style={{ marginBottom: "20px" }}
            />
            <Form.Control.Feedback type="invalid">
              {errors.date}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group controlId="holidayName">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Holiday Name <span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Form.Control
              type="text"
              name="holiday"
              value={formData.holiday}
              onChange={handleChange}
              style={{ marginBottom: "20px" }}
            />
            <Form.Control.Feedback type="invalid">
              {errors.holiday}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group controlId="holidayOptional">
            <Form.Check
              type="checkbox"
              label="Is Optional"
              name="isOptional"
              checked={formData.isOptional}
              onChange={handleChange}
            />
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSave}>
          {holiday ? "Update" : "Save"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateHolidayModal;
