import { showErrorAlert, showSuccessAlert } from "@/services/alertService";
import React from "react";
import { fileDownload, getRequest } from "../../../services/apiService";
import { API_URLS } from "../../../constants/apiConstants";
import "../TicketRaise/Subject.css";
import "../TicketRaise/Subject.css";

type TicketStatus = "Open" | "close" | "hold" | "reopen";

// const handleDownload = (file: any) => {
//   try {
//     const fileName = file.split("/").pop();
//     const response:any = fileDownload(
//       `${API_URLS?.TICKET_DOWNLOAD}${file}`,
//       true
//     );

//     if (!response.ok) {
//       throw new Error("Network response was not ok");
//     }

//     const blob = response.blob();
//     const urlBlob = window.URL.createObjectURL(blob);

//     const a = document.createElement("a");
//     a.href = urlBlob;
//     a.target = "_blank";
//     a.download = fileName;
//     document.body.appendChild(a);
//     a.click();
//     a.remove();
//     showSuccessAlert(`File "${fileName}" downloaded successfully.`);
//   } catch (error) {
//     showErrorAlert("No file for now:");
//   }
// };

const handleDownload = (file: any) => {
  const fileName = file.split("/").pop();

  fileDownload(`${API_URLS?.TICKET_DOWNLOAD}${file}`, true)
    .then((response: any) => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.blob(); // Returning the Promise of blob()
    })
    .then((blob: Blob) => {
      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.target = "_blank";
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();

      showSuccessAlert(`File "${fileName}" downloaded successfully.`);
    })
    .catch(() => {
      showErrorAlert("No file for now:");
    });
};

const TicketHeaderCard = ({ details }: { details: any }) => {
  const {
    ticketId,
    subject,
    description,
    department,
    category,
    assigned_to,
    createdByName,
    createdAt,
    status,
    file,
    priority,
    escalatedByName,
    escalatedAt,
    escalationDetails,
    allowReply,
  } = details;

  const isFileAvailable = file && file.trim() !== null;

  const escalationLevel = escalationDetails.length;
  const escalationBadgeColor =
    escalationLevel === 1 ? "#FF9C05" : escalationLevel === 2 ? "#E4080A" : "";

  const statusMapping: { [key in TicketStatus]: string } = {
    Open: "In Progress",
    close: "Resolved",
    hold: "Hold",
    reopen: "Reopened",
  };

  const statusLabel =
    statusMapping[status as TicketStatus] ||
    status.charAt(0).toUpperCase() + status.slice(1);

  return (
    <div
      className="ticket-detail-container"
      style={{
        textAlign: "left",
        backgroundColor: "white",
        borderRadius: "7px",
        boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
        width: "100%",
        minHeight: "350px",
        display: "flex",
        flexDirection: "column",
        gap: "15px",
        padding: "20px",
        marginRight: "25px",
        fontSize: "18px",
      }}
    >
      <h4
        className="ticket-title2"
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "10px",
        }}
      >
        <span
          className="ticketlabel"
          style={{ fontWeight: "bold", fontSize: "18px" }}
        >
          Ticket Details
        </span>

        <span
          className="badge"
          style={{
            backgroundColor: escalationBadgeColor,
            color: "white",
            padding: "3px 8px",
            borderRadius: "5px",
            fontSize: "15px",
            marginLeft: "auto",
            marginBottom: "7px",
          }}
        >
          {escalationLevel === 1 ? "Escalated" : "Escalated"}
        </span>
      </h4>

      <div
        className="ticket-row"
        style={{
          display: "flex",
          height: "50px",
          justifyContent: "space-between",
          alignItems: "center",
          fontSize: "18px",
          paddingBottom: "14px",
          borderBottom: "1px solid #d3d3d3",
        }}
      >
        <div className="ticket-field">
          <span className="ticketlabel">Ticket Number:</span>{" "}
          <span className="value">{ticketId}</span>
        </div>
        <div
          style={{
            display: "flex",
            flex: "1",
            width: "572px",
            marginLeft: "0px",
          }}
        >
          <span className="ticketlabel">Status:</span>
          <span className="value" style={{ marginLeft: "10px" }}>
            {statusLabel}
          </span>
        </div>

        <div className="ticket-field attachment">
          {isFileAvailable && (
            <>
              <span className="ticket-field.attachment label">Attachment:</span>

              <a
                className={`text-primary p-1 ${!allowReply ? "disabled" : ""}`}
                onClick={
                  !allowReply
                    ? (e) => e.preventDefault()
                    : () => handleDownload(file)
                }
                style={{
                  cursor: allowReply ? "pointer" : "not-allowed",
                  marginTop: "-10px",
                  marginBottom: "-10px",
                  pointerEvents: allowReply ? "auto" : "none",
                }}
                title={
                  !allowReply ? "You are not allowed to download this file" : ""
                }
              >
                <img
                  src="/image/download.png"
                  alt="Download"
                  style={{ width: "25px", marginLeft: "-10px" }}
                />
              </a>
              {/* <a
  className={`text-primary p-1`}
  onClick={() => handleDownload(file)}
  style={{
    cursor: "pointer",
    marginTop: "-10px",
    marginBottom: "-10px",
    pointerEvents: "auto",
  }}
  title=""
>
  <img src="/image/download.png" alt="Download" style={{ width: "25px", marginLeft: "-10px" }} />
</a> */}
            </>
          )}
        </div>
      </div>

      <div className="ticket-row" style={{ height: "auto" }}>
        <div className="ticket-field">
          <span className="ticketlabel">Subject:</span>
          <span className="value">{subject}</span>
        </div>
      </div>
      <div className="ticket-row" style={{ height: "auto" }}>
        <div className="ticket-field">
          <span className="ticketlabel">Description:</span>
          {typeof description === "string" &&
          description.includes("<") &&
          description.includes(">") ? (
            <span
              className="value with-margin"
              dangerouslySetInnerHTML={{ __html: description }}
            />
          ) : (
            <span className="value">{description}</span>
          )}
        </div>
      </div>
      <div className="ticket-row" style={{ height: "50px" }}>
        <div className="ticket-field">
          <span className="ticketlabel">Department:</span>
          <span className="value">{department}</span>
        </div>
        <div className="ticket-field">
          <span className="ticketlabel">Issue Type:</span>
          <span className="value">
            {category}
            {priority && (
              <span
                className="priority-badge"
                style={{
                  backgroundColor:
                    priority === "High"
                      ? "#ff4d4d"
                      : priority === "Medium"
                      ? "#ffcc00"
                      : priority === "Low"
                      ? "#28a745"
                      : //: "#007bff",
                        "#ff4d4d",
                  color: "#black",
                  display: "inline-block",
                  marginLeft: "5.5px",
                }}
              >
                {priority}
              </span>
            )}
          </span>
        </div>

        <div className="ticket-field assignedto">
          <span className="ticket-field.assignedto label">Assigned To:</span>
          <span className="ticket-field.assignedto .value">{assigned_to}</span>
        </div>
      </div>

      <div className="ticket-row" style={{ height: "50px" }}>
        <div className="ticket-field">
          <span className="ticketlabel">Created By:</span>
          <span className="value">{createdByName}</span>
        </div>
        <div className="ticket-field CreatedAt">
          <span className="ticket-field.CreatedAt label">Created At:</span>
          <span className="ticket-field.CreatedAt .value">{createdAt}</span>
        </div>
      </div>

      {escalationLevel > 0 && (
        <div className="ticket-row" style={{ height: "50px" }}>
          <div className="ticket-field">
            <span className="ticketlabel">Escalated By:</span>
            <span className="value">{escalationDetails[0].createdByName}</span>
          </div>

          <div className="ticket-field CreatedAt">
            <span className="ticket-field.CreatedAt label">
              Escalated Time:
            </span>
            <span className="ticket-field.CreatedAt .value">
              {escalationDetails[0].createdAt}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default TicketHeaderCard;
