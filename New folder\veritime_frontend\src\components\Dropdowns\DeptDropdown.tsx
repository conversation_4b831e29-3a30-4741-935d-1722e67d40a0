import React, { useState } from "react";
import Select, { components } from "react-select";
import { Checkbox } from "@material-ui/core";
import { Modal } from "react-bootstrap";

const CustomOption = (props: any) => {
  return (
    <components.Option {...props}>
      <input type="checkbox" checked={props.isSelected} />
      <label style={{ marginLeft: "10px" }}>{props.label}</label>
    </components.Option>
  );
};

const DepartmentDropdown = ({
  departments,
  onChange,
  isMulti = true,
  value,
}: {
  departments: any[];
  onChange: (selected: any) => void;
  isMulti?: boolean;
  value: any[] | null;
}) => {
  const [showModal, setShowModal] = useState(false);

  const toggleModal = () => setShowModal(!showModal);

  const handleChange = (selectedOptions: any) => {
    if (
      selectedOptions &&
      selectedOptions.some((option: any) => option.value === "select-all")
    ) {
      if (value && value.length === departments.length) {
        onChange([]);
      } else {
        const allValues = departments.map(
          (department) => department.departmentId
        );
        onChange(allValues);
      }
    } else {
      if (isMulti) {
        const selectedValues = selectedOptions
          ? selectedOptions.map((option: any) => option.value)
          : [];
        onChange(selectedValues);
      } else {
        const selectedValue = selectedOptions ? selectedOptions.value : null;
        onChange(selectedValue);
      }
    }
  };

  const getSelectedDepartments: any = () => {
    if (isMulti) {
      return value
        ? value
            .map((id: string) => {
              const department = departments.find((d) => d.departmentId === id);
              return department
                ? {
                    label: department.departmentName,
                    value: department.departmentId,
                  }
                : null;
            })
            .filter(Boolean)
        : [];
    } else {
      const department = departments.find((d) => d.departmentId === value);
      return department
        ? { label: department.departmentName, value: department.departmentId }
        : null;
    }
  };
  const MultiValue = (props: any) => {
    const values = props.getValue();
    const isLastVisible = props.index === 1;
    const extraCount = values.length - 1;

    return isLastVisible && extraCount > 0 ? (
      <div onClick={toggleModal} style={{ cursor: "pointer", color: "grey" }}>
        +{extraCount}
      </div>
    ) : (
      props.index < 2 && (
        <components.MultiValue {...props}>
          <span>{props.data.label}</span>
        </components.MultiValue>
      )
    );
  };

  const optionsWithSelectAll = [
    {
      label:
        value && value.length === departments.length
          ? "Deselect All"
          : "Select All",
      value: "select-all",
    },
    ...departments.map((department) => ({
      label: department.departmentName,
      value: department.departmentId,
    })),
  ];

  const customStyles = {
    menu: (provided: any) => ({
      ...provided,
      overflowY: "auto",
    }),
    multiValue: (styles: any) => ({
      ...styles,
      backgroundColor: "#e2e2e2",
      borderRadius: "2px",
      marginRight: "5px",
      padding: "3px",
      display: "flex",
      alignItems: "center",
    }),

    multiValueLabel: (styles: any) => ({
      ...styles,
      whiteSpace: "nowrap",
    }),
    multiValueRemove: (styles: any) => ({
      ...styles,
      cursor: "pointer",
      ":hover": {
        backgroundColor: "#ff6b6b",
        color: "white",
      },
    }),
    control: (provided: any, state: any) => ({
      ...provided,
      minHeight: "40px",
      height: "40px",
      display: "flex",
      width: "50%",
      "@media (min-width: 768px) and (max-width: 1023px)": {
        width: "200px",
      },
      "@media (min-width: 1024px) and (max-width: 1199px)": {
        width: "170px",
      },
      "@media (min-width: 1200px) and (max-width: 1299px)": {
        width: "230px",
      },
      "@media (min-width: 1300px) and (max-width: 1599px)": {
        width: "240px",
      },
      "@media (min-width: 1600px)": {
        width: "260px",
      },
    }),
    indicatorsContainer: (styles: any) => ({
      ...styles,
      marginLeft: "auto",
    }),
    valueContainer: (styles: any) => ({
      ...styles,
      display: "flex",
      flexWrap: "wrap",
      maxHeight: "40px",
      overflowY: value && value.length > 2 ? "auto" : "hidden",
    }),
    option: (styles: any, { isSelected }: any) => ({
      ...styles,
      backgroundColor: isSelected ? "transparent" : "transparent",
      color: "black",
      ":hover": {
        backgroundColor: "transparent",
      },
      fontSize: "14px", // Default font size
      "@media (min-width: 1025px) and (max-width: 1600px)": {
        fontSize: "12px",
      },
      "@media (min-width: 769px) and (max-width: 1024px)": {
        fontSize: "10px",
      },
      "@media (min-width: 481px) and (max-width: 768px)": {
        fontSize: "8px",
      },
      "@media (max-width: 480px)": {
        fontSize: "6px",
      },
    }),
    placeholder: (styles: any) => ({
      ...styles,
      fontSize: "16px",
      "@media (min-width: 1201px) and (max-width: 1600px)": {
        fontSize: "14px",
      },
      "@media (min-width: 1025px) and (max-width: 1200px)": {
        fontSize: "12px",
      },
      "@media (min-width: 769px) and (max-width: 1024px)": {
        fontSize: "10px",
      },
      "@media (max-width: 768px)": {
        fontSize: "8px",
      },
    }),
  };

  return (
    <>
      <Select
        isMulti={isMulti}
        closeMenuOnSelect={!isMulti}
        isClearable={!isMulti}
        hideSelectedOptions={false}
        components={{ Option: CustomOption, MultiValue }}
        options={optionsWithSelectAll}
        value={getSelectedDepartments()}
        onChange={handleChange}
        styles={customStyles}
        placeholder="Select Department"
        isSearchable={true}
      />
      <Modal show={showModal} onHide={toggleModal}>
        <Modal.Body>
          {getSelectedDepartments().map((item: any) => (
            <div
              key={item.value}
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "5px",
              }}
            >
              <span style={{ marginRight: "auto" }}>{item.label}</span>
              <button
                onClick={() =>
                  onChange(value?.filter((val: string) => val !== item.value))
                }
                style={{
                  border: "none",
                  background: "none",
                  color: "red",
                  cursor: "pointer",
                }}
              >
                ✕
              </button>
            </div>
          ))}
        </Modal.Body>
      </Modal>
    </>
  );
};

export default DepartmentDropdown;
