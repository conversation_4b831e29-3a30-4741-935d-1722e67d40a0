package com.company.wfm.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorProductsRequestDTO {

    public Long vendorId;
    public int offset=0;
    public int limit=10;




    public VendorProductsRequestDTO(Long vendorId, Integer offset, Integer limit) {
        this.vendorId = vendorId;
        this.offset = offset; // Keep it as is, can be null
        this.limit = limit;   // Keep it as is, can be null
    }


}
