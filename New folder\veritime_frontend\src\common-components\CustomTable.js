import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

export default function CustomTable() {
    const [customers, setCustomers] = useState([]);
    const approvalData = [
        {
          id: '#AHGA2',
          requestDate: '23/05/2024',
          name: '<PERSON>',
          leaveType: 'Maternity',
          from: "23/06/2024", to: "23/07/2024",
          remark: 'One month a head',
          status: 'Pending',
        action:<button className="deny-button">Cancel Leave</button> 
        },
        { id: "#AHGA48", requestDate: "20/05/2024", name: "<PERSON>", leaveType: "Vacation", from: "02/06/2024", to: "03/08/2024", remark: "Personal", status: "Pending", action:<button className="deny-button">Cancel Leave</button>  },
        { id: "#AHGA68", requestDate: "19/05/2024", name: "<PERSON>", leaveType: "Sick", from: "19/05/2024", to: "19/05/2024", remark: "Not feel well", status: "Accepted" , action:<button className="deny-button">Cancel Leave</button> },
        { id: "#AHGA78", requestDate: "18/05/2024", name: "Jacob Marcus", leaveType: "Sick", from: "19/05/2024", to: "19/05/2024", remark: "Not feel well", status: "Accepted", action:<button className="deny-button">Cancel Leave</button>  },
        { id: "#AHGA38", requestDate: "14/05/2024", name: "Jacob Marcus", leaveType: "Vacation", from: "02/06/2024", to: "03/06/2024", remark: "Holiday", status: "Rejected", action:<button className="deny-button">Cancel Leave</button>  },
        { id: "#AHGA89", requestDate: "12/05/2024", name: "Jacob Marcus", leaveType: "Sick", from: "19/05/2024", to: "19/05/2024", remark: "One month ahead", status: "Rejected" , action:<button className="deny-button">Cancel Leave</button> },
        { id: "#AHGA11", requestDate: "10/05/2024", name: "Jacob Marcus", leaveType: "Vacation", from: "02/06/2024", to: "03/06/2024", remark: "Holiday", status: "Accepted", action:<button className="deny-button">Cancel Leave</button>  },
        { id: "#AHGA51", requestDate: "06/05/2024", name: "Jacob Marcus", leaveType: "Vacation", from: "02/06/2024", to: "03/06/2024", remark: "Personal", status: "Accepted", action:<button className="deny-button">Cancel Leave</button>  },
        { id: "#AHGA10", requestDate: "02/05/2024", name: "Jacob Marcus", leaveType: "Vacation", from: "02/06/2024", to: "03/06/2024", remark: "Personal", status: "Accepted" , action:<button className="deny-button">Cancel Leave</button> },
  
        // Add more data as needed
      ];
    
    return (
        <div className="card">
            <DataTable value={approvalData} paginator rows={5}  tableStyle={{ minWidth: '50rem', height:'200px' }}>
                <Column field="requestDate" header="Request Date" style={{ width: '15%' }}></Column>
                <Column field="name" header="Name" style={{ width: '15%' }}></Column>
                <Column field="leaveType" header="Leave Type" style={{ width: '15%' }}></Column>
                <Column field="from" header="From" style={{ width: '15%' }}></Column>
                <Column field="to" header="To" style={{ width: '15%' }}></Column>
                <Column field="remark" header="Remark" style={{ width: '15%' }}></Column>
                <Column field="status" header="Status" style={{ width: '15%' }}></Column>
                <Column field="action" header="" style={{ width: '15%' }}> </Column>

            </DataTable>
            
        </div>
    );
}
        