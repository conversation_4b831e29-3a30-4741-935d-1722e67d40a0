"use client";
import { useEffect, useState } from "react";
import ChatLayout from "../../../../modules/Ticket/Eyebtn/ChatLayout";
import TicketId from "../../../../modules/Ticket/Eyebtn/TicketId";
import Layout from "../../../../components/Layout";
import { colors } from "@constants/colors";
import TicketHistory from "../../../../modules/Ticket/TicketRaise/Tickethistory";
import TicketHeaderCard from "../../../../modules/Ticket/TicketRaise/TicketHeaderCard";
import {
  postRequest,
  putRequest,
  postRequestWithSecurity,
} from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showSuccessAlert, showSuccessAlert2 } from "@/services/alertService";
import SupportPersonForm from "../../../../modules/Ticket/ViewTicket/SupportPersonForm";
import Link from "next/link";
import { decryptData } from "@/utils/encryption";
import "../[id]/page.css";
import useLocalStorage from "@/services/localstorage";
import { margin } from "@mui/system";
import { postRequestWithOnlyResponseDecrypted } from "../../../../services/apiService";

const EyebtnTicket = () => {
  const [selectedTicket, setSelectedTicket] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [showSupportForm, setShowSupportForm] = useState(false);
  const [replyMessage, setReplyMessage] = useState("");
  const [ticketStatus, setTicketStatus] = useState("");
  const [pendingStatus, setPendingStatus] = useState("");
  const [isSuccess, setIsSuccess] = useState(false);
  const [ticket, setTicket] = useState(null);
  const [sendToClient, setSendToClient] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [fileAttachment, setFileAttachment] = useState(null);
  const [remark, setRemark] = useState("");
  const [notifyCreator, setNotifyCreator] = useState(true);
  const [showButtons, setShowButtons] = useState();
  const [isCreator, setIsCreator] = useState(false);
  const [isLoading, setIsloading] = useState(false);
  const [escalateTicket, setEscalateTicket] = useState(false);
  const [role, setRole] = useLocalStorage("role", "");
  const [isAssigned, setIsAssigned] = useState(false);
  // This code is commented out for testing purpose (Escalation btn disable)
  // const [isEscalateDisabled, setIsEscalateDisabled] = useState(true);
  useEffect(() => {
    getTicketDetails();
    if (typeof window !== "undefined") {
      if (
        !localStorage.getItem("accessToken") ||
        !localStorage.getItem("role")
      ) {
        localStorage.clear();
        localStorage.setItem("currentPath", window.location.href);

        window.location.href = "/login?sessionExpired=1";
      }
    }
  }, []);
  const handleSupportClick = () => setShowSupportForm(true);

  const handleShowModal = () => setShowModal(true);
  const handleCloseModal = () => setShowModal(false);
  const [escalationError, setEscalationError] = useState("");

  const handleCloseTicket = () => {
    setIsloading(true);
    setTimeout(() => {
      setTicketStatus("");

      setIsSuccess(true);
    }, 10);
  };

  const handleShowStatusModal = () => {
    //setTicketStatus("");
    setRemark("");
    setShowStatusModal(true);
  };

  const handleCloseStatusModal = () => {
    //setTicketStatus("");
    setShowStatusModal(false);
  };

  const [showEscalateModal, setShowEscalateModal] = useState(false);
  const [escalateRemark, setEscalateRemark] = useState("");

  const handleEscalateClick = () => {
    setShowEscalateModal(true);
  };

  // const handleBackClick = () => {
  //   window.history.back();
  //   // const previousPage = localStorage.getItem("previousPage");

  //   // if (previousPage === "escalateTicket") {
  //   //   window.history.pushState(null, "", "/ticket/escalateTicket");
  //   // } else if (previousPage === "viewTicket") {
  //   //   window.history.pushState(null, "", "/ticket/viewTickets");
  //   // } else {
  //   //   window.history.pushState(null, "", "/ticket/viewTickets");
  //   // }
  // };

  const handleBackClick = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      window.location.href = "/ticket/viewTickets"; // Default fallback
    }
  };

  const handleUpdateModal = () => {
    if (!remark) {
      alert("Remark is required before updating the ticket.");
      return;
    }
    if (!escalateRemark) {
      alert("Escalation remark is required before submitting the escalation.");
      return;
    }
    try {
      const ticketData = {
        ticketId: ticket.ticketId,
        status: ticketStatus,
        responseRemark: replyMessage,

        ...(sendToClient && { sendToClient: sendToClient }),
        ...(fileAttachment && { file: fileAttachment }),
      };

      const response = putRequest(API_URLS.UPDATE_TICKET, ticketData);

      if (response) {
        showSuccessAlert2(response);
        handleCloseModal();
      } else {
        showSuccessAlert2(response);
      }
    } catch (error) {}
  };

  const handleStatusChange = () => {
    if (!remark) {
      alert("Remark is required before submitting the status change.");
      return;
    }

    try {
      const statusToSubmit =
        ticket.status === "close" ? "reopen" : ticketStatus;

      setTicket((prevTicket) => ({
        ...prevTicket,
        status: statusToSubmit,
      }));

      setIsloading(true);
      const formdata = new FormData();

      formdata.append(
        "ticket",
        JSON.stringify({
          ticketId: ticket.ticketId,
          responseRemark: remark,
          status: statusToSubmit,
          notifyCreator,
        })
      );

      postRequestWithOnlyResponseDecrypted(API_URLS.Status_Update, formdata)
        .then((response) => {
          console.log("API Response:", response);

          //  If response is a string
          const successMessage =
            typeof response === "string"
              ? response
              : response?.responsedata?.message ||
                "Ticket status updated successfully!";

          showSuccessAlert2(successMessage);

          setRemark("");
          handleCloseStatusModal();
        })
        .catch((error) => {
          console.error(" Error updating status:", error);
          showErrorAlert("Something went wrong. Please try again.");
        })
        .finally(() => {
          setIsloading(false);
        });
    } catch (error) {
      console.error(" Unexpected error:", error);
      setIsloading(false);
    }
  };

  const handleEscalateToggle = () => {
    const newEscalateState = !escalateTicket;
    setEscalateTicket(newEscalateState);
    if (ticketStatus === "reopen" && !escalateTicket) {
    }
  };

  const submitEscalationRequest = () => {
    if (!escalateRemark) {
      setEscalationError(
        "Escalation remark is required before submitting the escalation."
      );
      return; // Return early to prevent submission
    }
    setEscalationError("");
    try {
      const payload = {
        ticketId: ticket.ticketId,
        escalationRemark: "Kindly solve this ticket",
        escalate: true,
      };

      const response = postRequestWithSecurity(
        API_URLS.EXCALATE_TICKET,
        payload
      );

      if (response) {
        showSuccessAlert2(response);

        setRemark("");
      } else {
      }
    } catch (error) {}
  };
  const handleReplyUpdate = () => {
    try {
      setIsloading(true);

      const formdata = new FormData();
      formdata.append("files", fileAttachment);

      formdata.append(
        "ticket",
        new Blob(
          [
            JSON.stringify({
              ticketId: ticket.ticketId,
              responseRemark: replyMessage,
            }),
          ],
          { type: "application/json" }
        )
      );

      const response = postRequestWithOnlyResponseDecrypted(
        API_URLS.Status_Update,
        formdata
      );

      if (response) {
        showSuccessAlert2(response);

        // Update ticket history
        const userData = decryptData(localStorage.getItem("userData"));
        const history = [
          {
            createdBy: userData?.empId,
            createdByName: userData?.empName,
            remark: replyMessage,
            createdAt: new Date(),
          },
          ...ticket.history,
        ];
        setTicket({
          ...ticket,
          history: history,
        });

        setReplyMessage("");
        handleCloseModal();
        setIsloading(false);
      } else {
        showSuccessAlert2("Failed to update the reply.");
        setRemark("");
      }
    } catch (error) {
      setIsloading(false);
    }
  };

  const handleEscalate = () => {
    setIsloading(true);

    const payload = {
      ticketId: ticket.ticketId,
      escalationRemark: escalateRemark,
      escalate: true,
    };

    postRequestWithSecurity(API_URLS.EXCALATE_TICKET, payload)
      .then((response) => {
        console.log(" Escalate API Response:", response);

        if (response) {
          const successMessage =
            typeof response === "string"
              ? response
              : response?.responsedata?.message ||
                "Ticket escalated successfully!";

          showSuccessAlert2(successMessage);
          setRemark("");
          setShowEscalateModal(false);
        } else {
          showErrorAlert("Unexpected response. Please try again.");
        }
      })
      .catch((error) => {
        console.error(" Error escalating ticket:", error);
        showErrorAlert("Something went wrong. Please try again.");
      })
      .finally(() => {
        setIsloading(false);
      });
  };

  const [myEmpId, setMyEmpId] = useState(null);

  // NEW FUNCTION CREATED TO DISTRIBUT FUNCTIOANLITY

  const getTicketDetails = async () => {
    try {
      setIsloading(true);
      console.log("Fetching ticket details...");

      const response = await postRequestWithSecurity(API_URLS.TICKET_DETAILS, {
        ticketId: parseInt(atob(window.location.pathname?.split("/")?.[3])),
      });

      console.log("Response received:", response);
      if (!response) throw new Error("Empty response received");

      setTicket(response); //  Set ticket first
    } catch (error) {
      console.error("Error fetching ticket details:", error);
    } finally {
      setIsloading(false);
    }
  };

  const getUserData = async () => {
    try {
      const encryptedUserData = localStorage.getItem("userData");
      if (!encryptedUserData)
        throw new Error("No user data found in localStorage");

      const userData = await decryptData(encryptedUserData);
      console.log(" Decrypted User Data:", userData);

      setMyEmpId(userData?.empId);
    } catch (error) {
      console.error("Error decrypting user data:", error);
    }
  };

  useEffect(() => {
    if (!ticket || !myEmpId) return; //  Ensure both ticket & myEmpId exist

    //  Extract createdBy from the first history entry
    const createdBy =
      ticket.history?.length > 0 ? ticket.history[0].createdBy : null;

    //  Extract assignedTo from the latest history entry where assignedTo is present
    const assignedToEntry = ticket.history?.find(
      (entry) => entry.assignedTo !== null
    );
    const assignedTo = assignedToEntry ? assignedToEntry.assignedTo : null;

    //  Extract escalatedTo from the latest escalationDetails entry
    const latestEscalation =
      ticket.escalationDetails?.length > 0
        ? ticket.escalationDetails[ticket.escalationDetails.length - 1]
        : null;
    const escalatedTo = latestEscalation ? latestEscalation.escalatedTo : null;
    console.log("Ticket EMPID:", ticket.empId);

    console.log("Ticket Data:", ticket);
    console.log("Created By:", createdBy);
    console.log("Assigned To:", assignedTo);
    console.log("Escalation To:", escalatedTo);

    //  Set visibility based on user role in ticket
    const showButtons =
      createdBy === myEmpId ||
      assignedTo === myEmpId ||
      escalatedTo === myEmpId;
    console.log("Show Buttons:", showButtons);

    setShowButtons(showButtons);
  }, [ticket, myEmpId]);

  useEffect(() => {
    getTicketDetails();
    getUserData();
  }, []);

  // const getTicketDetails = () => {
  //   try {
  //     setIsloading(true);
  //     console.log("Fetching ticket details...");

  //     postRequestWithSecurity(API_URLS.TICKET_DETAILS, {
  //       ticketId: parseInt(atob(window.location.pathname?.split("/")?.[3])),
  //     })
  //       .then((response) => {
  //         console.log("Response received:", response);
  //         if (!response) {
  //           throw new Error("Empty response received");
  //         }

  //         setTicket(response);
  //         setTicketStatus(response.status);

  //         const encryptedUserData = localStorage.getItem("userData");
  //         if (!encryptedUserData) {
  //           throw new Error("No user data found in localStorage");
  //         }

  //         decryptData(encryptedUserData)
  //           .then((userData) => {
  //             console.log("🔹 Decrypted User Data:", userData); // Debug full user data

  //             const myEmpId = userData?.empId;
  //             console.log("🔹 Extracted Employee ID:", myEmpId); // Check if empId exists

  //             if (!myEmpId) {
  //               console.error(" Employee ID is missing or undefined!");
  //               throw new Error("Failed to retrieve Employee ID from userData");
  //             }

  //             // ✅ Check if Employee ID matches ticket data
  //             console.log("🔹 Ticket Data:", ticket);
  //             console.log("🔹 Created By:", ticket?.createdBy);
  //             console.log("🔹 Assigned To:", ticket?.assigned_to);
  //             console.log("🔹 Escalation Details:", ticket?.escalationDetails);

  //             const showButtons =
  //               ticket?.createdBy === myEmpId || // If the user is the creator
  //               console.log(
  //                 ticket?.createdBy === myEmpId,
  //                 "ticket?.createdBy === myEmpId "
  //               );
  //             ticket?.assigned_to === myEmpId || // If the user is assigned
  //               console.log(
  //                 ticket?.assigned_to === myEmpId,
  //                 " ticket?.assigned_to === myEmpId"
  //               )(
  //                 ticket?.escalationDetails?.length > 0 &&
  //                   ticket?.escalationDetails.some((e) => e.userId === myEmpId)
  //               ); // If user is in escalated list

  //             console.log("✅ Show Buttons:", showButtons);
  //             setShowButtons(showButtons);
  //           })
  //           .catch((error) => {
  //             console.error("Error decrypting user data:", error);
  //           });
  //       })
  //       .catch((error) => {
  //         console.error("Error fetching ticket details:", error);
  //       })
  //       .finally(() => {
  //         console.log("Finished fetching ticket details.");
  //         setIsloading(false);
  //       });
  //   } catch (error) {
  //     console.error("Unexpected error:", error);
  //   }
  // };

  // Conditional render for ticket loading
  if (!ticket) {
    console.log("Ticket not available, showing loading...");
    return <div>Loading...</div>;
  }

  const handleSupportPersonCloseForm = (remark, empName) => {
    if (remark && empName) {
      // Backup the old history
      let oldData = ticket?.history || [];

      const userData = decryptData(localStorage.getItem("userData"));
      oldData.unshift({
        createdBy: userData?.empId,
        createdByName: userData?.empName,
        image: process.env.NEXT_PUBLIC_S3_URL + userData?.imgUre,
        remark: `Ticket number: ${ticket?.ticketId} has been assigned to ${empName}. Remark: ${remark}`,
        createdAt: new Date(),
        files: userData?.files,
        assignedToName: empName,
      });

      setTicket({
        ...ticket,
        history: oldData,
        assignedToName: empName,
      });
    }

    setShowSupportForm(false);
  };

  const updateAssignment = () => {
    fetchUpdatedTicket(); // Refetch ticket details after updating assignment
  };

  // const updateTicketDetails = (data) => {
  //   try {
  //     setIsloading(true);
  //     const response = postRequestWithSecurity(
  //       API_URLS.TICKET_DETAILS,
  //       {
  //         ticketId: data,
  //       },
  //       true
  //     );

  //     if (response) {
  //       setIsloading(false);
  //       setTicket(response);
  //       window.history.pushState(null, "", "/ticket/details/" + btoa(data));
  //     }
  //   } catch (error) {}
  // };

  const updateTicketDetails = (data) => {
    try {
      setIsloading(true);
      postRequestWithSecurity(API_URLS.TICKET_DETAILS, { ticketId: data }, true)
        .then((response) => {
          setIsloading(false);
          if (response) {
            setTicket(response);
            window.history.pushState(null, "", "/ticket/details/" + btoa(data));
          }
        })
        .catch((error) => {
          console.error("Error updating ticket details:", error);
          setIsloading(false);
        });
    } catch (error) {
      console.error("Unexpected error:", error);
    }
  };

  return (
    <Layout>
      <div
        className="container-fluid mt-3"
        style={{ backgroundColor: colors.blue12 }}
      >
        <div
          className="container-fluid mt-3"
          style={{ backgroundColor: colors.blue12 }}
        >
          <div className="d-flex justify-content-between align-items-center p-3">
            <div
              className="d-flex align-items-center"
              style={{ width: "100%" }}
            >
              <div className="d-flex align-items-center">
                {/* <Link href={"/ticket/viewTickets"}> */}
                <div onClick={handleBackClick}>
                  <img
                    src="/image/backArrowticket.png"
                    style={{
                      width: "40px",
                      height: "40px",
                      borderRadius: "50%",
                      cursor: "pointer",
                    }}
                  />
                  {/* </Link> */}
                </div>
              </div>
              <div
                className="d-flex justify-content-center align-items-center"
                style={{ flex: 1 }}
              ></div>
              {showButtons && (
                <button
                  className="btn d-flex align-items-center"
                  style={{
                    backgroundColor: "white",
                    marginRight: "20px",
                    cursor: "pointer",
                  }}
                  onClick={handleEscalateClick}
                  // This code is commented out for testing purpose (Escalation btn disable)
                  // disabled={isEscalateDisabled}
                >
                  Escalate
                </button>
              )}
            </div>

            {showButtons && (
              <div className="d-flex align-items-center">
                {showButtons && ticket.status !== "close" && (
                  <button
                    className="btn d-flex align-items-center"
                    style={{
                      backgroundColor: "white",

                      marginRight: "20px",
                      width: "140px",
                    }}
                    onClick={handleShowStatusModal}
                  >
                    Status Change
                  </button>
                )}
                {showButtons && ticket.status === "close" && (
                  <button
                    className="btn d-flex align-items-center"
                    style={{
                      backgroundColor: "white",
                      marginRight: "20px",
                      width: "130px",
                    }}
                    onClick={handleShowStatusModal}
                  >
                    Reopen Ticket
                  </button>
                )}
              </div>
            )}
            {showButtons && ticket.status !== "close" && (
              <button
                className="btn d-flex align-items-center"
                style={{ backgroundColor: "white" }}
                onClick={handleSupportClick}
              >
                Assign
              </button>
            )}
          </div>
        </div>

        {showSupportForm && (
          <SupportPersonForm
            show={showSupportForm}
            handleClose={(remark, empName) =>
              handleSupportPersonCloseForm(remark, empName)
            }
            ticketId={ticket.ticketId}
            empId={ticket.empId}
          />
        )}

        <div
          className={`modal ${showStatusModal ? "show" : ""}`}
          style={{
            display: showStatusModal ? "block" : "none",
            marginTop: "50px",
          }}
          role="dialog"
        >
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-body p-4">
                <h5 className="mb-4 font-weight-bold text-center">
                  Change Ticket Status
                </h5>
                <form>
                  <div className="modal-body">
                    {/* <div>
                      <label htmlFor="status">Select Status</label>

                      <select
                        id="status"
                        className="form-control"
                        value={ticketStatus || ""}
                        onChange={(e) => {
                          setTicketStatus(e.target.value);
                          console.log(
                            "Selected status in DropDown:",
                            e.target.value
                          );
                        }}
                      >
                        <option value="" disabled>
                          Select a status
                        </option>
                        {ticket.status !== "close" ? (
                          <>
                            <option value="Open">In Progress</option>
                            <option value="close">Resolved</option>
                            <option value="hold">Hold</option>
                          </>
                        ) : null}

                        {ticket.status === "close" && (
                          <option value="reopen">Reopen</option>
                        )}
                      </select>
                    </div> */}
                    <div>
                      <label htmlFor="status">Select Status</label>
                      <select
                        id="status"
                        className="form-control"
                        value={
                          ticket.status === "close"
                            ? "reopen"
                            : ticketStatus || ""
                        }
                        onChange={(e) => {
                          if (ticket.status !== "close") {
                            setTicketStatus(e.target.value);
                          }
                        }}
                        disabled={ticket.status === "close"}
                      >
                        {ticket.status !== "close" && !isCreator && (
                          <>
                            <option value="">Select a status</option>
                            <option value="Open">In Progress</option>
                            <option value="hold">Hold</option>
                            <option value="close">Resolved</option>
                          </>
                        )}

                        {ticket.status !== "close" && isCreator && (
                          <>
                            <option value="">Select a status</option>
                            <option value="close">Resolved</option>
                          </>
                        )}

                        {ticket.status === "close" && (
                          <option value="reopen">Reopen</option>
                        )}
                      </select>
                    </div>

                    <div style={{ marginTop: "10px" }}>
                      <label htmlFor="remark">Remark</label>
                      <textarea
                        id="remark"
                        className="form-control"
                        value={remark}
                        onChange={(e) => setRemark(e.target.value)}
                      />
                    </div>
                  </div>

                  {ticketStatus === "reopen" && (
                    <div className="form-check mt-3">
                      <input
                        type="checkbox"
                        className="form-check-input"
                        id="escalateTicketCheckbox"
                        checked={escalateTicket}
                        onChange={handleEscalateToggle}
                      />
                      <label
                        className="form-check-label ms-2"
                        htmlFor="escalateTicketCheckbox"
                      >
                        Do you want to Escalate the Ticket
                      </label>
                    </div>
                  )}

                  {ticket.status !== "close" && (
                    <div className="form-check mt-3">
                      <input
                        type="checkbox"
                        className="form-check-input"
                        id="notifyCreatorCheckbox"
                        checked={notifyCreator}
                        onChange={() => setNotifyCreator(!notifyCreator)}
                      />
                      <label
                        className="form-check-label ms-2"
                        htmlFor="notifyCreatorCheckbox"
                      >
                        Notify Creator
                      </label>
                    </div>
                  )}

                  <div className="form-group mt-4 text-center">
                    <button
                      type="button"
                      className="btn btn-primary"
                      style={{ marginRight: "10px" }}
                      onClick={handleStatusChange} // 🛠️ Add this line to call the function
                    >
                      Submit
                    </button>
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={handleCloseStatusModal}
                    >
                      Close
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <div
          className={`modal ${showEscalateModal ? "show" : ""}`}
          style={{
            display: showEscalateModal ? "block" : "none",
            marginTop: "50px",
          }}
          role="dialog"
        >
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-body p-4">
                <h5 className="mb-4 font-weight-bold text-center">
                  Escalate Ticket
                </h5>
                <form>
                  <div className="form-group">
                    <label htmlFor="remark">Remark</label>
                    <textarea
                      className="form-control"
                      id="remark"
                      value={escalateRemark}
                      onChange={(e) => setEscalateRemark(e.target.value)}
                      placeholder="Enter your remark"
                    ></textarea>
                  </div>

                  {escalationError && (
                    <div className="alert alert-danger mt-2">
                      {escalationError}
                    </div>
                  )}

                  <div className="form-group mt-4 text-center">
                    <button
                      type="button"
                      className="btn btn-primary"
                      onClick={handleEscalate}
                    >
                      Submit
                    </button>
                    <button
                      type="button"
                      className="btn btn-secondary"
                      style={{ marginLeft: "10px" }}
                      onClick={() => setShowEscalateModal(false)}
                    >
                      Close
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <div
          className={`modal ${showModal ? "show" : ""}`}
          style={{ display: showModal ? "block" : "none", marginTop: "50px" }}
          role="dialog"
        >
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-body p-4">
                <h5 className="mb-4 font-weight-bold text-center">
                  Reply to Ticket
                </h5>
                <form>
                  <div className="form-group">
                    <textarea
                      className="form-control"
                      id="replyMessage"
                      placeholder="Enter your message..."
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                    ></textarea>
                  </div>
                  {ticket?.type === "external" && (
                    <div className="form-check mt-3">
                      <input
                        type="checkbox"
                        style={{ padding: 0, marginRight: 10 }}
                        className="form-check-input"
                        id="sendToClientCheckbox"
                        checked={sendToClient}
                        onChange={() => setSendToClient(!sendToClient)}
                      />
                      <label
                        className="form-check-label"
                        htmlFor="sendToClientCheckbox"
                      >
                        Send to Client
                      </label>
                    </div>
                  )}
                  <br />
                  <div className="form-group mt-3">
                    <input
                      type="file"
                      className="form-control-file"
                      id="fileAttachment"
                      onChange={(e) =>
                        setFileAttachment(
                          e.target.files ? e.target.files[0] : null
                        )
                      } // Keep file input for reply
                    />
                  </div>
                  <div className="form-group mt-4 text-center">
                    <button
                      type="button"
                      className="btn btn-primary"
                      style={{ marginRight: "10px" }}
                      onClick={handleReplyUpdate}
                    >
                      Submit
                    </button>
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={handleCloseStatusModal}
                    >
                      Close
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <div className="row">
          <div className="parent-container" style={{ padding: "0 20px" }}>
            <TicketHeaderCard
              details={{
                subject: ticket?.ticketSubject,
                description: ticket?.ticketMessage || "No description provided",
                department: ticket?.departmentName || "Unknown Department",
                category: ticket?.categoryName || "General",
                priority: ticket?.priority || "/",

                assigned_to: ticket?.assignedToName || "Unassigned",
                ticketId: ticket?.ticketId || "No Code",
                status: ticket?.status || "No Code",
                createdByName: ticket?.createdByName || "Unknown Creator",
                file: ticket?.filePath || "",
                allowReply: showButtons,

                createdAt:
                  new Date(ticket?.createdTime).toLocaleString() ||
                  "Unknown Date",
                escalationDetails: ticket?.escalationDetails || [],
              }}
            />
          </div>

          <div className="col-12 col-md-3 col-lg-3 col-sm-3">
            <TicketHistory
              isFromCreateTicket={false}
              updateTicketDetails={updateTicketDetails}
            />
          </div>
          <div className="col-12 col-md-8 col-lg-9">
            <div className="container mt-3 bg-white p-2">
              {!isLoading ? (
                <>
                  <ChatLayout
                    ticketId={ticket?.ticketId}
                    assignments={ticket?.history}
                    IsSuccess={isSuccess}
                    ticketStatus={ticket.status}
                    allowReply={showButtons}
                    // Pass the condition as a prop
                  />
                </>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default EyebtnTicket;
