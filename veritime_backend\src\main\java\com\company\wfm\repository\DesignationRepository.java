package com.company.wfm.repository;


import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.DesignationIdNameDTO;
import com.company.wfm.entity.Designation;

@Repository
public interface DesignationRepository extends JpaRepository<Designation, Long> {

boolean existsByCode(String code);
    Optional<Designation> findByCode(String code);

    boolean existsByName(String name);
    Optional<Designation> findByName(String name);

    @Query("SELECT d.level FROM Designation d")
    List<String> findAllDesignationLevel();

    @Query("SELECT new com.company.wfm.dto.DesignationIdNameDTO(d.id, d.name) " +
            "FROM Designation d ")
    List<DesignationIdNameDTO> findDesignationIdsAndNamesByDepartmentId();

    @Query("SELECT new com.company.wfm.dto.DesignationIdNameDTO(d.id, d.name) FROM Designation d WHERE d.department.departmentId =  ?1")
    List<DesignationIdNameDTO> findDesignationByDepartmentId(Long departmentId);

    @Query("SELECT d FROM Designation d WHERE d.department.departmentId =  ?1")
    List<Designation> findAllByDepartmentId(Long departmentId);

   /* @Query("SELECT d FROM Designation d WHERE d.isActive = true")
    List<Designation> findAllActiveDesignations();*/

}

