const AES_PASSWORD = process.env.NEXT_PUBLIC_AES_PASSWORD;
const AES_SALT = process.env.NEXT_PUBLIC_AES_SALT;
const IV_LENGTH = 12;
const ITERATIONS = 100000;
const KEY_SIZE = 256;

const base64ToBytes = (base64) => {
  return new Uint8Array([...atob(base64)].map((c) => c.charCodeAt(0)));
};

const bytesToBase64 = (bytes) => {
  return btoa(String.fromCharCode(...bytes));
};

const stringToBytes = (str) => new TextEncoder().encode(str);
const bytesToString = (bytes) => new TextDecoder().decode(bytes);

const deriveKey = async () => {
  const passwordBytes = stringToBytes(AES_PASSWORD);
  const saltBytes = base64ToBytes(AES_SALT);

  const keyMaterial = await crypto.subtle.importKey(
    "raw",
    passwordBytes,
    { name: "PBKDF2" },
    false,
    ["deriveBits", "deriveKey"]
  );

  return await crypto.subtle.deriveKey(
    {
      name: "PBKDF2",
      salt: saltBytes,
      iterations: ITERATIONS,
      hash: "SHA-256",
    },
    keyMaterial,
    { name: "AES-GCM", length: KEY_SIZE },
    false,
    ["encrypt", "decrypt"]
  );
};

export const encryptData = async (data) => {
  const key = await deriveKey();
  const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));

  const encryptedData = await crypto.subtle.encrypt(
    { name: "AES-GCM", iv },
    key,
    stringToBytes(JSON.stringify(data))
  );

  const combinedArray = new Uint8Array(iv.length + encryptedData.byteLength);
  combinedArray.set(iv);
  combinedArray.set(new Uint8Array(encryptedData), iv.length);

  return bytesToBase64(combinedArray);
};

export const decryptData = async (encryptedText) => {
  try {
    const key = await deriveKey();
    const encryptedBytes = base64ToBytes(encryptedText);
    const iv = encryptedBytes.slice(0, IV_LENGTH);
    const ciphertext = encryptedBytes.slice(IV_LENGTH);
    const decryptedArrayBuffer = await crypto.subtle.decrypt(
      { name: "AES-GCM", iv },
      key,
      ciphertext
    );

    const decryptedString = bytesToString(new Uint8Array(decryptedArrayBuffer));
    // console.log("Decrypted String:", decryptedString);

    if (decryptedString.startsWith("{") || decryptedString.startsWith("[")) {
      return JSON.parse(decryptedString);
    } else {
      console.warn("Decrypted data is not JSON, returning raw string.");
      return decryptedString;
    }
  } catch (error) {
    console.error("Decryption error:", error);
    throw error;
  }
};
