import React, { useEffect, useState } from 'react';
import './Carousel.css';

const CarouselComp = ({ arr , setValue}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const prevSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? arr.length - 1 : prevIndex - 1 
    );
   
  };
  useEffect(()=>{
    setValue(currentIndex)
  },[currentIndex])
  const nextSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === arr.length - 1 ? 0 : prevIndex + 1
    );
  };

  const selectSlide = (index) => {
    setCurrentIndex(index);
  };

  return (
    <div className="carousel">
      <button onClick={prevSlide} className="carousel-button">
        &lt;
      </button>
      <div className="content">
        <p>{arr[currentIndex].text}</p>
        <div><button className='carousa-button' onClick={()=>{setValue(currentIndex)}} style={{fontWeight:'bold'}}> {arr[currentIndex]} </button></div>

      </div>
      <button onClick={nextSlide} className="carousel-button">
        &gt;
      </button>
    </div>
  );
};

export default CarouselComp;
