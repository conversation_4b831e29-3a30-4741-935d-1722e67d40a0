import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import "./Tickethistory.css";
import { postRequest, postRequestWithSecurity } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import moment from "moment";
import { convertToSouthAfricaTime } from "@/services/utils";
import Link from "next/link";
import { FaPaperclip } from "react-icons/fa";

const truncateText = (text, maxLength = 20) => {
  if (!text) return "Unknown";
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};

// const TicketHistory = (props, ref, isFromCreateTicket) => {
//   console.log("props::: ", props);
//   console.log("refs::: ", ref);

const TicketHistory = forwardRef((props, ref) => {
  //console.log("refs::: ", ref);
  const [tickets, setTickets] = useState([]);
  const [offset, setOffset] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const ticketContainerRef = useRef(null);

  const handleTicketClick = (ticket) => {
    const selectTicket = {
      srNo: ticket.ticketId,
      date: ticket.createdTime,
      subject: ticket.ticketSubject,
      message: ticket.ticketMessage,
      status: ticket.status,
    };
  };

  useImperativeHandle(ref, () => ({
    refreshTickets: () => {
      setTickets([]);
      setOffset(0);
      fetchTickets(0);
    },
  }));

  const fetchTickets = async (offsetValue = 0) => {
    setLoading(true);
    try {
      const requestPayload = { offset: offsetValue, limit: 10 };
      const response = await postRequestWithSecurity(
        API_URLS.FILTER_TICKETS,
        requestPayload
      );

      // /  if (response?.content?.length > 0) {
      //     setTickets((prevTickets) => [...prevTickets, ...response.content]);
      //   }

      if (response?.content?.length > 0) {
        setTickets((prevTickets) => {
          // Remove duplicate tickets by their ticketId
          const uniqueTickets = [
            ...new Map(
              [...prevTickets, ...response.content].map((ticket) => [
                ticket.ticketId,
                ticket,
              ])
            ).values(),
          ];
          return uniqueTickets;
        });
      }
      if (response?.content?.length < 10) {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error fetching tickets:", error);
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      fetchTickets(offset);
    }
  }, [offset]);

  const handleScroll = () => {
    if (!ticketContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } =
      ticketContainerRef.current;
    if (scrollTop + clientHeight >= scrollHeight && hasMore && !isFetching) {
      setIsFetching(true);
      setOffset((prevOffset) => prevOffset + 1);
    }
  };

  // const handleScroll = () => {
  //   if (!ticketContainerRef.current) return;

  //   const { scrollTop, scrollHeight, clientHeight } =
  //     ticketContainerRef.current;

  //   if (scrollTop + clientHeight >= scrollHeight && hasMore && !isFetching) {
  //     setIsFetching(true);
  //     // Ensure offset increments by 10, not 1
  //     setOffset((prevOffset) => prevOffset + 10);
  //   }
  // };

  const getRelativeTime = (createdTime) => {
    return moment(createdTime).fromNow();
  };

  const filteredTickets = tickets.filter(
    (ticket) =>
      ticket.ticketSubject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.ticketId.toString().includes(searchTerm)
  );

  const statusMapping = {
    open: "InProgress",
    close: "Resolve",
    hold: "Hold",
    reopen: "Reopened",
    assigned: "Assigned",
  };

  return (
    <div className="container mt-3 bg-white p-2 conter-ticket">
      <h5 className="font-weight-bold">Recent Tickets</h5>
      <div className="input-group mb-3">
        <input
          type="text"
          className="form-control srch-ticket"
          placeholder="Search"
          aria-label="Search"
          style={{ width: "350px" }}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      <div
        className="list-group scrollable-ticket-container"
        // ref={ticketContainerRef}
        onScroll={handleScroll}
      >
        {filteredTickets.map((ticket, index) =>
          props?.isFromCreateTicket == false ? (
            <div
              key={index}
              onClick={() => props?.updateTicketDetails(ticket.ticketId)}
              className="list-group-item list-group-item-action d-flex justify-content-between align-items-center p-2"
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "flex-start",
                }}
              >
                <div className="ticket-title" title={ticket.ticketSubject}>
                  #{ticket.ticketId} - {ticket.ticketSubject}
                  {ticket.filePath && ticket.filePath !== null ? (
                    <a
                      href={ticket.filePath}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ marginLeft: "8px" }}
                    >
                      <img
                        src="/image/icons.png"
                        alt="Attachment"
                        className="file-icon"
                        style={{ width: "16px", height: "16px" }}
                      />
                    </a>
                  ) : null}
                </div>
                <small className="text-muted text f14">
                  • {ticket.categoryName ? ticket.categoryName : "-"}
                </small>
              </div>
              <span
                className={`f12 badge ${
                  ticket.status?.toLowerCase() === "open"
                    ? "badge-danger"
                    : ticket.status?.toLowerCase() === "close"
                    ? "badge-success"
                    : ticket.status?.toLowerCase() === "hold"
                    ? "badge-success"
                    : ticket.status?.toLowerCase() === "reopen"
                    ? "badge-success"
                    : ticket.status?.toLowerCase() === "assigned"
                    ? "badge-success"
                    : "badge-secondary"
                }`}
              >
                {statusMapping[ticket.status?.toLowerCase()] || "Unknown"}
              </span>
            </div>
          ) : (
            <Link
              key={index}
              href={`/ticket/details/${btoa(ticket.ticketId)}`}
              className="list-group-item list-group-item-action d-flex justify-content-between align-items-center p-2"
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "flex-start",
                }}
              >
                <div className="ticket-title" title={ticket.ticketSubject}>
                  #{ticket.ticketId} - {ticket.ticketSubject}
                  {ticket.filePath && ticket.filePath !== null ? (
                    <a
                      href={ticket.filePath}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{ marginLeft: "8px" }}
                    >
                      <img
                        src="/image/icons.png"
                        alt="Attachment"
                        className="file-icon"
                        style={{ width: "16px", height: "16px" }}
                      />
                    </a>
                  ) : null}
                </div>

                <div style={{ display: "flex", alignItems: "center" }}>
                  <small className="text-muted text f14">
                    • {ticket.categoryName ? ticket.categoryName : "-"}
                  </small>
                </div>
              </div>

              <span
                className={`f12 badge ${
                  ticket.status?.toLowerCase() === "open"
                    ? "badge-danger"
                    : ticket.status?.toLowerCase() === "close"
                    ? "badge-success"
                    : ticket.status?.toLowerCase() === "hold"
                    ? "badge-success"
                    : ticket.status?.toLowerCase() === "reopen"
                    ? "badge-success"
                    : ticket.status?.toLowerCase() === "assigned"
                    ? "badge-success"
                    : "badge-secondary"
                }`}
              >
                {statusMapping[ticket.status?.toLowerCase()] || "Unknown"}
              </span>
            </Link>
          )
        )}

        {loading && <div className="loader">Loading...</div>}
      </div>
      {!loading && !hasMore && filteredTickets.length === 0 && (
        <div className="no-more-tickets">No more tickets to load</div>
      )}
    </div>
  );
});

export default TicketHistory;
