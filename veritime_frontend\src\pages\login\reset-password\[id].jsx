import { useEffect, useState } from "react";
import Swal from 'sweetalert2';
import '../login.css';
import { postRequest, postRequestNoToken } from "../../../services/apiService";
import { API_URLS } from "../../../constants/apiConstants";
import Link from "next/link";

const ResetPassword = () => {
  const [newPassword, setNewPassword] = useState("");
  const [token, setToken] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  useEffect(() => {
    validateToken()
    try {
      const appLink = "wfm://reset-password/"+window.location.pathname?.split("/")?.[3];
      window.location.href = appLink;
    } catch (error) {
      // console.log('error::: ', error);
    }
  }, [])
  

  const validateToken = async () => {
    const resetToken = window.location.pathname?.split("/")?.[3];
    try {
      const validationRequest = { resetToken };
      const validateResponse = await postRequestNoToken(API_URLS.VALID_TOKEN, validationRequest);
      // console.log('validateResponse::: ', validateResponse);
      setToken(resetToken)
    } catch (error) {
      // console.error("Error validating reset token:", error?.response?.data);
      Swal.fire({
        title: "Error",
        text: error?.response?.data || "something went wrong.",
        icon: "error",
        confirmButtonText: "OK"
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href="/login"
        }
      });
    }
  };

  const handleResetPassword = async (event) => {
    event.preventDefault();

    if (newPassword !== confirmPassword) {
      Swal.fire("Passwords do not match.");
      return;
    }

    try {
      const request = {
        token: token, 
        newPassword: newPassword,
        confirmPassword: confirmPassword
      };

      const response = await postRequestNoToken(API_URLS.RESET_PASSWORD, request);

      if (response) {
        Swal.fire({
          text: response || "Password has been reset successfully.",
          icon: "success",
          confirmButtonText: "OK"
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = "/login"; 
          }
        });
      } else {
        throw new Error("Failed to reset password.");
      }
    } catch (error) {
      Swal.fire({
        title: "Error",
        text: error?.response?.data || "Failed to reset password. Please try again",
        icon: "error",
        confirmButtonText: "OK"
      });
    }
  };

  return (
    <div className="login-container">
      <div className="left-pannel">
        <img className="loginImg" src={"/image/landing-page-forgetpassword.png"} />
      </div>
      <div className="right-pannel">
        <img className="logoImg" src={"/image/logo_full.png"} />
        <form className="login-form" onSubmit={handleResetPassword}>
          <div className="form-group mb-3">
            <label
              htmlFor="newPassword"
              className="fw-bold"
              style={{ fontSize: "14px", fontFamily: "Arial, sans-serif", color: "#4A4A4A" }}
            >
              Enter New Password
            </label>
            <input
              type="password"
              className="form-control"
              id="newPassword"
              placeholder=""
              style={{
                borderRadius: "10px",
                fontSize: "14px",
                color: "#4A4A4A",
                border: "1px solid #CCCCCC",
                padding: "10px",
                backgroundColor: "#F0F0F0",
                marginBottom: "15px"
              }}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
            />

            <label
              htmlFor="confirmPassword"
              className="fw-bold"
              style={{ fontSize: "14px", color: "#4A4A4A" }}
            >
              Re-Enter New Password
            </label>
            <input
              type="password"
              className="form-control"
              id="confirmPassword"
              placeholder=""
              style={{
                borderRadius: "10px",
                fontFamily: "Arial, sans-serif",
                fontSize: "14px",
                color: "#4A4A4A",
                border: "1px solid #CCCCCC",
                padding: "10px",
                backgroundColor: "#F0F0F0"
              }}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
          </div>

          <div className="d-flex flex-column gap-3 mt-3">
            <button
              className="btn btn-primary"
              type="submit"
              style={{
                backgroundColor: "#0056b3",
                borderColor: "#0056b3",
                borderRadius: "8px",
                fontSize: "14px",
                fontFamily: "Arial, sans-serif",
                padding: "10px 0"
              }}
            >
              Reset Password
            </button>
          </div>
              <Link href={"/login"}>
          <div className="button-back mt-3">
            <button className="back-to-signin" type="button">
              Back To Signin
            </button>
          </div>
              </Link>
        </form>
      </div>
    </div>
  );
};

export default ResetPassword;
