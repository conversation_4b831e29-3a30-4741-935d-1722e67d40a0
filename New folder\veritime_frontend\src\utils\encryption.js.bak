
// import CryptoJS from "crypto-js";

// const SECRET_KEY = "PRIMARYKEYTECHNOLOGIESPVTLTD"; 

// export const encryptData = (data) => {
//   const ciphertext = CryptoJS.AES.encrypt(
//     JSON.stringify(data),
//     SECRET_KEY
//   ).toString();
//   return ciphertext;
// };

// export const decryptData = (ciphertext) => {
//   const bytes = CryptoJS.AES.decrypt(ciphertext, SECRET_KEY);
//   const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
//   return JSON.parse(decryptedData);
// };

import CryptoJS from "crypto-js";

const SHARED_KEY = "1234567890123456"; 
const SHARED_IV = "1234567890123456"; 

export const encryptData = (data) => {
  const key = CryptoJS.enc.Utf8.parse(SHARED_KEY);
  const iv = CryptoJS.enc.Utf8.parse(SHARED_IV);
  
  const ciphertext = CryptoJS.AES.encrypt(
    JSON.stringify(data),
    key,
    {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    }
  ).toString();
  return ciphertext;
};

export const decryptData = (ciphertext) => {
  const key = CryptoJS.enc.Utf8.parse(SHARED_KEY);
  const iv = CryptoJS.enc.Utf8.parse(SHARED_IV);
  
  const bytes = CryptoJS.AES.decrypt(
    ciphertext,
    key,
    {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    }
  );
  const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
  try {
    return JSON.parse(decryptedData);
  
} catch (error) {
  return decryptedData
}
};
