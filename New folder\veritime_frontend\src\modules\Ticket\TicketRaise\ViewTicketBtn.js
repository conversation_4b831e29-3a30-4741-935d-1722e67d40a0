"use client";
import { useEffect, useState } from "react";
import React from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import { colors } from '@constants/colors';
import Link from 'next/link';

const ViewTicketBtn = () => {
  const [role, setRole] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("accessToken");
      const userRole = localStorage.getItem("role");

      if (!token || !userRole) {
        localStorage.clear();
        localStorage.setItem("currentPath", window.location.href);

        window.location.href = "/login?sessionExpired=1";
      } else {
        setRole(userRole);
      }
    }
  }, []); 

  return (
    <div className="col-12 col-md-3 d-flex position-relative grid-layout justify-content-end">
      {role !== "employee" && (
        <Link href={'./ticket/EscalateList'}>
          <button
            className="d-flex flex-row p-2 round-3"
            style={{ backgroundColor: colors.purple, height: "40px", border: "0px", marginRight: '10px' }}
          >
            <div className="d-flex align-items-center cursor-pointer" style={{ fontSize: '0.875rem' }}>
              Escalate Tickets
            </div>
          </button>
        </Link>
      )}

      <Link href={'./ticket/viewTickets'}>
        <button
          className="d-flex flex-row p-2 round-3"
          style={{ backgroundColor: colors.purple, height: "40px", border: "0px", marginRight: '10px' }}
        >
          <div className="d-flex align-items-center cursor-pointer" style={{ fontSize: '0.875rem' }}>
            All Tickets
          </div>
        </button>
      </Link>
    </div>
  );
};

export default ViewTicketBtn;
