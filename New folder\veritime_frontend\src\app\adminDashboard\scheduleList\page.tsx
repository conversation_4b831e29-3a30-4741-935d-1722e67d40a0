"use client";
import { useEffect, useState } from "react";
import { Container, <PERSON>, Col, Button } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter.js";
import SwipeableViews from "react-swipeable-views";
import { useTheme } from "@material-ui/core/styles";
import Popover from "@mui/material/Popover";
import { styled } from "@mui/system";
import DeleteIcon from "@mui/icons-material/Delete";
import { TabPanel } from "../../../common-components/utils.js";
import Layout from "@/components/Layout";
import { API_URLS } from "@/constants/apiConstants.js";
import { getRequest, postRequest } from "../../../services/apiService.js";
import { appConstants } from "@/constants/appConstants.js";
import { addIdToData } from "@/services/utils.js";
import CreateScheduleModal from "../modals/CreateScheduleModal";
import ScheduleDetaileModal from "../modals/ScheduleDetailModal";
//import ScheduleDetailModal from "../modals/ScheduleDetailModal.jsx";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility"; // Import the eye icon

const scheduleList = ({ toggleMenu, expanded }: any) => {
  // const [username, setusername] = useState(localStorage.getItem(appConstants?.username))

  const [username, setUsername] = useState("");
  // Existing state declarations
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState(null);


  const handleDetail = (row: any) => {
    setSelectedSchedule(row); // Set the selected schedule
    setDetailModalOpen(true); // Open the detail modal
  };

  const handleDetailModalClose = () => {
    setDetailModalOpen(false); // Close the detail modal
    setSelectedSchedule(null); // Clear selected schedule
  };
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem(appConstants?.username);
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
  }, []);
  const theme = useTheme();
  const [value, setValue] = useState(0);
  const [rows, setRows] = useState([]);
  const [rowToEdit, setRowToEdit] = useState<any>({});

  const [anchorEl, setAnchorEl] = useState(null);

  const [data, setData] = useState([]);
  const [branchNames, setBranchNames] = useState<string[]>([]);

  const [deptNames, setdeptNames] = useState<string[]>([]);

  const fetachBranchNames = async () => {
    try {
      
      const branchResponse = await getRequest(`${API_URLS.GET_BRANCH}`);
      if (branchResponse && typeof branchResponse === "object") {
        const branchNamesArray = Object.values(branchResponse);
        setBranchNames(
          branchNamesArray.map((branch: any) => branch.branchName)
        );
      } else {
      }
    } catch (error) {
    }
  };

  const fetachdeptNames = async () => {
    try {
      const departmentResponse = await getRequest(`${API_URLS.GET_DEPARTMENT}`);
      if (departmentResponse && departmentResponse.length > 0) {
        // const deptNamesArray = departmentResponse.map((department: any) => department.departmentName);
        setdeptNames(departmentResponse);
      }
      // if (departmentResponse && typeof departmentResponse === 'object') {
      //   const deptNamesArray = Object.values(departmentResponse);
      //   setdeptNames(deptNamesArray.map((department:any) => department.departmentName));
      // } else {
      //   console.error('Invalid department response:', departmentResponse);
      // }
    } catch (error) {
    }
  };

  const fetchItems = async () => {
    try {
      const payload = {
        // deptId:'',
        // branchId:'',
        // isActive:1,
        // offset:0,
        // limit:10
      };
      //const data: any = await getRequest(`${API_URLS.SCHEDULE_LIST}`);
      //const data = await postRequest(`${API_URLS.SCHEDULE_LIST}`, payload);
      const data = await postRequest(API_URLS.SCHEDULE_LIST, payload);
      const rowsWithScheduleId = data.map((row: any, index: any) => ({
        ...row,
        scheduleId: row?.id,  
         srno: index + 1,       
      }))
      setData(addIdToData(rowsWithScheduleId));
      setRows(addIdToData(rowsWithScheduleId));
    } catch (error) {
    }
  };
  useEffect(() => {
    fetachdeptNames();
    // fetachBranchNames();
    fetchItems();
  }, []);
  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const handleChange = (event: any, newValue: any) => {
    setValue(newValue);
  };

  const handleChangeIndex = (index: any) => {
    setValue(index);
  };

  const handleClick = (event: any) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleEdit = (row: any) => {
    setRowToEdit(row);
    setOpenModal(true);
  };

  const handleModalClose = (response:any) => {
    setOpenModal(false);
    setRowToEdit(null);
    // fetchItems()
    if(response && response?.type !== "click") {

      let oldList:any = [...data]
      oldList.push(response)
      const rowsWithScheduleId = data.map((row: any, index: any) => ({
        ...row,
        scheduleId: row?.scheduleId,  
         srno: index + 1,      
      }))
      setData(addIdToData(rowsWithScheduleId));
      setRows(addIdToData(rowsWithScheduleId));
    }

  };


  const [openModal, setOpenModal] = useState(false);
  const handleOpen = () => {
    setRowToEdit(null);
    setOpenModal(true);
  };
  const columns = [
    {
      field: "srno", // Fixed field name
      headerName: "Sr.no",
      width: 120,
      renderCell: (params: any) => {
        return <span>{params.row.srno}</span>; // Ensure srno is displayed
      },
    },
    {
      field: "branchName",
      headerName: "Facility",
      width: 150,
      renderCell: (params: any) => {
        return <span className="fw-bold">{params.row.branchName}</span>;
      },
    },
    {
      field: "departmentName",
      headerName: "Department",
      width: 200,
      renderCell: (params: any) => {
        return <span className="fw-bold">{params.row.departmentName}</span>;
      },
    },
    // {
    //   field: 'branchIds',
    //   headerName: 'Branch',
    //   width: 200,
    //   renderCell: (params: any) => {
    //     return params.row.department?.branchIds?.map((el: any) => (
    //       <button className="branch-button" key={el}>{el}</button>
    //     ));
    //   }
    // },
    {
      field: "timeSlots",
      headerName: "Default Time Slots",
      width: 300,
      renderCell: (params: any) => {
        return params?.row?.timeSlots?.map((slot: any) => (
          <div key={slot.id}>
            <span>{`${slot.startTime.split(".")[0]} - ${
              slot.endTime.split(".")[0]
            }`}</span>
          </div>
        ));
      },
    },
    {
      field: "action",
      headerName: "Action",
      width: 200,
      renderCell: (params: any) => {
        return (
          <>
            <VisibilityIcon
              style={{ marginRight: "30px", cursor: "pointer" }}
              onClick={() => handleDetail(params.row)}
            />
            <EditIcon
              style={{ cursor: "pointer" }}
              onClick={() => handleEdit(params.row)}
            />
            <button
              className="deny-button"
              style={{ background: "transparent" }}
              onClick={() => deleteRow(params.id)}
            >
              {/* <DeleteIcon /> */}
            </button>
          </>
        );
      },
    },
  ];

  const deleteRow = async (id: any) => {
    // try {
    //     const data = deleteData('departments/'+id);
    //     console.log(data,"data")
    // } catch (error) {
    //     console.error('Error fetching items:', error);
    // }
    setData(data.filter((el: any) => el.id !== id));
  };

  const handleChangeFilter = (event: any) => {

    if (event.target.name === "branch") {
      setRows(data.filter((el: any) => el.branch == event.target.value));
    }
    if (event.target.name === "department") {
      setRows(
        data.filter((el: any) => el.departmentName === event.target.value)
      );
    }
  };

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;
  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px", background: "#BDCDD6" }}>
        <Row className="my-3">
          <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
            {" "}
            <Row>
              <Col>
                <h4>{"Schedule List"}</h4>
                {/* <span style={{ fontSize: "12px", color: "grey" }}>
                  Hi, {username ? username?.toLowerCase() : ""}. Your
                  organizations schedules are listed here
                </span> */}
              </Col>
            </Row>
            <Row>
              {/* <Button style={{
                width: '100px', margin: 'auto',
                marginRight: '0px'
              }} aria-describedby={id} variant="contained" className={"carousal-container"} onClick={handleClick}>
                <FilterAltIcon />
              </Button> */}
              <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "left",
                }}
              >
                {/* <div>
                  <select className="filter-select" onChange={handleChangeFilter}>
                    <option disabled selected>Select Branch</option>
                    {branchNames.length && branchNames.map((el, i) => <option key={i}>{el}</option>)}

                  </select>
                </div>
                <hr />

                <div>
                  <select className="filter-select" onChange={handleChangeFilter}>
                    <option disabled selected>Select Department</option>
                    {deptNames.length && deptNames.map((el, i) => <option key={i}>{el}</option>)}

                  </select>
                </div> */}
                <div></div>
              </Popover>
            </Row>
          </Col>
          <Col md={8}>
            <Row style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                style={{
                  width: "200px",
                  background: "green",
                  marginRight: "0",
                }}
                onClick={handleOpen}
              >
                + New Schedule{" "}
              </Button>
            </Row>
            {/* <Row className='filter-container' >
              <Col md={4}>
                <select className="filter-select" name='branch' onChange={handleChangeFilter}>
                  <option disabled selected>Select Branch</option>

                  {branchNames.length && branchNames.map((el, i) => <option key={i}>{el}</option>)}
                </select>
              </Col>
              <Col md={4}>
                <select className="filter-select" name='department' onChange={ handleChangeFilter}>
                  <option disabled selected>Select Department</option>
                  {deptNames.length && deptNames.map((el, i) => <option key={i}>{el}</option>)}
                </select>
              </Col>
            </Row> */}
          </Col>
          <Col></Col>
        </Row>
        <Row style={{ margin: "10px" }}>
          {/* <Col md={2} style={{ borderRight: '7px solid #BDCDD6', backgroundColor: "#6097b4" }}>
            <Tabs
              value={value}
              onChange={handleChange}
              indicatorColor="secondary"
              textColor="inherit"
              variant="fullWidth"
              aria-label="full width tabs example"
              className="tab-pannel-test "
            >
              <Tab label={`Nurse (${data.length})`} {...a11yProps(0)} style={{color: "#000", backgroundColor: "#6097b4"}}/>
              <Tab label={`Doctors (${data.length})`} {...a11yProps(1)} style={{color: "#000", backgroundColor: "#6097b4"}}/>
            </Tabs>
            {/* <Row className='carousal-container'>
              <CarouselComp setValue={setValue} arr={["Nurse", "Doctor"]} />
            </Row> */}
          {/* </Col> */}
          <Col md={10} style={{ background: "white", width: "100%" }}>
            {/* <SwipeableViews
              axis={theme.direction === "rtl" ? "x-reverse" : "x"}
              index={value}
              onChangeIndex={handleChangeIndex} */}
            {/* > */}
              <TabPanel value={value} index={0} dir={theme.direction}>
                <Row>
                  <div id="tableWrapper">
                    <TableFilter columns={columns} rows={data} />
                  </div>
                </Row>
              </TabPanel>
              {/* <TabPanel value={value} index={1} dir={theme.direction}>
                <Row>
                  <div id="tableWrapper">
                    <TableFilter columns={columns} rows={data} />
                  </div>
                </Row>
              </TabPanel> */}
            {/* </SwipeableViews> */}
          </Col>
        </Row>
        {openModal && (
          <CreateScheduleModal
            show={openModal}
            handleClose={handleModalClose}
            rowData={rowToEdit}
          />
        )}


        <ScheduleDetaileModal
          schedule={selectedSchedule}
          show={detailModalOpen}
          handleClose={handleDetailModalClose}
          //onClose={handleDetailModalClose}
          rowData={selectedSchedule}
        />
      </Container>
    </Layout>
  );
};

export default scheduleList;

const PopupBody = styled("div")(
  ({ theme }) => `
    width: max-content;
    padding: 12px 16px;
    margin: 8px;
    border-radius: 8px;
    border: 1px solid ${theme.palette.mode === "dark" ? grey[700] : grey[200]};
    background-color: ${theme.palette.mode === "dark" ? grey[900] : "#fff"};
    box-shadow: ${
      theme.palette.mode === "dark"
        ? `0px 4px 8px rgb(0 0 0 / 0.7)`
        : `0px 4px 8px rgb(0 0 0 / 0.1)`
    };
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    z-index: 1;
  `
);

const FilterButton = styled("button")(
  ({ theme }) => `
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: ${blue[500]};
    padding: 8px 16px;
    border-radius: 8px;
    color: white;
    transition: all 150ms ease;
    cursor: pointer;
    border: 1px solid ${blue[500]};
    box-shadow: 0 2px 4px ${
      theme.palette.mode === "dark"
        ? "rgba(0, 0, 0, 0.5)"
        : "rgba(0, 127, 255, 0.5)"
    }, inset 0 1.5px 1px ${blue[400]}, inset 0 -2px 1px ${blue[600]};
  
    &:hover {
      background-color: ${blue[600]};
    }
  
    &:active {
      background-color: ${blue[700]};
      box-shadow: none;
    }
  
    &:focus-visible {
      box-shadow: 0 0 0 4px ${
        theme.palette.mode === "dark" ? blue[300] : blue[200]
      };
      outline: none;
    }
  
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      box-shadow: none;
      &:hover {
        background-color: ${blue[500]};
      }
    }
  `
);

const grey = {
  50: "#F3F6F9",
  100: "#E5EAF2",
  200: "#DAE2ED",
  300: "#C7D0DD",
  400: "#B0B8C4",
  500: "#9DA8B7",
  600: "#6B7A90",
  700: "#434D5B",
  800: "#303740",
  900: "#1C2025",
};

const blue = {
  200: "#99CCFF",
  300: "#66B2FF",
  400: "#3399FF",
  500: "#007FFF",
  600: "#0072E5",
  700: "#0066CC",
};
