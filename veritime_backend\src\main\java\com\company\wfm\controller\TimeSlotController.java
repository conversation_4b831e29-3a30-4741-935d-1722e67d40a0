package com.company.wfm.controller;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.UserTokenService;

@RestController
@RequestMapping("/api/v1/timeslots")
@CrossOrigin(origins = "*")
public class TimeSlotController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserTokenService userTokenService;

    @GetMapping
    public Map<String, Object> getTimeSlotsForEmployeeDepartment() {
        Long employeeId = userTokenService.getEmployeeIdFromToken();

        String departmentSql = "SELECT DEPARTMENT_ID FROM t_employee WHERE EMP_ID = ?";
        Long departmentId = Optional.ofNullable(jdbcTemplate.queryForObject(departmentSql, new Object[]{employeeId}, Long.class)).orElse(0l);

        String scheduleSql = "SELECT schedule_id FROM t_schedule WHERE department_id = ?";
        List<Long> scheduleIds = jdbcTemplate.queryForList(scheduleSql, new Object[]{departmentId}, Long.class);

        if (scheduleIds.isEmpty()) {
            Map<String, Object> emptyResponse = new HashMap<>();
            emptyResponse.put("employee_id", employeeId);
            emptyResponse.put("department_id", departmentId);
            emptyResponse.put("time_slots", List.of());
            return emptyResponse;
        }

        // Use NamedParameterJdbcTemplate to handle the IN clause safely
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);

        String timeSlotSql = "SELECT ts.time_slot_id, ts.start_time, ts.end_time " +
                "FROM t_time_slot ts " +
                "WHERE ts.schedule_id IN (:scheduleIds) AND ts.is_active = 1";

        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("scheduleIds", scheduleIds);

        List<Map<String, Object>> timeSlots = namedParameterJdbcTemplate.query(timeSlotSql, parameters, (rs, rowNum) -> {
            Map<String, Object> timeSlot = new HashMap<>();

            timeSlot.put("time_slot_id", rs.getLong("time_slot_id"));

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("h:mm a");
            LocalTime startTime = rs.getTime("start_time").toLocalTime();
            LocalTime endTime = rs.getTime("end_time").toLocalTime();
            String timeRange = startTime.format(formatter) + " - " + endTime.format(formatter);

            timeSlot.put("timeRange", timeRange);

            return timeSlot;
        });

        Map<String, Object> response = new HashMap<>();
        response.put("employee_id", employeeId);
        response.put("department_id", departmentId);
        response.put("time_slots", timeSlots);

        return response;
    }

}