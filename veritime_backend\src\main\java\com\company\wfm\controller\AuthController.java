package com.company.wfm.controller;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.EncryptedRequest;
import com.company.wfm.dto.EncryptedResponse;
import com.company.wfm.dto.JwtAuthResponse;
import com.company.wfm.dto.LoginDto;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.User;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.UserRepository;
import com.company.wfm.service.AuthService;
import com.company.wfm.util.EncryptionUtil;
import com.google.gson.Gson;

import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
@Slf4j
public class AuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

	@Autowired
	private EncryptionUtil encryptionUtil;

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody EncryptedRequest encryptedRequest) {
        try {
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());
            Gson gson = new Gson();
            LoginDto loginDto = gson.fromJson(decryptedData, LoginDto.class);

            User user = userRepository.findByUsername(loginDto.getUsername());

            if (user == null) {
                throw new EntityNotFoundException("Invalid username or password");
            }

            // Fetch employee details from t_employee using EMP_ID
            Optional<Employee> employeeOpt = employeeRepository.findById(user.getEmployee().getEmpId());

            if (employeeOpt.isPresent()) {
                Employee employee = employeeOpt.get();
                if (!employee.getInService()) {
                    // Encrypt the response before sending it back
                    String encryptedMessage = encryptionUtil.encrypt("You are inactive, you do not have access to login.",encryptionUtil.generateKey());
                    return new ResponseEntity<>(new EncryptedResponse(encryptedMessage), HttpStatus.FORBIDDEN);
                }
            } else {
                // Encrypt the response before sending it back
                String encryptedMessage = encryptionUtil.encrypt("Employee record not found",encryptionUtil.generateKey());
                return new ResponseEntity<>(new EncryptedResponse(encryptedMessage), HttpStatus.NOT_FOUND);
            }

            JwtAuthResponse jwtAuthResponse = authService.login(loginDto);
            // Encrypt the JwtAuthResponse before sending it back to the client
            String encryptedResponse = encryptionUtil.encrypt(gson.toJson(jwtAuthResponse),encryptionUtil.generateKey());
            return new ResponseEntity<>(new EncryptedResponse(encryptedResponse), HttpStatus.OK);
        } catch (BadCredentialsException e) {
            return new ResponseEntity<>("Invalid username or password", HttpStatus.UNAUTHORIZED);
        } catch (DisabledException e) {
            return new ResponseEntity<>("Account is disabled", HttpStatus.FORBIDDEN);
        } catch (LockedException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.FORBIDDEN);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
			log.error("Exception occured while login", e);
            return new ResponseEntity<>("An error occurred while logging in", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}