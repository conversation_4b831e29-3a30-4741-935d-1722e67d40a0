import React, { useEffect, useState } from "react";
import { API_URLS } from "@/constants/apiConstants";
import { getRequest } from "@/services/apiService.js";

const AddressDetailsForm = ({ data, goToNextTab, onSubmit }) => {
  const [provinceList, setProvinceList] = useState([]);
  const [districtList, setDistrictList] = useState([]);
  const [subDistrict, setSubDistrict] = useState([]);

  const [formData, setFormData] = useState({
    countryId: "",
    provinceId: "",
    city: "",
    suburb: "",
    street: "",
    unitNumber: "",
    zipCode: "",
  });
  
  useEffect(() => {
    if (Object.keys(data).length > 0) {
      setFormData(data);
      loadDataFromLocalOrAPI(data);
    } else {
      loadDataFromLocalOrAPI();
    }
  }, [data]);

  const loadDataFromLocalOrAPI = async (initialData = {}) => {
    const cachedProvinceList = JSON.parse(localStorage.getItem("provinceList"));
    if (cachedProvinceList) {
      setProvinceList(cachedProvinceList);
    } else {
      const provinceApi = await getRequest(API_URLS.GET_PROVIENCE);
      if (provinceApi) {
        setProvinceList(provinceApi);
        localStorage.setItem("provinceList", JSON.stringify(provinceApi));
      }
    }

    if (initialData.provinceId) {
      loadDistrictList(initialData.provinceId);
    }
    
    if (initialData.city) {
      loadSubDistrictList(initialData.provinceId, initialData.city);
    }
  };

  const loadDistrictList = async (provinceId) => {
    const cachedDistrictList = JSON.parse(localStorage.getItem(`districtList_${provinceId}`));
    if (cachedDistrictList) {
      setDistrictList(cachedDistrictList);
    } else {
      const districtApi = await getRequest(API_URLS.GET_DISTRICT(provinceId));
      if (districtApi) {
        setDistrictList(districtApi);
        localStorage.setItem(`districtList_${provinceId}`, JSON.stringify(districtApi));
      }
    }
  };

  const loadSubDistrictList = async (provinceId, cityId) => {
    const cachedSubDistrictList = JSON.parse(localStorage.getItem(`subDistrict_${provinceId}_${cityId}`));
    if (cachedSubDistrictList) {
      setSubDistrict(cachedSubDistrictList);
    } else {
      const subDistrictApi = await getRequest(API_URLS.GET_SUB_DISTRICT(provinceId, cityId));
      if (subDistrictApi) {
        setSubDistrict(subDistrictApi);
        localStorage.setItem(`subDistrict_${provinceId}_${cityId}`, JSON.stringify(subDistrictApi));
      }
    }
  };

  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }

    // Load data based on selection changes
    if (name === "countryId") {
      loadDataFromLocalOrAPI();
    } else if (name === "provinceId") {
      setDistrictList([]); // Reset dependent lists
      setSubDistrict([]);
      loadDistrictList(value);
    } else if (name === "city") {
      setSubDistrict([]);
      loadSubDistrictList(formData.provinceId, value);
    }
  };


  const validate = () => {
    let newErrors = {};

    if (!formData.countryId) newErrors.countryId = "Country is required";
    if (!formData.provinceId) newErrors.provinceId = "Province is required";
    if (!formData.city) newErrors.city = "District is required";
    if (!formData.suburb) newErrors.suburb = "Suburb is required";
    if (!formData.street.trim()) newErrors.street = "Address is required";
    if (!formData.unitNumber.trim())
      newErrors.unitNumber = "Unit number is required";
    else if (!/^\d+$/.test(formData.zipCode)) {
      newErrors.unitNumber = "Unit number must contain only numbers";
    }
    if (!formData.zipCode.trim()) newErrors.zipCode = "Zip code is required";
   else if (!/^\d+$/.test(formData.zipCode)) {
    newErrors.zipCode = "Zip code must contain only numbers";
  }

    return newErrors;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    onSubmit(formData);
    // console.log("Address data submitted: ", formData);
  };

  return (
    <form onSubmit={handleSubmit} style={formStyle}>
      <h3 style={headingStyle}>Address Detail</h3>
      <label htmlFor="ticketSubject" className="ticket-text-primary" >
                <span className="text-danger">The fields with * marks are mandatory</span>
          </label>

      <div style={formRowStyle}>
        <div style={inputGroupStyle}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Country <span className="text-danger">*</span>
          </label></label></label>
          <select
            id="countryId"
            name="countryId"
            value={formData.countryId}
            onChange={handleInputChange}
            style={selectStyle}
          >
            <option value="">Select</option>
            <option value="1">South Africa</option>
          </select>
          {errors.countryId && (
            <span style={errorStyle}>{errors.countryId}</span>
          )}
        </div>

        <div style={inputGroupStyle}>
          <label htmlFor="provinceId"><label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Province <span className="text-danger">*</span>
          </label></label></label></label>
          <select
            id="provinceId"
            name="provinceId"
            value={formData.provinceId}
            onChange={handleInputChange}
            style={selectStyle}
          >
            <option value="">Select</option>
            {provinceList.map((item) => (
              <option key={item.id} value={item.id}>
                {item.name}
              </option>
            ))}
          </select>
          {errors.provinceId && (
            <span style={errorStyle}>{errors.provinceId}</span>
          )}
        </div>
      </div>

      <div style={formRowStyle}>
        <div style={inputGroupStyle}>
          <label htmlFor="city"> <label htmlFor="provinceId"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          District <span className="text-danger">*</span>
          </label></label></label>
          <select
            id="city"
            name="city"
            value={formData.city}
            onChange={handleInputChange}
            style={selectStyle}
          >
            <option value="">Select</option>
            {districtList.map((district) => (
              <option key={district.id} value={district.id}>
                {district.name}
              </option>
            ))}
          </select>
          {errors.city && <span style={errorStyle}>{errors.city}</span>}
        </div>
        <div style={inputGroupStyle}>
        <div style={inputGroupStyle}>
          <label htmlFor="city"> <label htmlFor="provinceId"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Sub district <span className="text-danger">*</span>
          </label></label></label>
          <select
            id="suburb"
            name="suburb"
            value={formData.suburb}
            onChange={handleInputChange}
            style={selectStyle}
          >
            <option value="">Select</option>
            {subDistrict.map((sub) => (
              <option key={sub.id} value={sub.id}>
                {sub.name}
              </option>
            ))}
          </select>
          {errors.suburb && <span style={errorStyle}>{errors.suburb}</span>}
        </div>
      </div>
      </div>

      <div style={formRowStyle}>
        <div style={inputGroupStyle}>
        <label htmlFor="city"> <label htmlFor="provinceId"><label htmlFor="ticketSubject" className="ticket-text-primary" >
        Address <span className="text-danger">*</span>
          </label></label></label>
          <input
            type="text"
            id="street"
            name="street"
            placeholder="Street"
            value={formData.street}
            onChange={handleInputChange}
            style={inputStyle}
          />
          {errors.street && <span style={errorStyle}>{errors.street}</span>}
        </div>
      </div>

      <div style={formRowStyle}>
        <div style={inputGroupStyle}>
        <label htmlFor="city"> <label htmlFor="provinceId"><label htmlFor="ticketSubject" className="ticket-text-primary" >
        Unit Number <span className="text-danger">*</span>
          </label></label></label>
          <input
  type="text"
  id="unitNumber"
  name="unitNumber"
  placeholder="Unit Number"
  value={formData.unitNumber}
  onChange={(e) => {
    const value = e.target.value;
    // Allow only integer numbers (digits)
    if (/^\d*$/.test(value)) {
      handleInputChange(e);
    }
  }}
  style={inputStyle}
/>
          {errors.unitNumber && (
            <span style={errorStyle}>{errors.unitNumber}</span>
          )}
        </div>

        <div style={inputGroupStyle}>
        <label htmlFor="city"> <label htmlFor="provinceId"><label htmlFor="ticketSubject" className="ticket-text-primary" >
        Zip Code <span className="text-danger">*</span>
          </label></label></label>
          <input
            type="text"
            id="zipCode"
            name="zipCode"
            placeholder="Zip Code"
            value={formData.zipCode}
            onChange={(e) => {
              const value = e.target.value;
              // Allow only digits
              if (/^\d*$/.test(value)) {
                handleInputChange(e);
              }
            }}
            style={inputStyle}
          />
          {errors.zipCode && <span style={errorStyle}>{errors.zipCode}</span>}
        </div>
      </div>

      <div style={formRowStyle}>
        <button type="submit" style={submitButtonStyle}>
          Save
        </button>
      </div>
    </form>
  );
};

const formStyle = {
  width: "60%",
  margin: "-24px auto",
  // padding: '20px',
  borderRadius: "8px",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
};

const headingStyle = {
  marginBottom: "20px",
  fontWeight: "bold",
};

const formRowStyle = {
  width: "100%",
  display: "flex",
  marginBottom: "20px",
  gap: "85px",
};

const inputGroupStyle = {
  flex: "1",
  marginRight: "0px",
  display: "flex",
  flexDirection: "column",
};

const inputStyle = {
  padding: "10px",
  borderRadius: "4px",
  border: "1px solid #ccc",
  fontSize: "16px",
};

const selectStyle = {
  padding: "10px",
  borderRadius: "4px",
  border: "1px solid #ccc",
  fontSize: "16px",
};

const submitButtonStyle = {
  width: "150px",
  padding: "10px 20px",
  borderRadius: "4px",
  border: "none",
  backgroundColor: "#007bff",
  color: "#fff",
  fontSize: "16px",
  cursor: "pointer",
  textAlign: "center",
};

const errorStyle = {
  color: "red",
  fontSize: "12px",
  marginTop: "5px",
};

export default AddressDetailsForm;
