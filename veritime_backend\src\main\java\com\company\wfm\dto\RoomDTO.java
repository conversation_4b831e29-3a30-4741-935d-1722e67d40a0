package com.company.wfm.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RoomDTO {

	@NotBlank(message = "Room ID is required.")
	private String roomId; // Required: Room ID

	private String roomName; // Optional: Room name (required for searching residents)

	private Integer roomNum; // Optional: Room number (required for searching residents)

	@NotBlank(message = "Building ID is required.")
	private String buildId; // Required: Building ID

	private String buildName; // Optional: Building name (required for searching residents)

	@NotNull(message = "Area ID is required.")
	private Integer areaId; // Required: Area ID

	private String areaName; // Optional: Area name (required for searching residents)

	@NotNull(message = "Account type is required.")
	private Integer accountType; // Required: Resident type (1: householder, 0: family member)

}
