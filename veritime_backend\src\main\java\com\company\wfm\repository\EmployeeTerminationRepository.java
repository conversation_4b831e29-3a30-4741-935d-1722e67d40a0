package com.company.wfm.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.EmployeeTermination;

@Repository
public interface EmployeeTerminationRepository extends JpaRepository<EmployeeTermination, Long> {
    List<EmployeeTermination> findByTerminationType(String terminationType);

    List<EmployeeTermination> findByTerminationIdIn(List<Long> terminationIds);

}
