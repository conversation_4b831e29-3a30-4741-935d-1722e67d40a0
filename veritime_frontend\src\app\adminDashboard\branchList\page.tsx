"use client";
import { useState, useEffect } from "react";
import { Container, <PERSON>, Col, Button } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter2.js";
import { useTheme } from "@material-ui/core/styles";
import CreateBranchModal from "../modals/CreateBranchModal";
import DeleteIcon from "@mui/icons-material/Delete";
import {
  //  a11yProps,
  TabPanel,
} from "../../../common-components/utils.js";
import Layout from "@/components/Layout";
import { appConstants } from "@/constants/appConstants.js";
import { getRequest, postRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import EditIcon from "@mui/icons-material/Edit";
import useLocalStorage from "@/services/localstorage";
// import useLocalStorage from '@/services/localstorage.js';
import BranchDetailModal from "../modals/BranchDetailModal";
import VisibilityIcon from "@mui/icons-material/Visibility";
import Link from "next/link.js";
const BranchList = ({ toggleMenu, expanded }: any) => {
  const [username, setUsername] = useState("");
  const [rows, setRows] = useState([]);
  const [data, setData] = useState([]);
  const [rowToEdit, setRowToEdit] = useState<any>({});
  const [role, setRole] = useLocalStorage("role", "");
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedRole = localStorage.getItem(appConstants?.role);
      if (storedRole) {
        setRole(storedRole); // Set role from localStorage
      }
    }
  }, []);

  // // Existing state and hooks
  const [openDetailModal, setOpenDetailModal] = useState(false);
  const [selectedBranchDetails, setSelectedBranchDetails] = useState<any>({});
  const handleDetailOpen = (row: any) => {
    setSelectedBranchDetails(row); // Set the selected row details
    setOpenDetailModal(true); // Open the detail modal
  };

  const handleDetailClose = () => {
    setOpenDetailModal(false); // Close the detail modal
    setSelectedBranchDetails({
      branch: "",
      code: "",
      branchHeadId: "",
      branchTypes: "",
      hospital: "",
    }); // Reset the details to default values
  };

  // const handleDetailClose = () => {
  //   setOpenDetailModal(false); // Close the detail modal
  //   setSelectedBranchDetails({}); // Reset the details
  // };

  const [openModal, setOpenModal] = useState(false);

  // eslint-disable-next-line
  const [branchNames, setBranchNames] = useState<string[]>([]);

  const [deptNames, setdeptNames] = useState([]);

  const fetachdeptNames = async () => {
    try {
      const departmentResponse = await getRequest(`${API_URLS.GET_DEPARTMENT}`);
      if (departmentResponse && departmentResponse.length > 0)
        setdeptNames(departmentResponse);
      // if (departmentResponse && typeof departmentResponse === 'object') {
      //   const deptNamesArray = Object.values(departmentResponse);
      //   setdeptNames(deptNamesArray.map(department => department.departmentName));
      // } else {
      //   console.error('Invalid department response:', departmentResponse);
      // }
    } catch (error) {}
  };
  useEffect(() => {
    //fetachdeptNames()
    // fetachBranchNames();
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem(appConstants?.username);
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
    setIsMounted(true);
  }, []);

  useEffect(() => {
    fetchBranchData();
  }, [offset, limit]);

  const fetchBranchData = async () => {
    try {
      const payload = {
        // deptId:'',
        // branchId:'',
        isActive: 1,
        offset: offset,
        limit: limit,
      };

      const response = await postRequest(API_URLS.GET_BRANCH, payload, true);
      // console.log('response::: ', response);

      if (response) {
        setTotalItems(response?.totalElements);
        const formattedRows: any = response?.content?.map(
          (branch: any, index: any) => ({
            id: branch?.branchId || `branch-${index}`,
            // id: branch?.branchId,
            sr_no: offset * limit + (index + 1),
            code: branch?.branchCode,
            cluster: branch?.cluster,
            hospitalId: branch?.hospitalId,
            hospitalName: branch?.hospitalName,
            branch: branch?.branchName,
            branchType: branch?.branchType,
            branchHeadId: branch?.branchHeadId,
            branchHeadName: branch?.branchHeadName,
            timeZone: branch?.timeZone,
            //department : branch.department.departmentName,
            departmentIds: branch?.departments?.map(
              (department: any) => department?.departmentId
            ),
            department: branch?.departments
              ?.map(
                (dept: any) =>
                  dept.departmentName || branch.department.departmentName
              )
              .join(", "),
          })
        );

        setRows(formattedRows);
        setData(formattedRows);
      } else {
        // console.error("Failed to fetch facilities:", response);
      }
    } catch (error) {
      // console.error("Error fetching facilities data:", error);
      console.error("Error fetching branch data:", error);
    }
  };

  const handlePageChange = (newPage: any) => {
    setOffset(newPage?.page);
    setLimit(newPage?.pageSize);
  };

  const theme = useTheme();
  const [value, setValue] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  // eslint-disable-next-line
  const id = open ? "simple-popover" : undefined;

  const handleChange = (event: any) => {
    if (event.target.name === "branch") {
      setRows(data.filter((el: any) => el.branch == event.target.value));
    } else {
      // setRows(data.filter((el: any) => el.branchName == event.target.value))
    }
  };

  const handleChangeIndex = (index: any) => {
    setValue(index);
  };

  // eslint-disable-next-line
  const handleClick = (event: any) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  // const handleClose = () => {
  //   setAnchorEl(null);
  // };

  const handleEdit = (row: any) => {
    setOpenModal(true);
    setRowToEdit(row);
  };

  const handleOpen = () => {
    setOpenModal(true);
    setRowToEdit(null);
  };

  const handleModalClose = () => {
    setOpenModal(false);
    setRowToEdit(null);
  };

  const columns = [
    {
      field: "sr_no",
      headerName: "Sr No",
      width: 80,

      //renderCell: (params: any) => branchNames.indexOf(params.row.branchId) + 1,
    },
    {
      field: "hospitalName",
      headerName: "Feeder Hospital",
      width: 200,
      //  renderCell: (params: any) => {
      //    return <button className="branch-button" style={{ background: "#CAFFDC" }} >{params.row.companyId}</button>;
      //  }
    },
    {
      field: "branchName",
      headerName: "Facility",
      width: 300,
      renderCell: (params: any) => {
        return (
          <span className="fw-bold" title={params.row.branch}>
            {params.row.branch}
          </span>
        );
      },
    },
    // {
    //   field: 'branchCode',
    //   headerName: 'Code',
    //   width: 100,
    //   renderCell: (params: any) => {
    //     return <span style={{ color: "blue" }} >{params.row.code}</span>;
    //   }
    // },
    {
      field: "department",
      headerName: "Departments",
      width: 500,
      renderCell: (params: any) => {
        return (
          <i className="" title={params.row.department}>
            {params.row.department}
          </i>
        );
      },
    },

    ...(role !== "admin" && role !== "supervisor" && role !== "employee"
      ? [
          {
            field: "action",
            headerName: "Action",
            width: 180,
            renderCell: (params: any) => (
              <div>
                {" "}
                {/* Add gap here */}
                <Link
                  href={`/adminDashboard/employee/empDetails/${btoa(
                    "new"
                  )}?hospitalId=${btoa(params.row.hospitalId)}&branchId=${btoa(
                    params.row.id
                  )}`}
                >
                  <svg
                    role="button"
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    fill="currentColor"
                    className="bi bi-person-fill-add"
                    viewBox="0 0 16 16"
                    style={{ marginRight: "10px" }}
                  >
                    <path d="M12.5 16a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7m.5-5v1h1a.5.5 0 0 1 0 1h-1v1a.5.5 0 0 1-1 0v-1h-1a.5.5 0 0 1 0-1h1v-1a.5.5 0 0 1 1 0m-2-6a3 3 0 1 1-6 0 3 3 0 0 1 6 0" />
                    <path d="M2 13c0 1 1 1 1 1h5.256A4.5 4.5 0 0 1 8 12.5a4.5 4.5 0 0 1 1.544-3.393Q8.844 9.002 8 9c-5 0-6 3-6 4" />
                  </svg>
                </Link>
                <VisibilityIcon
                  style={{ marginRight: "30px", cursor: "pointer" }}
                  onClick={() => handleDetailOpen(params.row)}
                />
                <EditIcon
                  style={{ cursor: "pointer" }}
                  onClick={() => handleEdit(params.row)}
                />
                {/* <button className="deny-button" style={{ background: 'transparent', border: 'none', cursor: 'pointer' }} onClick={() => deleteRow(params.id)}>
            <DeleteIcon />
          </button> */}
              </div>
            ),
          },
        ]
      : []),
  ];

  const deleteRow = (id: any) => {
    setRows(rows.filter((el: any) => el.id !== id));
  };

  const [isMounted, setIsMounted] = useState(false);

  if (!isMounted) return null;

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <Row className="my-3">
          <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
            <h4>{"Facility List"}</h4>
            {/* <span
              style={{
                fontSize: "12px",
                color: "grey",
                textTransform: "capitalize",
              }}
            >
              Hi, {username ? username : ""}. Your organization's facilities are
              listed here
            </span> */}
          </Col>
          <Col md={8}>
            {role !== "admin" && (
              <Row style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  style={{
                    width: "150px",
                    background: "green",
                    marginRight: "35px",
                  }}
                  onClick={handleOpen}
                >
                  + New Facility
                </Button>
              </Row>
            )}
            {/* <Row className='filter-container' >
              
              <Col md={6}>
                <select className="filter-select" name='department' onChange={handleChange}>
                  <option disabled selected>Select Department</option>
                  {deptNames.length && deptNames.map((el, i) => <option key={i}>{el}</option>)}
                </select>
              </Col>



            </Row> */}
          </Col>
        </Row>
        <Row className="my-3">
          {/* <Col md={2} style={{ backgroundColor: "#6097b4" }}>
            <Tabs
              value={value}
              onChange={handleChange}
              indicatorColor="secondary"
              textColor="inherit"
              variant="fullWidth"
              aria-label="full width tabs example"
              className="tab-pannel-test "
            style={{backgroundColor: "#6097b4"}}
            >
              <Tab label={`Durban (${rows.length})`} {...a11yProps(0)} style={{color: "#000", backgroundColor: "#ccc", boxShadow: "none", fontFamily: "inherit"}}/>
              <Tab label={`Johannesburg (${rows.length})`} {...a11yProps(1)} style={{color: "#000", backgroundColor: "#ccc", boxShadow: "none", fontFamily: "inherit"}}/>
            </Tabs>
            <Row className='carousal-container'>
              <CarouselComp setValue={setValue} arr={["Durban", "Johannesburg"]} />
            </Row>
          </Col> */}
          <Col md={15}>
            <TabPanel value={value} index={0} dir={theme.direction}>
              <Row>
                <div id="tableWrapper">
                  <TableFilter
                    columns={columns}
                    rows={rows}
                    onPageChange={handlePageChange}
                    totalCount={totalItems}
                    pageSize={limit}
                  />
                </div>
              </Row>
            </TabPanel>
            {/* <TabPanel value={value} index={1} dir={theme.direction}>
                <Row>
                  <div id="tableWrapper">
                    <TableFilter columns={columns} rows={rows} />
                  </div>
                </Row>
              </TabPanel> */}
          </Col>
        </Row>
        {openModal && (
          <CreateBranchModal
            show={openModal}
            handleClose={handleModalClose}
            rows={rows}
            setRows={setRows}
            rowToEdit={rowToEdit}
          />
        )}
        {openDetailModal && (
          <BranchDetailModal
            show={openDetailModal}
            handleClose={handleDetailClose}
            branchDetails={selectedBranchDetails}
          />
        )}
      </Container>
    </Layout>
  );
};

export default BranchList;
