package com.company.wfm.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.EmployeeDetailsDto;
import com.company.wfm.dto.ForgotPasswordRequestDTO;
import com.company.wfm.dto.ForgotPasswordResponseDTO;
import com.company.wfm.dto.ResetPasswordDTO;
import com.company.wfm.dto.ResetTokenDTO;
import com.company.wfm.dto.UserCreationDTO;
import com.company.wfm.entity.User;
import com.company.wfm.service.impl.UserService;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api/v1/users")
@CrossOrigin(origins = "*")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;
    @PostMapping("/create")
    public ResponseEntity<EmployeeDetailsDto> createUser(@RequestBody UserCreationDTO userDTO) {
        EmployeeDetailsDto responseDTO = userService.createUser(userDTO);
        return ResponseEntity.ok(responseDTO);
    }

    @PostMapping("/createNew")
    public ResponseEntity<User> createUser1(@RequestBody UserCreationDTO userDTO) {
        User responseDTO = userService.createUser1(userDTO);
        return ResponseEntity.ok(responseDTO);

    }

    @PostMapping("/forgot-password")
    public ResponseEntity<ForgotPasswordResponseDTO> forgotPassword(@RequestBody ForgotPasswordRequestDTO request, HttpServletRequest httpRequest) {

        String frontendUrl = httpRequest.getHeader("Referer");
        if (frontendUrl == null) {
            frontendUrl = httpRequest.getHeader("Origin");
        }

        if (frontendUrl != null) {
            logger.info("Request received from frontend URL: {}", frontendUrl);
        } else {
            logger.warn("Unable to detect frontend URL (No Referer or Origin header).");
        }

        // Add frontend URL to HttpServletRequest as an attribute
        httpRequest.setAttribute("frontendUrl", frontendUrl);
        try {

            // Validate that the email field is provided
            if (request.getEmail() == null || request.getEmail().isEmpty()) {
                return ResponseEntity.badRequest().body(null);
            }

            // Process the forgot password request
            ForgotPasswordResponseDTO response = userService.forgotPassword(request.getEmail(),httpRequest);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("An error occurred while processing the forgot password request: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(null);
        }
    }

    @PostMapping("/reset-password")
    public ResponseEntity<String> resetPassword(@RequestBody ResetPasswordDTO request) {
        try {
            // Validate that newPassword and confirmPassword match
            if (!request.getNewPassword().equals(request.getConfirmPassword())) {
                return ResponseEntity.badRequest().body("New password and confirm password do not match.");
            }

            // Call the service method and return the response
            String responseMessage = userService.resetPassword(request.getToken(), request.getNewPassword());
            return ResponseEntity.ok(responseMessage);
        } catch (Exception e) {
            logger.error("An error occurred while resetting the password: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("An error occurred while resetting the password. Please try again later.");
        }
    }


    @PostMapping("/validate-reset-token")
    public ResponseEntity<String> validateResetToken(@RequestBody ResetTokenDTO resetTokenDTO) {
        try {
            boolean isValid = userService.validateResetToken(resetTokenDTO.getResetToken());

            if (isValid) {
                return ResponseEntity.ok("Reset token is valid.");
            } else {
                return ResponseEntity.status(400).body("Invalid reset token.");
            }
        } catch (Exception e) {
            logger.error("An error occurred while validating the reset token: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("An error occurred while validating the reset token. Please try again later.");
        }
    }

}