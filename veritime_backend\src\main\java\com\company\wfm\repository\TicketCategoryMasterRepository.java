package com.company.wfm.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.TicketCategoryResponseDTO;
import com.company.wfm.entity.TicketCategoryMaster;

@Repository
public interface TicketCategoryMasterRepository extends JpaRepository<TicketCategoryMaster, Long> {


    @Query("SELECT new com.company.wfm.dto.TicketCategoryResponseDTO(t.id, t.category, t.priority) " +
            "FROM TicketCategoryMaster t WHERE t.id = :id")
    TicketCategoryResponseDTO findCategoryDetailsById(@Param("id") Long id);


   // List<TicketCategoryMaster> findByIsActiveTrueOrderByPriorityDesc();
   /*@Query("SELECT t.id AS id, t.category AS category, t.priority AS priority FROM TicketCategoryMaster t WHERE t.isActive = true ORDER BY t.id DESC")
   List<Object[]> findActiveCategoriesWithIdCategoryPriorityOrderedByIdDesc();*/

    @Query("SELECT t.id AS id, t.category AS category, t.priority AS priority " +
            "FROM TicketCategoryMaster t " +
            "WHERE t.isActive = true AND t.branchId = :branchId " +
            "ORDER BY t.id DESC")
    List<Object[]> findActiveCategoriesWithIdCategoryPriorityOrderedByIdDesc(@Param("branchId") Long branchId);





    @Query("SELECT t FROM TicketCategoryMaster t WHERE t.branchId = :branchId AND t.departmentId = :departmentId")
    List<TicketCategoryMaster> findCategoriesByBranchAndDepartment(
            @Param("branchId") Long branchId,
            @Param("departmentId") Long departmentId);

    List<TicketCategoryMaster> findByBranchIdAndDepartmentId(Long branchId, Long departmentId);

    @Query("SELECT c FROM TicketCategoryMaster c WHERE c.branchId = :branchId")
    Page<TicketCategoryMaster> findByBranchId(@Param("branchId") Long branchId, Pageable pageable);

    @Query("SELECT c.category FROM TicketCategoryMaster c WHERE c.id = :categoryId")
    Optional<String> findCategoryNameById(@Param("categoryId") Long categoryId);


}










