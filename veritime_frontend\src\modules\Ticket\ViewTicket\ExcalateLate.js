import React, { useEffect, useState } from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import {
  postRequest,
  postRequestWithSecurity,
  fileDownload,
} from "../../../services/apiService";
import { API_URLS } from "../../../constants/apiConstants";
import moment from "moment";
import { convertToSouthAfricaTime } from "@/services/utils";
import Link from "next/link";
import "../TicketRaise/Subject.css";

const truncateMessage = (message, maxLength = 20) => {
  if (!message) return "No message";
  return message.length > maxLength
    ? `${message.substring(0, maxLength)}...`
    : message;
};

const handleDownload = async (file) => {
  try {
    const fileName = file.split("/").pop();
    const response = await fileDownload(
      `${API_URLS?.TICKET_DOWNLOAD}${file}`,
      true
    );

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const blob = await response.blob();
    const urlBlob = window.URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = urlBlob;
    a.target = "_blank";
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    a.remove();
    showSuccessAlert(`File "${fileName}" downloaded successfully.`);
  } catch (error) {
    showErrorAlert("Error downloading file:", error.message);
  }
};

const TableRow = ({
  srNo,
  subject,
  message,
  status,
  ageing,
  ticketId,
  file,
  categoryName,
  latestRemark,
  lastEscalationTime,
}) => {
  const isFileAvailable = file && file.trim() !== "";

  return (
    <tr
      className="border-bottom pt-1 fontsize-row"
      style={{ textAlign: "center", textAlign: "left" }}
    >
      {/* <td className="pt-5">{srNo}</td> */}
      <td className="pt-1">
        <Link
          className="text-primary"
          href={`/ticket/details/${btoa(ticketId)}`}
          style={{ cursor: "pointer", padding: "0" }}
        >
          #{ticketId}
        </Link>
      </td>
      {/* <td className="pt-1">{subject || "No subject"}</td> */}
      {/* Truncate the Subject */}
      <td className="pt-1" title={subject} style={{ marginLeft: "1%" }}>
        {truncateMessage(subject, 20)}
      </td>
      <td
        className="pt-1 text-truncate"
        title={message}
        dangerouslySetInnerHTML={{ __html: truncateMessage(message, 60) }}
      ></td>

      <td className="pt-1">{categoryName || "No categoryName"}</td>
      {/* <td className="pt-5" title={latestRemark}>
        {truncateMessage(latestRemark)}
      </td> */}
      {/* 
      <td className="pt-5">{status || "Unknown"}</td> */}
      <td className="pt-1">{ageing}</td>
      <td className="pt-1">
        {lastEscalationTime
          ? moment(lastEscalationTime).format("YYYY-MM-DD HH:mm:ss")
          : "N/A"}
      </td>
      {/* <td className="pt-5">
        <Link
          className="text-primary p-3"
          href={`/ticket/details/${btoa(ticketId)}`}
          style={{ cursor: "pointer" }}
        >
          <img
            src="/image/eyebutton.png"
            style={{ width: "20px" }}
            alt="View Details"
          />
        </Link>
      </td> */}
      <td className="pt-1">
        {isFileAvailable ? (
          <a
            className="text-primary"
            onClick={() => handleDownload(file)}
            style={{ cursor: "pointer" }}
          >
            <img
              src="/image/icons8-download-24.png"
              style={{ width: "20px", marginLeft: "40px" }}
            />
          </a>
        ) : (
          <span style={{ marginLeft: "22px", width: "10px" }}></span>
        )}
      </td>
    </tr>
  );
};

const Table = ({
  onEyeClick,
  departmentId,
  status,
  assignedTo,
  createdBy,
  myDepartmentTicket,
}) => {
  const [tableData, setTableData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalCount, settotalCount] = useState(0);
  const rowsPerPage = 10;

  const fetchTickets = async (page = 0) => {
    try {
      const response = await postRequestWithSecurity(API_URLS.EXCALATE_LIST, {
        offset: page,
        limit: rowsPerPage,
      });
      // console.log("Fetched Data:", response);

      if (response && response.content) {
        setTableData(response.content);
        settotalCount(response.totalCount);
      } else {
        setTableData([]);
      }
    } catch (error) {
    }
  };

  useEffect(() => {
    fetchTickets(currentPage);
    const intervalId = setInterval(() => {
      fetchTickets(currentPage);
    }, 10000);
    return () => clearInterval(intervalId);
  }, [
    departmentId,
    status,
    assignedTo,
    createdBy,
    myDepartmentTicket,
    currentPage,
  ]);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber - 1);
  };

  const calculateAgeing = (createdTime) => {
    const now = moment();
    const createdDate = moment(createdTime);
    const duration = moment.duration(now.diff(createdDate));
    const days = duration.asDays().toFixed(0);
    return `${days} day${days > 1 ? "s" : ""}`;
  };

  return (
    <div
      className="container mt-1"
      style={{ width: "100%", overflow: "hidden" }}
    >
      <div
        className="table-responsive"
        style={{ width: "100%", overflow: "hidden" }}
      >
        <table className="table">
          <thead>
            <tr>
              {/* <th scope="col" style={{width:'100px'}}>Sr No</th> */}
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Ticket No
              </th>
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Subject
              </th>
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Description
              </th>
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Issue Type
              </th>
              {/* <th scope="col">Remark</th> */}
              {/* <th scope="col" style={{ width: "100px" }}>
                Status
              </th> */}
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Ageing
              </th>
              <th scope="col" style={{ width: "150px" }}>
                Escalates time{" "}
              </th>
              {/* <th scope="col">Action</th> */}
              <th
                scope="col"
                style={{
                  whiteSpace: "nowrap",
                  fontSize: "15px",
                  textAlign: "left",
                }}
              >
                Download
              </th>
            </tr>
          </thead>
          <tbody>
            {tableData.length > 0 ? (
              tableData.map((row, index) => (
                <TableRow
                  key={row.ticketId}
                  // srNo={index + 1 + currentPage * rowsPerPage}
                  date={convertToSouthAfricaTime(row.createdTime)}
                  subject={row.ticketSubject ?? "No subject"}
                  message={row.ticketMessage ?? "No message"}
                  ticketId={row.ticketId}
                  categoryName={row.categoryName}
                  // latestRemark={row.latestRemark}
                  // status={row.status ?? "Unknown"}
                  ageing={calculateAgeing(row.createdTime)}
                  lastEscalationTime={row.lastEscalationTime}
                  // onEyeClick={onEyeClick}
                  file={row.filePath}
                />
              ))
            ) : (
              <tr>
                <td colSpan="11" className="text-center">
                  No tickets found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <nav aria-label="Page navigation example">
        <ul className="pagination justify-content-center">
          {Array.from({ length: totalCount }, (_, index) => (
            <li
              key={index + 1}
              className={`page-item ${currentPage === index ? "active" : ""}`}
            >
              <a
                className="page-link"
                onClick={() => handlePageChange(index + 1)}
              >
                {index + 1}
              </a>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default Table;
