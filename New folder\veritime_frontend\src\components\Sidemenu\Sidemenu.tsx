import React, { useEffect, useState } from "react";
import useLocalStorage from "@/services/localstorage";
import { getRequest, postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants.js";
import "./Sidemenu.css";
import Link from "next/link";
import { showSuccessAlert2, showErrorAlert } from "@/services/alertService";
import { Modal, Button, Form } from "react-bootstrap";
import axios from "axios";

export const Sidemenu = ({ expanded, toggleCard, mastersButtonRef }: any) => {
  const [menuData, setMenuData] = useState<any[]>([]);
  const [currentPath, setCurrentPath] = useState("");
  const [role] = useLocalStorage("role", "");
  const appVersion = "v1.0.0";
  const [isWorkoffFormOpen, setIsWorkoffFormOpen] = useState(false);
  const [reason, setReason] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(""); // Store error message

  useEffect(() => {
    const fetchMenuData = async () => {
      const cachedMenu = localStorage.getItem("menuData");
      if (cachedMenu) {
        setMenuData(JSON.parse(cachedMenu));
      } else {
        try {
          const data = await getRequest(API_URLS.SIDEMENU_LIST);
          localStorage.setItem("menuData", JSON.stringify(data));
          setMenuData(data);
        } catch (error) {}
      }
    };

    fetchMenuData();
    setCurrentPath(window.location.pathname);
  }, []);

  const isActive = (path: string) => currentPath === path;

  const renderMenuItems = (items: any[]) => {
    return items
      .filter((item) => item.is_active)
      .sort((a, b) => a.sequence - b.sequence)
      .map((item) => (
        <Link href={item.menu_path} key={item.id}>
          <div
            className={`menu-item ${isActive(item.menu_path) ? "active" : ""} ${
              expanded ? "expanded" : ""
            }`}
          >
            <img
              src={isActive(item.menu_path) ? item.icon_path : item.icon_path}
              className="menu-item-image"
              alt={item.menu_label}
            />
            <span className="menu-item-text">{item.menu_label}</span>
          </div>
        </Link>
      ));
  };

  // const handleResignation = async () => {
  //   if (!reason.trim()) {
  //     alert("Please provide a reason for termination.");
  //     return;
  //   }

  //   console.log("Submitting Resignation Request - Reason:", reason);

  //   const token = localStorage.getItem("accessToken");

  //   if (!token) {
  //     alert("Authentication token missing. Please log in again.");
  //     return;
  //   }

  //   setIsLoading(true);

  //   try {
  //     const response = await axios.post(
  //       API_URLS.RESIGNATION,
  //       { terminationReason: reason },

  //       {
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //         },
  //       }
  //     );
  //     console.log("API Payload:", { terminationReason: reason });

  //     if (response?.status === 200 || response?.status === 201) {
  //       showSuccessAlert2(
  //         response.data.message || "Resignation submitted successfully."
  //       );
  //       closeWorkoffForm();
  //     } else {
  //       showErrorAlert(response.data.message || "Something went wrong.");
  //     }
  //   } catch (error: any) {
  //     console.error("Error submitting resignation:", error);

  //     const errorMessage =
  //       error.response?.data?.message ||
  //       error.response?.data ||
  //       "An unexpected error occurred.";

  //     showErrorAlert(errorMessage);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };
  // const openWorkoffForm = () => {
  //   setIsWorkoffFormOpen(true);
  // };

  // const closeWorkoffForm = () => {
  //   setIsWorkoffFormOpen(false);
  // };

  // const handleReasonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
  //   setReason(e.target.value);
  // };

  return (
    <div className={`sidemenu-container ${expanded ? "expanded" : ""}`}>
      <img src="/image/logo_full.png" alt="Logo" className="logo" />
      {!expanded && <img src="/image/sidemenu_log.png" className="logo_sm" />}
      <div className={`menu-container ${expanded ? "expanded" : ""}`}>
        <div className="menu-items">
          {menuData.map((item) => (
            <React.Fragment key={item.id}>
              {item.menu_label === "Masters" ? (
                <div
                  className={`menu-item ${expanded ? "expanded" : ""}`}
                  ref={mastersButtonRef}
                  onClick={toggleCard}
                >
                  <img
                    src={item.icon_path}
                    alt="Masters"
                    className="menu-item-image"
                  />
                  <span className="menu-item-text">Setup</span>
                </div>
              ) : (
                renderMenuItems([item])
              )}
            </React.Fragment>
          ))}

          {/* Add Workoff Icon */}
          {/* <div
            className={`menu-item ${expanded ? "expanded" : ""}`}
            onClick={openWorkoffForm}
          >
            <img
              src="/image/exit.png"
              alt="Workoff"
              className="menu-item-image"
            />
            <span className="menu-item-text">Resignation</span>
          </div> */}
        </div>
      </div>

      {/* {isWorkoffFormOpen && (
        <Modal show={isWorkoffFormOpen} onHide={closeWorkoffForm}>
          <Modal.Header closeButton>
            <Modal.Title>Resignation</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <p style={{ marginBottom: "10px" }}>
              Are you sure you want to Resign?
            </p>
            <Form.Group controlId="terminationReason">
              <Form.Label style={{ marginTop: "10px" }}>
                Reason for Resignation
              </Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={reason}
                onChange={handleReasonChange}
                placeholder="Please provide a reason for termination."
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={closeWorkoffForm}>
              Cancel
            </Button>
            <Button
              variant="success"
              onClick={handleResignation}
              disabled={isLoading}
            >
              {isLoading ? "Resignation..." : "Resignation"}
            </Button>
          </Modal.Footer>
        </Modal>
      )} */}

      <p
        style={{
          position: "absolute",
          marginLeft: "30px",
          color: "grey",
          fontSize: "12px",
          bottom: 0,
        }}
      >
        {appVersion}
      </p>
    </div>
  );
};

export default Sidemenu;
function onHide() {
  throw new Error("Function not implemented.");
}
