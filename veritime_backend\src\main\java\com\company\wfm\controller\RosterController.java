package com.company.wfm.controller;

import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.service.UserTokenService;

@RestController
@RequestMapping("/api/v1/roster")
@CrossOrigin(origins = "*")
public class RosterController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserTokenService userTokenService;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @GetMapping
    public Map<String, Object> getAllEmployeeDetails(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate) {

        // Set default dates if not provided
        if (fromDate == null) {
            fromDate = LocalDate.now();
        }
        if (toDate == null) {
            toDate = fromDate.plusDays(7);  // Default: 7 days from today
        }

        // Get logged-in employee details
        Long loggedInEmpId = userTokenService.getEmployeeIdFromToken();
        String role = userTokenService.getEmployeeFromToken().getRole();

        // Retrieve employee IDs based on the role of the logged-in employee
        List<Long> employeeIds = getEmployeeIdsForRole(loggedInEmpId, role);

        // If no employees are found, return an empty response
        if (employeeIds.isEmpty()) {
            Map<String, Object> emptyResponse = new HashMap<>();
            emptyResponse.put("employees", Collections.emptyList());
            emptyResponse.put("weekdays", generateWeekdaysArray(fromDate, toDate));
            return emptyResponse;
        }

        // SQL query using named parameters
        String employeeSql = "SELECT e.EMP_NAME as name, e.IMG_URE as avatar, e.EMP_ID, e.UPPER_ID, " +
                "d.designation_name, d.designation_code, d.designation_level, d.ROLE, " +
                "dept.DEPARTMENT_NAME as department_name " +
                "FROM t_employee e " +
                "INNER JOIN t_designation d ON e.DESIGNATION_ID = d.designation_id " +
                "INNER JOIN t_department dept ON e.DEPARTMENT_ID = dept.DEPARTMENT_ID " +
                "WHERE e.IN_SERVICE = 1 AND e.EMP_ID IN (:employeeIds)";

        // Parameters for the named query
        Map<String, Object> params = new HashMap<>();
        params.put("employeeIds", employeeIds);

        // Execute the query using NamedParameterJdbcTemplate
        LocalDate finalFromDate = fromDate;
        LocalDate finalToDate = toDate;
        List<Map<String, Object>> employees = namedParameterJdbcTemplate.query(employeeSql, params, (rs, rowNum) -> {
            Map<String, Object> employee = new HashMap<>();
            employee.put("emp_id", rs.getLong("EMP_ID"));
            employee.put("upper_id", rs.getLong("UPPER_ID"));
            employee.put("name", rs.getString("name"));
            employee.put("avatar", rs.getString("avatar"));
            employee.put("department_name", rs.getString("department_name"));

            // Retrieve employee schedule for the given date range
            List<Map<String, Object>> schedule = getEmployeeSchedule(rs.getLong("EMP_ID"), rs, finalFromDate, finalToDate);
            employee.put("schedule", schedule);
            return employee;
        });

        // Generate weekdays for the given date range
        List<Map<String, Object>> weekdays = generateWeekdaysArray(fromDate, toDate);

        // Return the response containing employees and weekdays
        Map<String, Object> response = new HashMap<>();
        response.put("employees", employees);
        response.put("weekdays", weekdays);

        return response;
    }



    private List<Long> getEmployeeIdsForRole(Long loggedInEmpId, String role) {
        if ("employee".equalsIgnoreCase(role)) {
            return Collections.singletonList(loggedInEmpId); // Only their own data
        } else {
            String query = "SELECT EMP_ID FROM t_employee WHERE UPPER_ID = ?";
            return jdbcTemplate.query(query, new Object[]{loggedInEmpId}, (rs, rowNum) -> rs.getLong("EMP_ID"));
        }
    }

    private List<Map<String, Object>> getEmployeeSchedule(Long empId, ResultSet rsEmployee, LocalDate fromDate, LocalDate toDate) {
       /* String scheduleSql = "SELECT es.actual_shift, es.modified_shift, ts.start_time, ts.end_time, es.date " +
                "FROM t_employee_schedule es " +
                "INNER JOIN t_time_slot ts ON ts.time_slot_id = es.actual_shift " +
                "WHERE es.EMP_ID = ? AND es.date BETWEEN ? AND ?";*/

        String scheduleSql = "SELECT es.actual_shift, es.modified_shift, ts.start_time, ts.end_time, es.date, " +
                "(CASE WHEN es.modified_shift IS NOT NULL AND es.modified_shift <> '' " +
                " THEN es.modified_shift ELSE es.actual_shift END) AS selected_shift " +
                "FROM t_employee_schedule es " +
                "INNER JOIN t_time_slot ts ON ts.time_slot_id = " +
                "(CASE WHEN es.modified_shift IS NOT NULL AND es.modified_shift <> '' " +
                " THEN es.modified_shift ELSE es.actual_shift END) " +
                "WHERE es.EMP_ID = ? AND es.date BETWEEN ? AND ?";

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("hh:mm a");

        List<Map<String, Object>> shifts = jdbcTemplate.query(scheduleSql, new Object[]{empId, fromDate, toDate}, (rs, rowNum) -> {
            Map<String, Object> shift = new HashMap<>();

            LocalTime startTime = rs.getTime("start_time").toLocalTime();
            LocalTime endTime = rs.getTime("end_time").toLocalTime();
            LocalDate scheduleDate = rs.getDate("date").toLocalDate();

            String shiftTime = startTime.format(formatter) + " - " + endTime.format(formatter);

            String selectedShift = rs.getString("selected_shift");
            shift.put("shift", shiftTime);
            shift.put("type", getShiftType(selectedShift));
            shift.put("date", scheduleDate.toString());
            shift.put("designation", rsEmployee.getString("designation_name"));

            return shift;
        });

        return buildCompleteSchedule(shifts, fromDate, toDate);
    }

    private List<Map<String, Object>> buildCompleteSchedule(List<Map<String, Object>> shifts, LocalDate fromDate, LocalDate toDate) {
        List<Map<String, Object>> completeSchedule = new ArrayList<>();
        Map<LocalDate, Map<String, Object>> shiftMap = new HashMap<>();

        for (Map<String, Object> shift : shifts) {
            LocalDate shiftDate = LocalDate.parse(shift.get("date").toString());
            shiftMap.put(shiftDate, shift);
        }

        for (LocalDate date = fromDate; !date.isAfter(toDate); date = date.plusDays(1)) {
            Map<String, Object> dayMap = new HashMap<>();
            dayMap.put("date", date.toString());

            if (shiftMap.containsKey(date)) {
                Map<String, Object> shift = shiftMap.get(date);
                dayMap.put("shift", shift.get("shift"));
                dayMap.put("designation", shift.get("designation"));
                dayMap.put("type", shift.get("type"));
            } else {
                dayMap.put("shift", "");
                dayMap.put("designation", shiftMap.getOrDefault(fromDate, new HashMap<>()).get("designation"));
                dayMap.put("type", "holiday");
            }

            completeSchedule.add(dayMap);
        }

        return completeSchedule;
    }

    private List<Map<String, Object>> generateWeekdaysArray(LocalDate fromDate, LocalDate toDate) {
        List<Map<String, Object>> weekdays = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM dd");

        if (fromDate == null) {
            fromDate = LocalDate.now();
        }
        if (toDate == null) {
            toDate = fromDate.plusDays(7);
        }

        for (LocalDate date = fromDate; !date.isAfter(toDate); date = date.plusDays(1)) {
            Map<String, Object> dayMap = new HashMap<>();
            dayMap.put("day", date.getDayOfWeek().toString());
            dayMap.put("date", "(" + date.format(formatter) + ")");
            weekdays.add(dayMap);
        }

        return weekdays;
    }

    private String getShiftType(String shift) {
        if (shift == null) {
            return "normal";
        } else if (shift.equalsIgnoreCase("Holiday")) {
            return "holiday";
        } else if (shift.equalsIgnoreCase("On Leave")) {
            return "leave";
        } else if (shift.equalsIgnoreCase("Out door duty")) {
            return "outdoor";
        }
        return "normal";
    }
}
