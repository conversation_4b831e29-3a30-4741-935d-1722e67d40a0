package com.company.wfm.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.NotificationDTO;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.FirebaseNotificationService;
import com.company.wfm.service.UserTokenService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/employees")
@CrossOrigin(origins = "*")
@Slf4j
public class FirebaseController {

    @Autowired
    private FirebaseNotificationService firebaseNotificationService;

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private CommonNotificationService notificationService;

    @PostMapping("/updateToken")
    public ResponseEntity<?> updateFirebaseToken(@RequestBody Map<String, Object> payload) {
        try {
            Long empId = tokenService.getEmployeeIdFromToken();
            String firebaseToken = payload.get("firebaseToken").toString();
            String logHistoryId = payload.get("logHistoryId").toString();

            boolean isUpdated = firebaseNotificationService.updateFirebaseToken(empId,firebaseToken,logHistoryId);

            if (isUpdated) {
                return new ResponseEntity<>("Firebase token updated successfully", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Failed to update Firebase token", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Failed to update Firebase token", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/logoutUser")
    public ResponseEntity<?> logoutUser(@RequestBody Map<String, Object> payload) {
        try {
            String logHistoryId = payload.get("logHistoryId").toString();

            boolean isUpdated = firebaseNotificationService.removeToken(logHistoryId);

            if (isUpdated) {
                return new ResponseEntity<>("user is logged out", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Failed to logout user", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            return new ResponseEntity<>("Failed to logout user", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/testNotif")
    public ResponseEntity<?> testNotif(@RequestBody Map<String, Object> payload) {
        try {
            Long empId = tokenService.getEmployeeIdFromToken();
            String title = payload.get("title").toString();
            String msg = payload.get("msg").toString();
            String type = payload.containsKey("type") ? payload.get("type").toString() : null;
            String sentById = payload.containsKey("sentById") ? payload.get("sentById").toString() : null;

            notificationService.sendNotificationToEmployee(empId,title,msg,type,sentById);
            return new ResponseEntity<>("notification sent successfully", HttpStatus.OK);

        } catch (Exception e) {
        	log.error("Exception occured while saveEmployeeLeaveBalances", e);
            return new ResponseEntity<>("error sending notif", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/notifications")
    public ResponseEntity<?> getNotifications(@RequestBody Map<String, Integer> params) {
        try {
            Long empId = tokenService.getEmployeeIdFromToken();

            int limit = params.getOrDefault("limit", 5);
            int offset = params.getOrDefault("offset", 0);

            int totalCount = notificationService.getNotificationCountByEmployeeId(empId);
            int totalreadCount = notificationService.getNotificationReadCountByEmployeeId(empId);

            List<NotificationDTO> notifications = notificationService.getNotificationsByEmployeeId(empId, limit, offset);

            Map<String, Object> response = new HashMap<>();
            response.put("count", totalCount);
            response.put("readcount", totalreadCount);
            response.put("list", notifications);

            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Exception occured while getNotifications", e);
            return new ResponseEntity<>("Error getting notifications", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/notifications/read/{id}")
    public ResponseEntity<?> markNotificationAsRead(@PathVariable("id") Long notificationId) {
        try {
            if (notificationId == null) {
                return new ResponseEntity<>("Notification ID is required", HttpStatus.BAD_REQUEST);
            }
            boolean isUpdated = notificationService.markNotificationAsRead(notificationId);
            if (isUpdated) {
                return new ResponseEntity<>("Notification marked as read", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("Notification not found", HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
        	log.error("Exception occured while markNotificationAsRead", e);
            return new ResponseEntity<>("Error marking notification as read", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


}
