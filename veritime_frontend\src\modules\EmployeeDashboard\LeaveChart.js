import React, { useEffect, useState } from "react";
import { Doughn<PERSON> } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Too<PERSON><PERSON>, Legend } from "chart.js";
import { colors } from "@constants/colors";
import { getRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";

ChartJS.register(Arc<PERSON><PERSON>, Tooltip, Legend);

const LeaveBalanceChart = () => {
  const [chartSize, setChartSize] = useState({ width: "60%", height: "274px" });
  const [legendFontSize, setLegendFontSize] = useState("10px");
  const [legendColorBoxSize, setLegendColorBoxSize] = useState("15px");

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 1100) {
        setChartSize({ width: "50%", height: "240px" });
        setLegendFontSize("11px");
        setLegendColorBoxSize("12px");
      } else if (window.innerWidth <= 968) {
        setChartSize({ width: "50%", height: "200px" });
        setLegendFontSize("11px");
        setLegendColorBoxSize("12px");
      } else if (window.innerWidth <= 576) {
        setChartSize({ width: "40%", height: "150px" });
        setLegendFontSize("11px");
        setLegendColorBoxSize("10px");
      } else {
        setChartSize({ width: "50%", height: "280px" });
        setLegendFontSize("11px");
        setLegendColorBoxSize("15px");
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: "70%",
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        callbacks: {
          label: function (context) {
            let label = context.label || "";
            if (label) {
              label += ": ";
            }
            label += context.raw;
            return label;
          },
        },
      },
    },
  };

  const staticColors = ["#1E488E", "#5B73A6", "#6495ED", "#B0C4DE", "#000080"];

  const [chartData, setChartData] = useState({});
  const [legendItems, setLegendItems] = useState([]);
  useEffect(() => {
    const fetchLeaveTypes = async () => {
      try {
        const response = await getRequest(
          `${API_URLS.MASTER_LEAVE_LIST}?isActive=true`
        );

        if (response && Array.isArray(response) && response.length > 0) {
          const activeLeaveTypes = response.slice(7);
          const chartLabels = activeLeaveTypes?.map((item) => item.type);
          const chartValues = activeLeaveTypes?.map((item) => item.leaveCount);
          setChartData({
            labels: chartLabels,
            datasets: [
              {
                data: chartValues,
                backgroundColor: staticColors.slice(0, chartLabels.length),
                borderColor: staticColors.slice(0, chartLabels.length),
                borderWidth: 1,
              },
            ],
          });

          const mappedLegendItems = activeLeaveTypes?.map((item, index) => ({
            ...item,
            color: staticColors[index % staticColors.length],
          }));

          setLegendItems(mappedLegendItems);
        } else {
        }
      } catch (error) {}
    };

    fetchLeaveTypes();
  }, []);

  return (
    <div
      className="col-12 col-md-4 col-lg-3 mb-4"
      style={{
        borderWidth: 1,
        borderColor: "black",
        // overflow: "hidden", // Prevents overflow
        // maxHeight: "320px",
      }}
    >
      <div
        className="d-flex align-items-center justify-content-between"
        style={{
          height: chartSize.height,
          backgroundColor: "white",
          borderRadius: "10px",
          padding: "10px",
          boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
        }}
      >
        <div style={{ width: chartSize.width, position: "relative" }}>
          {Object.keys(chartData).length > 0 ? (
            <Doughnut data={chartData} options={options} />
          ) : null}
          <div
            style={{
              position: "absolute",
              left: "50%",
              top: "50%",
              transform: "translate(-50%, -50%)",
              fontSize: "10px",
              fontWeight: "bold",
              color: colors.purple2,
              textAlign: "center",
            }}
          >
            Leave Assigned
          </div>
        </div>

        <div
          className="d-flex flex-column justify-content-center align-items-start"
          style={{
            // width: "30%",
            // minWidth: "160px",
            width: "100%",
            minWidth: "auto",
            maxWidth: "100%",
            //maxHeight: "300px",
            maxHeight: "100%",

            overflowY: "auto",
            // paddingRight: '10px',
            scrollBehavior: "smooth",
            marginTop: "5px",
          }}
        >
          {legendItems?.map((item, index) => (
            <div
              key={index}
              className="d-flex align-items-center mb-3"
              style={{ marginBottom: "1rem", marginLeft: "1rem" }}
            >
              <div
                style={{
                  width: legendColorBoxSize,
                  height: legendColorBoxSize,
                  borderRadius: "50%",
                  backgroundColor: item.color,
                  marginRight: "10px",
                }}
              />
              <p
                style={{
                  fontSize: legendFontSize,
                  color: colors.purple2,
                  margin: 0,
                  wordWrap: "break-word",
                  whiteSpace: "normal",
                }}
              >
                {item?.type}
              </p>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .d-flex {
          -ms-overflow-style: none; /* For Internet Explorer and Edge */
          scrollbar-width: none; /* For Firefox */
        }
        .d-flex::-webkit-scrollbar {
          display: none; /* For Webkit browsers (Chrome, Safari, etc.) */
        }
      `}</style>
    </div>
  );
};

export default LeaveBalanceChart;
