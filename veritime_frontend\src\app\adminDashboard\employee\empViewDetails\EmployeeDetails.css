
.card {
  border-radius: 10px;

}
.text-primary {
  color: var(--blue11) !important;
}

.me-md-3 {
  margin-right: 1rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.ms-md-auto {
  margin-left: auto !important;
}

.btn-outline-success {
  border-color: var(--green2);
  color: var(--green2);
}

.btn-outline-success:hover {
  background-color: var(--green2);
  color: var(--red14)
}

.profile-section1{
  background-color: var(--red12);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  line-height: 3; 
}
.profile-section2 {
  background-color: var(--red12);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  line-height: 3; 
  margin-left: 1px !important; 
  width:72% !important; 
  padding:35px !important;
}
.container-box{
  width:102%;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.04);


}
.profile-section1 {
  margin-right: 10px;
  width:30%;
  margin-left: 50px;
}

/* .profile-section2 {
  margin-left: 1px !important; 
  width:60% !important; 
  padding:35px !important;
} */

.profile-section h5 {
  color: var(--blue11);
  font-weight: 700;
}

.profile-section p, .profile-section .info {
  color: var(--grey1);
  margin-bottom: 10px;
  font-weight: 700;
}
.btn.w-100.btn-primary{

  background-color: #6096b4;
}


.d-flex {
  display: flex !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-md-row {
  flex-direction: row !important;
}

.text-center {
  text-align: center !important;
}

.text-md-start {
  text-align: left !important;
}
.section-divider {
  border: 0;
  height: 1px;
  background-color: #0056b3; /* Adjust to your desired color */
  margin-top: 0.5rem;
  margin-bottom: 1.5rem;
}



@media (max-width: 767px) {
  .profile-section1 {
    width: 80% !important; 
    margin-left: 30px;
    margin-right: 0;
  }

  .profile-section2 {
    width: 80% !important; 
    margin-left: 30px;
    margin-right: 0;
  }
  .card-body {
    flex-direction: column !important; 
  }
}


.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background-color: white;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.04);
}

.card-header img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

.card-header .info {
  flex-grow: 1;
  padding-left: 20px;
  color: var(--blue11);
}

.info {
  margin: 5px 0;
  color: var(--blue11);
}

.card-header .status {
  background-color: #dff0d8;
  color: #3c763d;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 700;
}
