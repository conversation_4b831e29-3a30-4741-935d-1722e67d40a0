export const roleBasedRedirect = (currentPath: string) => {
    const role = localStorage.getItem('role');
  
  
    if (role === 'supervisor') {
      if (
        currentPath === '/adminDashboard' 
        // currentPath === '/approvals' 
        //  currentPath === '/employeeDashboard'
      ) {
        window.location.replace("./404");  
      }
    }
  

    if (role === 'employee') {
      if (
        currentPath === '/adminDashboard' || 
        currentPath === '/roster' || 
        currentPath === '/approvals'
      ) {
        window.location.href = './404';  
      }
    }
  

    if (role === 'admin') {
      // if (
      //   currentPath === '/employeeDashboard'
      // ) {
      //   window.location.href = './404'; 
      // }
    }
  };
  