import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap-icons/font/bootstrap-icons.css";

import "../app/dashboards/Admin.css";
import "../css/statuscard.css";

import { useEffect } from "react";
import OneSignal from 'react-onesignal';


function MyApp({ Component, pageProps }) {
  useEffect(() => {
    // Ensure this code runs only on the client side
    if (typeof window !== 'undefined') {
      OneSignal.init({
        appId: "************************************",
        safari_web_id: "web.onesignal.auto.************************************",
        // notifyButton: {
        //   enable: true,
        // },
        // Uncomment the below line to run on localhost. See: https://documentation.onesignal.com/docs/local-testing
        allowLocalhostAsSecureOrigin: true
      }).then(()=>{
      })

      OneSignal.Notifications.requestPermission()
      OneSignal.Notifications.addEventListener('click', (event) => {
      });
      OneSignal.Notifications.addEventListener('foregroundWillDisplay', (event) => {
      });
      OneSignal.Notifications.addEventListener('permissionChange', (permission) => {
      });
    
      
    }
  }, []);

 

  return <Component {...pageProps} />;
}

export default MyApp;
