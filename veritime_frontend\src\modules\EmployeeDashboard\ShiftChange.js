import React, { useState, useRef, useEffect } from 'react';
//import 'bootstrap/dist/css/bootstrap.min.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Form, InputGroup } from 'react-bootstrap';
import { BsCalendar } from 'react-icons/bs';
import { getRequest, postRequest, postRequestWithSecurity } from '../../services/apiService';
import { API_URLS } from '../../constants/apiConstants';
import { showSuccessAlert, showSuccessAlert2 } from '../../services/alertService';
import moment from 'moment';

const ShiftChange = ({ closeApplyModal }) => {
  const [scheduleOptions, setscheduleOptions] = useState([]);
  const [recommendationOptions, setrecommendationOptions] = useState([]);

  useEffect(() => {
    getSchedule();
  }, []);

  const getSchedule = async () => {
    try {
      const data = await getRequest(API_URLS.SHIFTCHANGE_TIMESLOT);
      setscheduleOptions(data);
    } catch (error) {
      // setscheduleOptions(data);
    }
  };


  const today = new Date();

  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [schedule, setSchedule] = useState('');
  const [recommendation, setRecommendation] = useState('');
  const [reason, setReason] = useState('');
  const [errors, setErrors] = useState({});
  const StartdateInputRef = useRef(null);
  const EnddateInputRef = useRef(null);

  const handleStartDateIconClick = () => {
    StartdateInputRef.current.setFocus();
  };

  const handleEndDateIconClick = () => {
    EnddateInputRef.current.setFocus();
  };

  const validateForm = () => {
    const newErrors = {};

    if (!startDate) {
      newErrors.startDate = '*Start Date is required';
    }

    if (!endDate) {
      newErrors.endDate = '*End Date is required';
    }

    if (startDate && endDate && startDate > endDate) {
      newErrors.endDate = '*End Date cannot be earlier than Start Date';
    }

    if (!schedule) {
      newErrors.schedule = '*Schedule selection is required';
    }

    // if (!recommendation) {
    //   newErrors.recommendation = 'Recommendation selection is required';
    // }

    if (!reason.trim()) {
      newErrors.reason = '*Reason is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleStartDateChange = (date) => {
    if (date) {
      const formattedDate = moment(date).isValid() ? moment(date).format("YYYY-MM-DD") : null;
      setStartDate(formattedDate);
      if (errors.startDate || (errors.endDate && formattedDate <= endDate)) {
        setErrors((prevErrors) => ({ ...prevErrors, startDate: '' }));
      }
    } else {
      setStartDate(null);
    }
  };
  

  const handleEndDateChange = (date) => {
    if (date) {
      const formattedDate = moment(date).isValid() ? moment(date).format("YYYY-MM-DD") : null;
      setEndDate(formattedDate);
      if (errors.endDate || (errors.startDate && startDate <= formattedDate)) {
        setErrors((prevErrors) => ({ ...prevErrors, endDate: '' }));
      }
    } else {
      setEndDate(null);
    }
  };
  const getRecommendation = async () => {
    try {
      const data = await getRequest(API_URLS.GET_RECOMMENDATION_LIST(schedule,startDate,endDate));

      if (data) setrecommendationOptions(data);
    } catch (error) {
      setrecommendationOptions(data);
    }
  };

  useEffect(()=>{
    if(schedule && startDate && endDate){
      getRecommendation()

    }
  },[schedule,startDate,endDate])
 
  const handleScheduleChange = (e) => {
    setSchedule(e.target.value);
  
    if (errors.schedule) {
      setErrors((prevErrors) => ({ ...prevErrors, schedule: '' }));
    }
  };

  const handleRecommendationChange = (e) => {
    setRecommendation(e.target.value);
    if (errors.recommendation) {
      setErrors((prevErrors) => ({ ...prevErrors, recommendation: '' }));
    }
  };

  const handleReasonChange = (e) => {
    setReason(e.target.value);
    if (errors.reason) {
      setErrors((prevErrors) => ({ ...prevErrors, reason: '' }));
    }
  };

  const handleSubmit = async () => {
    if (validateForm()) {
      const finalRecommendation = recommendation === null || recommendation === '' ? '0' : recommendation;
  
      const formData = {
        startDate: startDate,
        endDate: endDate,
        schedule,
        recommendation: finalRecommendation,  
        reason,
      };
  
      try {
        const data = await postRequestWithSecurity(API_URLS.SHIFT_CHANGE, formData);
        if (data) {
          showSuccessAlert2('Shift Change Applied successfully');
        }
        closeApplyModal();
      } catch (error) {
        showErrorAlert(data.message || 'Shift Change Failed');
      } finally {
        closeApplyModal();
      }
    } else {
    }
  };
  
  
  return (
    <div className="container mt-4">
      <div className="row justify-content-center">
        <div className="col-12 col-md-6 col-lg-4">
          <div className="card shadow-sm border-0" style={{ borderRadius: '8px' }}>
            <div className="card-body p-4 cldr-modal-body" style={{ maxHeight: '500px', overflowY: 'auto' , overflowX: 'hidden', }}>
              <h5 className="card-title text-center fw-bold mb-4" style={{ fontSize: '18px' }}>
                Shift Change
              </h5>
              <label htmlFor="ticketSubject" className="ticket-text-primary">
                <span className="text-danger">The fields with * marks are mandatory</span>
          </label>
              <span onClick={closeApplyModal} style={{ position: 'absolute', top: 0, color: 'black', right: 4, cursor: 'pointer' }}>
                X
              </span>
              <Form.Group className="mb-3" controlId="formStartDate">
                <Form.Label className="fw-bold" style={{ fontSize: '14px' }}> <label htmlFor="ticketSubject" className="ticket-text-primary">
                Start Date<span className="text-danger">*</span>
          </label></Form.Label>
                <InputGroup>
                <DatePicker
                    selected={startDate}
                    onChange={(date) => handleStartDateChange(date)}
                    onChangeRaw={(e) => {
                      const typeDate = e.target.value;
                      const regex = /^\d{0,4}(-\d{0,2})?(-\d{0,2})?$/;
                      if (regex.test(typeDate)) {
                        handleStartDateChange(new Date(typeDate));
                      } else {
                        e.preventDefault();
                      }
                    }}
                    className={`form-control ${
                      errors.startDate ? "is-invalid" : ""
                    }`}
                    dateFormat="dd-MM-yyyy"
                    minDate={today} // Restrict start date to today or later
                    ref={StartdateInputRef}
                    showPopperArrow
                  />
                  <InputGroup.Text onClick={handleStartDateIconClick} style={{ cursor: 'pointer' }}>
                    <BsCalendar />
                  </InputGroup.Text>
                  {errors.startDate && <div className="invalid-feedback d-block">{errors.startDate}</div>}
                </InputGroup>
              </Form.Group>

              <Form.Group className="mb-3" controlId="formEndDate">
                <Form.Label className="fw-bold" style={{ fontSize: '14px' }}> <label htmlFor="ticketSubject" className="ticket-text-primary">
                End Date <span className="text-danger">*</span>
          </label></Form.Label>
                <InputGroup>
                <DatePicker
                    selected={endDate}
                    onChange={(date) => handleEndDateChange(date)}
                    onChangeRaw={(e) => {
                      const typeDate = e.target.value;
                      const regex = /^\d{0,4}(-\d{0,2})?(-\d{0,2})?$/;
                      if (regex.test(typeDate)) {
                        handleEndDateChange(new Date(typeDate));
                      } else {
                        e.preventDefault();
                      }
                    }}
                    className={`form-control ${
                      errors.endDate ? "is-invalid" : ""
                    }`}
                    dateFormat="dd-MM-yyyy"
                    minDate={startDate || today} // Restrict end date to be after or equal to the start date
                    ref={EnddateInputRef}
                    showPopperArrow
                  />
                  <InputGroup.Text onClick={handleEndDateIconClick} style={{ cursor: 'pointer' }}>
                    <BsCalendar />
                  </InputGroup.Text>
                  {errors.endDate && <div className="invalid-feedback d-block">{errors.endDate}</div>}
                </InputGroup>
              </Form.Group>

              <Form.Group className="mb-3" controlId="formSchedule">
                <Form.Label className="form-label fw-bold" style={{ fontSize: '14px' }}><label htmlFor="ticketSubject" className="ticket-text-primary">
                Select Schedule <span className="text-danger">*</span>
          </label></Form.Label>
                <select
                  className={`form-select ${errors.schedule ? 'is-invalid' : ''}`}
                  value={schedule}
                  onChange={handleScheduleChange}
                  style={{ fontSize: '16px', backgroundColor: '#E6ECF7', borderColor: '#E6ECF7', color: '#000000', width: '100%' }}
                >
                  <option value="">Select Schedule</option>
                  {scheduleOptions?.time_slots?.map((option, index) => (
                    <option key={index} value={option?.time_slot_id}>
                      {option.timeRange}
                    </option>
                  ))}
                </select>
                {errors.schedule && <div className="invalid-feedback d-block">{errors.schedule}</div>}
              </Form.Group>

              <Form.Group className="mb-3" controlId="formRecommendation">
                <Form.Label className="form-label fw-bold" style={{ fontSize: '14px' }}><label htmlFor="ticketSubject" className="ticket-text-primary">
                Recommendations 
          </label></Form.Label>
                <select
                  className={`form-select ${errors.recommendation ? 'is-invalid' : ''}`}
                  value={recommendation}
                  onChange={handleRecommendationChange}
                  style={{ fontSize: '16px', backgroundColor: '#95A5A6', borderColor: '#95A5A6', color: '#FFFFFF', width: '100%' }}
                >
                  <option value="">Select Recommendation</option>
                  {recommendationOptions?.employees?.map((option, index) => (
                    <option key={index} value={option.employee_id}>
                      {option.employee_name}
                    </option>
                  ))}
                </select>
                {errors.recommendation && <div className="invalid-feedback d-block">{errors.recommendation}</div>}
              </Form.Group>

              <Form.Group className="mb-4" controlId="formReason">
                <Form.Label className="form-label fw-bold" style={{ fontSize: '14px' }}><label htmlFor="ticketSubject" className="ticket-text-primary">
                Reason <span className="text-danger">*</span>
          </label></Form.Label>
                <textarea
                  className={`form-control ${errors.reason ? 'is-invalid' : ''}`}
                  placeholder="Reason"
                  rows="3"
                  value={reason}
                  onChange={handleReasonChange}
                  style={{ fontSize: '16px', width: '100%' }}
                ></textarea>
                {errors.reason && <div className="invalid-feedback d-block">{errors.reason}</div>}
              </Form.Group>

              <button onClick={handleSubmit} className="btn btn-primary w-100" style={{ backgroundColor: '#2B2E4A', fontSize: '16px', padding: '10px' }}>
                Submit
              </button>
              <button onClick={closeApplyModal} className="btn btn-primary w-100" style={{ marginTop: 5, backgroundColor: 'white', color: '#2B2E4A', fontSize: '16px', padding: '10px' }}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShiftChange;