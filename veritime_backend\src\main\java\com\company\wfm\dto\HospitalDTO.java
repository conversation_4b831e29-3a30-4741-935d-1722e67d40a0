package com.company.wfm.dto;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HospitalDTO {

    private Long id;
    private String name;
    private String address;
    private String type;
    private String facility;
    private Long provinceId;
    private String province;
    private Long districtId;
    private String district;
    private Long subDistrictId;
    private String subDistrict;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

}