package com.company.wfm.controller;
import java.util.List;

import com.company.wfm.dto.DesignationResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.DesignationDTO;
import com.company.wfm.dto.DesignationIdNameDTO;
import com.company.wfm.entity.Designation;
import com.company.wfm.service.impl.DesignationService;

import jakarta.persistence.EntityNotFoundException;

@RestController
@RequestMapping("/api/v1/designation")
@CrossOrigin(origins = "*")
public class DesignationController {

    @Autowired
    private DesignationService designationService;

    // Endpoint to create a new Designation
    @PostMapping("/create")
    public ResponseEntity<?> createDesignation(@RequestBody DesignationDTO designationDTO) {
        try {
            Designation createdDesignation = designationService.createDesignation(designationDTO);
            return new ResponseEntity<>(createdDesignation, HttpStatus.CREATED);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>("An error occurred while creating the designation", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/list")
    public ResponseEntity<List<DesignationDTO>> getAllDesignations() {

        List<DesignationDTO> designations = designationService.getAllDesignations();
        return ResponseEntity.ok(designations);
    }

    @GetMapping("/getDesignationByDepartment")
    public ResponseEntity<List<DesignationDTO>> getAllDesignationsByDepartmentId(@RequestParam(value = "departmentId", required = true) Long departmentId) {
        List<DesignationDTO> designations = designationService.getAllDesignationsByDepartmentId(departmentId);
        return ResponseEntity.ok(designations);
    }

    @GetMapping("/designation/level")
    public ResponseEntity<List<String>> getAllDesignationLevel() {
        List<String> designationNames = designationService.getAllDesignationLevel();
        return ResponseEntity.ok(designationNames);
    }

    @GetMapping("/lookupDesgination")
    public List<DesignationIdNameDTO> getDesignationIdsAndNamesByDepartmentId() {
        return designationService.getDesignationIdsAndNamesByDepartmentId();
    }

    @GetMapping("/lookupDesginationByDepartmentId")
    public List<DesignationIdNameDTO> getDesignationByDepartmentId(@RequestParam(value = "departmentId", required = true) Long departmentId) {
        return designationService.getDesignationsKeyPairDepartmentId(departmentId);
    }


    // update and delete feature degination

    // Update Designation
    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateDesignation(@PathVariable Long id, @RequestBody DesignationDTO designationDTO) {
        try {
            DesignationResponseDTO updatedDesignation = designationService.updateDesignation(id, designationDTO);
            return ResponseEntity.ok(updatedDesignation);
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error updating designation: " + e.getMessage());
        }
    }

    // Soft Delete Designation
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> deleteDesignation(@PathVariable Long id) {
        try {
            designationService.softDeleteDesignation(id);
            return ResponseEntity.ok("Designation soft deleted successfully.");
        } catch (EntityNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Designation not found: " + e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error deleting designation: " + e.getMessage());
        }
    }

}
