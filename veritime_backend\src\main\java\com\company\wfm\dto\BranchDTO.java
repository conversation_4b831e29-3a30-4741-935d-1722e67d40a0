package com.company.wfm.dto;

import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchDTO {

    private Long id;
    private String branchName;
    private String branchCode;
    private String branchHeadName;
    private Integer companyId;
    private long hospitalId;
    private String hospitalName;
    private List<DepartmentDTO> departments;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Integer createdBy;
    private Integer updatedBy;

}
