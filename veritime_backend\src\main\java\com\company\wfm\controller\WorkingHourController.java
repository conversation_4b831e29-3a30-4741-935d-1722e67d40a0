package com.company.wfm.controller;


import java.time.LocalDate;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.AttendanceRequest;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.service.WorkingHourService;

@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = "*")
public class WorkingHourController {

    @Autowired
    private WorkingHourService workingHourService;

    @Autowired
    private UserTokenService userTokenService;

    @PostMapping("/working-hours")
    public ResponseEntity<Map<String, Object>> getWorkingHours(
            @RequestBody Map<String, String> request) {

        // Extract dates from the request body
        LocalDate startDate = LocalDate.parse(request.get("startDate"));
        LocalDate endDate = LocalDate.parse(request.get("endDate"));

        Long empId = userTokenService.getEmployeeIdFromToken();

        System.out.println("========="+empId);

        // Call the service to calculate working hours
        Map<String, Object> response = workingHourService.getTotalAndExpectedWorkingHours(empId, startDate, endDate);
        return ResponseEntity.ok(response);
    }

    //day week month
    @PostMapping("/attendance/activity")
    public ResponseEntity<?> getAttendanceActivity(@RequestBody AttendanceRequest attendanceRequest) {
        System.out.println("========"+attendanceRequest.getPeriod());

        String period = attendanceRequest.getPeriod();
        // Get empId from the authenticated user's token
        Long empId = userTokenService.getEmployeeIdFromToken();
        System.out.println("========="+empId);
        // Call the service method with the extracted empId and period
        return ResponseEntity.ok(workingHourService.getAttendanceActivity(empId, period));
    }

}
