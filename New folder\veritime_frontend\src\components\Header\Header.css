.header {
    background-color: var(--blue10);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 25px;
    position: fixed;
    top: 0;
    left: 100px;
    right: 0;
    z-index: 1001;
    /* transition: left 0.3s; */
    width: auto;

}

.menu-expanded .header {
    left: 250px;
}

.search-bar-container {
    flex-grow: 1;
    position: relative;
}

.form-control {
    width: 100%;
}

.search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
}

.notification,
.profile {
    display: flex;
    align-items: center;
}

.username {
    font-size: 16px;
    color: var(--grey1);
    margin-right: 10px;
}

.profile-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
}

.profile-pic-notification {
    width: 25px;
    height: 25px;
    border-radius: 30%;
    cursor: pointer;
}

.notification-badge {
    position: relative;
    top: -5px;
    right: 13px;
    background-color: #2196f3;
    color: white;
    border-radius: 50%;
    width: 22px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
}

.menu-toggle {
    display: none;
    font-size: 24px;
    background: none;
    border: none;
    color: black;
    cursor: pointer;
    margin-right: 10px;
}

.MuiTablePagination-selectLabel,
.MuiTablePagination-displayedRows {
    margin-bottom: 4px !important;
}

.approvedTxt {
    color: green
}

.rejectedTxt {
    color: red
}

.MuiTabs-root.tab-pannel-test.css-1ujnqem-MuiTabs-root {
    margin-top: 23px;
    border-radius: 10px
}

@media (max-width: 768px) {
    .username {
        display: none;
    }

    .menu-toggle {
        display: block;
    }

    .header {
        left: 0;
    }

    .menu-expanded .header {
        left: 0;
    }
}

* {
    font-family: 'Barlow', sans-serif !important;
}



@media (min-width: 992px) {
    .col-lg-85 {
        flex: 0 0 auto !important;
        width: 72.6% !important;
    }
}