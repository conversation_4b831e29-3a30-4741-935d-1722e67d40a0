.carousel {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 300px;
  }
  .carousel .content{
    margin-left: 50px;
    margin-right: 50px;
    text-align: center;
  }
.carousel .text-primary{
color: #0d6efd;
text-align: center;
  }
  
  .carousel-image {
    width: 100%;
    border-radius: 10px;
  }
  
  .carousel-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
  }
  
  .carousel-button:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .carousel-button:nth-of-type(1) {
    left: 10px;
  }
  
  .carousel-button:nth-of-type(2) {
    right: 10px;
  }
  
  .carousel-dots {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
  
  .carousel-dot {
    width: 10px;
    height: 10px;
    margin: 0 5px;
    background-color: #ccc;
    border-radius: 50%;
    cursor: pointer;
  }
  
  .carousel-dot.active {
    background-color: #333;
  }
  