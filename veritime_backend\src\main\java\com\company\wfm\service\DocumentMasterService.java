package com.company.wfm.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.company.wfm.dto.DocumentMasterDTO;
import com.company.wfm.entity.DocumentMaster;

public interface DocumentMasterService {
    public DocumentMaster saveDocument(DocumentMasterDTO documentMasterDTO);
    public Page<DocumentMaster> getDocuments(Pageable pageable);

    public DocumentMaster updateDocument(Long documentId, DocumentMasterDTO updateDTO);


}
