package com.company.wfm.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.company.wfm.dto.NotificationDTO;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CommonNotificationService {

    @Autowired
    private FirebaseNotificationService firebaseNotificationService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void sendNotificationToEmployee(Long empId, String title, String messageBody, String type, String sentById) {
        try {
            List<String> tokens = jdbcTemplate.query(
                    "SELECT fb_token FROM t_log_history WHERE is_active=1 AND empId = ?",
                    new Object[]{empId},
                    (rs, rowNum) -> rs.getString("fb_token")
            );

            if (tokens != null && !tokens.isEmpty()) {
                firebaseNotificationService.sendNotification(title, messageBody, tokens);

                if (type == null || type.isEmpty()) {
                    type = "normal";
                }
            } else {
                System.out.println("Firebase tokens are empty");
            }

            String insertQuery = "INSERT INTO t_notifications (emp_id, title, body, type, create_time, sent_by_id) " +
                    "VALUES (?, ?, ?, ?, GETDATE(), ?)";
            jdbcTemplate.update(insertQuery, empId, title, messageBody, type, sentById);
        } catch (Exception e) {
        	log.error("Exception occured while sendNotificationToEmployee", e);
        }
    }

    public int getNotificationCountByEmployeeId(Long empId) {
        String countQuery = "SELECT COUNT(*) FROM t_notifications WHERE emp_id = ?";
        return jdbcTemplate.queryForObject(countQuery, new Object[]{empId}, Integer.class);
    }
    public int getNotificationReadCountByEmployeeId(Long empId) {
        String countQuery = "SELECT COUNT(*) FROM t_notifications WHERE read_status=0 AND emp_id = ?";
        return jdbcTemplate.queryForObject(countQuery, new Object[]{empId}, Integer.class);
    }

    public List<NotificationDTO> getNotificationsByEmployeeId(Long empId, int limit, int offset) {
        // Calculate the starting row for SQL OFFSET
        int startRow = offset * limit;

        String query = "SELECT * FROM t_notifications WHERE emp_id = ? ORDER BY create_time DESC OFFSET ? ROWS FETCH NEXT ? ROWS ONLY";

        return jdbcTemplate.query(query, new Object[]{empId, startRow, limit}, (rs, rowNum) ->
                new NotificationDTO(
                        rs.getString("title"),
                        rs.getString("body"),
                        rs.getString("type"),
                        rs.getString("sent_by_id"),
                        rs.getTimestamp("create_time").toInstant(),
                        rs.getInt("read_status"),
                        rs.getInt("id")
                )
        );
    }

    public boolean markNotificationAsRead(Long notificationId) {
        String query = "UPDATE t_notifications SET read_status = 1 WHERE id = ?";
        int updatedRows = jdbcTemplate.update(query, notificationId);
        return updatedRows > 0;
    }


}
