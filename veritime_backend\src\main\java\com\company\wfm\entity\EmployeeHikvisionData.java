package com.company.wfm.entity;


import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Data;

@Data
@Entity
@Table(name = "t_employee_hikvision_data", uniqueConstraints = {
    @UniqueConstraint(columnNames = "person_id")
})
public class EmployeeHikvisionData implements Serializable {

	private static final long serialVersionUID = -7289654561091012870L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "person_id", nullable = false)
    private String personId;

    @Column(name = "emp_code")
    private String empCode;

    @Column(name = "branch_id")
    private String branchId;

    @Column(name = "image_mapped")
    private int imageMapped;


}

