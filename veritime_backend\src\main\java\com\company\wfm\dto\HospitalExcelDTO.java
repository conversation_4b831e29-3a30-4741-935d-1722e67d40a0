package com.company.wfm.dto;


import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

@Data
public class HospitalExcelDTO {

    /*private String hospitalName;
    private String shortCode;
    *//*private Long provinceId;
    private Long districtId;*//*
    private String provinceName; // Add this field
    private String districtName; // Add this field
    private String subDistrict;
    private String addressLine1;
    private String addressLine2;
    private Double lat; // Optional
    private Double lng; // Optional
    private LocalDateTime validity;
    private Long createdBy;  // Store user ID as Long
    private Long updatedBy;  // Store user ID as Long
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String hospitalType;
    private Boolean isActive;
    private String clusterName;
    private String departmentName;*/

    private String hospitalName;
    private String shortCode;
    private String facilityName;
    private String provinceName;
    private String districtName;
    private String subDistrictName;
    private String addressLine1;
    private String addressLine2;
    private Double lat;
    private Double lng;
    private LocalDateTime validity;
    private Long createdBy;
    private Long updatedBy;
    private String hospitalType;
    private String clusterName;
    private List<Long> departmentIds;
    // private Long branchHeadId;
    private List<String> departmentNames;
    private List<String> branchNames;
    private List<BranchSaveDto> branches;

    private String facilityHeadName;
   /* private String facilityHeadEmail;
    private long facilityHeadId;
    private String password;*/




}
