package com.company.wfm.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_attendance")
public class AttendanceEntity implements Serializable {

  /**
	 *
	 */
	private static final long serialVersionUID = 1L;

@Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ATTENDANCE_ID")
  private Long id;

  @ManyToOne
  @JoinColumn(name = "EMP_ID")
  private Employee empId;

  @Column(name = "CHECK_IN_TIME")
  private Time checkInTime;

  @Column(name = "CHECK_OUT_TIME")
  private Time checkOutTime;

  @Column(name = "CREATED_BY")
  private Long createdBy;

  @Column(name = "CREATED_TIME")
  private LocalDateTime createdTime;

  @Column(name = "UPDATED_BY")
  private Long updatedBy;

  @Column(name = "UPDATED_TIME")
  private LocalDateTime updatedTime;

  @Column(name = "MODE_TYPE")
  private String modeType;

  @Column(name = "DATE")
  private LocalDate date;

  @Column(name = "MULTI_DATE") // Add multi_date column mapping
  private String multiDate;
  
  @Column(name = "CHECK_OUT_DATE")
  private LocalDate checkOutDate;
  
  @Column(name = "OVERTIME")
  private BigDecimal overtime;

  @Column(name = "actual_shift_start_time")
  private Time  actualShiftStartTime;

  @Column(name = "actual_shift_end_time")
  private Time actualShiftEndTime;


}
