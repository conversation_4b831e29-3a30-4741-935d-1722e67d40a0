package com.company.wfm.entity;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_schedule_job")
public class ScheduleJobTracker implements Serializable{
	private static final long serialVersionUID = 1L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Long id;

	@Column(name = "run_date")
	private LocalDate runDate;

	@Column(name = "job_name")
	private String name;

	@Column(name = "execution_start_time")
	private LocalDateTime executionStartTime;

	@Column(name = "execution_end_time")
	private LocalDateTime executionEndTime;

	@Column(name = "status")
	private String status;

	@Column(name = "faulure_reason")
	private String reason;
}
