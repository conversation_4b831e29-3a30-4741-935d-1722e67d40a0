import "./login.css";
import { useEffect, useState } from "react";
import { API_URLS } from "@/constants/apiConstants";
import {
  getRequest,
  getRequestWithSecurity,
  postRequest,
  postRequestNoToken,
  postRequestNoTokenWithSecurity,
} from "../../services/apiService";
import { encryptData, decryptData } from "@/utils/encryption.js";
import Link from "next/link";
import OneSignal from "react-onesignal";

const Login = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState({ username: "", password: "" });

  // useEffect(() => {
  //   if (window.location.href.includes("sessiondExpired")) {
  //     alert("Session expired! Please login again.");
  //   }
  // }, []);
  useEffect(() => {
    if (window.location.href.includes("sessiondExpired")) {
      alert("Session expired! Please login again.");
    }
    (async () => {
      const data =
        "UHFXytkoxqhEwJl7VLzvvbIand7J1flbSU/BUpNT18c4wgUeYoyEWIIaJ75UuwMWf74KYZuwavalyEUA1lAnCm7bdesVYpVqLb4sfI8Jrhkao/3fJk6nI4FK";
      const decryptedData = await decryptData(data);

      //console.log("Decrypted:", decryptedData);
    })();
  }, []);

  const validateForm = () => {
    let formIsValid = true;
    let errors = {};

    if (!username.trim()) {
      formIsValid = false;
      errors["username"] = "Please enter your username or email.";
    }

    if (!password.trim()) {
      formIsValid = false;
      errors["password"] = "Please enter your password.";
    }

    setErrors(errors);
    return formIsValid;
  };

  const handleLogin = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      const userDetail = {
        username,
        password,
      };
      try {
        let response = await postRequestNoTokenWithSecurity(
          API_URLS.LOGIN,
          userDetail,
          true
        );
        //console.log(response, "profiledetail2");

        //console.log("Employee ID:", response.data?.empId);

        if (response && response.accessToken) {
          localStorage.setItem("accessToken", response.accessToken);

          //console.log("Employee ID:", response.empId);

          const additionalResponse = await getRequestWithSecurity(
            API_URLS.PROFILE_DETAILS
          );
          //console.log("Employee ID:", response?.empId);

          //console.log(additionalResponse, "profiledetail");

          // Check if profile image exists
          if (additionalResponse?.imgUre) {
            //console.log("Profile Image exists:", additionalResponse.imgUre);
          } else {
            //console.log("No Profile Image found.");
          }
          if (additionalResponse && additionalResponse.companyId) {
            localStorage.setItem("companyId", additionalResponse.companyId);
            localStorage.setItem(
              "userData",
              await encryptData(additionalResponse)
            );
            localStorage.setItem("username", response.employeeName);
            //console.log("Employee ID:", additionalResponse?.empId);
            localStorage.setItem("empId", additionalResponse?.empId);
            localStorage.setItem("role", response.role);
            localStorage.setItem("logHistoryId", response?.logHistoryId);
            localStorage.setItem("profileImg", additionalResponse?.imgUre);
            const firebaseToken = new Date().getTime()?.toString();
            localStorage.setItem("fb", firebaseToken);
            await OneSignal.login(firebaseToken);

            const profileImg = localStorage.getItem("profileImg");

            if (profileImg) {
              //console.log("Profile image exists:", profileImg);
            }
            if (firebaseToken) {
              const pushToken = await postRequest(
                API_URLS.NOTIFICATION_UPDATE,
                {
                  firebaseToken: firebaseToken,
                  logHistoryId: response?.logHistoryId,
                }
              );
            }

            const currentPath = localStorage.getItem("currentPath");
            if (currentPath) {
              localStorage.removeItem("currentPath");
              window.location.href = currentPath;
            } else {
              window.location.href = "/dashboards";
            }
          } else {
            setErrors({ username: "", password: "Something went wrong." });
          }
        } else {
          setErrors({ username: "Invalid username or password", password: "" });
        }
      } catch (error) {
        const defaultErrorMessage =
          "An unexpected error occurred. Please try again later.";

        if (error.response) {
          if (error.response.status === 403) {
            setErrors({
              username: "",
              password: "You are inactive, you do not have access to login.",
            });
          } else {
            const errorMessage =
              error.response.data?.error ||
              error.response.data ||
              defaultErrorMessage;
            setErrors({
              username: "",
              password: errorMessage,
            });
          }
        } else if (error.request) {
          // console.error("No response received:", error.request);
          setErrors({
            username: "",
            password:
              "No response from the server. Please check your connection.",
          });
        } else {
          // console.error("Error during request setup:", error.message);
          setErrors({
            username: "",
            password: error.message || defaultErrorMessage,
          });
        }
      }
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleLogin(e);
    }
  };
  return (
    <div className="login-container">
      <div className="left-pannel">
        <img className="logoimg" src={"/image/logo_full.png"} />
        <img className="loginImg" src={"/image/loginimg.png"} />
      </div>
      <div
        className="p-4"
        style={{ width: "320px", marginTop: "90px", marginLeft: "90px" }}
      >
        <div className="text-center mb-4">
          <button
            className="btn text-black fw-bold"
            style={{
              fontSize: "24px",
              padding: "10px 40px",
              borderRadius: "8px",
              // fontFamily: "Arial, sans-serif",
            }}
          >
            Login
          </button>
        </div>
        <div className="form-group mb-3">
          <label
            htmlFor="username"
            className="fw-bold"
            style={{
              fontSize: "14px",
              // fontFamily: "Arial, sans-serif",
              color: "#4A4A4A",
            }}
          >
            Username/E-Mail
          </label>
          <input
            type="text"
            className="form-control"
            id="username"
            placeholder=""
            style={{
              borderRadius: "10px",
              // fontFamily: "Arial, sans-serif",
              fontSize: "14px",
              color: "#4A4A4A",
              border: "1px solid #CCCCCC",
              padding: "10px",
              backgroundColor: "#F0F0F0",
            }}
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            onKeyDown={handleKeyDown}
          />
          {errors.username && (
            <div className="text-danger mt-1" style={{ fontSize: "12px" }}>
              {errors.username}
            </div>
          )}
        </div>
        <div className="form-group mb-3">
          <label
            htmlFor="password"
            className="fw-bold"
            style={{
              fontSize: "14px",
              // fontFamily: "Arial, sans-serif",
              color: "#4A4A4A",
            }}
          >
            Password
          </label>
          <input
            type="password"
            className="form-control"
            id="password"
            placeholder=""
            style={{
              borderRadius: "10px",
              // fontFamily: "Arial, sans-serif",
              fontSize: "14px",
              color: "#4A4A4A",
              border: "1px solid #CCCCCC",
              padding: "10px",
              backgroundColor: "#F0F0F0",
            }}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            onKeyDown={handleKeyDown}
          />
          {errors.password && (
            <div className="text-danger mt-1" style={{ fontSize: "12px" }}>
              {errors.password}
            </div>
          )}
        </div>
        <div className="text-end mb-4">
          <Link
            href="/login/forgetpassword"
            className="text-muted"
            style={{ fontSize: "13px", color: "#7D7D7D", fontWeight: 700 }}
          >
            Forgot your password?
          </Link>
        </div>
        <div className="d-flex flex-column gap-2">
          <button
            className="btn btn-primary"
            onClick={handleLogin}
            style={{
              backgroundColor: "#0056b3",
              borderColor: "#0056b3",
              borderRadius: "8px",
              fontSize: "14px",
              // fontFamily: "Arial, sans-serif",
              padding: "10px 0",
            }}
          >
            Sign In
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;
