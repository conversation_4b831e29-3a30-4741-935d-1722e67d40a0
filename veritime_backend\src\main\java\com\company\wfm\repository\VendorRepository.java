package com.company.wfm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.Vendor;

@Repository
public interface VendorRepository extends JpaRepository<Vendor, Long> {
    @Override
	Page<Vendor> findAll(Pageable pageable);

    @Query("SELECT v.primaryEmail FROM Vendor v WHERE v.id = :vendorId")
    String findPrimaryEmailById(@Param("vendorId") Long vendorId);
}
