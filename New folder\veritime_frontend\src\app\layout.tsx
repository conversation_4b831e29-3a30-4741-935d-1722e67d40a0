"use client";
import { Inter } from "next/font/google";
import "../globals.css";
import useLocalStorage from "../services/localstorage";
import { useEffect, useState } from "react";
import { appConstants } from "../constants/appConstants";


const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [isLoading, setLoading] = useState(true);
  const [username, setusername] = useLocalStorage(appConstants?.username, "");

  return (
    <html lang="en" style={{overflowY:"hidden"}}>
      <body className={inter.className}>{children}</body>
    </html>
  );
}
