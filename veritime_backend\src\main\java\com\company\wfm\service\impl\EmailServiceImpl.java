package com.company.wfm.service.impl;

import java.io.File;
import java.time.LocalDateTime;
import java.util.Properties;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.mail.javamail.MimeMessagePreparator;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import com.company.wfm.dto.EmailData;
import com.company.wfm.entity.EmailLog;
import com.company.wfm.repository.EmailLogRepository;
import com.company.wfm.service.EmailService;

import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@DependsOn("appConfigurations")
public class EmailServiceImpl implements EmailService  {

	@Autowired
	private EmailLogRepository emailLogRepository;

    @Value("${mail.host}")
    private String host;

    @Value("${mail.port}")
    private String port;

    @Value("${mail.username}")
    private String username;

    @Value("${mail.password}")
    private String password;

	private JavaMailSenderImpl mailSender;

	@PostConstruct
	public void init(){

	    mailSender = new JavaMailSenderImpl();
	    mailSender.setHost(host);
	    mailSender.setPort(Integer.parseInt(port));

	    mailSender.setUsername(username);
	    mailSender.setPassword(password);

	    Properties props = mailSender.getJavaMailProperties();
	    props.put("mail.transport.protocol", "smtp");
	    props.put("mail.smtp.auth", "true");
	    props.put("mail.smtp.starttls.enable", "true");
	    props.put("mail.smtp.ssl.enable", "true");

	}

    /*@Override
    public void sendEmail(EmailData data) {

		new Thread(() -> {
	     	MimeMessagePreparator messagePreparator = new MimeMessagePreparator() {
	    		public void prepare(MimeMessage mimeMessage) throws Exception {

	    	        MimeMessageHelper mimeMessageSender = new MimeMessageHelper(mimeMessage, true, "UTF-8");
	    			mimeMessageSender.setFrom("<EMAIL>");

					if(log.isDebugEnabled()) {
						mimeMessageSender.setTo("<EMAIL>");
					} else {
						mimeMessageSender.setTo(data.getEmailId());
					}

	    			if(!Collections.isEmpty(data.getCcs()))
	    			mimeMessageSender.setCc(data.getCcs().stream().toArray(String[] ::new));;

	    			if(!Collections.isEmpty(data.getBccs()))
	    			mimeMessageSender.setBcc(data.getBccs().stream().toArray(String[] ::new));

					if (!StringUtils.isBlank(data.getFileToAttach())) {
						File file = ResourceUtils.getFile(data.getFileToAttach());
						mimeMessageSender.addAttachment(file.getName(),
								new FileSystemResource(file));
					}

	    			mimeMessageSender.setSubject(data.getSubject());
	    			mimeMessageSender.setText(data.getMessage(), data.isHtml());

	    		}
	    	};
	    	mailSender.send(messagePreparator);
	    	log.info("Mail sent successfully to {}",data.getEmailId());
		}).start();;
     }*/


	@Override
	public void sendEmail(EmailData data) {

		new Thread(() -> {
			EmailLog emailLog = new EmailLog();
			emailLog.setFromEmail("<EMAIL>");
			emailLog.setToEmail(log.isDebugEnabled() ? "<EMAIL>" : data.getEmailId());
			emailLog.setSubject(data.getSubject());
			emailLog.setMessage(data.getMessage());
			emailLog.setCc(data.getCcs() != null ? String.join(", ", data.getCcs()) : "");  // Default to empty string if null
			emailLog.setBcc(data.getBccs() != null ? String.join(", ", data.getBccs()) : "");  // Default to empty string if null
			// Null check for Attachment
			emailLog.setAttachment(data.getFileToAttach() != null ? data.getFileToAttach() : "");
			emailLog.setTimestamp(LocalDateTime.now());

			try {
				MimeMessagePreparator messagePreparator = mimeMessage -> {
					MimeMessageHelper mimeMessageSender = new MimeMessageHelper(mimeMessage, true, "UTF-8");
					mimeMessageSender.setFrom("<EMAIL>");
					mimeMessageSender.setTo(emailLog.getToEmail());

					if (!Collections.isEmpty(data.getCcs())) {
						mimeMessageSender.setCc(data.getCcs().toArray(new String[0]));
					}
					if (!Collections.isEmpty(data.getBccs())) {
						mimeMessageSender.setBcc(data.getBccs().toArray(new String[0]));
					}
					if (!StringUtils.isBlank(data.getFileToAttach())) {
						File file = ResourceUtils.getFile(data.getFileToAttach());
						mimeMessageSender.addAttachment(file.getName(), new FileSystemResource(file));
					}

					mimeMessageSender.setSubject(data.getSubject());
					mimeMessageSender.setText(data.getMessage(), data.isHtml());
				};

				mailSender.send(messagePreparator);
				emailLog.setStatus("SENT");
				emailLog.setErrorMessage(null);
				log.info("Mail sent successfully to {}", data.getEmailId());
			} catch (Exception e) {
				emailLog.setStatus("FAILED");
				emailLog.setErrorMessage(e.getMessage());
				log.error("Failed to send mail to {}: {}", data.getEmailId(), e.getMessage());
			}

			emailLogRepository.save(emailLog);
		}).start();
	}

}
