package com.company.wfm.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.Hospital;

@Repository
public interface HospitalRepository extends JpaRepository<Hospital, Long> {


    @Query("SELECT h FROM Hospital h " +
            "WHERE (:provinceIds IS NULL OR h.province.id IN :provinceIds) " +
            "AND (:districtIds IS NULL OR h.district.id IN :districtIds) " +
            "AND (:subDistrictIds IS NULL OR h.subDistrict.id IN :subDistrictIds) " +
            "AND (:query IS NULL OR :query = '' OR h.hospitalName LIKE %:query%) " +
            "ORDER BY h.id DESC")
    Page<Hospital> findHospitalByFilters(
            @Param("provinceIds") List<Long> provinceIds,
            @Param("districtIds") List<Long> districtIds,
            @Param("subDistrictIds") List<Long> subDistrictIds,
            @Param("query") String query,
            Pageable pageable);


    @Query("SELECT h FROM Hospital h WHERE h.hospitalName = :hospitalName")
    Optional<Hospital> findByHospitalName(@Param("hospitalName")String hospitalName);

   /* @Query("SELECT h FROM Hospital h WHERE h.hospitalName = :hospitalName AND h.isActive = 1")
    Optional<Hospital> findFirstHospitalName(@Param("hospitalName") String hospitalName);*/


    @Query("SELECT h FROM Hospital h WHERE h.hospitalName = :hospitalName")
    List<Hospital> findByHospitalName1(@Param("hospitalName") String hospitalName);






}
