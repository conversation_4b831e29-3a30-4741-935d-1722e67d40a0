package com.company.wfm.service;

import java.io.IOException;
import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.HospitalExcelDTO;

public interface HospitalService {

    void processExcelFile(MultipartFile file) throws IOException;

    /**
     * Saves a list of hospitals to the database.
     *
     * @param hospitalDTOs The list of HospitalDTOs to save.
     */
    void saveHospitals(List<HospitalExcelDTO> hospitalDTOs);

    public void uploadAndSaveHospitals(MultipartFile file);
}
