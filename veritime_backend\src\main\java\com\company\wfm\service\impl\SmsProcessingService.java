package com.company.wfm.service.impl;

import com.company.wfm.dto.SmsDataRequest;
import com.company.wfm.service.SmsService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SmsProcessingService {
    private final SmsService smsService;

    public SmsProcessingService(SmsService smsService) {
        this.smsService = smsService;
    }

    public ResponseEntity<String> processSmsRequest(SmsDataRequest request) {
        log.info("Received SMS request: {}", request);

        try {
            smsService.sendMessage(request);
            return ResponseEntity.ok("SMS request has been submitted successfully.");
        } catch (JsonProcessingException e) {
            log.error("Error while processing SMS request: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Failed to process SMS request.");
        } catch (Exception e) {
            log.error("Unexpected error occurred while sending SMS: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("An unexpected error occurred while sending the SMS.");
        }
    }
}
