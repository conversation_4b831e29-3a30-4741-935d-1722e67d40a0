package com.company.wfm.entity;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "t_branch_room_details")
public class BranchRoomDetails implements Serializable {

	private static final long serialVersionUID = 3371686748373339095L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "room_Id")
    private String roomId;

    @Column(name = "room_num")
    private String roomNum;

    @Column(name = "build_id")
    private String buildId;

    @Column(name = "build_name")
    private String buildName;

    @Column(name = "area_id")
    private String areaId;

    @Column(name = "area_name")
    private String areaName;

    @Column(name = "account_type")
    private String accountType;

}
