import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Container, <PERSON>, Col } from "react-bootstrap";
import axios from "axios";
import "../EmployeeDashboard/EmployeeCalender.css";
import { postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import AnimatedModal from "@components/AnimatedModal";
import ApplyLeave from "./ApplyLeave";
import AttendanceRegularisation from "./AttendanceREgularisation";
import ShiftChange from "./ShiftChange";
import { colors } from "@constants/colors";
import moment from "moment";
import Link from "next/link";

const EmployeeCalender = () => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [calendarData, setCalendarData] = useState([]);
  const [viewMode, setViewMode] = useState("shift");
  const [isMDScreen, setMDScreen] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    const fetchCalendarData = async () => {
      try {
        const response = await postRequest(API_URLS.EMPLOYEE_CALENDER, {
          year: currentMonth.getFullYear(),
          month: currentMonth.getMonth() + 1,
        });
        setCalendarData(response);
        setErrorMessage("");
      } catch (error) {
        setErrorMessage("Failed to fetch calendar data. Please try again later.");
      }
    };
    fetchCalendarData();
  }, [currentMonth]);

  useEffect(() => {
    const handleResize = () => {
      setMDScreen(window.innerWidth >= 750 && window.innerWidth <= 1100);
    };
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handlePrevMonth = () => {
    setCurrentMonth(new Date(currentMonth.setMonth(currentMonth.getMonth() - 1)));
  };

  const handleNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.setMonth(currentMonth.getMonth() + 1)));
  };

  const daysInMonth = (month, year) => new Date(year, month + 1, 0).getDate();

  const startDayOfMonth = (month, year) => new Date(year, month, 1).getDay();

  const isFutureDate = (date) => {
    return moment(date).isAfter(moment(), "day");
  };

  const renderDayLabels = () => {
    const dayLabels = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    return dayLabels.map((label, index) => (
      <div
        key={`${label}-${index}`}
        className="cldr-day-label"
        style={{ color: label === "Sun" ? "red" : colors.black }}
      >
        {label}
      </div>
    ));
  };

  const renderDays = () => {
    const days = [];
    const totalDays = daysInMonth(currentMonth.getMonth(), currentMonth.getFullYear());
    const startDay = startDayOfMonth(currentMonth.getMonth(), currentMonth.getFullYear());

    days.push(...renderDayLabels());

    for (let i = 0; i < startDay; i++) {
      days.push(<div key={`empty-${i}`} className="cldr-day cldr-non-month-day" />);
    }

    for (let day = 1; day <= totalDays; day++) {
      const dateStr = `${currentMonth.getFullYear()}-${(currentMonth.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
      const dayData = calendarData.find((d) => d.date === dateStr);
      days.push(renderDay(day, dayData, dateStr));
    }

    return days;
  };
  const renderDay = (day, dayData, dateStr) => {
    const shiftType = dayData ? dayData.shift_type : "week_off";
    const shiftTime = dayData && dayData.shiftTime ? dayData.shiftTime : "";
    const modifiedShiftTime = dayData && dayData.modified_shift ? dayData.modified_shift : "";
    const leaveReason = dayData && dayData.leaveReason ? dayData.leaveReason : "";
    const holidayName = dayData && dayData.holidayName ? dayData.holidayName : "";
    const checkInTime = dayData && dayData.checkInTime ? dayData.checkInTime : ""; 
    const checkOutTime = dayData && dayData.checkOutTime ? dayData.checkOutTime : "";
    const isAbsent = dayData && dayData.isPresent === false;
    const isPresent = dayData && dayData.isPresent === true;
    const leaveType = dayData && dayData.leaveType ? dayData.leaveType : "";
    const isFuture = isFutureDate(dateStr);
  
    const getDotColor = () => {
      if (shiftType === "week_off") return "orange"; 
      if (shiftType === "leave" && !isPresent) return "yellow"; // Only yellow if not present
      if (shiftType === "holiday") return "blue"; 
      if (isPresent) return "green"; 
      if (isAbsent) return isFuture ? "grey" : "red"; 
      if (shiftType === "working") return isFuture ? "grey" : "green"; 
      return "";
    };
    
    const getBgColor = () => {
      if (shiftType === "week_off") return "#EFC27D"; 
      if (shiftType === "leave" && !isPresent) return "#FFFECB";  // Leave days should have a specific background
      if (shiftType === "holiday") return "#D4F6FF"; 
      if (isPresent) return "#C9E9D2"; 
      if (isAbsent) return isFuture ? "#E0E0E0" : "#FFAAAA"; 
      if (shiftType === "working") return isFuture ? "#E0E0E0" : "#C9E9D2"; 
      return "";
    };
    
    const getTextColor = () => {
      if (isFuture) return "black"; 
      if (shiftType === "working") return "green"; 
      if (shiftType === "week_off" || shiftType === "leave") return "inherit"; 
      return "inherit";
    };
    
    const convertTimeRange = (checkInTime, checkOutTime) => {
      if (!checkInTime || !checkOutTime) return "-";
      const formattedCheckIn = moment(checkInTime, "HH:mm:ss").format("hh:mma").toLowerCase();
      const formattedCheckOut = moment(checkOutTime, "HH:mm:ss").format("hh:mma").toLowerCase();
      return `${formattedCheckIn} - ${formattedCheckOut}`;
    };
  
    return (
      <div
        key={day}
        className={`cldr-day ${shiftType}`}
        style={{ backgroundColor: getBgColor(), borderRadius: 10 }}
      >
        <div className="cldr-date">
          <div className="cldr-dot" style={{ backgroundColor: getDotColor() }}></div>
          <div>{day}</div>
        </div>
  
        {/* Display Holiday Name */}
        {shiftType === "holiday" && !isPresent && (
          <div
            className="cldr-hours2"
            style={{
              fontSize: viewMode === "holiday" ? 13 : 10,
              fontWeight: "bold",
              color: getDotColor(),
              textAlign: "start",
            }}
          >
            {holidayName}
          </div>
        )}
  
        {/* Display Leave Information if not present */}
        {shiftType === "leave" && !isPresent && leaveType && (
          <div
            className="cldr-leave-type"
            style={{
              fontSize: viewMode === "shift" ? 10 : 10,
              color: getTextColor(),
              marginTop: '-3.5px',
              textAlign: 'start',
            }}
          >
            <div>{leaveType}</div>
          </div>
        )}
  
        {/* Show working hours for present employees */}
        {isPresent && (
          <div
            className="cldr-hours2"
            style={{
              fontSize: viewMode === "shift" ? 10 : 10, marginTop:'-2px',
              color: getTextColor(),
              textAlign: 'start',
            }}
          >
            {convertTimeRange(checkInTime, checkOutTime)}
          </div>
        )}

{isPresent && shiftTime && (
  <div
    className="cldr-shift-time"
    style={{
      fontSize: 10,
      fontWeight: "bold",
      color: getDotColor(),
      textAlign: "start",
    }}
  >
    {modifiedShiftTime ? (
      <>
        <span className="Modified-Time" style={{ marginRight: "5px" }}>
          {modifiedShiftTime}
        </span>
        <span style={{ textDecoration: "line-through" }}>{shiftTime}</span>
      </>
    ) : (
      shiftTime
    )}
  </div>
)}
  
        {/* Display Shift Time */}
        {shiftType === "working" && !isPresent && (
          <div
            className="cldr-hrs2"
            style={{
              fontSize: viewMode === "working" ? 13 : 10,
              fontWeight: "bold",
              color: getDotColor(),
            }}
          >
            {modifiedShiftTime ? (
              <>
                <span
                  className="Modified-Time"
                  style={{
                    display: "inline-block",
                    marginRight: "5px",
                  }}
                >
                  {modifiedShiftTime}
                </span>
                <span
                  style={{
                    display: "inline-block",
                    textDecoration: "line-through",
                  }}
                >
                  {shiftTime}
                </span>
              </>
            ) : (
              shiftTime
            )}
          </div>
        )}
      </div>
    );
  };
  

  const [isApplyModalOpen, setIsApplyModalOpen] = useState(false);
  const [currentModal, setCurrentModal] = useState("");

  const toggleApplyModal = (modaltype) => {
    setIsApplyModalOpen(!isApplyModalOpen);
    setCurrentModal(modaltype);
  };

  const closeApplyModal = () => {
    setIsApplyModalOpen(false);
  };

  const convertTimeRange = (timeRange) => {
    if (timeRange === "-") return "-";
    const [start, end] = timeRange.split(" - ");

    const formattedStart = moment(start, "HH:mm:ss").format("hh:mma").toLowerCase();
    const formattedEnd = moment(end, "HH:mm:ss").format("hh:mma").toLowerCase();

    return `${formattedStart} - ${formattedEnd}`;
  };

  return (
    <div className="col col-12 col-md-12 col-lg-12 mb-4">
      <Container
        fluid
        className="p-4"
        style={{
          backgroundColor: "#f9f9f9",
          borderRadius: "10px",
          boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
          width: "97.5%",
          marginLeft: "0px",
        }}
      >
        <div className="row mb-3 align-items-center">
          <div className="col-12 col-md-5 col-lg-11 d-flex justify-content-center">
            <button className="btn btn-light me-2" onClick={handlePrevMonth}>
              &lt;
            </button>
            <h5 className="fw-bold text-primary m-2 cardHeaderTxt">
              {currentMonth.toLocaleString("default", {
                month: isMDScreen ? "short" : "long",
              })}{" "}
              {currentMonth.getFullYear()}
            </h5>
            <button className="btn btn-light ms-2" onClick={handleNextMonth}>
              &gt;
            </button>
          </div>
          <div className="col-12 col-md-4 col-lg-1 d-flex justify-content-end">
            <Button
              onClick={() => setViewMode("shift")}
              variant="outline-primary"
              className={`me-2 ${viewMode === "shift" ? "active" : ""} btnTxt1`}
            >
              Shifts
            </Button>
            <Button
              onClick={() => setViewMode("holiday")}
              variant="outline-secondary"
              className={`${viewMode === "holiday" ? "active" : ""} btnTxt1`}
            >
              Holidays
            </Button>
          </div>
        </div>
        
        <Row>
          <Col>
            <div className="cldr-calendar">
              <div className="cldr-calendar-days">{renderDays()}</div>
            </div>
          </Col>
        </Row>
        {errorMessage && (
          <Row className="mt-3">
            <Col>
              <div className="alert alert-danger text-center">{errorMessage}</div>
            </Col>
          </Row>
        )}
             <Row className="mt-2 justify-content-center">
        <Col xs={12} className="text-center">
          <div className="d-flex justify-content-center align-items-center">
            <div className="d-flex align-items-center me-3">
              <div
                className="legend-item"
                style={{
                  width: '15px',
                  height: '15px',
                  backgroundColor: '#32CD32', 
                  borderRadius: '50%',
                  marginRight: '8px',
                }}
              ></div>
              <span>Present</span>
            </div>

            
            <div className="d-flex align-items-center me-3">
              <div
                className="legend-item"
                style={{
                  width: '15px',
                  height: '15px',
                  backgroundColor: 'Red',
                  borderRadius: '50%',
                  marginRight: '8px',
                }}
              ></div>
              <span>Absent</span>
            </div>
            


            <div className="d-flex align-items-center me-3">
              <div
                className="legend-item"
                style={{
                  width: '15px',
                  height: '15px',
                  backgroundColor: '#FE9900',
                  borderRadius: '50%',
                  marginRight: '8px',
                }}
              ></div>
              <span>Week Off</span>
            </div>

            <div className="d-flex align-items-center me-3">
              <div
                className="legend-item"
                style={{
                  width: '15px',
                  height: '15px',
                  backgroundColor: '#FFD700', 
                  borderRadius: '50%',
                  marginRight: '8px',
                }}
              ></div>
              <span>Leave</span>
            </div>
            <div className="d-flex align-items-center">
              <div
                className="legend-item"
                style={{
                  width: '15px',
                  height: '15px',
                  backgroundColor: '#1E90FF', 
                  borderRadius: '50%',
                  marginRight: '8px',
                }}
              ></div>
              <span>Holiday</span>
            </div>
          </div>
        </Col>
      </Row>
        <Row className="mt-4 justify-content-center">
          <Col xs={6} md={3} className="text-center">
            <Button
              onClick={() => toggleApplyModal("apply")}
              variant="primary"
              className="btn-block mb-2"
              style={{ backgroundColor: "#1E488E", borderColor: "#1E488E" }}
            >
              Apply Leave
            </Button>
          </Col>
          <Col xs={6} md={3} className="text-center">
            <Button
              onClick={() => toggleApplyModal("regularise")}
              // variant="warning"
               variant="primary"
              className="btn-block mb-2"
              // style={{ backgroundColor: "#F7C11C", borderColor: "#F7C11C" }}
              style={{ backgroundColor: "#1E488E", borderColor: "#1E488E" }}
            >
              Modification
            </Button>
          </Col>
          <Col xs={6} md={3} className="text-center">
            <Button
              onClick={() => toggleApplyModal("shiftchange")}
               variant="primary"
              // variant="success"
              className="btn-block mb-2"
              style={{ backgroundColor: "#1E488E", borderColor: "#1E488E" }}
              //style={{ backgroundColor: "#28a745", borderColor: "#28a745" }}
            >
              Shift Change
            </Button>
          </Col>
          <Col xs={6} md={3} className="text-center">
            <Link href={"/employeeDashboard/viewHistory"}>
              <Button
               variant="primary"
               // variant="secondary"
                className="btn-block mb-2"
                style={{ backgroundColor: "#1E488E", borderColor: "#1E488E" }}
             //   style={{ backgroundColor: "#6c757d", borderColor: "#6c757d" }}
              >
                View History
              </Button>
            </Link>
          </Col>
        </Row>
        {isApplyModalOpen && (
          <AnimatedModal isOpen={isApplyModalOpen} closeModal={closeApplyModal}>
            {currentModal === "apply" && <ApplyLeave closeApplyModal={closeApplyModal} />}
            {currentModal === "regularise" && (
              <AttendanceRegularisation closeApplyModal={closeApplyModal} />
            )}
            {currentModal === "shiftchange" && (
              <ShiftChange closeApplyModal={closeApplyModal} />
            )}
            {currentModal === "history" && <ApplyLeave closeApplyModal={closeApplyModal} />}
          </AnimatedModal>
        )}
      </Container>
    </div>
  );
};

export default EmployeeCalender;
