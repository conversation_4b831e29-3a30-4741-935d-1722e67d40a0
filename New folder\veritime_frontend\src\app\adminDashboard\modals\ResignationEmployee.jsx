import { <PERSON><PERSON>, <PERSON><PERSON>, Form } from "react-bootstrap";
import React, { useState } from "react";

const TerminateEmployeeModal = ({ show, onHide, empId, onTerminate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [reason, setReason] = useState(""); // New state for reason

  const handleReasonChange = (e) => {
    setReason(e.target.value);
  };

  const handleTerminate = async () => {
    if (!reason.trim()) {
      alert("Please provide a reason for termination.");
      return;
    }

    setIsLoading(true);
    try {
      // Replace with your API call to terminate the employee, including the reason
      // Example API request:
      // const response = await postRequest(API_URLS.TERMINATE_EMPLOYEE, { empId, reason });

      // Simulating the API response:
      
      // Call onTerminate callback after successful termination
      onTerminate(empId, reason);
      onHide(); // Close the modal
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>Resignation Employee</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p style={{marginBottom:'10px'}}>Are you sure you want to Resign?</p>
        
        <Form.Group controlId="ResignationReason">
          <Form.Label>Reason for Resignation</Form.Label>
          <Form.Control
            as="textarea"
            rows={3}
            value={reason}
            onChange={handleReasonChange}
            placeholder="Please provide a reason for Resignation."
          />
        </Form.Group>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button
          variant="sucess"
          onClick={handleTerminate}
          disabled={isLoading}
        >
          {isLoading ? "Resignation..." : "Resignation"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default TerminateEmployeeModal;
