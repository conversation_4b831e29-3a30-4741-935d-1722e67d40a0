import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Form, Alert } from "react-bootstrap";
import { API_URLS } from "@/constants/apiConstants";
import { showSuccessAlert2 } from "@/services/alertService";
import { getRequest, postRequest, putRequest } from "@/services/apiService";

const CreateHospitalModal = ({
  show,
  handleClose,
  fetchItems,
  rowToEdit,
}: any) => {
  const [provinceList, setProvinceList] = useState([]);
  const [districtList, setDistrictList] = useState([]);
  const [subDistrictList, setSubDistrictList] = useState([]);
  const [formData, setFormData] = useState({
    hospital_name: "",
    short_code: "",
    hospital_type: "",
    province: "",
    district_id: "",
    sub_district_id: "",
    cluster_NO: "",
    address: "",
    address_line2: "",
    lat: "",
    lng: "",
    validity: "",
    departmentName: "",
  });

  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    loadDataFromLocalOrAPI();

    if (rowToEdit) {
      loadDistrictList(rowToEdit.provinceId),
        loadSubDistrictList(rowToEdit.provinceId, rowToEdit.districtId);
      setFormData({
        hospital_name: rowToEdit.name || "",
        short_code: rowToEdit.short_code || "",
        hospital_type: rowToEdit.type || "",
        province: rowToEdit.provinceId || "",
        district_id: rowToEdit.districtId || "",
        sub_district_id: rowToEdit.subDistrictId || "",
        cluster_NO: rowToEdit.cluster_NO || "",
        address: rowToEdit.address || "",
        address_line2: rowToEdit.address_line2 || "",
        lat: rowToEdit.lat || "",
        lng: rowToEdit.lng || "",
        validity: rowToEdit.validity || "",
        departmentName: rowToEdit.departmentName || "",
      });
    }
  }, [rowToEdit]);

  const loadDataFromLocalOrAPI = async () => {
    const cachedProvinceList = localStorage.getItem("provinceList");
    if (cachedProvinceList) {
      setProvinceList(JSON.parse(cachedProvinceList));
    } else {
      const provinceApi = await getRequest(API_URLS.GET_PROVIENCE);
      if (provinceApi) {
        setProvinceList(provinceApi);
        localStorage.setItem("provinceList", JSON.stringify(provinceApi));
      }
    }
  };

  const loadDistrictList = async (provinceId: string) => {
    const cachedDistrictList = localStorage.getItem(
      `districtList_${provinceId}`
    );
    if (cachedDistrictList) {
      setDistrictList(JSON.parse(cachedDistrictList));
    } else {
      const districtApi = await getRequest(API_URLS.GET_DISTRICT(provinceId));
      if (districtApi) {
        setDistrictList(districtApi);
        localStorage.setItem(
          `districtList_${provinceId}`,
          JSON.stringify(districtApi)
        );
      }
    }
  };

  const loadSubDistrictList = async (
    provinceId: string,
    districtId: string
  ) => {
    const cachedSubDistrictList = localStorage.getItem(
      `subDistrict_${provinceId}_${districtId}`
    );
    if (cachedSubDistrictList) {
      setSubDistrictList(JSON.parse(cachedSubDistrictList));
    } else {
      const subDistrictApi = await getRequest(
        API_URLS.GET_SUB_DISTRICT(provinceId, districtId)
      );
      if (subDistrictApi) {
        setSubDistrictList(subDistrictApi);
        localStorage.setItem(
          `subDistrict_${provinceId}_${districtId}`,
          JSON.stringify(subDistrictApi)
        );
      }
    }
  };

  const handleChange = (e: { target: { name: string; value: string } }) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    if (name === "province") {
      loadDistrictList(value);
    }

    if (name === "district_id") {
      loadSubDistrictList(formData.province, value);
    }
  };
  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");

    try {
      let validityDate = "";
      if (formData.validity) {
        const parsedDate = Date.parse(formData.validity);
        if (!isNaN(parsedDate)) {
          validityDate = new Date(parsedDate).toISOString();
        } else {
          throw new Error("Invalid date format for validity.");
        }
      }

      const transformedData = {
        hospitalName: formData.hospital_name,
        shortCode: formData.short_code,
        provinceId: parseInt(formData.province),
        districtId: parseInt(formData.district_id),
        subDistrictId: parseInt(formData.sub_district_id),
        addressLine1: formData.address,
        addressLine2: formData.address_line2,
        lat: parseFloat(formData.lat),
        lng: parseFloat(formData.lng),
        validity: validityDate,
        hospitalType: formData.hospital_type,
        isActive: true,
        clusterName: "Healthcare Cluster",
        branches: [
          {
            branchName: "Ellisras Hospital",
            branchCode: "ellisras-hospital",
            departmentIds: [1, 2, 3, 4, 6],
          },
        ],
      };

      if (rowToEdit) {
        const updateResponse = await putRequest(
          `${API_URLS.UPDATE_HOSPITAL(rowToEdit.hospital_id)}`,
          transformedData
        );

        showSuccessAlert2("Hospital updated successfully!");
      } else {
        const createResponse = await postRequest(
          API_URLS.UPDATE_HOSPITAL,
          transformedData
        );
        showSuccessAlert2("Hospital created successfully!");
      }

      await fetchItems();

      handleClose();
    } catch (error) {
      setError("An error occurred while updating the hospital.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>
          {rowToEdit ? "Edit Hospital" : "Add Hospital"}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ maxHeight: "500px", overflowY: "auto" }}>
        {error && <Alert variant="danger">{error}</Alert>}
        <Form onSubmit={handleSubmit}>
          {/* Hospital Name and Type in one row */}
          <div className="d-flex justify-content-between">
            <Form.Group
              controlId="hospital_name"
              style={{ marginBottom: "15px" }}
              className="me-2"
            >
              <Form.Label>Hospital Name</Form.Label>
              <Form.Control
                type="text"
                name="hospital_name"
                value={formData.hospital_name}
                onChange={handleChange}
                required
              />
            </Form.Group>
            <Form.Group
              controlId="hospital_type"
              style={{ marginBottom: "15px" }}
              className="me-2"
            >
              <Form.Label>Hospital Type</Form.Label>
              <Form.Control
                type="text"
                name="hospital_type"
                value={formData.hospital_type}
                onChange={handleChange}
                required
              />
            </Form.Group>
          </div>

          {/* Short Code and Province Name in one row */}
          <div className="d-flex justify-content-between">
            {/* <Form.Group controlId="short_code" className="me-2">
              <Form.Label>Short Code</Form.Label>
              <Form.Control
                type="text"
                name="short_code"
                value={formData.short_code}
                onChange={handleChange}
                required
              />
            </Form.Group> */}
          </div>

          <Form.Group controlId="province_id" style={{ marginBottom: "15px" }}>
            <Form.Label>Province</Form.Label>
            <Form.Control
              as="select"
              name="province"
              value={formData.province}
              onChange={handleChange}
              required
            >
              <option value="">Select Province</option>
              {provinceList.map((province: any) => (
                <option key={province.id} value={province.id}>
                  {province.name}
                </option>
              ))}
            </Form.Control>
          </Form.Group>

          <Form.Group controlId="district_id" style={{ marginBottom: "15px" }}>
            <Form.Label>District</Form.Label>
            <Form.Control
              as="select"
              name="district_id"
              value={formData.district_id}
              onChange={handleChange}
              required
            >
              <option value="">Select District</option>
              {districtList.map((district: any) => (
                <option key={district.id} value={district.id}>
                  {district.name}
                </option>
              ))}
            </Form.Control>
          </Form.Group>

          <Form.Group
            controlId="sub_district_id"
            style={{ marginBottom: "15px" }}
          >
            <Form.Label>Sub-District</Form.Label>
            <Form.Control
              as="select"
              name="sub_district_id"
              value={formData.sub_district_id}
              onChange={handleChange}
              required
            >
              <option value="">Select Sub-District</option>
              {subDistrictList.map((subDistrict: any) => (
                <option key={subDistrict.id} value={subDistrict.id}>
                  {subDistrict.name}
                </option>
              ))}
            </Form.Control>
          </Form.Group>

          <div className="d-flex justify-content-between">
            {/* <Form.Group controlId="cluster_NO" className="me-2">
              <Form.Label>Cluster No</Form.Label>
              <Form.Control
                type="text"
                name="cluster_NO"
                value={formData.cluster_NO}
                onChange={handleChange}
                required
              />
            </Form.Group> */}

            {/* <Form.Group
              controlId="validity"
              style={{ marginBottom: "15px" }}
              className="me-2"
            >
              <Form.Label>Validity</Form.Label>
              <Form.Control
                type="text"
                name="validity"
                value={formData.validity}
                onChange={handleChange}
                required
              />
            </Form.Group> */}
          </div>

          {/*<Form.Group
            controlId="address_line1"
            style={{ marginBottom: "15px" }}
          >
            <Form.Label>Address Line 1</Form.Label>
            <Form.Control
              type="text"
              name="address"
              value={formData.address}
              onChange={handleChange}
              required
            />
          </Form.Group>*/}

          {/* <Form.Group
            controlId="address_line2"
            style={{ marginBottom: "15px" }}
          >
            <Form.Label>Address Line 2</Form.Label>
            <Form.Control
              type="text"
              name="address_line2"
              value={formData.address_line2}
              onChange={handleChange}
            />
          </Form.Group> */}

          {/* <div className="d-flex justify-content-between">
            <Form.Group controlId="lat" className="me-2">
              <Form.Label>Latitude</Form.Label>
              <Form.Control
                type="text"
                name="lat"
                value={formData.lat}
                onChange={handleChange}
                required
              />
            </Form.Group>

            <Form.Group
              controlId="lng"
              style={{ marginBottom: "15px" }}
              className="me-2"
            >
              <Form.Label>Longitude</Form.Label>
              <Form.Control
                type="text"
                name="lng"
                value={formData.lng}
                onChange={handleChange}
                required
              />
            </Form.Group>

          <Form.Group
            controlId="departmentName"
            style={{ marginBottom: "15px" }}
          >
            <Form.Label>Department Name</Form.Label>
            <Form.Control
              type="text"
              name="departmentName"
              value={formData.departmentName}
              onChange={handleChange}
              required
            />
          </Form.Group> */}
          {/* </div> */}

          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? "Submitting..."
              : rowToEdit
              ? "Update Hospital"
              : "Add Hospital"}
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default CreateHospitalModal;
