import { API_URLS } from "@/constants/apiConstants";
import {
  getRequest,
  postRequest,
  getRequestWithSecurity,
} from "@/services/apiService";
import React, { useEffect, useRef, useState } from "react";
import { MultiSelect } from "react-multi-select-component";
import DepartmentDropwdown from "@/components/Dropdowns/DepartmentDropdown";
import useLocalStorage from "@/services/localstorage";
import { decryptData } from "@/utils/encryption.js";

import Select from "react-select"; // Import react-select for searchable dropdowns
const headingStyle = {
  marginBottom: "20px",
  fontWeight: "bold",
};
function OfficialDetailsForm({
  data,
  goToNextTab,
  onSubmit,
  companyName, // Renamed hospitalId to companyName
  empDetail, // Renamed  branchId to empDetail
}) {
  const didRun = useRef(false);
  const typingTimeoutRef = useRef(null);
  const menuRef = useRef(null);
  const [selectedReportingPerson, setselectedReportingPerson] = useState({});
  const getEmpDetail = async () => {
    if (data.reportingPerson) {
      const empData = await getRequestWithSecurity(
        `${API_URLS.EMPLOYEE_LIST}/${data.reportingPerson}`
      );
      setselectedReportingPerson({
        value: empData.empId,
        label: empData.empName,
      });
    }
  };

  const daysOfWeek = [
    { value: "Monday", label: "Monday" },
    { value: "Tuesday", label: "Tuesday" },
    { value: "Wednesday", label: "Wednesday" },
    { value: "Thursday", label: "Thursday" },
    { value: "Friday", label: "Friday" },
    { value: "Saturday", label: "Saturday" },
    { value: "Sunday", label: "Sunday" },
  ];

  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const [designations, setDesignations] = useState([]);
  const [scheduleList, setScheduleList] = useState([]);
  const [timeSlots, setTimeSlots] = useState([]);
  const [dept, setdept] = useState([]);
  const [empList, setEmpList] = useState([]);
  const [leaveOnDays, setLeaveOnDays] = useState(data?.leaveOnDaysList || []);
  const [role, setRole] = useLocalStorage("role", "");
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    reportingPerson: "",
    workZone: "",
    empCode: "",
    defaultTimeSlotId: "",
    biometricID: "",
    departmentId: "",
    designationId: "",
    hireDate: "",
    probationPeriod: "",
    leaveOnDays: [],
  });
  // console.log("formData", formData);

  // useEffect(() => {
  //   fetchReportingPersonOptions();
  //   if (data) setFormData(data);

  //   if (data?.departmentId) {
  //     fetchDesignations(data?.departmentId);
  //     fetchScheduleOptions(data?.departmentId);
  //   }
  // }, []);
  const [branchId, setBranchId] = useState(null);
  useEffect(() => {
    if (!didRun.current) {
      didRun.current = true;
      getEmpDetail();
      if (empDetail) {
        setOffset(0);
        //setEmpList([]);
        setHasMore(true);
        fetchReportingPersonOptions(empDetail, 0);
      }
    }
  }, []);
  useEffect(() => {
    if (
      Object.keys(selectedReportingPerson).length !== 0 &&
      Object.keys(empList).length !== 0
    ) {
      if (
        empList.filter((emp) => emp.label === selectedReportingPerson.label)
          .length == 0
      )
        setEmpList((prev) => [selectedReportingPerson, ...prev]);
    }
  }, [empList, selectedReportingPerson]);
  useEffect(() => {
    // console.log("empDetail (branchId) from offical detail form:", empDetail); // the empDetail prop
    const fetchProfileData = async () => {
      // try {
      //   fetchReportingPersonOptions(empDetail);
      // } catch (error) {
      //   // console.error("Error fetching profile details:", error);
      // }
    };

    fetchProfileData();

    if (data) setFormData(data);

    if (data?.departmentId) {
      fetchDesignations(data?.departmentId);
      fetchScheduleOptions(data?.departmentId);
    }
  }, [empDetail, data]);

  const handleChange = async (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
    if (name === "departmentId" && value) {
      fetchDesignations(value);
      fetchScheduleOptions(value);
    }
  };

  const fetchDesignations = async (departmentId) => {
    const designationKey = `designations_${departmentId}`;
    const cachedDesignations = JSON.parse(localStorage.getItem(designationKey));

    if (cachedDesignations) {
      // console.log("Loaded designations from cache:", cachedDesignations);
      setDesignations(cachedDesignations);
    } else {
      // console.log("Fetching designations from API...");
      const data = await getRequest(
        API_URLS.DEPT_WISE_DESIGNATION(departmentId)
      );
      if (data && data.length > 0) {
        setDesignations(data);
        localStorage.setItem(designationKey, JSON.stringify(data));
        // console.log("Saved designations to cache:", data);
      }
    }
  };

  const fetchScheduleOptions = async (departmentId) => {
    const scheduleKey = `scheduleList_${departmentId}`;
    const cachedSchedule = JSON.parse(localStorage.getItem(scheduleKey));

    if (cachedSchedule) {
      setScheduleList(cachedSchedule);
      setTimeSlots(cachedSchedule.flatMap((item) => item.timeSlots || []));
    } else {
      const urlParams = new URLSearchParams(window.location.search);
      const userData = await decryptData(localStorage.getItem("userData"));
      let payload = {};
      if (data?.isEditing) {
        payload = {
          hospitalId: data.companyId,
          branchId: data.branchId,
          departmentId: departmentId,
        };
      } else {
        payload = {
          hospitalId: urlParams.get("hospitalId")
            ? atob(urlParams.get("hospitalId"))
            : userData.companyId,
          branchId: urlParams.get("branchId")
            ? atob(urlParams.get("branchId"))
            : userData.branchId,
          departmentId: departmentId,
        };
      }

      const schedule = await postRequest(API_URLS.SCHEDULE_LIST, payload);

      if (schedule) {
        setScheduleList(schedule);
        setTimeSlots(schedule.flatMap((item) => item.timeSlots || []));
        localStorage.setItem(scheduleKey, JSON.stringify(schedule));
      }
    }
  };
  const fetchReportingPersonOptions = async (
    empDetail,
    newOffset = offset,
    inputText
  ) => {
    if (loading || !hasMore) return;

    setLoading(true);

    try {
      const requestBody = {
        branchId: empDetail,
        offset: newOffset,
        limit: 10,
        search_param: inputText != undefined ? inputText : "",
      };

      const response = await postRequest(
        API_URLS.GET_EMPLOYEES_BY_BRANCH,
        requestBody
      );

      if (response?.content?.length) {
        const newEmployees = response.content.map((emp) => ({
          value: emp.empId,
          label: emp.empName,
        }));

        //console.log("new", newEmployees);
        if (offset > 0) {
          setEmpList((prev) => [...prev, ...newEmployees]);
        } else {
          setEmpList(newEmployees);
        }
        setOffset((prev) => prev + 1);

        const totalFetched = empList.length + newEmployees.length;
        if (totalFetched >= response.totalElements) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error fetching reporting persons:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleMultiSelectChange = (selected) => {
    setLeaveOnDays(selected);
    setFormData({
      ...formData,
      leaveOnDays: selected.map((day) => day.value),
    });
    if (errors.leaveOnDays) {
      setErrors({ ...errors, leaveOnDays: "" });
    }
  };

  const formatTime = (inputTime) => {
    const [hours, minutes] = inputTime.split(":");
    return `${hours}:${minutes}`;
  };

  const validate = () => {
    let validationErrors = {};

    if (!formData.workZone || formData.workZone.trim() === "") {
      validationErrors.workZone = "Work zone is required";
    }

    if (!formData.empCode || formData.empCode.trim() === "") {
      validationErrors.empCode = "Employee code is required";
    }

    if (!formData.biometricID || formData.biometricID.trim() === "") {
      validationErrors.biometricID = "Biometric ID is required";
    }

    if (!formData.departmentId) {
      validationErrors.departmentId = "Department is required";
    }

    if (!formData.designationId) {
      validationErrors.designationId = "Designation is required";
    }

    if (!formData.hireDate) {
      validationErrors.hireDate = "Hire Date is required";
    }

    if (!formData.defaultTimeSlotId) {
      validationErrors.defaultTimeSlotId = "Shift is required";
    }

    if (!formData.reportingPerson) {
      validationErrors.reportingPerson = "reportingPerson is required";
    }

    // Validate probationPeriod: must be a number between 1 and 30
    // if (!formData.probationPeriod) {
    //   validationErrors.probationPeriod = "Probation period is required";
    // } else if (!/^(?:[1-9]|[1-2][0-9]|30)$/.test(formData.probationPeriod)) {
    //   validationErrors.probationPeriod =
    //     "Probation period must be between 1 and 30";
    // }

    if (!formData.probationPeriod) {
      validationErrors.probationPeriod = "Probation period is required";
    } else if (!/^\d+$/.test(formData.probationPeriod)) {
      validationErrors.probationPeriod = "Probation period must be a number";
    } else if (
      parseInt(formData.probationPeriod) < 1 ||
      parseInt(formData.probationPeriod) > 180
    ) {
      validationErrors.probationPeriod =
        "Probation period must be between 1 and 180 days";
    }

    // **Validation for Leave on Days**
    if (leaveOnDays.length === 0) {
      validationErrors.leaveOnDays = "At least one leave day must be selected";
    }
    return validationErrors;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
    } else {
      onSubmit(formData);
      // console.log("Form Data:", formData);
    }
  };

  const handleKeyDown = (value) => {
    // Clear the previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setOffset(0);
      //setEmpList([]);
      setHasMore(true);
      fetchReportingPersonOptions(empDetail, 0, value);
    }, 500); // Delay in ms
  };
  const handleReportingPersonChange = (selectedOption) => {
    setFormData({
      ...formData,
      reportingPerson: selectedOption ? selectedOption.value : "",
    });
  };
  const handleMenuScroll = () => {
    const menu = menuRef.current;
    console.log(menu);
    if (menu) {
      // Check if the dropdown is scrolled to the bottom
      if (menu.scrollTop + menu.clientHeight === menu.scrollHeight) {
        console.log("Reached the bottom of the select dropdown!");
      }
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      style={{ maxWidth: "900px", margin: "25px auto" }}
    >
      <h3 style={headingStyle}>Work Details</h3>

      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <div>
          <div style={{ marginBottom: "15px" }}>
            <label>
              <label htmlFor="firstName">
                <label
                  htmlFor="ticketSubject"
                  className="ticket-text-primary"
                  style={{ marginTop: "10px", marginLeft: "14px" }}
                >
                  Reporting person<span className="text-danger">*</span>
                </label>
              </label>
            </label>
            <Select
              options={[
                { value: "0", label: "is HOD" },
                ...empList.map((person) => ({
                  value: person.value,
                  label: person.label,
                })),
              ]}
              value={{
                value: formData.reportingPerson,
                label:
                  empList.find(
                    (person) => person.value === formData.reportingPerson
                  )?.label || "Select Reporting Person",
              }}
              onChange={(selectedOption) => {
                handleReportingPersonChange(selectedOption);
              }}
              onInputChange={(inputValue, { action }) => {
                if (action === "input-change") {
                  handleKeyDown(inputValue); // Only when typed
                }
              }}
              placeholder="Select Reporting Person"
              isClearable={
                formData.reportingPerson &&
                formData.reportingPerson !== "0" &&
                formData.reportingPerson !== ""
              }
              onMenuScrollToBottom={() => {
                if (hasMore && !loading) {
                  fetchReportingPersonOptions(empDetail, offset);
                }
              }}
              menuRef={menuRef}
              isLoading={loading}
            />

            {errors.reportingPerson && (
              <span style={errorStyle}>{errors.reportingPerson}</span>
            )}
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label>
              <label htmlFor="firstName">
                <label
                  htmlFor="ticketSubject"
                  className="ticket-text-primary"
                  style={{ marginTop: "10px", marginLeft: "14px" }}
                >
                  Employee Code <span className="text-danger">*</span>
                </label>
              </label>
            </label>
            <input
              type="text"
              name="empCode"
              value={formData.empCode}
              onChange={handleChange}
              placeholder="Enter Employee Code"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.empCode && <span style={errorStyle}>{errors.empCode}</span>}
          </div>
          <div style={{ marginBottom: "15px" }}>
            <label>
              <label htmlFor="firstName">
                <label
                  htmlFor="ticketSubject"
                  className="ticket-text-primary"
                  style={{ marginTop: "10px", marginLeft: "14px" }}
                >
                  Designation <span className="text-danger">*</span>
                </label>
              </label>
            </label>
            <Select
              options={designations.map((designation) => ({
                value: designation.id,
                label: designation.name,
              }))}
              value={
                formData.designationId
                  ? {
                      value: formData.designationId,
                      label:
                        designations.find(
                          (designation) =>
                            designation.id === formData.designationId
                        )?.name || "Select Designation",
                    }
                  : { value: "", label: "Select Designation" }
              }
              onChange={(selectedOption) =>
                setFormData({
                  ...formData,
                  designationId: selectedOption ? selectedOption.value : "",
                })
              }
              placeholder="Select Designation"
              isClearable={
                formData.designationId && formData.designationId !== ""
              }
            />
            {errors.designationId && (
              <span style={errorStyle}>{errors.designationId}</span>
            )}
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label>
              <label htmlFor="firstName">
                <label
                  htmlFor="ticketSubject"
                  className="ticket-text-primary"
                  style={{ marginTop: "10px", marginLeft: "14px" }}
                >
                  Biometric ID <span className="text-danger">*</span>
                </label>
              </label>
            </label>
            <input
              type="text"
              name="biometricID"
              value={formData.biometricID}
              onChange={handleChange}
              placeholder="Enter Biometric ID"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.biometricID && (
              <span style={errorStyle}>{errors.biometricID}</span>
            )}
          </div>

          <div style={inputGroupStyle}>
            <label>
              <label htmlFor="firstName">
                <label
                  htmlFor="ticketSubject"
                  className="ticket-text-primary"
                  style={{ marginTop: "10px", marginLeft: "14px" }}
                >
                  Hire Date <span className="text-danger">*</span>
                </label>
              </label>
            </label>
            <input
              type="date"
              id="hireDate"
              name="hireDate"
              value={formData.hireDate}
              onChange={handleChange}
              style={inputStyle}
            />
            {errors.hireDate && (
              <span style={errorStyle}>{errors.hireDate}</span>
            )}
          </div>
        </div>
        <div>
          <div style={{ marginBottom: "15px" }}>
            <label
              htmlFor="workZone"
              className="ticket-text-primary"
              style={{ marginTop: "10px", marginLeft: "14px" }}
            >
              Work Zone <span className="text-danger">*</span>
            </label>
            <input
              type="text"
              name="workZone"
              value={formData.workZone}
              onChange={handleChange}
              placeholder="Enter Work Zone"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.workZone && (
              <span style={errorStyle}>{errors.workZone}</span>
            )}
          </div>

          <DepartmentDropwdown
            empDetail={empDetail} // branchId (empDetail)
            formData={formData}
            onChange={handleChange}
            error={errors.departmentId}
          />

          <div style={{ marginBottom: "15px" }}>
            <label>
              <label htmlFor="firstName">
                <label
                  htmlFor="ticketSubject"
                  className="ticket-text-primary"
                  style={{ marginTop: "10px", marginLeft: "14px" }}
                >
                  Shift Time <span className="text-danger">*</span>
                </label>
              </label>
            </label>
            <select
              name="defaultTimeSlotId"
              value={formData.defaultTimeSlotId}
              onChange={handleChange}
              style={{ width: "100%", padding: "8px" }}
            >
              <option value="">
                <label>
                  <label htmlFor="firstName">
                    <label
                      htmlFor="ticketSubject"
                      className="ticket-text-primary"
                      style={{ marginTop: "10px", marginLeft: "14px" }}
                    >
                      Select Schedule<span className="text-danger"></span>
                    </label>
                  </label>
                </label>
              </option>
              {timeSlots.map((schedule) => (
                <option key={schedule.id} value={schedule.id}>
                  {formatTime(schedule.startTime)} -{" "}
                  {formatTime(schedule.endTime)}
                </option>
              ))}
            </select>
            {errors.defaultTimeSlotId && (
              <span style={errorStyle}>{errors.defaultTimeSlotId}</span>
            )}
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label>
              <label htmlFor="firstName">
                <label
                  htmlFor="ticketSubject"
                  className="ticket-text-primary"
                  style={{ marginTop: "10px", marginLeft: "14px" }}
                >
                  Probation Period <span className="text-danger">*</span>
                </label>
              </label>
            </label>
            {/* <input
              type="text"
              name="probationPeriod"
              value={formData.probationPeriod}
              onChange={handleChange}
              placeholder="Enter Probation Period"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.probationPeriod && (
              <span style={errorStyle}>{errors.probationPeriod}</span>
            )} */}
            <input
              type="text"
              name="probationPeriod"
              value={formData.probationPeriod}
              onChange={(e) => {
                const value = e.target.value;
                if (/^\d*$/.test(value)) {
                  // This allows only digits (no decimals or characters)
                  handleChange(e);
                }
              }}
              placeholder="Enter Probation Period (in days)"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.probationPeriod && (
              <span style={errorStyle}>{errors.probationPeriod}</span>
            )}
          </div>

          <div style={{ marginBottom: "15px" }}>
            <label>
              <label htmlFor="firstName">
                <label
                  htmlFor="ticketSubject"
                  className="ticket-text-primary"
                  style={{ marginTop: "10px", marginLeft: "14px" }}
                >
                  Leave on Days <span className="text-danger">*</span>
                </label>
              </label>
            </label>
            <MultiSelect
              options={daysOfWeek}
              value={leaveOnDays}
              onChange={handleMultiSelectChange}
              labelledBy="Select Days"
              hasSelectAll={true}
              overrideStrings={{
                selectSomeItems: "Select Leave Days",
              }}
              styles={{
                container: (base) => ({
                  ...base,
                  height: "100%",
                  minHeight: "200px",
                }),
                dropdown: (base) => ({
                  ...base,
                  maxHeight: "400px",
                  overflowY: "auto",
                  position: "absolute",
                  width: "100%",
                }),
              }}
            />
            {errors.leaveOnDays && (
              <span
                style={{ color: "red", fontSize: "12px", marginTop: "5px" }}
              >
                {errors.leaveOnDays}
              </span>
            )}
          </div>
        </div>
      </div>
      <div style={formRowStyle}>
        <button type="submit" style={submitButtonStyle}>
          Save
        </button>
      </div>
    </form>
  );
}

const errorStyle = {
  color: "red",
  fontSize: "12px",
  marginTop: "5px",
};

const formRowStyle = {
  width: "100%",
  display: "flex",
  marginBottom: "20px",
};

const submitButtonStyle = {
  width: "150px",
  padding: "10px 20px",
  borderRadius: "4px",
  border: "none",
  backgroundColor: "#007bff",
  color: "#fff",
  fontSize: "16px",
  cursor: "pointer",
};

const inputGroupStyle = {
  flex: "1",
  marginRight: "10px",
  display: "flex",
  flexDirection: "column",
  marginBottom: "13px",
  width: "100%",
};

const inputStyle = {
  padding: "8px",
  borderRadius: "4px",
  border: "1px solid #ccc",
  fontSize: "15px",
};

export default OfficialDetailsForm;
