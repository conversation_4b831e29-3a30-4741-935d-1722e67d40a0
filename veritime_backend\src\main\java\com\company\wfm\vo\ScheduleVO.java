package com.company.wfm.vo;

import java.util.List;

import com.company.wfm.entity.Schedule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleVO {
    private Long id;
    private Long branchId;
    private String branchName;
    private Long departmentId;
    private String departmentName;
    private List<TimeSlotVO> timeSlots;

    public ScheduleVO(Schedule schedule, List<TimeSlotVO> timeSlots) {
        this.id = schedule.getId();
        this.branchId = schedule.getBranch().getId();
        this.branchName = schedule.getBranch().getBranchName();
        this.departmentId = schedule.getDepartment().getDepartmentId();
        this.departmentName = schedule.getDepartment().getDepartmentName();
        this.timeSlots = timeSlots;
    }
}

