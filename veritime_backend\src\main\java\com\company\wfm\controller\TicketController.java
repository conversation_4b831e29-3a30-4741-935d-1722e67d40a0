package com.company.wfm.controller;


import java.io.IOException;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.EmployeeBranchRequestDTO;
import com.company.wfm.dto.EmployeeDepartmentDTO;
import com.company.wfm.dto.EmployeeDetailsDto;
import com.company.wfm.dto.EncryptedRequest;
import com.company.wfm.dto.EncryptedResponse;
import com.company.wfm.dto.TicketAssignmentDTO;
import com.company.wfm.dto.TicketDTO;
import com.company.wfm.dto.TicketDetailsResponseDTO;
import com.company.wfm.dto.TicketEscalationRequestDTO;
import com.company.wfm.dto.TicketEscalationResponseDTO;
import com.company.wfm.dto.TicketFilterRequestDTO;
import com.company.wfm.dto.TicketRespondDTO;
import com.company.wfm.dto.TicketResponseDTO;
import com.company.wfm.dto.TicketUserMappingRequest;
import com.company.wfm.dto.VendorEmployeeRequestDTO;
import com.company.wfm.dto.closeTicketDTO;
import com.company.wfm.entity.Ticket;
import com.company.wfm.entity.Vendor;
import com.company.wfm.repository.TicketUserMappingRepository;
import com.company.wfm.service.AmazonS3Service;
import com.company.wfm.service.TicketAssignmentService;
import com.company.wfm.service.TicketService;
import com.company.wfm.util.EncryptionUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

@RestController
@CrossOrigin(origins = "*")
@RequestMapping("/api/v1/tickets")
public class TicketController {
    private static final Logger logger = LoggerFactory.getLogger(TicketController.class);

    @Autowired
    private TicketService ticketService;

    @Autowired
    private TicketUserMappingRepository ticketUserMappingRepository;

    @Autowired
    private TicketAssignmentService ticketAssignmentService;

    @Autowired AmazonS3Service s3Service;

    @Autowired
    private ObjectMapper objectMapper;

	@Autowired
	private EncryptionUtil encryptionUtil;

    @PostMapping(path = "/create")
    public ResponseEntity<EncryptedResponse> createTicket(
            @RequestParam("ticket") String ticketDTO,
            @RequestPart(value = "files", required = false) MultipartFile[] file) throws Exception {
        try {
            TicketDTO ticketDTO1 = objectMapper.readValue(ticketDTO, TicketDTO.class);
            // Process the ticket creation
            TicketResponseDTO ticketResponse = ticketService.createTicket(ticketDTO1, file);

            // Serialize the response object into JSON
            String responseJson = new Gson().toJson(ticketResponse);

            // Encrypt the response
            String encryptedResponseData = encryptionUtil.encrypt(responseJson,encryptionUtil.generateKey());

            // Return the encrypted response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponseData));
        } catch (RuntimeException e) {

            String errorResponseMessage = e.getMessage();

            // Encrypt the exception message directly
            String encryptedResponseData = encryptionUtil.encrypt(errorResponseMessage,encryptionUtil.generateKey());

            // Return the encrypted response
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new EncryptedResponse(encryptedResponseData));
        } catch (Exception e) {
            // Handle general errors and encrypt the error message
            TicketResponseDTO errorResponse = new TicketResponseDTO(
                    null, null, "Error creating ticket: " + e.getMessage(), null, null, null, null, null);
            String errorResponseJson = new Gson().toJson(errorResponse);
            String encryptedError = encryptionUtil.encrypt(errorResponseJson,encryptionUtil.generateKey());

            // Return the encrypted error response
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse(encryptedError));
        }
    }

    @GetMapping("/list")
    public ResponseEntity<?> listTickets(
            @RequestParam(defaultValue = "0") int offset,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            Pageable pageable = PageRequest.of(offset, limit);
            Page<Ticket> tickets = ticketService.listAllTickets(pageable);
            return ResponseEntity.ok(tickets);
        } catch (Exception e) {
            // Log the error message
            logger.error("Error fetching tickets: {}", e.getMessage(), e);
            // Return a simple error message
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error fetching tickets: " + e.getMessage());
        }

    }
    //same listing post method

    @PostMapping("/list1")
    public ResponseEntity<?> listTickets(@RequestBody TicketFilterRequestDTO ticketFilterRequestDTO) {
        try {
            int offset = ticketFilterRequestDTO.getOffset() == 0 ? 0 : ticketFilterRequestDTO.getOffset();
            int limit = ticketFilterRequestDTO.getLimit() == 0 ? 10 : ticketFilterRequestDTO.getLimit();
            Pageable pageable = PageRequest.of(offset, limit);

            Page<Ticket> tickets;
            // Apply filters based on the request
            tickets = ticketService.listAllTickets1(pageable, ticketFilterRequestDTO);

            return ResponseEntity.ok(tickets);
        } catch (Exception e) {
            logger.error("Error fetching tickets: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error fetching tickets: " + e.getMessage());
        }
    }

    @PostMapping("/genericList")
    public ResponseEntity<?> listFilteredTickets(@RequestBody EncryptedRequest encryptedRequest) {
        try {
            // Create Gson object with adapters
            Gson gson = encryptionUtil.createGson();

            // Extract and decrypt the incoming request
            String decryptedRequest = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());
            TicketFilterRequestDTO filterRequest = gson.fromJson(decryptedRequest, TicketFilterRequestDTO.class);

            // Process the request
            int offset = Math.max(filterRequest.getOffset(), 0);
            int limit = Math.max(filterRequest.getLimit(), 10);
            Pageable pageable = PageRequest.of(offset, limit);
            Page<Ticket> tickets = ticketService.listTicketsWithFilters(pageable, filterRequest);

            // Encrypt the response
            String encryptedResponse = encryptionUtil.encrypt(gson.toJson(tickets),encryptionUtil.generateKey());
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));
        } catch (Exception e) {
            logger.error("Error fetching tickets: {}", e.getMessage(), e);
            try {
                // Encrypt the error message
                String encryptedErrorResponse = encryptionUtil.encrypt("Error fetching tickets: " + e.getMessage(),encryptionUtil.generateKey());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new EncryptedResponse(encryptedErrorResponse));
            } catch (Exception encryptionError) {
                logger.error("Error encrypting error response: {}", encryptionError.getMessage(), encryptionError);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Critical encryption error.");
            }
        }
    }

    @GetMapping("/file/download")
    public void downloadFile(@RequestParam("name") String fileName,
                             HttpServletResponse response) {
        try {
            // Set response headers for file download
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // Stream the file directly to the response output stream
            s3Service.downloadFile(fileName, response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException("Error writing file to response output stream", e);
        }
    }
    //convetting listing in post method
    /**
     *
     * @param ticketId
     * @param ticketDTO
     * @param file
     * @return
     */
    @PutMapping("/update/{ticketId}")
    public ResponseEntity<TicketResponseDTO> updateTicket(
            @PathVariable Long ticketId,
            @RequestBody TicketDTO ticketDTO,
            @RequestParam(value = "file", required = false) MultipartFile file) {

        try {

            TicketResponseDTO ticketResponse = ticketService.updateTicket(ticketId, ticketDTO, file);

            return ResponseEntity.ok(ticketResponse);
        } catch (Exception e) {
            // Return an error response with a message
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new TicketResponseDTO(null, null, "Error creating ticket: " + e.getMessage(), null, null, null,null,null));

        }
    }

    @PostMapping("/department_branch_user/save")
    public ResponseEntity<String> saveMappings(@Valid @RequestBody TicketUserMappingRequest request) {
        try {
            ticketService.saveMappings(request.getBranchId(), request.getDepartmentId(), request.getUserIds());
            return new ResponseEntity<>("Mappings saved successfully", HttpStatus.CREATED);
        } catch (IllegalArgumentException e) {
            // Return 400 Bad Request with the specific message
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            // Catch any other exceptions and return a generic error message if needed
            return new ResponseEntity<>("An unexpected error occurred", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping("/employees/branches")
    public ResponseEntity<?> getAllEmployeesByDepartmentBranches(
            @RequestBody EmployeeBranchRequestDTO request) {
        try {
            // Set default values if not provided
            int offset = (request.getOffset() != 0) ? request.getOffset() : 0;
            int limit = (request.getLimit() != 0) ? request.getLimit() : 10;
            String type = request.getType();

            Pageable pageable = PageRequest.of(offset, limit);
            Page<EmployeeDepartmentDTO> employees = ticketService.getAllEmployeesByDepartmentBranch(pageable, type);
            return ResponseEntity.ok(employees);
        }catch (RuntimeException e) {
            // Handle specific runtime exceptions
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "An unexpected error occurred."));
        }
    }

    @PostMapping("/empVendorList")
    public ResponseEntity<?> getList(@RequestBody VendorEmployeeRequestDTO request) {

        // Default values for offset and limit are handled by the DTO
        int offset = request.getOffset();
        int limit = request.getLimit();
        String type = request.getType();

        Pageable pageable = PageRequest.of(offset, limit);

        try {
            if ("internal".equalsIgnoreCase(type)) {
                Page<EmployeeDetailsDto> employeePage = ticketService.getAllEmployees(request,pageable);
                return ResponseEntity.ok(employeePage);
            } else if ("external".equalsIgnoreCase(type)) {
                Page<Vendor> vendors = ticketService.getAllVendors(pageable);
                return ResponseEntity.ok(vendors);
            } else {
                return ResponseEntity.badRequest().body("Invalid type. Use 'internal' or 'external'.");
            }
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while processing the request: " + e.getMessage());
        }
    }


    //aggign api save

   /* @PostMapping("/assign")
    public ResponseEntity<String> assignTicket(@RequestBody TicketAssignmentDTO ticketAssignmentDTO) {
        logger.info("Received TicketAssignmentDTO: {}", ticketAssignmentDTO);

        try {
            ticketAssignmentService.assignTicket(ticketAssignmentDTO);
            return new ResponseEntity<>("Ticket assigned successfully", HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>("Error assigning ticket: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }*/


    @PostMapping("/assign")
    public ResponseEntity<EncryptedResponse> assignTicket(@RequestBody EncryptedRequest encryptedRequest) {
        try {
            logger.info("EncryptedRequest received: {}", encryptedRequest);
            // Decrypt the incoming encrypted request
            String decryptedRequest = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());
            logger.info("Decrypted Request: {}", decryptedRequest);

            // Parse the decrypted request into TicketAssignmentDTO
            Gson gson = encryptionUtil.createGson();
            TicketAssignmentDTO ticketAssignmentDTO = gson.fromJson(decryptedRequest, TicketAssignmentDTO.class);

            // Log the decrypted DTO
            logger.info("Received TicketAssignmentDTO: {}", ticketAssignmentDTO);

            // Perform the ticket assignment logic
            ticketAssignmentService.assignTicket(ticketAssignmentDTO);

            // Prepare and encrypt the success response
            String responseMessage = "Ticket assigned successfully";
            String encryptedResponse = encryptionUtil.encrypt(responseMessage,encryptionUtil.generateKey());

            return new ResponseEntity<>(new EncryptedResponse(encryptedResponse), HttpStatus.OK);
        } catch (Exception e) {
            // Log the exception
            logger.error("Error assigning ticket: {}", e.getMessage(), e);

            try {
                // Prepare and encrypt the error response
                String errorMessage = "Error assigning ticket: " + e.getMessage();
                String encryptedErrorResponse = encryptionUtil.encrypt(errorMessage,encryptionUtil.generateKey());
                return new ResponseEntity<>(new EncryptedResponse(encryptedErrorResponse), HttpStatus.INTERNAL_SERVER_ERROR);
            } catch (Exception encryptionError) {
                logger.error("Error encrypting error response: {}", encryptionError.getMessage(), encryptionError);
                return new ResponseEntity<>(new EncryptedResponse("Critical encryption error."), HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
    }



   /* @PostMapping("/getById")
    public ResponseEntity<TicketDetailsResponseDTO> getTicketById(@RequestBody Map<String, Long> request) {
        Long ticketId = request.get("ticketId");

        Optional<TicketDetailsResponseDTO> ticketDetails = ticketService.getTicketDetails(ticketId);

        return ticketDetails
                .map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.notFound().build());
    }*/

    @PostMapping("/getById")
    public ResponseEntity<Map<String, String>> getTicketById(@RequestBody EncryptedRequest encryptedRequest) throws Exception {
        try {
            // Decrypt the request body to get the ticketId JSON string
            String decryptedRequest = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

            // Parse the decrypted JSON to extract ticketId as Long
            Gson gson = encryptionUtil.createGson(); // Ensure Gson is properly set up for date-time serialization
            Map<String, Object> requestMap = gson.fromJson(decryptedRequest, Map.class);

            // Extract and validate ticketId
            Object ticketIdObj = requestMap.get("ticketId");
            Long ticketId = null;

            if (ticketIdObj instanceof Double) {
                ticketId = ((Double) ticketIdObj).longValue();
            } else if (ticketIdObj instanceof Number) {
                ticketId = ((Number) ticketIdObj).longValue();
            }

            if (ticketId == null) {
                String errorResponse = encryptionUtil.encrypt("Invalid ticketId format",encryptionUtil.generateKey());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("encryptedData", errorResponse));
            }

            // Fetch ticket details
            Optional<TicketDetailsResponseDTO> ticketDetails = ticketService.getTicketDetails(ticketId);

            // Handle response
            if (ticketDetails.isPresent()) {
                // Serialize the ticket details to JSON
                String responseJson = gson.toJson(ticketDetails.get());

                // Encrypt the serialized JSON response
                String encryptedResponse = encryptionUtil.encrypt(responseJson,encryptionUtil.generateKey());

                // Return the encrypted response
                return ResponseEntity.ok(Map.of("encryptedData", encryptedResponse));
            } else {
                String notFoundResponse = encryptionUtil.encrypt("Ticket not found",encryptionUtil.generateKey());
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("encryptedData", notFoundResponse));
            }
        } catch (Exception e) {
            // Encrypt and return error message
            String encryptedError = encryptionUtil.encrypt("Error processing the request: " + e.getMessage(),encryptionUtil.generateKey());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("encryptedData", encryptedError));
        }
    }

    @GetMapping("/exists")
    public ResponseEntity<Boolean> checkUserExists(
            @RequestParam Long userId,
            @RequestParam Long branchId,
            @RequestParam Long departmentId) {

        boolean isSupportPerson = ticketUserMappingRepository.existsByUser_IdAndBranch_IdAndDepartment_DepartmentId(userId, branchId, departmentId);
        return ResponseEntity.ok(isSupportPerson);
    }

    @PostMapping("/respond")
    public ResponseEntity<EncryptedResponse> respondAndCloseTicket(
            @RequestParam("ticket") String ticketRespondDTO,
            @RequestPart(value = "files", required = false) MultipartFile[] file,
            HttpServletRequest httpRequest) throws Exception {
        try {
            String frontendUrl = httpRequest.getHeader("Referer");
            if (frontendUrl == null) {
                frontendUrl = httpRequest.getHeader("Origin");
            }

            if (frontendUrl != null) {
                logger.info("Request received from frontend URL: {}", frontendUrl);
            } else {
                logger.warn("Unable to detect frontend URL (No Referer or Origin header).");
            }

            // Add frontend URL to HttpServletRequest as an attribute
            httpRequest.setAttribute("frontendUrl", frontendUrl);
            TicketRespondDTO ticketRespondDTO1 = objectMapper.readValue(ticketRespondDTO, TicketRespondDTO.class);

            Long ticketId = ticketRespondDTO1.getTicketId();
            String responseRemark = ticketRespondDTO1.getResponseRemark();
            String status = ticketRespondDTO1.getStatus();
            boolean notifyCreator = ticketRespondDTO1.isNotifyCreator();
            boolean escalate = ticketRespondDTO1.isEscalate();

            String successMessage = ticketAssignmentService.respondAndCloseInternalTicket(
                    ticketId, responseRemark, status, notifyCreator, file, escalate, httpRequest);

            // Encrypt the success message
            String encryptedResponse = encryptionUtil.encrypt(successMessage,encryptionUtil.generateKey());

            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));
        } catch (Exception e) {
            logger.error("Error while responding to ticket: {}", e.getMessage());

            // Encrypt the error message
            String encryptedError = encryptionUtil.encrypt("Error while responding to ticket: " + e.getMessage(),encryptionUtil.generateKey());

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse(encryptedError));
        }
    }

    @PostMapping("/close")
    public ResponseEntity<String> closeTicketByHR(@RequestBody closeTicketDTO closeTicketDTO) {
        try {
            Long ticketId = closeTicketDTO.getTicketId();
            String finalRemark = closeTicketDTO.getFinalRemark();
            ticketAssignmentService.closeTicketByHR(ticketId, finalRemark);
            return ResponseEntity.ok("Ticket closed successfully.");
        } catch (Exception e) {
            logger.error("Error while closing ticket: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error while closing ticket: " + e.getMessage());
        }
    }

    @PostMapping("/escalate")
    public ResponseEntity<EncryptedResponse> escalateTicket(@RequestBody EncryptedRequest encryptedRequest, HttpServletRequest httpRequest) {
        try {
            // Decrypt the incoming encrypted request
            String decryptedRequest = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());
            logger.info("Decrypted Request1: {}", decryptedRequest);

            // Parse the decrypted request into TicketEscalationRequestDTO
            Gson gson = encryptionUtil.createGson();
            TicketEscalationRequestDTO request = gson.fromJson(decryptedRequest, TicketEscalationRequestDTO.class);
            logger.info("Parsed TicketEscalationRequestDTO: {}", request);

            // Extract frontend URL from the headers
            String frontendUrl = httpRequest.getHeader("Referer");
            if (frontendUrl == null) {
                frontendUrl = httpRequest.getHeader("Origin");
            }

            if (frontendUrl != null) {
                logger.info("Request received from frontend URL1: {}", frontendUrl);
            } else {
                logger.warn("Unable to detect frontend URL1 (No Referer or Origin header).");
            }

            // Add frontend URL to HttpServletRequest as an attribute
            httpRequest.setAttribute("frontendUrl", frontendUrl);

            // Perform ticket escalation logic
            ticketService.escalateTicket(request, httpRequest);

            // Prepare and encrypt the success response
            String responseMessage = "Ticket escalated successfully";
            String encryptedResponse = encryptionUtil.encrypt(responseMessage,encryptionUtil.generateKey());
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));

        } catch (EntityNotFoundException ex) {
            logger.error("Entity not found: {}", ex.getMessage(), ex);
            return handleEncryptedErrorResponse(HttpStatus.NOT_FOUND, ex.getMessage());
        } catch (IllegalArgumentException ex) {
            logger.error("Invalid argument: {}", ex.getMessage(), ex);
            return handleEncryptedErrorResponse(HttpStatus.BAD_REQUEST, ex.getMessage());
        } catch (Exception ex) {
            logger.error("Unexpected error occurred while escalating the ticket", ex);
            return handleEncryptedErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "An unexpected error occurred. Please try again later.");
        }
    }

    private ResponseEntity<EncryptedResponse> handleEncryptedErrorResponse(HttpStatus status, String errorMessage) {
        try {
            String encryptedErrorResponse = encryptionUtil.encrypt(errorMessage,encryptionUtil.generateKey());
            return ResponseEntity.status(status).body(new EncryptedResponse(encryptedErrorResponse));
        } catch (Exception encryptionError) {
            logger.error("Error encrypting error response: {}", encryptionError.getMessage(), encryptionError);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse("Critical encryption error."));
        }
    }

    @PostMapping("/escalationList")
    public ResponseEntity<EncryptedResponse> getTickets(@RequestBody EncryptedRequest encryptedRequest) {
        try {
            // Decrypt the incoming encrypted request
            String decryptedRequest = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());
            logger.info("Decrypted Request2: {}", decryptedRequest);
            // Parse the decrypted request into TicketFilterRequestDTO
            Gson gson = encryptionUtil.createGson();
            TicketFilterRequestDTO ticketFilterRequestDTO = gson.fromJson(decryptedRequest, TicketFilterRequestDTO.class);
            logger.info("Parsed TicketFilterRequestDTO: {}", ticketFilterRequestDTO);
            // Extract pagination parameters from the request
            int size = ticketFilterRequestDTO.getLimit(); // Page size
            int page = ticketFilterRequestDTO.getOffset(); // Page number (starting from 0)

            // Log the pagination parameters (can be removed later)
            logger.info("Page: {}, Size: {}", page, size);

            // Call the service to get paginated tickets
            Page<TicketEscalationResponseDTO> tickets = ticketService.getTicketsWithDetailsByEmployeeId(page, size);
            logger.info("Parsed tickets: {}", tickets);
            // Convert the tickets page to JSON and encrypt the response
            String ticketsJson = gson.toJson(tickets);
            String encryptedResponse = encryptionUtil.encrypt(ticketsJson,encryptionUtil.generateKey());

            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));

        } catch (IllegalArgumentException e) {
            logger.error("Invalid request: {}", e.getMessage(), e);

            try {
                // Encrypt the error response
                String encryptedErrorResponse = encryptionUtil.encrypt("Invalid request: " + e.getMessage(),encryptionUtil.generateKey());
                return ResponseEntity.badRequest().body(new EncryptedResponse(encryptedErrorResponse));
            } catch (Exception encryptionError) {
                logger.error("Error encrypting error response: {}", encryptionError.getMessage(), encryptionError);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new EncryptedResponse("Critical encryption error."));
            }

        } catch (Exception e) {
            logger.error("An error occurred while processing the request: {}", e.getMessage(), e);

            try {
                // Encrypt the generic error response
                String encryptedErrorResponse = encryptionUtil.encrypt("An error occurred while processing the request: " + e.getMessage(),encryptionUtil.generateKey());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new EncryptedResponse(encryptedErrorResponse));
            } catch (Exception encryptionError) {
                logger.error("Error encrypting error response: {}", encryptionError.getMessage(), encryptionError);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new EncryptedResponse("Critical encryption error."));
            }
        }
    }



}
