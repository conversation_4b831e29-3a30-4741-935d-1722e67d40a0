import React from 'react';
import { Line } from 'react-chartjs-2';
import 'chart.js/auto';

const TotalDepartmentsChart = (value) => {
  const labels = [...new Set(value?.data?.map(item => item.name))];

  const hospitals = {};
  value?.data?.forEach(item => {
    if (!hospitals[item.hospitalName]) {
      hospitals[item.hospitalName] = {};
    }
    hospitals[item.hospitalName][item.name] = item.value;
  });

  const datasets = Object.keys(hospitals).map((hospitalName, index) => {
    const colors = [
      'rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)',
      'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'
    ];
    const backgroundColors = [
      'rgba(54, 162, 235, 0.2)', 'rgba(255, 99, 132, 0.2)',
      'rgba(75, 192, 192, 0.2)', 'rgba(153, 102, 255, 0.2)'
    ];

    return {
      label: hospitalName,
      data: labels.map(label => hospitals[hospitalName][label] || 0),
      borderColor: colors[index % colors.length],
      backgroundColor: backgroundColors[index % backgroundColors.length],
      fill: true,
      tension: 0.4,
    };
  });

  // Final data structure for the chart
  const data = {
    labels: labels,
    datasets: datasets,
  };



  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Total Departments',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Departments',
        },
      },
    },
  };

  return (
    <div className="col col-12 col-lg-12 col-xl-12 col-md-12" style={{ marginBottom: "20px" }}>
      <div className="card shadow-sm card-department" style={{ width: '100%' }}>
        <div className="card-body">
          <div style={{ height: '300px', width: '100%' }}>
            <Line data={data} options={options} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TotalDepartmentsChart;