package com.company.wfm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "t_employee_leave_history")
public class EmployeeLeaveHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "EMP_LEAVE_ID")
    private Long empLeaveId;

   /* @Column(name = "EMP_ID")
    private Long empId;*/

    @Column(name = "EMP_ID")
    private Long empId;

    //@ManyToOne(fetch = FetchType.EAGER)
    //@JoinColumn(name = "EMP_ID", nullable = false)
    //private Employee employee;

    @Column(name = "APPLIED_LEAVE_COUNT")
    private Integer appliedLeaveCount;

    @Column(name = "START_DATE")
    private LocalDate startDate;

    @Column(name = "END_DATE")
    private LocalDate endDate;

    @Column(name = "APPROVAL_STATUS")
    private String approvalStatus;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "CREATED_TIME")
    private LocalDateTime createdTime;
    @Column(name = "REASON")  // Add this new field
    private String reason;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Column(name = "UPDATED_TIME")
    private LocalDateTime updatedTime;
    @Column(name = "COMMENT")
    private String comment;


    @Column(name = "FILE_PATH")
    private String filePath; // Add the file path field

    @Column(name = "LEAVE_ID", nullable = true)
    private Long leaveId;

    @Column(name = "LEAVE_COUNT", nullable = true)
    private Integer leaveCount;




}
