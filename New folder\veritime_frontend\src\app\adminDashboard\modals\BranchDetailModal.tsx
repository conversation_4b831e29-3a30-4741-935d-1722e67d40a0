import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

interface BranchDetailModalProps {
  show: boolean;
  handleClose: () => void;
  branchDetails: {
    branch: string;
    code: string;
    branchHeadName: string;
    branchType: string;
    hospitalName: string;
    department: string;
    timeZone: string;
    leaveCreditDay: string;
  };
}

const BranchDetailModal: React.FC<BranchDetailModalProps> = ({
  show,
  handleClose,
  branchDetails,
}) => {
  return (
    <Modal show={show} onHide={handleClose} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>Facility Details</Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ overflowY: "auto", maxHeight: "400px" }}>
        <div style={{ padding: "10px" }}>
          {/* Hospital Name */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Hospital Name
            </div>
            <div>{branchDetails.hospitalName || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Facility Name */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Facility Name
            </div>
            <div>{branchDetails.branch || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Facility Code */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Facility Code
            </div>
            <div>{branchDetails.code || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Facility Head */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Facility Head
            </div>
            <div>{branchDetails.branchHeadName || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Facility Type */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Facility Type
            </div>
            <div>{branchDetails.branchType || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />
          {/* Time Zone */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Time Zone
            </div>
            <div>{branchDetails.timeZone || ""}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Leave Credit Day
            </div>
            <div>{branchDetails.leaveCreditDay || ""}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />
          {/* Department Name
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Department Name:
            </div>
            <div>{branchDetails.department || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />
        </div> */}

          {/* Department Name Section with Same Layout as Associate Facilities */}
          <div style={{ marginBottom: "10px" }}>
            <div
              style={{
                fontWeight: "bold",
                width: "150px",
                whiteSpace: "nowrap",
              }}
            >
              Associate Departments
            </div>
            <div
              style={{
                maxHeight: "150px", // Set a fixed height for scrolling
                overflowY: "auto", // Enable vertical scroll
                padding: "5px",
                marginTop: "5px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "5px", // Space between tags
                }}
              >
                {/* Split and map each department name to render it as an individual tag */}
                {branchDetails.department ? (
                  branchDetails.department
                    .split(",")
                    .map((department, index) => (
                      <div
                        key={index}
                        style={{
                          backgroundColor: "#e0e0e0",
                          borderRadius: "8px",
                          padding: "5px 10px",
                          fontSize: "14px",
                          color: "#333",
                          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                        }}
                      >
                        {department.trim()} {/* Trims any extra spaces */}
                      </div>
                    ))
                ) : (
                  <div>N/A</div>
                )}
              </div>
            </div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={handleClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default BranchDetailModal;
