package com.company.wfm.dto;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TicketEscalationResponseDTO {

    private Long ticketId;
    private String ticketCode;
    private Long empId;
    private String ticketSubject;
    private String ticketMessage;
    private String status;
    private String category;
    private String categoryName;
    private String filePath;
    private Long createdBy;
    private LocalDateTime createdTime;
    private Long updatedBy;
    private LocalDateTime updatedTime;
    private Long departmentId;
    private Long assignedBy;
    private Long assignedTo;
    private String internalTicketStatus;
    private String latestRemark;
    private String currentType;
    private LocalDateTime lastEscalationTime;
}
