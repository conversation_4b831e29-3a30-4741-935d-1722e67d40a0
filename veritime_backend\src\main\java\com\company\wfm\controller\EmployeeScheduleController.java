package com.company.wfm.controller;

import java.time.LocalDate;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.ShiftChangeDto;
import com.company.wfm.entity.EmployeeSchedule;
import com.company.wfm.service.impl.EmployeeScheduleService;
@RestController
@RequestMapping("/api/employee-schedules")
@CrossOrigin(origins = "*")
public class EmployeeScheduleController {
    @Autowired
    private EmployeeScheduleService employeeScheduleService;

    @GetMapping
    public ResponseEntity<List<EmployeeSchedule>> getSchedules(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<EmployeeSchedule> schedules = employeeScheduleService.getSchedulesForDateRange(startDate, endDate);
        return ResponseEntity.ok(schedules);
    }

    @PostMapping("/create-schedules")
    public ResponseEntity<String> createSchedulesImmediately() {
        employeeScheduleService.createSchedulesForNextFifteenDays();
        return ResponseEntity.ok("Schedules created successfully");
    }
    @PostMapping("/change-shift")
    public ResponseEntity<EmployeeSchedule> changeShift(@RequestBody ShiftChangeDto request) {
        EmployeeSchedule updatedSchedule = employeeScheduleService.changeEmployeeShift(request);
        return ResponseEntity.ok(updatedSchedule);
    }
}
