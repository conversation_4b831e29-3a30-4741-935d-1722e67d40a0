package com.company.wfm.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventCertificateRecordResponse {

    private EventCertificateData data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EventCertificateData {
        private int totalNum;
        private int pageIndex;
        private int pageSize;
        private List<EventRecord> recordList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EventRecord {
        private String recordGuid;
        private String elementId;
        private String elementName;
        private String elementPicUrl;
        private int elementType;
        private String areaId;
        private String areaName;
        private String deviceId;
        private String deviceName;
        private List<AcsSnapPic> acsSnapPicList;
        private String associatedCameraList;
        private String cardReaderId;
        private String cardReaderName;
        private String occurTime;
        private String deviceTime;
        private String cardNumber;
        private int eventType;
        private int swipeAuthResult;
        private int eventMainType;
        private int devSerialNo;
        private int hasCameraSnapPic;
        private int hasDevVideoRecord;
        private int masksStatus;
        private int attendanceStatus;
        private PersonInfo personInfo;
        private TemperatureInfo temperatureInfo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AcsSnapPic {
        private String snapPicUrl;
        private int snapPicType;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PersonInfo {
        private String id;
        private BaseInfo baseInfo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BaseInfo {
        private String fullPath;
        private String firstName;
        private String lastName;
        private String personCode;
        private String phoneNum;
        private String photoUrl;
        private String gender;
        private String email;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TemperatureInfo {
        private String temperatureData;
        private int temperatureStatus;
        private int temperatureUnit;
    }
}
