{"name": "workforce-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "dev:local": "cross-env NEXT_PUBLIC_ENV=local  NODE_ENV=local next dev --port 3001", "dev:staging": "cross-env NEXT_PUBLIC_ENV=staging  NODE_ENV=development next dev --port 3001", "dev:prod": "cross-env NEXT_PUBLIC_ENV=production  NODE_ENV=production next dev --port 3001", "build": "next build", "start": "next start", "start:local": "cross-env NEXT_PUBLIC_ENV=local  NODE_ENV=local next start", "start:prod": "cross-env NEXT_PUBLIC_ENV=production  NODE_ENV=production next start", "lint": "next lint", "export": "next export"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@floating-ui/core": "^1.6.9", "@floating-ui/dom": "^1.6.13", "@material-ui/core": "^4.12.4", "@mui/icons-material": "6.1.5", "@mui/material": "6.1.5", "@mui/styles": "^6.1.5", "@mui/system": "6.1.5", "@mui/x-data-grid": "^7.22.0", "axios": "^1.7.5", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.4", "crypto-js": "^4.2.0", "eslint-config-next": "^15.0.1", "firebase": "^11.3.1", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "next": "^15.0.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-bootstrap": "^2.10.4", "react-chartjs-2": "^5.2.0", "react-date-picker": "^11.0.0", "react-datepicker": "^7.3.0", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-moment": "^1.1.3", "react-multi-date-picker": "^4.5.2", "react-multi-select-component": "^4.3.4", "react-onesignal": "^3.0.1", "react-router-dom": "^6.26.0", "react-select": "^5.8.2", "react-slick": "^0.30.2", "react-swipeable-views": "^0.14.0", "slick-carousel": "^1.8.1", "sweetalert2": "^11.6.13"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-swipeable-views": "^0.13.5", "cross-env": "^7.0.3", "typescript": "^5"}}