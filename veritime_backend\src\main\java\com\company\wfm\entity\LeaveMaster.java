package com.company.wfm.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.company.wfm.util.LeaveCreditBasis;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonSetter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;

@Entity
@Data
@Table(name = "t_leave_master")
public class LeaveMaster {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "LEAVE_ID")
    private Long leaveId;

    @Column(name = "TYPE")
    private String type;

    @Column(name = "LEAVE_COUNT")
    private BigDecimal leaveCount;

    @Column(name = "CREATED_BY") private Long createdBy;

    @Column(name = "CREATED_TIME")
    private LocalDateTime createdTime;

    @Column(name = "UPDATED_BY")
    private Long updatedBy;

    @Column(name = "UPDATED_TIME")
    private LocalDateTime updatedTime;

    @Column(name = "VERSION")
    private Integer version;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "DAYS_APPLIED_BEFORE")
    private Integer daysAppliedBefore;

    @Column(name = "IS_ACTIVE")
    private Boolean isActive = true;

    // New fields
    @Column(name = "is_allowFileUpload")
    private Boolean allowFileUpload;

    @Column(name = "MINIMUM_COUNT")
    private Integer minimumCount;


    @Column(name = "LEAVE_CREDIT_METHOD")
    private String leaveCreditMethod;

    @Column(name = "LEAVE_CREDIT_BASIS")
    private String leaveCreditBasis;

    @Column(name = "CARRY_FORWARD")
    private Boolean carryForward;

    @Column(name = "LEAVE_CREDIT_INTERVAL")
    private String leaveCreditInterval;

    @Transient
    private BigDecimal leaveCredited;

    @Column(name = "LEAVE_EXCLUSION")
    private String leaveExclusion;


    // Newly added fields
    @Column(name = "EFFECTIVE_LEAVE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate effectiveLeaveDate;

    @Column(name = "DEEFECTIVE_LEAVE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate deefectiveLeaveDate;

    // Convert from List<String> to a comma-separated String for DB storage
    @JsonSetter("leaveExclusion")
    public void setLeaveExclusionList(List<String> leaveExclusionList) {
        this.leaveExclusion = String.join(",", leaveExclusionList);
    }
    // Convert from comma-separated String to List<String> for API response
    @JsonGetter("leaveExclusion")
    public List<String> getLeaveExclusionList() {
        return leaveExclusion != null ? Arrays.asList(leaveExclusion.split(",")) : new ArrayList<>();
    }


    @Transient
    private String createdByName;

    @Transient
    private String updatedByName;

    public LeaveCreditBasis getLeaveCreditBasisEnum() {
        return LeaveCreditBasis.fromString(leaveCreditBasis);
    }

    @Column(name = "LEAVE_ELIGIBILITY_RULES")
    private String leaveEligibilityRules;
}
