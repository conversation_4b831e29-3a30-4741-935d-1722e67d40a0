package com.company.wfm.dto;

import java.time.LocalDate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class TicketFilterRequestDTO {

    private int offset = 0;
    private int limit = 10;
    private Long departmentId; // For department filter
    private String myDepartmentTicket; // "createdByMe" or "assignedToMe"
    private String status; // Status filter
    private Long assignedTo; // Assigned to filter
    private Long createdBy; // Created by filter
    private String query; // Open text filter
    private Long employeeId;



  //  private String createdBy; // "me" or "other"
 //   private String assignedTo; // "me" or "other"
    //private String status; // e.g., "open", "closed", etc.
    private String categoryId; // ID of the category
    private LocalDate fromDate;
    private LocalDate toDate;


}
