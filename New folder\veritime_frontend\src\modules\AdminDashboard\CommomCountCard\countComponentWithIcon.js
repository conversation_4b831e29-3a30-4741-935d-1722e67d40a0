import Link from "next/link";
import React, { useState } from "react";
import { Card } from "react-bootstrap";
import "./countComponent.css";
import ViewWidget from "./PopupModal";
import { API_URLS } from "../../../constants/apiConstants";
import axios from "axios";
import { encryptData, decryptData } from "@/utils/encryption";
import { getRequest } from "@/services/apiService";
import { IoMdDownload } from "react-icons/io";
import { IoEyeSharp } from "react-icons/io5";
import PropTypes from "prop-types"; // Import PropTypes
const CountComponent1 = ({ handleViewClick, data, isDownload }) => {
  const [hover, setHover] = useState(false);
  const onMouseEnter = (e) => {
    //console.log(data.name,"download:",isDownload)
    if (isDownload != null) setHover(true);
  };

  const handleDownloadClick = () => {
    //console.log(1)
    //const token = "eyJhbGciOiJIUzM4NCJ9.**********************************************************************************************.PycMB2HhajrPngaqilzDqbvdans0V9Oa8beawNiaf2p2IsUxiFN_1dN5ydGv9IVh"
    const token =localStorage.getItem("accessToken");
    try {
      const response = axios.post(
        API_URLS.DOWNLOAD_WIDGET_DATA(isDownload, true), 
        {},
        {
          responseType: "blob",
          headers: { Authorization: `Bearer ${token}` },
          // Important for handling binary data
        }
      );

      response.then((response) => {
        //const disposition = response.headers;
        //console.log("hi", response);
        handleExport(response);
        //console.log("Is Blob:", response instanceof Blob);
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleExport = async (response) => {
    try {
      // // Try to get the filename from the content-disposition header
      // const disposition = response.headers["content-disposition"];

      let filename = "widget_data.xlsx"; // default filename

      // if (disposition && disposition.includes("filename=")) {
      //   const match = disposition.match(/filename="?([^"]+)"?/);
      //   if (match && match[1]) {
      //     filename = match[1];
      //   }
      // }

      const blob = new Blob([response.data], {
        type: "application/octet-stream",
      });

      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.download = filename;
      a.click();
      window.URL.revokeObjectURL(urlBlob);
    } catch (error) {
      console.error("Error exporting file:", error);
    }
  };

  return (
    <div className="col-12 col-md-6 col-lg-3 mb-3">
      {/* <Link href={data?.onClickPath || "#"}> */}
      {/* <Link href={"./ticket/viewTickets"}> */}
      {/* <Link href="/attendance?tab=TeamAttendance"> */}
      {/* {console.log("test", isDownload == "")} */}
      <Card className="card shadow-sm">
        <Card.Body
          onMouseEnter={(e) => onMouseEnter()}
          onMouseLeave={() => setHover(false)}
          style={
            hover
              ? {
                  height: 270,
                  cursor: "pointer",
                  backgroundColor: "rgb(0, 0, 0,0.1)",
                }
              : { height: 270, cursor: "pointer" }
          }
          className="d-flex flex-column justify-content-center align-items-center"
        >
          {!hover ? (
            <Link href={data?.onClickPath || "/attendance?tab=TeamAttendance"}>
              <div
                style={hover ? { opacity: "0.8" } : {}}
                className="d-flex align-items-center mb-2"
              >
                <div
                  style={{
                    backgroundColor: data?.backgroundColor || "#FFD8D8",
                    padding: 15,
                    borderRadius: 60,
                    marginRight: 15,
                  }}
                >
                  <img
                    src={data?.icon}
                    alt={`${data?.name} Icon`}
                    className="foreground-icon"
                  />
                </div>
                <div className="h2 mb-0">{data?.count ?? 0}</div>
              </div>
              <div className="small text-muted text-center">{data?.name}</div>
            </Link>
          ) : (
            <>
              <div
                style={hover ? { opacity: "0.5" } : {}}
                className="d-flex align-items-center mb-2"
              >
                <div
                  style={{
                    backgroundColor: data?.backgroundColor || "#FFD8D8",
                    padding: 15,
                    borderRadius: 60,
                    marginRight: 15,
                  }}
                >
                  <img
                    src={data?.icon}
                    alt={`${data?.name} Icon`}
                    className="foreground-icon"
                  />
                </div>
                <div className="h2 mb-0">{data?.count ?? 0}</div>
              </div>
              <div className="small text-muted text-center">{data?.name}</div>
            </>
          )}

          {hover && (
            <div className="btnContainer">
              <button className="btn" onClick={handleViewClick}>
                <IoEyeSharp size={30} />
              </button>
              <button className="btn" onClick={handleDownloadClick}>
                <IoMdDownload size={30} />
              </button>
            </div>
          )}
        </Card.Body>
      </Card>
    </div>
  );
};

// Declare prop types
CountComponent1.propTypes = {
  handleViewClick: PropTypes.func.isRequired,
  data: PropTypes.shape({
    onClickPath: PropTypes.string,
    backgroundColor: PropTypes.string,
    icon: PropTypes.string,
    count: PropTypes.number,
    name: PropTypes.string,
  }).isRequired,
  isDownload: PropTypes.oneOfType([PropTypes.string, PropTypes.oneOf([null])]),
};

export default CountComponent1;
