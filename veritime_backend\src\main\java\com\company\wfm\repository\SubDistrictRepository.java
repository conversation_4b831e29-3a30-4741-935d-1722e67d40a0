package com.company.wfm.repository;


import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.District;
import com.company.wfm.entity.SubDistrict;

@Repository
public interface SubDistrictRepository extends JpaRepository<SubDistrict, Long> {
    Optional<SubDistrict> findByNameAndDistrict(String name, District cluster);
    Optional<SubDistrict> findByName(String name);
}

