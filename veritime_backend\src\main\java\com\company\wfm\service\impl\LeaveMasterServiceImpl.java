package com.company.wfm.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.company.wfm.entity.LeaveMaster;
import com.company.wfm.repository.LeaveMasterRepository;
import com.company.wfm.service.LeaveMasterService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class LeaveMasterServiceImpl implements LeaveMasterService {

	@Autowired
	private LeaveMasterRepository leaveMasterRepository;

	 /**
	  *  Step 1: Evict the cache
	  *  
	  */
	@CacheEvict(value = "leaveTypes", allEntries = true)
	@Override
	public void evictLeaveTypesCache() {
		log.info("Cache evicted: leaveTypes");
	}

	/**
	 *  Step 2: Repopulate the cache
	 */
	@Cacheable("leaveTypes")
	@Override
	public List<LeaveMaster> getAllLeaveTypes() {
		log.info("Fetching fresh leave types from DB...");
		return leaveMasterRepository.findAllActiveLeaveTypes();
	}

	/**
	 * Step 3: Evict and reload
	 */
	public void refreshLeaveTypesCache() {
		evictLeaveTypesCache(); // Clears cache
		getAllLeaveTypes(); // Fetches fresh data & caches it
	}
}
