package com.company.wfm.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class PersonDTO {

	private String id; // Optional: Person ID (not required for adding new residents)

	private String personCode; // Optional: Person code (leave empty for updates)

	private String groupId; // Optional: Department ID for adding/updating person information

	@NotBlank(message = "First name is required.")
	private String firstName; // Required: Person's first name (max length: 128)

	@NotBlank(message = "Last name is required.")
	private String lastName; // Required: Person's last name (max length: 128)

	@NotNull(message = "Gender is required.")
	private Integer gender; // Required: 0 (female), 1 (male), 2 (unknown)

	private String phone; // Optional: Phone number

	@NotBlank(message = "Email is required.")
	@Email(message = "Email should be valid.")
	private String email; // Required: Email

	private String description; // Optional: Remark

	@NotBlank(message = "Start date is required.")
	private String startDate; // Required: Start time in ISO 8601 format

	@NotBlank(message = "End date is required.")
	private String endDate; // Required: End time in ISO 8601 format
}
