"use client";
import React, { useState, useEffect } from "react";
//import "bootstrap/dist/css/bootstrap.min.css";

import "../../../ProfileDetails/Profiledetails.css";
import Layout from "@/components/Layout";
import { colors } from "@constants/colors";
import {
  fileDownload,
  getRequest,
  getRequestWithSecurity,
} from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import Avatar from "@/components/Avatar";

interface EmployeeData {
  [x: string]: any;
  empName?: string;
  email?: string;
  imgUre?: string;
  inService?: boolean;
  birthday?: string;
  gender?: string;
  married?: boolean;
  mobileNo?: string;
  alternateNumber?: string;
  alternateEmail?: string;
  nation?: string;
  provinceName?: string;
  emergencyContactname1?: string;
  emergencyContactname2?: string;
  emergencyContact1?: string;
  emergencyContact2?: string;
  street?: string;
  unitNumber?: string;
  zipCode?: string;
  upperName?: string;
  companyName?: string;
  role?: string;
  branchName?: string;
  uid?: string;
  department?: string;
  hireDate?: string;
  designation?: string;
  empCode?: string;
  leaveOnDays?: string;
  subDistrictName?: string;
  districtName?: string;
  workZone?: string;
}

const ProfileDetail = () => {
  const [data, setData] = useState<EmployeeData>({});
  const [imgUrl, setImgUrl] = useState<string>("");
  const [activeTab, setActiveTab] = useState(0);
  const [isMounted, setIsMounted] = useState(false);
  const [imageErrors, setImageErrors] = useState<any>({});

  const getInitials = (name: string) => name?.charAt(0)?.toUpperCase() || "U";

  const handleTabChange = (newValue: number) => setActiveTab(newValue);

  // const handleImageError = () => {
  //   setImgUrl("/path/to/default-avatar.png");
  // };

  // Fetch employee details
  const fetchEmpDetails = async () => {
    try {
      const queryParams = window.location.search;
      const empId = new URLSearchParams(queryParams).get("empId");

      console.log("Extracted empId:", empId);

      if (empId) {
        const empData = await getRequestWithSecurity(
          `${API_URLS.EMPLOYEE_LIST}/${empId}`
        );
        //console.log("Fetched Employee Data:", empData);

        setData(empData);
        setImgUrl(empData?.imgUre || "");
      }
    } catch (error) {
      console.error("Error in fetchEmpDetails:", error);
    }
  };
  useEffect(() => {
    console.log("Updated Data in State:", data);
  }, [data]);

  useEffect(() => {
    console.log(data, "data");
    setIsMounted(true);
  }, []);

  // console.log("download function is called");
  const handleDownload = async (filePath: any) => {
    // console.log("handleDownload function called with filePath:", filePath);

    try {
      const fileName = filePath.split("/").pop();
      // console.log("File name extracted:", fileName);

      const response: any = await fileDownload(
        `${API_URLS.TICKET_DOWNLOAD}${filePath}`,
        true
      );

      // console.log("API called with response:", response);

      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      const blob = await response.blob();
      const urlBlob = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = urlBlob;
      a.target = "_blank";
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      a.remove();

      // console.log("File downloaded successfully.");
    } catch (error) {
      // console.error("Download Error: ", error);
    }
  };
  const handleImageError = (index: any) => {
    setImageErrors((prevErrors: any) => ({
      ...prevErrors,
      [index]: true,
    }));
  };
  useEffect(() => {
    if (isMounted) {
      fetchEmpDetails();
    }
  }, [isMounted]);

  if (!isMounted) return null;
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  return (
    <Layout>
      <div className="pt-3" style={{ backgroundColor: colors.blue12 }}>
        <div className="container-fluid my-4">
          <div className="row justify-content-center mx-3">
            <div className="col-12">
              <div className="card shadow-sm">
                <div className="card-body d-flex flex-column flex-md-row align-items-center">
                  {/* <Avatar
                    intial={getInitials(data.empName || "Unknown")}
                    //profileImg={process.env.NEXT_PUBLIC_S3_URL + imgUrl}
                    profileImg={`${S3URL}${imgUrl}`}
                    className={"rounded-circle mb-3 mb-md-0 me-md-3"}
                    styleInital={{
                      width: "90px",
                      height: "90px",
                      backgroundColor: "#AABFD5",
                      color: "#000",
                      fontSize: 38,
                    }}
                    styleImg={{ width: "100px", height: "100px" }}
                  /> */}

                  {!imgUrl || imageErrors[0] ? (
                    <div
                      className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                      style={{
                        width: "90px",
                        height: "90px",
                        backgroundColor: "#AABFD5",
                        color: "#000",
                        fontSize: 38,
                      }}
                    >
                      {getInitials(data.empName || "Unknown")}
                    </div>
                  ) : (
                    <img
                      src={`${S3URL}${imgUrl}`}
                      alt={data.empName}
                      className="rounded-circle me-2"
                      style={{ width: "90px", height: "90px" }}
                      onError={() => handleImageError(0)}
                    />
                  )}
                  <div className="flex-grow-1 text-center text-md-start">
                    <p className="mb-1">Name: {data.empName || ""}</p>
                    <p className="mb-1">Email ID: {data.email || ""}</p>
                  </div>
                  <button className="btn btn-outline-success ms-md-auto mt-3 mt-md-0">
                    {data?.inService ? "ACTIVE" : "INACTIVE"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="row">
          <div
            className="col-12 col-md-6 profile-section1"
            style={{ width: "20.5%", height: "100%" }}
          >
            <button
              className={`btn w-100 mb-2 ${
                activeTab === 0 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={() => handleTabChange(0)}
            >
              Personal Details
            </button>
            <button
              className={`btn w-100 ${
                activeTab === 1 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={() => handleTabChange(1)}
            >
              Work Details
            </button>
            <button
              className={`btn w-100 mb-1 ${
                activeTab === 2 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={() => handleTabChange(2)}
            >
              Documents
            </button>
            <button
              className={`btn w-100 mb-1 ${
                activeTab === 3 ? "btn-primary" : "btn-outline-secondary"
              }`}
              onClick={() => handleTabChange(3)}
            >
              Leave
            </button>
          </div>

          <div className="col-12 col-md-6 profile-section2">
            {activeTab === 0 && (
              <div className="container-box mt-1">
                <div className="row">
                  <div className="col-12">
                    <h3
                      className="section-header"
                      style={{ fontWeight: "bold" }}
                    >
                      Personal Details
                    </h3>
                    <hr className="section-divider" />
                  </div>
                </div>
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>
                      Date Of Birth:{" "}
                      {data.birthday
                        ? new Date(data.birthday).toLocaleDateString()
                        : "/"}
                    </p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Ethnicity: {data.ethnicity || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>Gender: {data.gender || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>
                      Marital Status: {data?.married ? "Married" : "Single"}
                    </p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>Phone Number: {data.mobileNo || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Email: {data.email || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>Alternate Number: {data?.alternateNumber || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Alternate Email: {data?.alternateEmail || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>Country: {data?.nation || "South Africa"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Province: {data?.provinceName || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6">
                    <p>
                      District:{" "}
                      {data?.districtName === "Unknown District"
                        ? "N/A"
                        : data?.districtName || "N/A"}
                    </p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>SubDistrict: {data?.subDistrictName || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>
                      Emergency Contact Name 1:{" "}
                      {data?.emergencyContactname1 || "/"}
                    </p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>
                      Emergency Contact Name 2:{" "}
                      {data?.emergencyContactname2 || "/"}
                    </p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>Emergency Contact 1: {data?.emergencyContact1 || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Emergency Contact 2: {data?.emergencyContact2 || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>Street: {data?.street || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Unit Number: {data?.unitNumber || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>
                      Zip Code: {data?.zipCode ? parseInt(data.zipCode) : "/"}
                    </p>
                  </div>

                  <div className="col-md-6 mb-3">
                    <p>National Id: {data?.nationalId || "/"}</p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 1 && (
              <div className="container-box mt-1">
                <div className="row">
                  <div className="col-12">
                    <h3
                      className="section-header"
                      style={{ fontWeight: "bold" }}
                    >
                      Work Details
                    </h3>
                    <hr className="section-divider" />
                  </div>

                  <div className="col-md-6 mb-3">
                    <p>Reporting Person: {data?.upperName || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Hospital Name: {data?.companyName || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>Role: {data?.role || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Facility: {data?.branchName || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>UID: {data?.uid || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Department: {data?.department || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>
                      Joining Date:{" "}
                      {data?.hireDate
                        ? new Date(data?.hireDate).toLocaleDateString()
                        : "/"}
                    </p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Designation: {data?.designation || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 mb-3">
                    <p>Employee Code: {data?.empCode || "/"}</p>
                  </div>
                  <div className="col-md-6 mb-3">
                    <p>Leave On Days: {data?.leaveOnDays || "/"}</p>
                  </div>
                </div>

                <div className="row">
                  <div className="col-md-6 ">
                    <p>
                      Probation Status:{" "}
                      {data?.isInProbation === "Yes"
                        ? "Under Probation"
                        : "Completed"}
                    </p>
                  </div>
                </div>
              </div>
            )}
            {activeTab === 2 && (
              <div className="container-box">
                <h3 className="section-header" style={{ fontWeight: "bold" }}>
                  Documents
                </h3>
                <hr className="section-divider" />
                <div className="table-responsive">
                  <table className="table  ">
                    <thead>
                      <tr
                        className="border-bottom pt-1  fontsize-row"
                        style={{ textAlign: "left", lineHeight: "2" }}
                      >
                        <th scope="col" style={{ width: "1%" }}>
                          Sr
                        </th>
                        <th scope="col" style={{ width: "30%" }}>
                          Name
                        </th>
                        <th scope="col" style={{ width: "3%" }}>
                          Type
                        </th>
                        <th
                          scope="col"
                          style={{ width: "3%", textAlign: "center" }}
                        >
                          Download
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.documents.map((doc: any, index: number) => (
                        <tr
                          key={doc.id}
                          className="border-bottom pt-1 fontsize-row"
                          style={{ textAlign: "left", lineHeight: "1.5" }}
                        >
                          <td className="pt-1">{index + 1}</td>
                          <td className="pt-1">{doc.documentName}</td>
                          <td className="pt-1">{doc.documentType}</td>
                          <td className="pt-1">
                            <a
                              className="text-primary p-3"
                              onClick={() => handleDownload(doc.filePath)}
                              style={{
                                cursor: "pointer",
                                marginLeft: "50px",
                              }}
                            >
                              <img
                                src="/image/icons8-download-24.png"
                                style={{ width: "20px" }}
                              />
                            </a>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
            {activeTab === 3 && (
              <div className="container-box">
                <h3 className="section-header" style={{ fontWeight: "bold" }}>
                  Leave Balances
                </h3>
                <hr className="section-divider" />
                <div className="table-responsive">
                  <table className="table " style={{ width: "80%" }}>
                    <thead>
                      <tr
                        className="border-bottom pt-1  fontsize-row"
                        style={{ textAlign: "left", lineHeight: "2" }}
                      >
                        <th scope="col" style={{ width: "1%" }}>
                          Sr
                        </th>
                        <th scope="col" style={{ width: "30%" }}>
                          Leave Name
                        </th>
                        <th
                          scope="col"
                          style={{ textAlign: "center", width: "10%" }}
                        >
                          Assigned
                        </th>
                        <th
                          scope="col"
                          style={{ textAlign: "center", width: "10%" }}
                        >
                          Balance
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.leaveBalances.map((leave: any, index: number) => (
                        <tr
                          key={leave.leaveId}
                          className="border-bottom pt-1 fontsize-row"
                          style={{ textAlign: "left", lineHeight: "1.5" }}
                        >
                          <td className="pt-1">{index + 1}</td>
                          <td className="pt-1">{leave.leaveName}</td>
                          <td style={{ textAlign: "center" }} className="pt-1">
                            {leave.assignedLeave}
                          </td>
                          <td style={{ textAlign: "center" }} className="pt-1">
                            {leave.balanceLeave}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ProfileDetail;
