package com.company.wfm.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeaveHistoryResponseDTO {

    private long leaveHistoryId;
    private Long empLeaveId;
    private String empLeaveName;
    private Long leaveId;
    private String leaveTypeName;
    private Long employeeId;
    private String empName;
    private String empCode;
    private String designationName;
    private String imageUrl;
    private Integer appliedLeaveCount;
    private LocalDate startDate;
    private LocalDate endDate;
    private String approvalStatus;
    private String reason;
    private String createdBy;
    private String createdByName;
    private LocalDateTime createdTime;
    private String updatedBy;
    private String updatedByName;
    private LocalDateTime updatedTime;
    private String comment;
    private String filePath;


}
