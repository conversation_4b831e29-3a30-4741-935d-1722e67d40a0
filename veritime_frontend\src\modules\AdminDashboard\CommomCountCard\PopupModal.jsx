import { DataGrid } from "@mui/x-data-grid";
import "./PopupModal.css";
import { Modal, ModalHeader, ModalBody } from "react-bootstrap";

const PopupModal = ({ title, show, columns, rows, handleClose }) => {
  return (
    <div>
      <Modal style={{ width: "100%" }} show={show} onHide={handleClose}>
        <ModalHeader closeButton>
          <Modal.Title>{title}</Modal.Title>
        </ModalHeader>
        <ModalBody>
          <DataGrid
            // style={{ maxHeight: "500px", overflowY: "auto" }}
            rows={rows}
            columns={columns}
            hideFooter
          />
        </ModalBody>
      </Modal>
    </div>
  );
};

export default PopupModal;
