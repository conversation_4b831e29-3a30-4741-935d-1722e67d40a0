package com.company.wfm.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import com.company.wfm.entity.VendorProduct;
import com.company.wfm.repository.VendorProductRepository;
import com.company.wfm.service.VendorProductService;

@Service
public class VendorProductServiceImpl implements VendorProductService {

    @Autowired
    private VendorProductRepository vendorProductRepository;

   /* @Override
    public List<VendorProduct> getProductsByVendorId(Long vendorId) {
        return vendorProductRepository.findByVendorId(vendorId);
    }*/

    @Override
    public Page<VendorProduct> getProductsByVendorId(Long vendorId, int offset, int limit) {
        // Check if the vendor exists
        if (!vendorExists(vendorId)) {
            throw new RuntimeException("Vendor with ID " + vendorId + " not found.");
        }
        return vendorProductRepository.findByVendorId(vendorId, PageRequest.of(offset, limit));
    }

    private boolean vendorExists(Long vendorId) {
        return vendorProductRepository.existsById(vendorId);
    }
}
