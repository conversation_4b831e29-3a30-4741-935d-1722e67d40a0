package com.company.wfm.controller;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.PaginationRequestDTO;
import com.company.wfm.dto.TicketCategoryDTO;
import com.company.wfm.dto.TicketCategoryResponseDTO;
import com.company.wfm.entity.TicketCategoryMaster;
import com.company.wfm.service.TicketCategoryMasterService;

import jakarta.persistence.EntityNotFoundException;

@RestController
@RequestMapping("/api/v1/categories")
@CrossOrigin(origins = "*")
public class TicketCategoryMasterController {
    private static final Logger logger = LoggerFactory.getLogger(TicketCategoryMasterController.class);

    @Autowired
    private TicketCategoryMasterService ticketCategoryMasterService;

    @PostMapping("/save")
    public ResponseEntity<?> createCategory(@RequestBody TicketCategoryMaster category) {
        logger.info("Received request to create a new category: {}", category);
        try {
            TicketCategoryMaster savedCategory = ticketCategoryMasterService.saveCategory(category);
            logger.info("Category saved successfully with ID: {}", savedCategory.getId());
            return ResponseEntity.ok(savedCategory);
        } catch (IllegalArgumentException ex) {
            logger.error("Validation error while saving category: {}", ex.getMessage());
            return ResponseEntity.badRequest().body("Invalid input: " + ex.getMessage());
        } catch (Exception ex) {
            logger.error("Unexpected error occurred: {}", ex.getMessage(), ex);
            return ResponseEntity.status(500).body("An unexpected error occurred: " + ex.getMessage());
        }
    }

    //listing
   /* @PostMapping("/list")
    public ResponseEntity<?> listCategories(@RequestBody PaginationRequestDTO paginationRequest) {
        try {
            if (paginationRequest == null) {
                paginationRequest = new PaginationRequestDTO();
            }
            int pageNumber = paginationRequest.getOffset(); // Treat offset as pageNumber
            int pageSize = paginationRequest.getLimit();    // Treat limit as pageSize

            Pageable pageable = PageRequest.of(
                    pageNumber,
                    pageSize,
                    org.springframework.data.domain.Sort.by("id").descending()
            );

            Page<TicketCategoryMaster> categories = ticketCategoryMasterService.listCategories(pageable);
            return ResponseEntity.ok(categories);
        } catch (Exception ex) {
            logger.error("Error occurred while fetching ticket categories: {}", ex.getMessage(), ex);
            return ResponseEntity.status(500).body("An error occurred while fetching data.");
        }

    }*/


    @PostMapping("/list")
    public ResponseEntity<?> listCategories(@RequestBody PaginationRequestDTO paginationRequest) {
        try {
            if (paginationRequest == null) {
                paginationRequest = new PaginationRequestDTO();
            }

            String type = paginationRequest.getType();
            if ("select".equalsIgnoreCase(type)) {
                // Fetch only id, category, and priority without pagination
                List<Object[]> categories = ticketCategoryMasterService.listCategoriesForSelect();

                if (categories.isEmpty()) {
                    logger.warn("No categories found in the select query");
                } else {
                    logger.info("Fetched {} categories", categories.size());
                }

                // Map the result into a list of maps
                List<Map<String, Object>> simplifiedCategories = categories.stream()
                        .map(category -> {
                            Map<String, Object> categoryMap = new HashMap<>();
                            categoryMap.put("id", category[0]); // Accessing id from the Object array
                            categoryMap.put("category", category[1]); // Accessing category
                            categoryMap.put("priority", category[2]); // Accessing priority
                            return categoryMap;
                        })
                        .collect(Collectors.toList());

                return ResponseEntity.ok(simplifiedCategories);

            } else {
                int pageNumber = paginationRequest.getOffset(); // Treat offset as pageNumber
                int pageSize = paginationRequest.getLimit();    // Treat limit as pageSize

                Pageable pageable = PageRequest.of(
                        pageNumber,
                        pageSize,
                        org.springframework.data.domain.Sort.by("id").descending()
                );

                Page<TicketCategoryDTO> categories = ticketCategoryMasterService.listCategories(pageable);
                return ResponseEntity.ok(categories);
            }
        } catch (Exception ex) {
            logger.error("Error occurred while fetching ticket categories: {}", ex.getMessage(), ex);
            return ResponseEntity.status(500).body("An error occurred while fetching data.");
        }
    }


    //update
    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateCategory(@PathVariable Long id, @RequestBody TicketCategoryMaster updatedCategory) {
        logger.info("Received request to update category with ID: {}", id);
        try {
            TicketCategoryMaster updatedEntity = ticketCategoryMasterService.updateCategory(id, updatedCategory);
            logger.info("Category updated successfully with ID: {}", id);
            return ResponseEntity.ok(updatedEntity);
        } catch (EntityNotFoundException ex) {
            logger.error("Category not found for ID: {}", id);
            return ResponseEntity.status(404).body("Category not found for ID: " + id);
        } catch (IllegalArgumentException ex) {
            logger.error("Validation error while updating category: {}", ex.getMessage());
            return ResponseEntity.badRequest().body("Invalid input: " + ex.getMessage());
        } catch (Exception ex) {
            logger.error("Unexpected error occurred while updating category: {}", ex.getMessage(), ex);
            return ResponseEntity.status(500).body("An unexpected error occurred: " + ex.getMessage());
        }
    }
    @PutMapping("/deactivate/{id}")//test
    public ResponseEntity<String> deactivateVendor(@PathVariable Long id, @RequestBody Map<String, Boolean> requestBody) {
        try {


            // Validate input
            if (id == null || id <= 0) {
                throw new IllegalArgumentException("Invalid ID provided");
            } //testA
            if (!requestBody.containsKey("isActive")) {
                throw new IllegalArgumentException("Missing 'isActive' field in request body");
            }
            //testA
            // Retrieve the vendor
            TicketCategoryMaster ticket = ticketCategoryMasterService.getTicketByID(id);

            // Update active status
            ticket.setTicketActive(requestBody.get("isActive"));
            ticketCategoryMasterService.updateCategory(id, ticket);
//testA
            String status = ticket.getActive() ? "active" : "inactive";
            return ResponseEntity.ok("The issueType is now " + status);

        } catch (IllegalArgumentException e) {
            // Handle validation errors
            return ResponseEntity.badRequest().body("Invalid input: " + e.getMessage());
//testA
        } catch (NoSuchElementException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            // Handle generic/unexpected errors
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("An error occurred: " + e.getMessage());
        }
    }
    //delete

    //delete
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> softDeleteCategory(@PathVariable Long id) {
        logger.info("Received request to soft delete category with ID: {}", id);
        try {
            boolean deleted = ticketCategoryMasterService.softDeleteCategory(id);
            if (deleted) {
                logger.info("Category with ID {} marked as inactive successfully.", id);
                return ResponseEntity.ok("Category deleted successfully.");
            } else {
                logger.warn("Category with ID {} not found or already inactive.", id);
                return ResponseEntity.status(404).body("Category not found or already deleted.");
            }
        } catch (Exception ex) {
            logger.error("Error occurred while deleting category: {}", ex.getMessage(), ex);
            return ResponseEntity.status(500).body("An error occurred while deleting the category.");
        }
    }


    @PostMapping("/categoryLookup")
    public ResponseEntity<?> getCategoriesByBranchAndDepartment(
            @RequestBody Map<String, Object> requestBody) {

        try {
            Long departmentId = Long.valueOf(requestBody.get("departmentId").toString());
            // Fetch categories based on the branchId and departmentId
            List<TicketCategoryMaster> categories = ticketCategoryMasterService.getTicketCategoriesByBranchAndDepartment(departmentId);

           if (categories.isEmpty()) {
                return ResponseEntity.noContent().build();
            }

            // Map the entity to the response DTO
            List<TicketCategoryResponseDTO> responseDTOs = categories.stream()
                    .map(category -> new TicketCategoryResponseDTO(
                            category.getId(),
                            category.getCategory(),
                            category.getPriority()))
                    .collect(Collectors.toList());

            // Return the response with a 200 OK status
            return ResponseEntity.ok(responseDTOs);

        } catch (Exception e) {
            // Return a 500 Internal Server Error with the exception message
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while fetching ticket categories: " + e.getMessage());
        }
    }



}
