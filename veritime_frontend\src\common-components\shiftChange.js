import React, { useEffect, useState } from "react";
import Swal from "sweetalert2";
import "sweetalert2/dist/sweetalert2.min.css"; // Ensure styles are loaded

import {
  getRequest,
  postRequest,
  postRequestWithSecurity,
  getRequestWithSecurity,
} from "../services/apiService";
import TableFilter from "./TableFilter";
import { API_URLS } from "../constants/apiConstants";
import VisibilityIcon from "@mui/icons-material/Visibility";
import "../common-components/ApprovalTable.css";

const ShiftChangeList = ({ showActionCol, fetchCount }) => {
  const [rows, setRows] = useState([]);
  const [imageErrors, setImageErrors] = useState({});
  const [selectedEmployeeId, setSelectedEmployeeId] = useState(null);
  const [errors, setErrors] = useState({});
  const [disabledRecommendation, setDisabledRecommendation] = useState({});

  const getInitials = (name) => {
    return name?.charAt(0)?.toUpperCase() || "U";
  };
  const handleImageError = (index) => {
    setImageErrors((prevErrors) => ({
      ...prevErrors,
      [index]: true,
    }));
  };
  const [recommendationOptions, setRecommendationOptions] = useState([]);
  const fetchRecommendationList = async (time_slot_id, startDate, endDate) => {
    console.log("Fetching recommendations for:", {
      time_slot_id,
      startDate,
      endDate,
    });

    try {
      const url = API_URLS.GET_RECOMMENDATION_LIST(
        time_slot_id,
        startDate,
        endDate
      );
      const response = await getRequest(url);

      console.log("Recommendation API Response:", response); // 🔍 Debugging

      if (response && Array.isArray(response.employees)) {
        return response.employees.map((employee) => ({
          label: employee.employee_name || " No Recommendation",

          value: employee.employee_id,
        }));
      } else {
        console.warn("No recommendations found in API response.");
        return [];
      }
    } catch (error) {
      console.error("Error fetching recommendations:", error);
      return [];
    }
  };

  const getRecommendation = async (time_slot_id, startDate, endDate) => {
    const formattedRecommendations = await fetchRecommendationList(
      time_slot_id,
      startDate,
      endDate
    );

    setRecommendationOptions(formattedRecommendations); // ✅ Update state

    return formattedRecommendations; // ✅ Ensure function returns data
  };

  // ✅ useEffect to log when state updates
  useEffect(() => {
    console.log("Updated recommendationOptions:", recommendationOptions);
  }, [recommendationOptions]);

  useEffect(() => {
    fetchCount("shiftchange");

    let apiurl = API_URLS?.SHIFT_CHANGE_LIST;
    if (window.location.pathname === "/employeeDashboard/viewHistory") {
      apiurl += "?type=history";
    }

    getRequestWithSecurity(apiurl)
      .then((data) => {
        console.log("API Response:", data);

        if (Array.isArray(data)) {
          const rowsWithId = data.map((item, index) => ({
            ...item,
            id: index + 1,
          }));

          setRows(rowsWithId); //  Update rows first
        } else {
          console.warn("API Response is not an array:", data);
        }
      })
      .catch((error) => {
        console.error("Error fetching shift change list:", error);
      });
  }, []);
  useEffect(() => {
    if (rows.length > 0) {
      const fetchAllRecommendations = async () => {
        const recommendationsMap = {};

        for (const row of rows) {
          const timeSlotId = row?.time_slot_id;
          const fromDateArray = row?.from_date;
          const toDateArray = row?.to_date;

          if (!timeSlotId || !fromDateArray || !toDateArray) {
            console.warn("Missing parameters for recommendation API call.");
            continue;
          }

          const formatDate = (dateArray) =>
            `${dateArray[0]}-${String(dateArray[1]).padStart(2, "0")}-${String(
              dateArray[2]
            ).padStart(2, "0")}`;

          const startDate = formatDate(fromDateArray);
          const endDate = formatDate(toDateArray);

          try {
            const recommendations = await getRecommendation(
              timeSlotId,
              startDate,
              endDate
            );
            recommendationsMap[row.swapRequestId] = recommendations;
          } catch (error) {
            console.error(
              `Error fetching recommendations for row ${row.id}:`,
              error
            );
          }
        }

        setRecommendationOptions(recommendationsMap);
      };

      fetchAllRecommendations();
    }
  }, [rows]);

  const handleRecommendationChange = (e) => {
    const selectedId = e.target.value;
    setSelectedEmployeeId(selectedId);

    if (errors.recommendation) {
      setErrors((prevErrors) => ({ ...prevErrors, recommendation: "" }));
    }
  };

  const handleAccept = async (swapRequestId) => {
    const { value: reason } = await Swal.fire({
      title: "Approve Shift Change Request",
      text: "Do you want to approve this shift change request?",
      input: "textarea",
      inputPlaceholder: "Enter your approval reason...",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, approve it!",
      cancelButtonText: "No, keep it pending",
      inputValidator: (value) => {
        if (!value) {
          return "Please mention a reason!";
        }
      },
    });

    if (reason) {
      try {
        const url = API_URLS.APPROVE_SHIFT(swapRequestId);
        const payload = {
          reason: reason,
          employeeId: selectedEmployeeId,
        };
        const response = await postRequestWithSecurity(url, payload, false);
        Swal.fire("Approved!", response, "success");

        setRows((prevRows) =>
          prevRows.map((row) =>
            row.swapRequestId === swapRequestId
              ? {
                  ...row,
                  status: "APPROVED",
                  updatedBy: "Supervisor",
                  actionReason: "",
                }
              : row
          )
        );
      } catch (error) {
        Swal.fire(
          "Error!",
          error?.response?.data || "An error occurred",
          "error"
        );
      }
    }
  };

  const handleDeny = async (swapRequestId) => {
    const { value: reason } = await Swal.fire({
      title: "Deny Shift Change Request",
      text: "Do you want to deny this shift change request?",
      input: "textarea",
      inputPlaceholder: "Enter your denial reason...",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, deny it!",
      cancelButtonText: "No, keep it pending",
      inputValidator: (value) => {
        if (!value) {
          return "Please mention a reason!";
        }
      },
    });

    if (reason) {
      try {
        const url = API_URLS.DENY_SHIFT(swapRequestId);
        const payload = {
          reason: reason,
        };
        const response = await postRequestWithSecurity(url, payload);
        Swal.fire("Denied!", response, "success");

        setRows((prevRows) =>
          prevRows.map((row) =>
            row.swapRequestId === swapRequestId
              ? {
                  ...row,
                  status: "DENIED",
                  updatedBy: "Supervisor",
                  actionReason: "",
                }
              : row
          )
        );
      } catch (error) {
        Swal.fire(
          "Error!",
          error?.response?.data || "An error occurred",
          "error"
        );
      }
    }
  };
  function formatDate(date) {
    const options = { day: "2-digit", month: "2-digit", year: "numeric" };
    const formattedDate = new Date(date).toLocaleDateString("en-GB", options);
    return formattedDate;
  }
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;
  const handleDetail = async (row) => {
    console.log("handleDetail called with row:", row);

    const { requestedSchedule, startDate, endDate } = row;
    if (!startDate || !endDate || !requestedSchedule) {
      console.error("Missing required data in row:", row);
      return;
    }

    const time_slot_id = requestedSchedule;

    try {
      const fetchedRecommendations = await getRecommendation(
        time_slot_id,
        startDate,
        endDate
      );

      if (!Array.isArray(fetchedRecommendations)) {
        console.error(
          "getRecommendation returned invalid data:",
          fetchedRecommendations
        );
        return;
      }

      console.log("Fetched Recommendations:", fetchedRecommendations);

      // Check if swapWithEmployeeId exists in recommendations
      const matchedEmployee = fetchedRecommendations.find(
        (emp) => emp.value === row.swapWithEmployeeId
      );

      if (matchedEmployee) {
        console.log(
          `%c MATCH FOUND! EMPID: ${matchedEmployee.value}, Name: ${matchedEmployee.label}`,
          "color: green; font-weight: bold;"
        );
      } else {
        console.log(
          `%c NO MATCH FOUND for EMPID: ${row.swapWithEmployeeId}`,
          "color: red; font-weight: bold;"
        );
      }

      const currentEmployee = {
        label: row.swapRequestedToEmployeeName || "",
        value: row.swapWithEmployeeId || 0,
      };

      const updatedRecommendations = matchedEmployee
        ? fetchedRecommendations
        : [currentEmployee, ...fetchedRecommendations];

      console.log("Updated recommendationOptions:", updatedRecommendations);

      setRecommendationOptions(updatedRecommendations);
      setSelectedEmployeeId(row.swapRequestedByEmployeeId);

      console.log("Final Dropdown Options:", updatedRecommendations);

      const baseURL = "https://wfm-upload-file.s3.ap-south-1.amazonaws.com/";
      //setTimeout(() => {
      Swal.fire({
        html: `
        <div style="font-size: 18px; line-height: 1.6; color: #333; display: flex; flex-direction: column; gap: 20px; max-width: 700px;">
      
        <div style="display: flex; justify-content: center; align-items: center; gap: 20px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0;">
           <img  
        src="${S3URL}${row.swapRequestedByEmployeeImage}" 
              alt="Employee Image" 
              style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover; border: 2px solid #ddd;" 
              onerror="this.onerror=null; this.src=''; this.style.display='none'; this.nextElementSibling.style.display='flex';"
            >
            <div style="display: none; align-items: center; justify-content: center; width: 60px; height: 60px; background-color: #ccc; color: #fff; font-weight: bold; border-radius: 50%;">
              ${getInitials(row.swapRequestedByEmployeeName)}
            </div>
            <div style="display: flex; flex-direction: column; align-items: center;">
              <span style="font-size: 22px; font-weight: bold; color: #1a1a1a;">${
                row.swapRequestedByEmployeeName
              }</span>
            </div>
          </div>

      
          <div style="display: flex; flex-direction: column; gap: 15px; padding-bottom: 15px;">
    
            <div style="display: flex; align-items: center; padding-bottom: 10px; margin-bottom: -14px;">
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">Name:</strong>
                <span>${row.swapRequestedByEmployeeName}</span>
              </div>
              
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px;">Reason:</strong>
                <span>${row.reason}</span>
              </div>
            </div>
      
            <div style="display: flex; align-items: center; padding-bottom: 10px; margin-bottom: -11px;">
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">Applied Date:</strong>
                <span>${formatDate(row.swapRequestDate)}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">Approval status:</strong>
                <span>${row.status}</span>
              </div>
            </div>
  
            <div style="display: flex; align-items: center; padding-bottom: 10px;">
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">Start Date:</strong>
                <span>${formatDate(row.startDate) || "N/A"}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">End Date:</strong>
                <span>${formatDate(row.endDate) || "N/A"}</span>
              </div>
            </div>
  
            <div style="display: flex; align-items: center; padding-bottom: 10px; margin-bottom: -25px;">
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">Start Time:</strong>
                <span>${row.startTime || "N/A"}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">End Time:</strong>
                <span>${row.endTime || "N/A"}</span>
              </div>
            </div>
             <!-- Recommendations Dropdown -->
    <div style="display: flex; align-items: center; flex-direction: column; gap: 10px; margin-top: 10px;">
      <label
        style="
          font-weight: bold;
          font-size: 18px;
          
        color: #333;
          margin-bottom: 5px;
          text-align: left;
          width: 100%;
        "
        for="recommendationSelect"
      >
        Recommendations <span style="color: red;"></span>
      </label>
    <select
    style="
      background-color: white;
      color: #333;
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #BDC3C7;
      border-radius: 6px;
      font-size: 18px;
      font-weight: Bold;
      appearance: none;
      outline: none;
    "
  id="recommendationSelect-${row.swapRequestId}"
  ${row.status === "APPROVED" || row.status === "DENIED" ? "disabled" : ""}
>
  ${recommendationOptions
    .map((option) => {
      const isSelected =
        option.value === row.swapWithEmployeeId ? "selected" : "";
      return `<option value="${option.value}" ${isSelected}>${option.label}</option>`;
    })
    .join("")}
</select>

    </div>


            
          <div style="display: flex; justify-content: center; gap: 10px;">
         ${
           window.location.pathname !== "/employeeDashboard/viewHistory" &&
           row.status === "PENDING"
             ? `
              <button id="approveBtn" class="approve-button">Approve</button>
              <button id="denyBtn" class="deny-button">Decline</button>
            `
             : ""
         }
          </div>
        </div>
      `,
        icon: "",
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: false,
        customClass: {
          container: "custom-swal-container",
          popup: "custom-swal-popup",
          title: "custom-swal-title",
          content: "custom-swal-content",
        },
        didOpen: () => {
          console.log("Modal Opened");

          const approveBtn = document.getElementById("approveBtn");
          const denyBtn = document.getElementById("denyBtn");
          const recommendationSelect = document.getElementById(
            `recommendationSelect-${row.swapRequestId}`
          );

          setSelectedEmployeeId(row.swapRequestedByEmployeeId);

          if (recommendationSelect) {
            recommendationSelect.addEventListener("change", (e) => {
              setSelectedEmployeeId(e.target.value);
            });
          }
          if (approveBtn) {
            approveBtn.addEventListener("click", () =>
              handleAccept(row.swapRequestId)
            );
          }
          if (denyBtn) {
            denyBtn.addEventListener("click", () =>
              handleDeny(row.swapRequestId)
            );
          }
        },
        allowOutsideClick: true,
      });
      // }, 1000); // <-- Timeout duration in milliseconds (e.g., 1000ms = 1 second)
    } catch (error) {
      console.error("Error in handleDetail:", error);
    }
  };

  const columns = [
    { field: "id", headerName: "Sr.no", width: 70 },
    {
      field: "swapRequestedByEmployeeName",
      headerName: "Name",
      width: 250,
      renderCell: (params) => {
        const imageUrl = params.row.swapRequestedByEmployeeImage;
        const name = params.row.swapRequestedByEmployeeName;

        return (
          <div style={{ display: "flex", alignItems: "center" }}>
            {!imageUrl || imageErrors[params.row.id] ? (
              <div
                className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                style={{
                  width: "50px",
                  height: "50px",
                  backgroundColor: "#ccc",
                  color: "#fff",
                  fontWeight: "bold",
                }}
              >
                {getInitials(name)}
              </div>
            ) : (
              <img
                src={`${S3URL}${imageUrl}`}
                alt={name}
                className="rounded-circle me-2"
                style={{ width: "50px", height: "50px" }}
                onError={() => handleImageError(params.row.id)}
              />
            )}
            {/* <img
              src={imageUrl}
              alt={name}
              style={{ width: 40, height: 40, borderRadius: '50%', marginRight: 10 }}
            /> */}
            <span>{name}</span>
          </div>
        );
      },
    },
    // {
    //   field: 'swapRequestedToEmployeeName',
    //   headerName: 'Recomended name',
    //   width: 200,
    //   renderCell: (params) => {
    //     const imageUrl = params.row.swapRequestedToEmployeeImage;
    //     const name = params.row.swapRequestedToEmployeeName;

    //     return (
    //       <div style={{ display: 'flex', alignItems: 'center' }}>
    //         <img
    //           src={imageUrl}
    //           alt={name}
    //           style={{ width: 40, height: 40, borderRadius: '50%', marginRight: 10 }}
    //         />
    //         <span>{name}</span>
    //       </div>
    //     );
    //   },
    // },
    { field: "swapRequestDate", headerName: "Request Date", width: 200 },
    // { field: 'startDate', headerName: 'Start Date', width: 150 },
    // { field: 'endDate', headerName: 'End Date', width: 150 },
    // { field: 'requestedSchedule', headerName: 'Requested Schedule', width: 200 },
    // { field: 'reason', headerName: 'Reason', width: 200 },
    { field: "status", headerName: "Status", width: 200 },
  ];

  // if (showActionCol) {
  //   columns.push({
  //     field: 'action',
  //     headerName: 'Action',
  //     width: 200,
  //     renderCell: (params) => {
  //       if (params.row.status === 'PENDING') {
  //         return (
  //           <div>
  //             <button
  //               className="approve-button"
  //               onClick={() => handleAccept(params.row.swapRequestId)}
  //             >
  //               Approve
  //             </button>
  //             <button
  //               className="deny-button"
  //               onClick={() => handleDeny(params.row.swapRequestId)}
  //             >
  //               Decline
  //             </button>
  //           </div>
  //         );
  //       } else if (params.row.status === 'APPROVED') {
  //         return <span className="approvedTxt">Approved by {params.row.updatedBy || "Supervisor"}</span>;
  //       } else if (params.row.status === 'DENIED') {
  //         return <span className="rejectedTxt">Declined by {params.row.updatedBy || "Supervisor"}</span>;
  //       }
  //       return null;
  //     },
  //   });

  columns.push({
    field: "Action",
    headerName: "action",
    width: 100,
    renderCell: (params) => (
      <button
        className="view-button"
        onClick={() => {
          console.log("View button clicked!");

          handleDetail(params.row);
        }}
        style={{ border: "none", background: "transparent", cursor: "pointer" }}
      >
        <VisibilityIcon style={{ cursor: "pointer", color: "black" }} />
      </button>
    ),
  });
  //}

  return (
    <div id="tableWrapper" style={{ marginTop: -24 }}>
      <TableFilter columns={columns} rows={rows} />
    </div>
  );
};

export default ShiftChangeList;
