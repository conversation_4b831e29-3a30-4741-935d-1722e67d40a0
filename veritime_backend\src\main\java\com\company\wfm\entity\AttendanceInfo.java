package com.company.wfm.entity;

import java.io.Serializable;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendanceInfo implements Serializable {

  private static final long serialVersionUID = 1L;

  private String id;

  private LocalDateTime datetime;

  private LocalDate date;

  private Time time;

  private String status;

  private String device;

  private String deviceno;

  private String empname;

  private String cardNo;

  private int consumeStatus;
}
