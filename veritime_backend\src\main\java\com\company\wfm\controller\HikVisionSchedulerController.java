package com.company.wfm.controller;

import com.company.wfm.scheduler.AttendanceScheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.scheduler.HikVisionScheduler;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/v1/hikvision")
@CrossOrigin(origins = "*")
public class HikVisionSchedulerController {

	@Autowired
	private HikVisionScheduler hikVisionScheduler;

	@Autowired
	private AttendanceScheduler attendanceScheduler;
	 
	@Async
	@GetMapping("/fetchAndInsertAttendance")
	public void generateCountryTargetData() {
		log.info("Triggering fetchAndInsertAttendance job for hikvision");
		hikVisionScheduler.fetchAndInsertAttendance();
	}
	
	@Async
	@GetMapping("/uploadProfilePictureToHikVision")
	public void uploadProfilePictureToHikVision() {
		log.info("Triggering uploadProfilePictureToHikVision job for hikvision");
		hikVisionScheduler.uploadProfilePictureToHikVision();
	}

	@Async
	@GetMapping("/getScannedData")
	public void fetchAndPrintEventCertificateRecords() {
		log.info("Triggering fetchAndPrintEventCertificateRecords job for hikvision");
		hikVisionScheduler.fetchAndPrintEventCertificateRecords();
	}

	@Async
	@GetMapping("/markDailyAttendance")
	public void fetchAndInsertAttendance() {
		log.info("Triggering fetchAndInsertAttendance job for hikvision");
		attendanceScheduler.fetchAndInsertAttendance();
	}

	@Async
	@GetMapping("/createPastAttendance")
	public void fetchAndInsertPastAttendance() {
		log.info("Triggering fetchAndInsertPastAttendance job for daily past attendance");
		attendanceScheduler.fetchAndInsertPastAttendance();
	}


}
