package com.company.wfm.repository;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.company.wfm.dto.FeederHospitalDTO;
import com.company.wfm.entity.Branch;

public interface BranchRepository extends JpaRepository<Branch,Long> {
    boolean existsByBranchCode(String branchCode);
    @Query("SELECT b.branchName FROM  Branch b")
    List<String> findAllBranchNames();

    @Query("SELECT b FROM  Branch b where branchHead.id = :empId and b.isActive=1")
    List<Branch> getAllBranch(@Param("empId") Long empId);

    @Query("SELECT b FROM  Branch b where b.id in (:branchIds) and b.isActive=1")
    List<Branch> findByIdIn(@Param("branchIds") List<Long> branchIds);

    @Query("SELECT b FROM Branch b WHERE LOWER(b.branchName) = LOWER(:branchName)")
    List<Branch> findAllByBranchName(@Param(("branchName")) String branchName);


    @Query("SELECT b FROM  Branch b where b.id=:branchId and isActive = 1")
    Optional<Branch> findByBranchId(@Param("branchId")long branchId);

   @Query("SELECT b FROM  Branch b where b.company.id=:hospitalId and b.isActive=1")
    Optional<List<Branch>> findByHospitalId(@Param("hospitalId") long hospitalId);

    @Query("SELECT b FROM  Branch b where b.company.id=:hospitalId and b.isActive=1")
    List<Branch> findByHospitalIdWise(@Param("hospitalId") long hospitalId);


    @Query("SELECT b FROM  Branch b where b.company.id in (:hospitalIds) and b.isActive=1")
    Optional<List<Branch>> findByHospitalIds(@Param("hospitalIds") List<Long> hospitalIds);


    @Query("SELECT new com.company.wfm.dto.FeederHospitalDTO(f.id, f.name) " +
            "FROM FeederHospital f " +
            "WHERE f.id = :hospitalId")
    FeederHospitalDTO findFeederHospitalById(@Param("hospitalId") Long hospitalId);

    @Modifying
    @Query("DELETE FROM Branch b WHERE b.company.id = :hospitalId")
    void deleteByHospitalId(@Param("hospitalId") Long hospitalId);

    @Query("SELECT b.id FROM Branch b WHERE b.branchName = :branchName")
    List<Long> findBranchIdsByBranchName(@Param("branchName") String branchName);

    Optional<Branch> findByBranchName(String branchName);
    
    List<Branch> findAllByIsActive(int isActive);

}

