import React, { useEffect, useState } from "react";

function ContactDetailsForm({ data, onSubmit,isEditing }) {
  const [formData, setFormData] = useState({
    mobileNo: "",
    alternateNumber: "",
    email: "",
    alternateEmail: "",
    emergencyContact1: "",
    emergencyContact2: "",
    emergencyContactname1: "",
    emergencyContactname2: "",
  });
  useEffect(() => {
    if (data) setFormData(data);
  }, []);

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Remove the error for the field as soon as the user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const validate = () => {
    let validationErrors = {};
  
    if (!formData.mobileNo) {
      validationErrors.mobileNo = "Mobile number is required";
    } else if (!/^\d{10}$/.test(formData.mobileNo)) {
      validationErrors.mobileNo = "Mobile number must be 10 digits";
    }
  
    if (!formData.email) {
      validationErrors.email = "Email is required";
    } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(formData.email)) {
      validationErrors.email = "Invalid email format";
    }
  
    if (!formData.alternateEmail) {
      validationErrors.alternateEmail = "Alternate email is required";
    } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(formData.alternateEmail)) {
      validationErrors.alternateEmail = "Invalid email format";
    }
  
    if (!formData.emergencyContact1) {
      validationErrors.emergencyContact1 = "Emergency contact number 1 is required";
    }
  
    if (!formData.emergencyContact2) {
      validationErrors.emergencyContact2 = "Emergency contact number 2 is required";
    }
  
    if (!formData.emergencyContactname1) {
      validationErrors.emergencyContactname1 = "Emergency contact name 1 is required";
    }
  
    if (!formData.emergencyContactname2) {
      validationErrors.emergencyContactname2 = "Emergency contact name 2 is required";
    }
  
    return validationErrors;
  };
  

  const handleSubmit = (e) => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
    } else {
      onSubmit(formData);
      console.log("Form Data:", formData);
    }
  };
  const headingStyle = {
    marginBottom: '20px',
    fontWeight: 'bold',
  };
  return (
    <form
      onSubmit={handleSubmit}
      style={{ maxWidth: "800px", margin: "20px auto" }}
    >
      <h2 style={headingStyle}>Contact Details</h2>
      <label htmlFor="ticketSubject" className="ticket-text-primary" >
                <span className="text-danger">The fields with * marks are mandatory</span>
          </label>
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <div style={{ marginRight: "20px", paddingRight: "100px" }}>
          <div style={{ marginBottom: "15px" }}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Mobile Number <span className="text-danger">*</span>
          </label></label></label>
            <input
              type="text"
              name="mobileNo"
              value={formData.mobileNo}
              onChange={handleChange}
              placeholder="Mobile Number"
              style={{ width: "100%", padding: "8px", marginBottom: "10px" }} // Added more margin-bottom here
            />
            {errors.mobileNo && (
              <span style={errorStyle}>{errors.mobileNo}</span>
            )}
          </div>

          <div style={{ marginBottom: "15px" }}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Alternate Number<span className="text-danger">*</span>
          </label></label></label> 
                     <input
              type="text"
              name="alternateNumber"
              value={formData.alternateNumber}
              onChange={handleChange}
              placeholder="Alternate Number"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
          </div>
          <div style={{ marginBottom: "15px" }}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Emergency Contact Name 1<span className="text-danger">*</span>
          </label></label></label>  
                     <input
              type="text"
              name="emergencyContactname1"
              value={formData.emergencyContactname1}
              onChange={handleChange}
              placeholder="Enter Emergency Contact Name 1"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.emergencyContactname1 && (
              <span style={errorStyle}>{errors.emergencyContactname1}</span>
            )}
          </div>

          <div style={{ marginBottom: "15px" }}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Emergency Contact Name 2<span className="text-danger">*</span>
          </label></label></label>             <input
              type="text"
              name="emergencyContactname2"
              value={formData.emergencyContactname2}
              onChange={handleChange}
              placeholder="Enter Emergency Contact Name 2"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.emergencyContactname2 && (
              <span style={errorStyle}>{errors.emergencyContactname2}</span>
            )}
          </div>
        </div>

        <div>
          <div style={{ marginBottom: "20px" }}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Email Id<span className="text-danger">*</span>
          </label></label></label>             <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Email Id"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.email && <span style={errorStyle}>{errors.email}</span>}
          </div>
          <div style={{ marginBottom: "10px" }}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Alternate Email Id<span className="text-danger">*</span>
          </label></label></label>              <input
              type="email"
              name="alternateEmail"
              value={formData.alternateEmail}
              onChange={handleChange}
              placeholder="Alternate Email Id"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.alternateEmail && (
              <span style={errorStyle}>{errors.alternateEmail}</span>
            )}
          </div>
          <div style={{ marginBottom: "15px" }}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Emergency Contact No. 1<span className="text-danger">*</span>
          </label></label></label> 
            <input
              type="text"
              name="emergencyContact1"
              value={formData.emergencyContact1}
              onChange={handleChange}
              placeholder="Enter Emergency Contact No. 1"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.emergencyContact1 && (
              <span style={errorStyle}>{errors.emergencyContact1}</span>
            )}
          </div>

          <div style={{ marginBottom: "15px" }}>
          <label htmlFor="countryId"><label htmlFor="firstName"><label htmlFor="ticketSubject" className="ticket-text-primary" >
          Emergency Contact No. 2<span className="text-danger">*</span>
          </label></label></label> 
            <input
              type="text"
              name="emergencyContact2"
              value={formData.emergencyContact2}
              onChange={handleChange}
              placeholder="Enter Emergency Contact No. 2"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.emergencyContact2 && (
              <span style={errorStyle}>{errors.emergencyContact2}</span>
            )}
          </div>
        </div>
      </div>

      <div style={formRowStyle}>
        <button type="submit" style={submitButtonStyle}>
        {isEditing ? "Update" : "Save"}
        </button>
      </div>
    </form>
  );
}

const errorStyle = {
  color: "red",
  fontSize: "12px",
  marginTop: "5px",
};
const formRowStyle = {
  width: "100%",
  display: "flex",
  marginBottom: "20px",
};
const submitButtonStyle = {
  width: "150px",
  padding: "10px 20px",
  borderRadius: "4px",
  border: "none",
  backgroundColor: "#007bff",
  color: "#fff",
  fontSize: "16px",
  cursor: "pointer",
  textAlign: "center",
};

export default ContactDetailsForm;