"use client";
import { useState, useEffect } from "react";
import { Con<PERSON><PERSON>, <PERSON>, Col, But<PERSON>, Table } from "react-bootstrap";
import { useTheme } from "@material-ui/core/styles";
import Layout from "@/components/Layout";
import { TabPanel } from "../../../common-components/utils.js";
import CreateDocumentModal from "../modals/CreateDocumentModal";
import EditIcon from "@mui/icons-material/Edit";
import TableFilter from "../../../common-components/TableFilter2.js";
import { getRequest, postRequest, deleteRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import CreateExcelModal from "../modals/CreateExcelModal";

const DocumentPage = () => {
  const [documents, setDocuments] = useState<any[]>([]);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [page, setPage] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [editDocument, setEditDocument] = useState<any>(null);
  const [username, setUsername] = useState("");
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [showExcelUploadModal, setShowExcelUploadModal] = useState(false);
  const [loading, setLoading] = useState(true);  // Loading state to show loading spinner

  const theme = useTheme();
  const [value, setValue] = useState(0);

  useEffect(() => {
    fetchDocuments();

    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem("username");
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
  }, [page, limit]);

  useEffect(() => {
    fetchDocuments();
  }, [offset, limit]);

  const fetchDocuments = async () => {
    setLoading(true);
    try {
      const payload = {
        offset: offset,
        limit: limit,
      };
  
      const response = await postRequest(API_URLS.DOCUMENT_LIST, payload);
      // console.log("Documents fetched:", response); 
      setTotalItems(response?.totalElements);
  
      const { content, totalElements } = response;
  
      const rowsWithId = content.map((row: any, index: number) => ({
        ...row,
        id: row.documentId || index + 1, 
        srno: offset * limit + index + 1, 
      }));
  
      setDocuments(rowsWithId);
      setTotalDocuments(totalElements || 0);
    } catch (error) {
      // console.error("Error fetching documents:", error);
    } finally {
      setLoading(false);
    }
  };
  

  const handleSaveDocument = async (newDocument: any) => {
    try {
      const updatedDocuments = [...documents];
      
      if (editDocument) {
        const index = updatedDocuments.findIndex(
          (doc) => doc.documentId === editDocument.documentId  
        );
        if (index !== -1) {
          updatedDocuments[index] = { ...newDocument, documentId: editDocument.documentId };  
        }
      } else {
        const newDocWithId = { ...newDocument, documentId: documents.length + 1 }; 
        updatedDocuments.unshift(newDocWithId); 
        setTotalDocuments(totalDocuments + 1);  
      }
      const rowsWithId = updatedDocuments.map((row: any, index: number) => ({
        ...row,
        srno: (offset * limit) + (index + 1),  
      }));
  
      // console.log("Updated documents:", rowsWithId);
  
      setDocuments(rowsWithId);  
      setShowModal(false); 
      setEditDocument(null);  
    } catch (error) {
      // console.error("Error saving document:", error); 
    }
  };
  
  const handleEdit = (document: any) => {
    setEditDocument(document);
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setEditDocument(null);
    setShowExcelUploadModal(false);
  };

  const handlePageChange = (newPage: any) => {
    setOffset(newPage?.page);
    setLimit(newPage?.pageSize);
  };

  const columns = [
    {
      field: "srno",
      headerName: "Sr.no",
      width: 100,
    },
    { field: "name", headerName: "Name", width: 200 },
    { field: "type", headerName: "Type", width: 200 },
    {
      field: "isMandatory",
      headerName: "Is Mandatory",
      width: 150,
      renderCell: (params: any) => (params.row.isMandatory ? "Yes" : "No"),
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 200,
      renderCell: (params: any) => (
        <div>
          <Button
            onClick={() => handleEdit(params.row)}
            style={{
              backgroundColor: "transparent",
              border: "none",
              color: "black",
            }}
          >
            <EditIcon />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        {loading ? (
          <div>Loading...</div>  
        ) : (
          <>
            <Row className="my-3">
              <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
                <h4>Document List</h4>
              </Col>
              <Col md={8}>
                <Row style={{ display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    style={{
                      width: "150px",
                      background: "green",
                      marginRight: "15px",
                      whiteSpace: "nowrap",
                    }}
                    onClick={() => setShowModal(true)}
                  >
                    + Add Document
                  </Button>
                </Row>
              </Col>
            </Row>

            <Row>
              <Col md={12}>
                <TabPanel value={value} index={0}>
                  <TableFilter
                    rows={documents}
                    columns={columns}
                    onPageChange={handlePageChange}
                    totalCount={totalItems}
                    pageSize={limit}
                  />
                </TabPanel>
              </Col>
            </Row>

            {showModal && (
              <CreateDocumentModal
                show={showModal}
                handleClose={handleModalClose}
                onSave={handleSaveDocument}
                document={editDocument}
              />
            )}
          </>
        )}
      </Container>
    </Layout>
  );
};

export default DocumentPage;
