"use client";
import React, { useEffect, useState } from "react";
import "./Admin.css";
import "../../css/statuscard.css";
import Team from "../../components/Team/Team";
import Layout from "../../components/Layout";
import Devices from "../../modules/AdminDashboard/Devices/device";
import Calenderr from "../../components/Calender/Calenderr";
import { appConstants } from "../../constants/appConstants";
import useLocalStorage from "@/services/localstorage";
import TopFacilities from "../../modules/AdminDashboard/TopFacilities/topFacilities";
import CountComponent1 from "../../modules/AdminDashboard/CommomCountCard/countComponentWithIcon";
import CountComponent2 from "../../components/Tickets/countComponentWithoutIcon";
import ScheduleReport from "../../modules/AdminDashboard/ScheduleWiseReport/scheduleReport";
import LeaveBalanceChart from "@/modules/EmployeeDashboard/LeaveChart";
import WeeklyAttendance from "@/modules/EmployeeDashboard/Weeklywise";
import WeeklyWorkingHours from "@/modules/EmployeeDashboard/HoursCheck";
import TicketsChart from "@/modules/EmployeeDashboard/TicketComponent";
import EmployeeCalender from "@/modules/EmployeeDashboard/EmployeeCalender";
import PieChartComponent from "@/modules/Dashboard/pieChartComponent";
import TotalDepartmentsChart from "@/modules/Dashboard/TotalDepartmentsChart";
import { getRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";

const AdminDashboard = () => {
  const [username, setusername] = useLocalStorage(appConstants?.username, "");
  const [isMounted, setIsMounted] = useState(false);

  const [blocksData, setBlocksData] = useState<any>([]);

  useEffect(() => {
    getDashboardData();

    if (typeof window !== "undefined") {
      setTimeout(() => {
        const chart = document.querySelectorAll("[data-percentage]");
        chart.forEach((element) => {
          const percentage = element.getAttribute("data-percentage");
          if (percentage) {
            (element as HTMLElement).style.setProperty(
              "--percentage",
              percentage
            );
          }
        });
      }, 100);
    }
  }, []);

  const getDashboardData = async () => {
    try {
      const data = await getRequest(`${API_URLS.DASHBOARDS}?type=myDashboard`);
      setBlocksData(data);
    } catch (error) {}
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Layout>
      <div className="container-fluid bg-custom p-4 pt-5">
        <div className="row mb-3">
          <div className="col-12 col-md-9">
            {/* <p className="small" style={{ textTransform: "capitalize" }}>
              Hi, {username ? username?.toLowerCase() : ""}. Welcome back to
              Veritime Workforce Management System!
            </p> */}
          </div>
          {/* <Calenderr /> */}
        </div>
        <div className="row">
          {blocksData?.map((item: any, index: any) => {
            switch (item.type) {
              //card to show icon and count
              case "countComponentWithIcon":
                return (
                  <CountComponent1
                    data={item.value}
                    key={item.type + index}
                    handleViewClick={() => {
                      // Define what should happen when the view button is clicked
                      console.log("View clicked for:", item);
                    }}
                  />
                );
              //card to show count
              case "countcomponent":
                return (
                  <CountComponent2 data={item.value} key={item.type + index} />
                );
              case "activeDevicesCount":
                return <Devices data={item.value} key={item.type + index} />;
              case "top3branches":
                return (
                  <TopFacilities data={item.value} key={item.type + index} />
                );
              case "scheduleWiseReport":
                return (
                  <ScheduleReport data={item.value} key={item.type + index} />
                );
              case "pieChartComponent":
                return (
                  <PieChartComponent
                    data={item.value}
                    key={item.type + index}
                  />
                );
              case "totalDepartments":
                return (
                  <TotalDepartmentsChart
                    data={item.value}
                    key={item.type + index}
                  />
                );
              case "teamsCount":
                return <Team data={item.value} key={item.type + index} />;
              case "leaveBalanceChart":
                return <LeaveBalanceChart key={item.type + index} />;
              case "weeklyAttendance":
                return <WeeklyAttendance key={item.type + index} />;
              case "weeklyWorkingHours":
                return <WeeklyWorkingHours key={item.type + index} />;
              // case "myTicketsChart":
              //   return <TicketsChart data={item.value} key={item.type} />
              case "employeeCalender":
                return <EmployeeCalender key={item.type + index} />;
              default:
                return null;
            }
          })}
        </div>
      </div>
    </Layout>
  );
};

export default AdminDashboard;
