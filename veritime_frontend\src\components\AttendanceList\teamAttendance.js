import React, { useEffect, useState } from "react";
import TableFilter from "../../common-components/TableFilter";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../../components/AttendanceList/teamAttendance.css";
import { getRequest, postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import VisibilityIcon from "@mui/icons-material/Visibility";
import {
  Modal,
  Button,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";

const AttendanceTable = ({ fromDate, toDate, goButton }) => {
  const [filteredRows, setFilteredRows] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedEmpId, setSelectedEmpId] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [auditDetails, setAuditDetails] = useState([]);

  useEffect(() => {
    fetchAttendance();
  }, [goButton]);

  const fetchAttendance = async () => {
    try {
      const response = await getRequest(
        API_URLS.GET_ATTENDANCE_LIST(fromDate, toDate)
      );
      if (response) {
        const formattedRows = response.map((item, index) => ({
          srno: index + 1,
          id: index + 1,
          empId: item.empId,
          Name: item.empName,
          Date: item.date,
          "Start Time": item.checkInTime,
          "End Time": item.checkOutTime,
          "Entry mode": item.modeType,
          mode: item.modeType,
          overtime: item.overtime || "00:00", // Add this line
        }));

        setFilteredRows(formattedRows);
      }
    } catch (error) {}
  };
  const handleViewClick = async (empId, date) => {
    console.log("Clicked Employee ID:", empId);
    console.log("Clicked Date:", date, "Type:", typeof date);

    if (!empId) {
      console.error("empId is undefined! Check your table data.");
      return;
    }

    if (typeof date !== "string") {
      console.error("Date is not a string! Check data format.");
      return;
    }

    setSelectedEmpId(empId);
    setSelectedDate(date);
    setModalOpen(true);

    const formattedDate = new Date(date).toISOString().split("T")[0];
    const payload = { empId, date: formattedDate };

    console.log("Sending Payload:", payload);

    try {
      const response = await postRequest(
        API_URLS.ATTENDANCE_AUDIT_DETAILS,
        payload
      );
      console.log("API Response:", JSON.stringify(response, null, 2));

      if (Array.isArray(response) && response.length > 0) {
        setAuditDetails(response);
      } else {
        console.warn("No data returned from API.");
        setAuditDetails([]);
      }
    } catch (error) {
      console.error("Error fetching audit details:", error);
    }
  };

  const columns = [
    { field: "srno", headerName: "Sr.no", width: 80 },
    { field: "Name", headerName: "Name", width: 150 },
    { field: "Entry mode", headerName: "Entry mode", width: 150 },

    { field: "Date", headerName: "Date", width: 120 },
    { field: "Start Time", headerName: "Start Time", width: 120 },
    { field: "End Time", headerName: "End Time", width: 120 },
    { field: "overtime", headerName: "over Time", width: 120 },

    {
      field: "Action",
      headerName: "View",
      width: 80,
      renderCell: (params) =>
        params.row.mode === "scan" ? (
          <Button
            onClick={() => handleViewClick(params.row.empId, params.row.Date)}
          >
            <VisibilityIcon style={{ cursor: "pointer", color: "black" }} />
          </Button>
        ) : null,
    },
  ];

  return (
    <div id="tableWrapper" style={{ width: "100%", padding: "20px" }}>
      <TableFilter
        columns={columns}
        rows={filteredRows}
        getRowId={(row) => row.id}
      />

      <Modal open={modalOpen} onClose={() => setModalOpen(false)}>
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "50%",
            bgcolor: "background.paper",
            borderRadius: "10px",
            boxShadow: 24,
            p: 4,
          }}
        >
          <Typography
            sx={{
              position: "absolute",
              top: 10,
              right: 15,
              cursor: "pointer",
              fontSize: 20,
              fontWeight: "bold",
            }}
            onClick={() => setModalOpen(false)}
          >
            ✖
          </Typography>
          <Typography variant="h6" gutterBottom>
            Attendance Audit Details
          </Typography>
          {Array.isArray(auditDetails) && auditDetails.length > 0 ? (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <strong>Date</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Punch In Time</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Device Type</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Created Date</strong>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {auditDetails.map((detail, index) => (
                    <TableRow key={index}>
                      <TableCell>{detail.date}</TableCell>
                      <TableCell>{detail.punchInTime}</TableCell>
                      <TableCell>
                        {detail.deviceType.charAt(0).toUpperCase() +
                          detail.deviceType.slice(1)}
                      </TableCell>

                      <TableCell>{detail.createdDate}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography>Loading details...</Typography>
          )}

          <Button
            variant="contained"
            sx={{ mt: 2 }}
            onClick={() => setModalOpen(false)}
          >
            Close
          </Button>
        </Box>
      </Modal>
    </div>
  );
};

export default AttendanceTable;
