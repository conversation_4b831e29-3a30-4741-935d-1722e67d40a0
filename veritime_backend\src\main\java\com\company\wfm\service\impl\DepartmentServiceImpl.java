package com.company.wfm.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.dto.BranchDTO;
import com.company.wfm.dto.BranchFilterDTO;
import com.company.wfm.dto.CreateDepartmentDTO;
import com.company.wfm.dto.DepartmentDTO;
import com.company.wfm.dto.DepartmentIdNameDTO;
import com.company.wfm.dto.UpdateDepartmentDTO;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.DepartmentBranch;
import com.company.wfm.entity.User;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentBranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.HospitalRepository;
import com.company.wfm.repository.UserRepository;
import com.company.wfm.service.DepartmentService;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.util.CommonConstant;
import com.company.wfm.vo.BranchDetailVO;
import com.company.wfm.vo.BranchVO;
import com.company.wfm.vo.DepartmentBranchVO;
import com.company.wfm.vo.DepartmentVO;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DepartmentServiceImpl implements DepartmentService {
	private final DepartmentRepository departmentRepository;

	private final BranchRepository branchRepository;

	@Autowired
	DepartmentBranchRepository departmentBranchRepository;

	@Autowired
	HospitalRepository hospitalRepository;

	@Autowired
	EmployeeRepository employeeRepository;

	@Autowired
	JdbcTemplate jdbcTemplate;

	@Autowired
	private UserTokenService userTokenService;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private UserTokenService tokenService;

	@Autowired
	private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

	@Autowired
	public DepartmentServiceImpl(DepartmentRepository departmentRepository, BranchRepository branchRepository) {
		this.departmentRepository = departmentRepository;
		this.branchRepository = branchRepository;
	}

	@Transactional
	@Override
	public Department createDepartment(CreateDepartmentDTO departmentDTO) {
		if (departmentRepository.existsByDepartmentCode(departmentDTO.getDepartmentCode())) {
			throw new IllegalArgumentException("Department code already exists");
		}

		Department department = new Department();
		department.setDepartmentName(departmentDTO.getDepartmentName());
		department.setDepartmentCode(departmentDTO.getDepartmentCode());
		department.setCategory(departmentDTO.getCategory()); // Set the category
		// department.setBranchIds(departmentDTO.getBranchIds()); // This will handle
		// null case
		department.setCreatedAt(LocalDateTime.now());
		department.setUpdatedAt(LocalDateTime.now());

		return departmentRepository.saveAndFlush(department);
	}

	@Override
	public Object getAllBranches(BranchFilterDTO filter, Pageable pageable) {
		log.info("Initiating call to fetch branch details");

		User employee = userTokenService.getEmployeeFromToken();

		// Check if the employee has the necessary authorization
		if (employee != null && (employee.getRole().equalsIgnoreCase("supervisor") || employee.getRole().equalsIgnoreCase("employee"))) {
			throw new RuntimeException("You are not authorized to view the branch details");
		}

		StringBuilder query = new StringBuilder();
		MapSqlParameterSource params = new MapSqlParameterSource();
		boolean hasCondition = false;

		// Apply filters based on user role
		if (employee.getRole().equalsIgnoreCase("superadmin")) {
			query.append(" AND branch.company_id = :companyId ");
			params.addValue("companyId", employee.getEmployee().getCompanyId());
			hasCondition = true;
		} else if (employee.getRole().equalsIgnoreCase("admin")) {
			query.append(" AND branch.branch_id = :branchId ");
			params.addValue("branchId", employee.getEmployee().getBranch().getId());
			hasCondition = true;
		} else if (employee.getRole().equalsIgnoreCase("ceo") && filter.getHospitalId() != null) {
			query.append(" AND branch.company_id = :companyId ");
			params.addValue("companyId", filter.getHospitalId());
			hasCondition = true;
		}

		// Apply search query filter
		if (!StringUtils.isEmpty(filter.getQuery())) {
			query.append(" AND (branch.branch_name LIKE :searchText OR hosp.hospital_name LIKE :searchText) ");
			params.addValue("searchText", "%" + filter.getQuery() + "%");
			hasCondition = true;
		}

		// Corrected Count Query Construction
		StringBuilder countQuery = new StringBuilder("SELECT COUNT(1) AS row_count FROM t_branch branch ");
		countQuery.append("INNER JOIN feeder_hospitals hosp ON branch.company_id = hosp.id WHERE 1=1 ");

		if (filter.getIsActive() != null) {
			countQuery.append(" AND branch.is_active = :isActive ");
			params.addValue("isActive", filter.getIsActive());
		}

// Execute the query
		long totalRecords = namedParameterJdbcTemplate.queryForObject(countQuery.toString(), params, Long.class);

		// Sorting logic
		if (StringUtils.isEmpty(filter.getType())) {
			query.append(" ORDER BY branch.branch_id DESC ");
		} else {
			query.append(" ORDER BY branch.branch_name ASC ");
		}

		// Apply pagination
		if (StringUtils.isEmpty(filter.getType())) {
			int offset = pageable.getPageNumber() * pageable.getPageSize();
			int limit = filter.getLimit();
			query.append(" OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY ");
			params.addValue("offset", offset);
			params.addValue("limit", limit);
		}

		// Fetch the list of branches
		List<BranchVO> branchList = namedParameterJdbcTemplate.query(
				CommonConstant.BranchQuery.BRANCH_EXTRACT_QUERY + query.toString(),
				params,
				(rs, rowNum) -> BranchVO.builder()
						.branchId(rs.getLong("branch_id"))
						.branchName(rs.getString("branch_name"))
						.branchCode(rs.getString("branch_code"))
						.branchHeadId(rs.getLong("BRANCH_HEAD_ID"))
						.branchHeadName(rs.getString("BRANCH_HEAD_NAME"))
						.hospitalId(rs.getLong("id"))
						.hospitalName(rs.getString("hospital_name"))
						.cluster(rs.getString("clustor_no"))
						.branchType(rs.getString("branch_type"))
						.createdAt(rs.getTimestamp("created_at") != null ? rs.getTimestamp("created_at").toLocalDateTime() : null)
						.createdBy(rs.getString("created_by"))
						.updatedAt(rs.getTimestamp("updated_at") != null ? rs.getTimestamp("updated_at").toLocalDateTime() : null)
						.updatedBy(rs.getString("updated_by"))
						.isActive(rs.getInt("is_active"))
						.leaveCreditDay(rs.getInt("leave_credit_day"))
						.timeZone(rs.getString("timezone"))

						.build()
		);

		// Return empty result if no branches are found
		if (branchList.isEmpty()) {
			if (StringUtils.isEmpty(filter.getType())) {
				return new PageImpl<>(Collections.emptyList(), pageable, totalRecords);
			} else {
				return Collections.emptyList();
			}
		}

		// If `filter.getType()` is empty, add department details
		if (StringUtils.isEmpty(filter.getType())) {
			MapSqlParameterSource departmentParams = new MapSqlParameterSource();
			departmentParams.addValue("branchList", branchList.stream().map(BranchVO::getBranchId).collect(Collectors.toList()));

			Map<Long, List<DepartmentVO>> departmentMap = namedParameterJdbcTemplate.query(
					CommonConstant.BranchQuery.DEPARTMENT_EXTRACT_QUERY,
					departmentParams,
					(rs, rowNum) -> DepartmentVO.builder()
							.branchId(rs.getLong("branch_id"))
							.departmentId(rs.getLong("DEPARTMENT_ID"))
							.departmentName(rs.getString("DEPARTMENT_NAME"))
							.isActive(rs.getInt("IS_ACTIVE_DEPARTMENT"))
							.build()
			).stream().collect(Collectors.groupingBy(DepartmentVO::getBranchId));

			branchList.forEach(branch -> branch.setDepartments(departmentMap.get(branch.getBranchId())));
		}

		// Return paginated results if `filter.getType()` is empty, else return the full list
		if (StringUtils.isEmpty(filter.getType())) {
			return new PageImpl<>(branchList, pageable, totalRecords);
		} else {
			return branchList;
		}
	}



	// Lookup for Deparatment
	@Override
	public List<String> getAllDepartmentNames() {
		return departmentRepository.findAllDepartmentNames();
	}

	@Override
	public List<DepartmentDTO> getAllDepartment() {

		List<Department> departments = departmentRepository.findAll();

		List<DepartmentDTO> departmentDTOS = new ArrayList<>();
		for (Department dept : departments) {
			DepartmentDTO departmentDTO;
			departmentDTO = new DepartmentDTO();

			departmentDTO.setDepartmentId(dept.getDepartmentId());
			departmentDTO.setDepartmentCode(dept.getDepartmentCode());
			departmentDTO.setDepartmentName(dept.getDepartmentName());
			departmentDTO.setCreatedBy(dept.getCreatedBy());
			departmentDTO.setUpdatedBy(dept.getUpdatedBy());
			departmentDTO.setCreatedAt(dept.getCreatedAt());
			departmentDTO.setUpdatedAt(dept.getUpdatedAt());
			List<DepartmentBranch> deptBranches = departmentBranchRepository.findByDepartmentId(dept.getDepartmentId());

			List<Long> branchIdsOfDept = deptBranches.stream().map(DepartmentBranch::getBranchId)
					.collect(Collectors.toList());

			List<Branch> branches1 = branchRepository.findByIdIn(branchIdsOfDept);

			List<BranchDTO> branchDTOS = branches1.stream().map(b -> {
				BranchDTO branchDTO = new BranchDTO();
				branchDTO.setId(b.getId());
				branchDTO.setBranchName(b.getBranchName());
				return branchDTO;
			}).collect(Collectors.toList());

			departmentDTO.setBranches(branchDTOS);
			departmentDTOS.add(departmentDTO);
		}
		return departmentDTOS;

	}


	@Override
	public List<DepartmentIdNameDTO> getAllActiveDepartmentIdsAndNames() {
		List<Object[]> results = departmentRepository.findAllActiveDepartmentIdsAndNames();
		return results.stream().map(result -> new DepartmentIdNameDTO((Long) result[0], (String) result[1]))
				.collect(Collectors.toList());
	}

	@Transactional
	@Override
	public void createDepartmentBranch(CreateDepartmentDTO departmentDTO) {

		if (departmentRepository.existsByDepartmentCode(departmentDTO.getDepartmentCode())) {
			throw new IllegalArgumentException("Department code already exists");
		}
		User employee = userTokenService.getEmployeeFromToken();
		Department department = new Department();
		department.setDepartmentName(departmentDTO.getDepartmentName());
		department.setDepartmentCode(departmentDTO.getDepartmentCode());
		department.setCategory(departmentDTO.getCategory()); // Set the category
		// department.setBranchIds(departmentDTO.getBranchIds());
		department.setCreatedAt(LocalDateTime.now());
		//department.setUpdatedAt(LocalDateTime.now());
		Long createdBy = employee.getEmployee().getEmpId();
		department.setCreatedBy(createdBy);

		Department savedDept = departmentRepository.saveAndFlush(department);

		for (String branchId : departmentDTO.getBranchIds()) {

			DepartmentBranch departmentBranch = new DepartmentBranch();
			departmentBranch.setBranchId(Long.valueOf(branchId));
			departmentBranch.setDepartmentId(savedDept.getDepartmentId());
			departmentBranchRepository.saveAndFlush(departmentBranch);
		}
	}

	@Override
	public Page<DepartmentBranchVO> searchDepartments(BranchFilterDTO filter) {
		log.info("Initiating call to fetch department details");
		User employee = userTokenService.getEmployeeFromToken();

		int pageNumber = filter.getOffset();
		Pageable pageable = PageRequest.of(filter.getOffset(), filter.getLimit());

		if (employee != null && (employee.getRole().equalsIgnoreCase("supervisor") || employee.getRole().equals("employee"))) {
			throw new RuntimeException("You are not authorized to view the branch details");
		}

		String query = "";
		String secondQuery = "";
		MapSqlParameterSource params = new MapSqlParameterSource();
		MapSqlParameterSource departmentParams = new MapSqlParameterSource();

		if (employee.getRole().equalsIgnoreCase("superadmin")) {
			query += " and branch.company_id = :companyId";
			params.addValue("companyId", employee.getEmployee().getCompanyId());
			secondQuery += " and branch.company_id = :companyId";
			departmentParams.addValue("companyId", employee.getEmployee().getCompanyId());
		} else if (employee.getRole().equalsIgnoreCase("admin")) {
			query += " and branch.branch_id = :branchId";
			params.addValue("branchId", employee.getEmployee().getBranch().getId());
			secondQuery += " and branch.branch_id = :branchId";
			departmentParams.addValue("branchId", employee.getEmployee().getBranch().getId());
		} else if (employee.getRole().equalsIgnoreCase("ceo")) {
			if (null != filter.getHospitalId()) {
				query += " and branch.company_id = :companyId";
				params.addValue("companyId", filter.getHospitalId());
			}
		}

		// Safely adding the search filter to the query
		if (!StringUtils.isEmpty(filter.getQuery())) {
			query += " and dept.department_name like :searchText";  // Use placeholder for dynamic value
			params.addValue("searchText", "%" + filter.getQuery().trim() + "%");
		}

		// Use parameterized query to prevent SQL injection
		String countQuery = CommonConstant.DepartmentQuery.DEPARTMENT_EXTRACT_COUNT_QUERY + query;
		int total = namedParameterJdbcTemplate.queryForObject(countQuery, params, (rs, rowNum) -> rs.getInt(1));

		if (total == 0) {
			return new PageImpl<>(Collections.emptyList(), pageable, total);
		}

		if (StringUtils.isEmpty(filter.getType())) {
			log.info("reached inside type");
			query += " ORDER BY dept.DEPARTMENT_ID DESC OFFSET :offset ROWS FETCH FIRST :limit ROWS ONLY";
			params.addValue("limit", filter.getLimit());
			params.addValue("offset", pageNumber * filter.getLimit());
		} else {
			log.info("reached outside type");
			query += " ORDER BY dept.DEPARTMENT_NAME ASC";
		}

		// Execute the main query with parameters
		String resultQuery = CommonConstant.DepartmentQuery.DEPARTMENT_EXTRACT_QUERY + query;
		List<DepartmentBranchVO> departmentList = namedParameterJdbcTemplate.query(resultQuery, params, (rs, rowNum) -> {
			return DepartmentBranchVO.builder()
					.departmentId(rs.getLong("DEPARTMENT_ID"))
					.departmentName(rs.getString("DEPARTMENT_NAME"))
					.departmentCode(rs.getString("DEPARTMENT_CODE"))
					.category(rs.getString("CATEGORY"))
					.isActive(rs.getInt("IS_ACTIVE_DEPARTMENT"))
					.createdBy(rs.getLong("CREATED_BY"))
					.createdByName(rs.getString("CREATED_BY_NAME"))
					.createdAt(rs.getTimestamp("CREATED_AT").toLocalDateTime())
					.updatedBy(rs.getLong("UPDATED_BY"))
					.updatedByName(rs.getString("UPDATED_BY_NAME"))
					.updatedAt(rs.getTimestamp("UPDATED_AT").toLocalDateTime())
					.build();
		});

		if (StringUtils.isEmpty(filter.getType())) {
			departmentParams.addValue("departmentList",
					departmentList.stream().map(DepartmentBranchVO::getDepartmentId).collect(Collectors.toList()));

			Map<Long, List<BranchDetailVO>> departmentMap = namedParameterJdbcTemplate
					.query(CommonConstant.DepartmentQuery.DEPARTMENT_BRANCH_EXTRACT_QUERY + secondQuery, departmentParams, (rs, rowNum) -> {
						return BranchDetailVO.builder()
								.departmentId(rs.getLong("department_id"))
								.branchId(rs.getLong("branch_id"))
								.branchName(rs.getString("branch_name"))
								.isActive(rs.getInt("is_active"))
								.build();
					}).stream().collect(Collectors.groupingBy(BranchDetailVO::getDepartmentId));

			departmentList.forEach(branch -> {
				branch.setBranchList(departmentMap.get(branch.getDepartmentId()));
			});
		}

		return new PageImpl<>(departmentList, pageable, total);
	}

	// delete department

	@Transactional
	@Override
	public void deleteDepartment(Long departmentId) {
		Department department = departmentRepository.findById(departmentId)
				.orElseThrow(() -> new EntityNotFoundException("Department not found"));
		department.setIsActiveDepartment(false);
		department.setUpdatedAt(LocalDateTime.now());
		departmentRepository.save(department);
	}


	// update department
	@Override
	@Transactional
	public void updateDepartmentBranch(UpdateDepartmentDTO departmentDTO) {
		Department department = departmentRepository.findById(departmentDTO.getDepartmentId())
				.orElseThrow(() -> new IllegalArgumentException("Department not found"));

		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		String loggedInUsername = authentication.getName();

		User loggedInUser = userRepository.findByUsername(loggedInUsername);
		Long currentUserId = loggedInUser.getId();

		department.setDepartmentName(departmentDTO.getDepartmentName());
		department.setDepartmentCode(departmentDTO.getDepartmentCode());
		department.setUpdatedBy(currentUserId);
		department.setUpdatedAt(LocalDateTime.now());
		department.setCategory(departmentDTO.getCategory());
		if (department.getIsActiveDepartment() != null && !department.getIsActiveDepartment()) {
			department.setIsActiveDepartment(true);
		}

		Department updatedDept = departmentRepository.saveAndFlush(department);


		// Manage branch mappings
		// First, delete existing branches associated with this department
		departmentBranchRepository.deleteByDepartmentId(updatedDept.getDepartmentId());

		// Now, add new branch mappings
		for (String branchId : departmentDTO.getBranchIds()) {
			DepartmentBranch departmentBranch = new DepartmentBranch();
			departmentBranch.setBranchId(Long.valueOf(branchId));
			departmentBranch.setDepartmentId(updatedDept.getDepartmentId());
			departmentBranchRepository.saveAndFlush(departmentBranch);
		}
	}

	@Override
	public boolean isDepartmentUser(Long empId, Long departmentId) {
		// Check if the employee belongs to the specified department
		return employeeRepository.existsByEmpIdAndDepartmentId(empId, departmentId);
	}



	//lookedup branch wise department

	@Override
	public List<DepartmentIdNameDTO> getDepartmentsForLoggedInUser() {
		try {
			Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
			String role = userTokenService.getEmployeeFromToken().getRole();
			if ("CEO".equalsIgnoreCase(role)) {
				// If the role is CEO, fetch all departments
				return departmentBranchRepository.findAllDepartmentsWithName();
			}
			if (loggedInEmpId == null) {
				throw new IllegalArgumentException("Employee ID not found for the logged-in user.");
			}
			Long branchId = employeeRepository.findBranchIdByEmployeeId(loggedInEmpId);

			if (branchId == null) {
				throw new IllegalArgumentException("Branch ID not found for the logged-in user.");
			}

			// Fetch departments with ID and name
			return departmentBranchRepository.findDepartmentsByBranchIdWithName(branchId);
		} catch (IllegalArgumentException ex) {
			// Log and rethrow the exception for the controller to handle
			System.err.println("An unexpected error occurred: " + ex.getMessage());
			throw ex; // Rethrowing for controller handling
		} catch (Exception ex) {
			// Log generic errors
			log.error("An unexpected error occurred: ",  ex);
			// Throw a runtime exception or custom exception
			throw new RuntimeException("Unable to fetch departments for the logged-in user.", ex);
		}
	}


	@Override
	public List<DepartmentIdNameDTO> getDepartmentsForLoggedInUser1(Long branchId) {
		try {
			if (branchId == null) {
				// If branchId is not provided, get it from the logged-in user's token
				Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

				if (loggedInEmpId == null) {
					throw new IllegalArgumentException("Employee ID not found for the logged-in user.");
				}
				branchId = employeeRepository.findBranchIdByEmployeeId(loggedInEmpId);

				if (branchId == null) {
					throw new IllegalArgumentException("Branch ID not found for the logged-in user.");
				}
			}

			// Fetch departments with ID and name for the provided branchId
			return departmentBranchRepository.findDepartmentsByBranchIdWithName(branchId);
		} catch (IllegalArgumentException ex) {
			// Log and rethrow the exception for the controller to handle
			log.error("Validation error: ",  ex);
			throw ex; // Rethrowing for controller handling
		} catch (Exception ex) {
			// Log generic errors
			log.error("An unexpected error occurred: ",  ex);
			// Throw a runtime exception or custom exception
			throw new RuntimeException("Unable to fetch departments for the provided branch.", ex);
		}
	}



}