"use client";
import { useEffect, useState } from "react";
import { Container, Row, Col, Button, Form } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter2.js";
import { useTheme } from "@material-ui/core/styles";
import Popover from "@mui/material/Popover";
import { styled } from "@mui/system";
import { TabPanel } from "../../../common-components/utils.js";
import Layout from "@/components/Layout";
import CreateDepartmentModal from "../modals/CreateDepartmentModal";
import DepartmentDetailModal from "../modals/DepartmentDetailModal";

import { API_URLS } from "@/constants/apiConstants.js";
import { getRequest, deleteRequest, postRequest } from "@/services/apiService";

import { appConstants } from "@/constants/appConstants.js";
import {
  addIdToData,
  convertBranchIdToId,
  convertDepartmentIdToId,
} from "@/services/utils.js";
import VisibilityIcon from "@mui/icons-material/Visibility";

import EditIcon from "@mui/icons-material/Edit";

function DepartmentList({ toggleMenu, expanded }: any) {
  // const [username, setusername] = useState(localStorage.getItem(appConstants?.username))
  const [username, setUsername] = useState(""); //value changed by deepak
  const [role, setRole] = useState("");
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const theme = useTheme();
  const [value, setValue] = useState(0);
  const [rows, setRows] = useState([]);
  const [rowToEdit, setRowToEdit] = useState<any>({});

  const [anchorEl, setAnchorEl] = useState(null);

  const [data, setData] = useState([]);
  const [branchNames, setBranchNames] = useState<any>([]);

  const [deptNames, setdeptNames] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  const [inputValue, setInputValue] = useState(""); // State to track user input separately

  // handle input change
  const handleInputChange = (e: any) => {
    setInputValue(e.target.value);

    //const value = e.target.value;

    //setInputValue(value); // Update the inputValue with what user types

    // If the input becomes empty, reset the table to show all rows
    // if (value === "") {
    //setRows(data); // Reset to full data when the search input is cleared
  };
  // };

  //  handle search when button is clicked
  const handleSearchChange = () => {
    const filteredRows = data.filter((row: any) =>
      row.departmentName.toLowerCase().includes(inputValue.toLowerCase())
    );
    setRows(filteredRows); // Update rows
    setSearchQuery(inputValue); // Update searchQuery
  };

  //Modal for showing Details

  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<any>(null);

  // Function to handle showing department details
  const handleShowDetails = (department: any) => {
    setSelectedDepartment(department);
    setShowDetailModal(true);
  };

  // Function to close the modal
  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedDepartment(null);
  };

  //fetching Branch Name

  const fetachBranchNames = async () => {
    //alert("hii");
    try {
      //const branchResponse = await getRequest(`${API_URLS.GET_BRANCH}`);
      const payload = {
        type: "select",
      };

      const branchResponse = await postRequest(API_URLS.GET_BRANCH, payload);
      const responseCode = branchResponse.status;
      //alert(branchResponse.length);
      if (branchResponse.length > 0) {
        setBranchNames(convertBranchIdToId(branchResponse));
      } else {
        // console.error("Invalid branch response:", branchResponse);
      }
    } catch (error) {
      //showErrorAlert('Failed to delete the leave.');
      // console.error("Error fetching department List:", error);
    }
  };

  const handleSearch = async () => {
    // try {
    // const response = await getRequest(`${API_URLS.DEPT_ALL(searchQuery)}`);
    // if (response?.content?.length > 0) {
    //     const rows = response?.content?.map((holiday: any) => ({
    //     }));
    // setHolidays(rows);
  };
  // } catch (error) {
  //     console.error('Error searching holidays:', error);
  // }
  // };

  const fetachdeptNames = async () => {
    try {
      const departmentResponse = await getRequest(`${API_URLS.GET_DEPARTMENT}`);

      if (departmentResponse && departmentResponse.length > 0) {
        // const deptNamesArray = departmentResponse.map((department: any) => department.departmentName);
        setdeptNames(departmentResponse);
      }
      // if (departmentResponse && typeof departmentResponse === 'object') {
      //   const deptNamesArray = Object.values(departmentResponse);
      //   setdeptNames(deptNamesArray.map((department:any) => department.departmentName));
      // } else {
      //   console.error('Invalid department response:', departmentResponse);
      // }
    } catch (error) {
      // console.error("Error fetching department names:", error);
    }
  };

  const handlePageChange = (newPage: any) => {
    setOffset(newPage?.page);
    setLimit(newPage?.pageSize);
  };

  useEffect(() => {
    //fetachdeptNames();
    fetachBranchNames();
    // fetchItems(limit, offset);
    setIsMounted(true);
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem(appConstants?.username);
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }

    if (typeof window !== "undefined") {
      const storedRole = localStorage.getItem(appConstants?.role);
      if (storedRole) {
        setRole(storedRole);
      }
    }
  }, []);

  useEffect(() => {
    fetchItems(limit, offset);
  }, [limit, offset]);

  const fetchItems = async (limit: any, offset: any) => {
    try {
      const request = {
        limit: limit,
        offset: offset,
      };
      const data = await postRequest(API_URLS.DEPT_LIST, request);
      console.log("department data", data);
      if (data) {
        setTotalItems(data.totalElements);
        const transformedData = convertDepartmentIdToId(data?.content, request);
        // console.log("tranformed data=", transformedData);
        setData(transformedData);
        setRows(transformedData);
      }
    } catch (error) {
      console.error("Error fetching items:", error);
    }
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const handleChange = (event: any, newValue: any) => {
    setValue(newValue);
  };

  const handleChangeIndex = (index: any) => {
    setValue(index);
  };

  const handleClick = (event: any) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleEdit = (row: any, branchIds: any) => {
    setOpenModal(true);

    setRowToEdit({ ...row, branchIds: branchIds });
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setRowToEdit(null);
    setRows([]);
    setOffset(0);
    fetchItems(limit, 0);
  };

  const [openModal, setOpenModal] = useState(false);
  const handleOpen = () => {
    setRowToEdit(null);
    setOpenModal(true);
  };
  const [srCounter, setSrCounter] = useState(1);

  const columns = [
    {
      field: "srno",
      headerName: "Sr. No",
      width: 150,
      renderCell: (params: any) => {
        return params.row.srno;
      },
    },
    {
      field: "category",
      headerName: "Category",
      width: 200,
      renderCell: (params: any) => {
        return params.row.category ? (
          <span className="fw-bold">{params.row.category}</span>
        ) : (
          <span style={{ color: "gray" }}></span>
        );
      },
    },
    {
      field: "departmentName",
      headerName: "Department",
      width: 200,
      renderCell: (params: any) => {
        return params.row.departmentName ? (
          <span className="fw-bold">{params.row.departmentName}</span>
        ) : (
          <span style={{ color: "gray" }}>No Department Name</span>
        );
      },
    },
    {
      field: "branchList",
      headerName: "Facility",
      width: 425,
      renderCell: (params: any) => {
        const branches = params.row.branchList || [];
        const branchNames = branches
          .map((branch: any) => branch.branchName)
          .join(",");
        return (
          <div>
            <i title={branchNames}>{branchNames}</i>
          </div>
        );
      },
    },
    ...(role !== "admin" && role !== "superadmin"
      ? [
          {
            field: "action",
            headerName: "Action",
            width: 100,
            headerClassName: "action-header", // Add this line
            renderCell: (params: any) => {
              //console.log("Action Data:", params.row); // Logging the action data
              const branches = params.row.branchList || [];
              const branchIds = branches.map((branch: any) => branch.branchId);

              return (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                  }}
                >
                  {/* Eye Icon to open modal for viewing details */}
                  <VisibilityIcon
                    style={{ cursor: "pointer", marginRight: "10px" }}
                    onClick={() => handleShowDetails(params.row)}
                  />

                  <EditIcon
                    style={{ cursor: "pointer" }}
                    onClick={() => handleEdit(params.row, branchIds)}
                  />
                  {/* <Button
                    onClick={() => handleDelete(params.row.id)}
                    style={{
                      backgroundColor: "transparent",
                      border: "none",
                      color: "red",
                    }}
                  >
                    <DeleteIcon />
                  </Button> */}
                </div>
              );
            },
          },
        ]
      : []),
  ];

  const deleteRow = async (id: any) => {
    // try {
    //     const data = deleteData('departments/'+id);
    //     console.log(data,"data")
    // } catch (error) {
    //     console.error('Error fetching items:', error);
    // }
    setData(data.filter((el: any) => el.id !== id));
  };

  const handleChangeFilter = (event: any) => {
    // console.log("event::: ", event.target.value);

    if (event.target.name === "branch") {
      setRows(
        data.filter((el: any) => {
          // console.log("el?.branchIds::: ", el?.branchIds, event.target.value);
          if (el?.branchIds?.includes(event.target.value.toString())) {
            return el;
          }
        })
      );
    }
    if (event.target.name === "department") {
      setRows(
        data.filter((el: any) => el.departmentName === event.target.value)
      );
    }
  };

  const [isMounted, setIsMounted] = useState(false);

  if (!isMounted) return null;
  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px", background: "#BDCDD6" }}>
        <Row className="my-3">
          <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
            {" "}
            <Row>
              <Col>
                <h4>{"Department List"}</h4>
                {/* <span style={{ fontSize: "12px", color: "grey" }}>
                  Hi, {username ? username?.toUpperCase() : ""}. Your
                  organizations Deparments are listed here
                </span> */}
              </Col>
            </Row>
            <Row>
              {/* <Button style={{
              width: '100px', margin: 'auto',
              marginRight: '0px'
            }} aria-describedby={id} variant="contained" className={"carousal-container"} onClick={handleClick}>
              <FilterAltIcon />
            </Button> */}
              <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "left",
                }}
              >
                <div>
                  <select
                    className="filter-select"
                    onChange={handleChangeFilter}
                  >
                    <option selected>Select Facility</option>
                    {branchNames.length &&
                      branchNames.map((el: any, i: any) => (
                        <option key={el.id}>{el.branchName}</option>
                      ))}
                  </select>
                </div>
                <hr />

                {/* <div>
                  <select
                    className="filter-select"
                    onChange={handleChangeFilter}
                  >
                    <option disabled selected>
                      Select Department
                    </option>
                    {deptNames.length &&
                      deptNames.map((el, i) => <option key={i}>{el}</option>)}
                  </select>
                </div> */}

                <div></div>
              </Popover>
            </Row>
          </Col>
          <Col md={8}>
            {role === "ceo" && (
              <Row style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  style={{
                    width: "200px",
                    background: "green",
                    marginRight: "0",
                  }}
                  onClick={handleOpen}
                >
                  + New Department{" "}
                </Button>
              </Row>
            )}
            <Row className="filter-container">
              <Col md={4}>
                <Form.Group controlId="searchQuery" className="mb-3">
                  <Form.Control
                    type="text"
                    placeholder="Search"
                    value={inputValue} // Use inputValue
                    onChange={handleInputChange} // Handle input changes
                    style={{
                      width: "100%",
                      padding: "10px",
                      marginTop: "20px",
                    }}
                  />
                </Form.Group>
              </Col>

              <Col
                md={2}
                className="d-flex align-items-center mb-3"
                style={{ marginTop: "25px" }}
              >
                <Button
                  variant="primary"
                  onClick={handleSearchChange}
                  style={{ width: "80%" }}
                >
                  Apply
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
        <Row style={{ margin: "10px" }}>
          {/* <Col md={2} style={{ borderRight: '7px solid #BDCDD6', backgroundColor: "#6097b4" }}>
              <Tabs
                value={value}
                onChange={handleChange}
                indicatorColor="secondary"
                textColor="inherit"
                variant="fullWidth"
                aria-label="full width tabs example"
                className="tab-pannel-test "
              >
                <Tab label={`Nurse (${data.length})`} {...a11yProps(0)} style={{color: "#000", backgroundColor: "#6097b4"}}/>
                <Tab label={`Doctors (${data.length})`} {...a11yProps(1)} style={{color: "#000", backgroundColor: "#6097b4"}}/>
              </Tabs>
              {/* <Row className='carousal-container'>
                <CarouselComp setValue={setValue} arr={["Nurse", "Doctor"]} />
              </Row> */}
          {/* </Col> */}
          <Col md={10} style={{ background: "white", width: "100%" }}>
            {/* <SwipeableViews
              axis={theme.direction === "rtl" ? "x-reverse" : "x"}
              index={value}
              onChangeIndex={handleChangeIndex}
            > */}
            <TabPanel value={value} index={0} dir={theme.direction}>
              <Row>
                <div id="tableWrapper">
                  <TableFilter
                    columns={columns}
                    rows={rows}
                    onPageChange={handlePageChange}
                    totalCount={totalItems}
                    pageSize={limit}
                  />
                </div>
              </Row>
            </TabPanel>
            {/* <TabPanel value={value} index={1} dir={theme.direction}>
              <Row>
                <div id="tableWrapper">
                  <TableFilter columns={columns} rows={data} />
                </div>
              </Row>
            </TabPanel> */}
            {/* </SwipeableViews> */}
          </Col>
        </Row>
        {openModal && (
          <CreateDepartmentModal
            show={openModal}
            handleClose={handleCloseModal}
            branchNames={branchNames}
            rowToEdit={rowToEdit}
          />
        )}

        {/* Your table and other content */}

        <DepartmentDetailModal
          show={showDetailModal}
          onClose={handleCloseDetailModal}
          departmentDetails={selectedDepartment}
        />
      </Container>
    </Layout>
  );
}

export default DepartmentList;

const PopupBody = styled("div")(
  ({ theme }) => `
    width: max-content;
    padding: 12px 16px;
    margin: 8px;
    border-radius: 8px;
    border: 1px solid ${theme.palette.mode === "dark" ? grey[700] : grey[200]};
    background-color: ${theme.palette.mode === "dark" ? grey[900] : "#fff"};
    box-shadow: ${
      theme.palette.mode === "dark"
        ? `0px 4px 8px rgb(0 0 0 / 0.7)`
        : `0px 4px 8px rgb(0 0 0 / 0.1)`
    };
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    z-index: 1;
  `
);

const FilterButton = styled("button")(
  ({ theme }) => `
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: ${blue[500]};
    padding: 8px 16px;
    border-radius: 8px;
    color: white;
    transition: all 150ms ease;
    cursor: pointer;
    border: 1px solid ${blue[500]};
    box-shadow: 0 2px 4px ${
      theme.palette.mode === "dark"
        ? "rgba(0, 0, 0, 0.5)"
        : "rgba(0, 127, 255, 0.5)"
    }, inset 0 1.5px 1px ${blue[400]}, inset 0 -2px 1px ${blue[600]};
  
    &:hover {
      background-color: ${blue[600]};
    }
  
    &:active {
      background-color: ${blue[700]};
      box-shadow: none;
    }
  
    &:focus-visible {
      box-shadow: 0 0 0 4px ${
        theme.palette.mode === "dark" ? blue[300] : blue[200]
      };
      outline: none;
    }
  
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      box-shadow: none;
      &:hover {
        background-color: ${blue[500]};
      }
    }
  `
);

const grey = {
  50: "#F3F6F9",
  100: "#E5EAF2",
  200: "#DAE2ED",
  300: "#C7D0DD",
  400: "#B0B8C4",
  500: "#9DA8B7",
  600: "#6B7A90",
  700: "#434D5B",
  800: "#303740",
  900: "#1C2025",
};

const blue = {
  200: "#99CCFF",
  300: "#66B2FF",
  400: "#3399FF",
  500: "#007FFF",
  600: "#0072E5",
  700: "#0066CC",
};
