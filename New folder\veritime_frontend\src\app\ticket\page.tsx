"use client";
import React, { useState, useEffect, useRef } from "react";
import Layout from "../../components/Layout";
import ViewTicketBtn from "../../modules/Ticket/TicketRaise/ViewTicketBtn";
import TicketHistory from "../../modules/Ticket/TicketRaise/Tickethistory";
import TicketInformation from "../../modules/Ticket/TicketRaise/TicketInfo";
import TicketSubject from "../../modules/Ticket/TicketRaise/Subject";
import Attachments from "../../modules/Ticket/TicketRaise/Attachments";
import { fileUploadRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import Swal from "sweetalert2";
import "./Ticket.css";
import { showSuccessAlert2 } from "@/services/alertService";
import { appConstants } from "@/constants/appConstants";
import { decryptData } from "@/utils/encryption";

const Ticket = () => {
  const [formData, setFormData] = useState({
    category: "",
    status: "Open",
    departmentId: "",
    priority: "",
    ticketSubject: "",
    ticketMessage: "",
  });

  const [username, setUsername] = useState("");
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem(appConstants?.username);
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
  }, []);

  const [selectedFiles, setSelectedFiles] = useState<any>([]);
  const ticketHistoryRef: any = useRef();
  const handleFileChange = (files: any) => {
    setSelectedFiles(files);
  };

  const handleCreateTicket = async () => {
    // Validation: Check for empty fields
    const { category, priority, ticketSubject, ticketMessage } = formData;

    if (!category || !priority || !ticketSubject || !ticketMessage) {
      Swal.fire({
        title: "Validation Error",
        text: "Please fill in all required fields.",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return; // Stop execution if validation fails
    }

    Swal.fire({
      title: "Creating Ticket...",
      text: "Please wait",
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    const formdata = new FormData();

    formdata.append(
      "ticket",
      JSON.stringify({
        ticketSubject: formData.ticketSubject,
        ticketMessage: formData.ticketMessage,
        status: formData.status,
        category: formData.category,
        departmentId: formData.departmentId,
      })
    );

    if (selectedFiles.length > 0) {
      for (let i = 0; i < selectedFiles.length; i++) {
        formdata.append("files", selectedFiles[i], selectedFiles[i].name);
      }
    }

    try {
      const response = await fileUploadRequest(
        API_URLS.CREATE_TICKET,
        formdata
      );
      (async () => {
        const decryptedData = await decryptData(response.encryptedData);
        const ticketNO = decryptedData.ticketId;

        Swal.close();
        Swal.fire({
          title: "Success",
          text: `Ticket created successfully! Your Ticket Number is: ${ticketNO}.`,
          icon: "success",
          confirmButtonText: "OK",
        }).then(() => {
          // window.location.href = "/ticket";
          if (ticketHistoryRef?.current) {
            ticketHistoryRef?.current?.refreshTickets();
          }
          // Reset form and files
          setFormData({
            category: "",
            status: "Open",
            departmentId: "",
            priority: "",
            ticketSubject: "",
            ticketMessage: "",
          });
          setSelectedFiles([]);
        });
      })();

      const decryptedData: any = await decryptData(response.encryptedData);
      const ticketNO = decryptedData.ticketId;

      Swal.close();
      Swal.fire({
        title: "Success",
        text: `Ticket created successfully! Your Ticket Number is: ${ticketNO}.`,
        icon: "success",
        confirmButtonText: "OK",
      }).then(() => {
        // window.location.href = "/ticket";
        if (ticketHistoryRef?.current) {
          ticketHistoryRef?.current?.refreshTickets();
        }
        // Reset form and files
        setFormData({
          category: "",
          status: "Open",
          departmentId: "",
          priority: "",
          ticketSubject: "",
          ticketMessage: "",
        });
        setSelectedFiles([]);
      });
    } catch (error) {
      Swal.fire({
        title: "Error",
        text: "No support persons found.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };
  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Layout>
      <div className="container-fluid bg-custom p-4 pt-5">
        <div className="row mb-1">
          <div className="col-12 col-md-9">
            <h1 className="h3">Ticket</h1>
            {/* <p className="small">
              Hi, {username}. Raise Your Ticket to solve your Problem!
            </p> */}
          </div>
          <ViewTicketBtn />
        </div>
        <div className="row">
          <div className="col-12 col-md-3 col-lg-3">
            {/* @ts-ignore */}
            <TicketHistory ref={ticketHistoryRef} isFromCreateTicket={true} />
          </div>
          <div className="col-12 col-md-8 col-lg-9">
            <TicketInformation formData={formData} setFormData={setFormData} />
            <TicketSubject
              subjectData={formData}
              setSubjectData={setFormData}
            />
            <Attachments onFileChange={handleFileChange} />
            <div className="col-12 col-md-7 col-sm-12 mt-3 d-flex position-relative grid-layout justify-content-end">
              <button
                className="d-flex flex-row p-2 rounded-2"
                style={{
                  backgroundColor: "#007bff",
                  height: "40px",
                  border: "0px",
                }}
                onClick={handleCreateTicket}
              >
                <span className="d-flex flex-column fs-7 text-white">
                  Create Ticket
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Ticket;
