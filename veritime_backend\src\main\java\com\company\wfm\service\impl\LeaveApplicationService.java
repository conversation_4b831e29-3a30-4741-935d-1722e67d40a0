package com.company.wfm.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import com.company.wfm.dto.*;
import com.company.wfm.util.LeaveCreditBasis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.entity.Employee;
import com.company.wfm.entity.EmployeeLeaveApplication;
import com.company.wfm.entity.EmployeeLeaveBalance;
import com.company.wfm.entity.EmployeeLeaveHistory;
import com.company.wfm.entity.LeaveMaster;
import com.company.wfm.repository.EmployeeLeaveApplicationRepository;
import com.company.wfm.repository.EmployeeLeaveBalanceRepository;
import com.company.wfm.repository.EmployeeLeaveHistoryRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.LeaveMasterRepository;
import com.company.wfm.repository.UserRepository;
import com.company.wfm.service.AmazonS3Service;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.EmployeeCalendarService;
import com.company.wfm.service.UserTokenService;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;

@Service
public class LeaveApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(LeaveApplicationService.class);

    @Autowired
    private EmployeeLeaveApplicationRepository applicationRepository;
    @Autowired
    private EmployeeLeaveBalanceRepository balanceRepository;
    @Autowired
    private EmployeeLeaveHistoryRepository historyRepository;
    @Autowired
    private LeaveMasterRepository leaveMasterRepository;
    @Autowired
    private EmployeeLeaveHistoryRepository employeeLeaveHistoryRepository;
    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private SmsProcessingService smsProcessingService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private CommonNotificationService notificationService;

    @Autowired
    AmazonS3Service s3Service;

    @Autowired
    private EmployeeCalendarService employeeCalendarService;

    @Transactional
    public void applyLeave(Long employeeId, LeaveApplicationDTO dto, Long leaveCreatedBy, MultipartFile file, HttpServletRequest request) {
        LeaveMaster leaveMaster = leaveMasterRepository.findByType(dto.getLeaveType())
                .orElseThrow(() -> new IllegalArgumentException("Invalid leave type"));

        EmployeeLeaveBalance leaveBalance = balanceRepository.findByEmpIdAndLeaveId(employeeId, leaveMaster.getLeaveId())
                .orElseThrow(() -> new IllegalArgumentException("Leave balance not found"));

        // Check if the leave type is mandatory (assumes there is a 'mandatory' field in LeaveMaster entity)

        long appliedDays = ChronoUnit.DAYS.between(dto.getStartDate(), dto.getEndDate()) + 1;

        // Fetch the holidays and week-offs for the given date range
        List<Map<String, Object>> calendarData = employeeCalendarService.getEmployeeCalendar(employeeId, dto.getStartDate().getYear(), dto.getStartDate().getMonthValue());
        Set<String> holidayAndWeekOffDays = new HashSet<>();

        for (Map<String, Object> dayData : calendarData) {
            String shiftType = (String) dayData.get("shift_type");
            if ("holiday".equals(shiftType) || "week_off".equals(shiftType)) {
                holidayAndWeekOffDays.add((String) dayData.get("date"));
            }
        }

        // Exclude holidays and week-offs from applied days count
        long actualLeaveDays = appliedDays;
        LocalDate date = dto.getStartDate();
        while (!date.isAfter(dto.getEndDate())) {
            if (holidayAndWeekOffDays.contains(date.toString())) {
                actualLeaveDays--;
            }
            date = date.plusDays(1);
        }

      /*  if (leaveBalance.getBalanceLeave() < appliedDays) {
            throw new IllegalStateException("Insufficient leave balance");
        }*/

        // Check if the leave balance is sufficient
        if (leaveBalance.getBalanceLeave() < actualLeaveDays) {
            throw new IllegalStateException("Insufficient leave balance");
        }

        //mendotry
        String fileUrl = null; // Declare fileUrl here
        Boolean isMandatory = leaveMaster.getAllowFileUpload();
        if (isMandatory != null && isMandatory) {
            int minimumRequiredDays = leaveMaster.getMinimumCount(); // Add this field in LeaveMaster

            if (appliedDays > minimumRequiredDays) {
                if (file == null || file.isEmpty()) {
                    throw new IllegalArgumentException("This mandatory leave requires a file upload if applied days exceed the minimum (" + minimumRequiredDays + ") days.");
                }

                if (file != null && !file.isEmpty()) {
                   /* if (file.getSize() > MAX_FILE_SIZE1) {
                        throw new MaxUploadSizeExceededException(MAX_FILE_SIZE1);
                    }*/

                    // Upload file to S3 bucket
                    String fileName = file.getOriginalFilename(); // Get the file name
                    String uniqueFileName = "leavemasterid/" + leaveMaster.getLeaveId() + "/" + System.currentTimeMillis() + "_" + fileName;

                    s3Service.uploadFile(file,uniqueFileName);

                    // Set the file name to be saved in the database
                    fileUrl = uniqueFileName;
                }
            }
        }



        EmployeeLeaveApplication application = new EmployeeLeaveApplication();
        application.setEmpId(employeeId);
        application.setEmpLeaveId(leaveBalance.getEmpLeaveId());
        application.setCreatedBy(String.valueOf(leaveCreatedBy));
        application.setCreatedTime(LocalDateTime.now());
        application = applicationRepository.saveAndFlush(application);

       // leaveBalance.setBalanceLeave(leaveBalance.getBalanceLeave() - (int)appliedDays);
        leaveBalance.setBalanceLeave(leaveBalance.getBalanceLeave() - (int) actualLeaveDays);
        balanceRepository.saveAndFlush(leaveBalance);

        EmployeeLeaveHistory history = new EmployeeLeaveHistory();
        history.setEmpLeaveId(leaveBalance.getEmpLeaveId());
        history.setEmpId(employeeId);
        history.setLeaveId(leaveBalance.getLeaveId());
        history.setAppliedLeaveCount((int)appliedDays);
        history.setLeaveCount((int) actualLeaveDays); // Save the adjusted leave count excluding holidays/week-offs
        history.setStartDate(dto.getStartDate());
        history.setEndDate(dto.getEndDate());
        history.setApprovalStatus("PENDING");
        history.setReason(dto.getReason());
        history.setCreatedBy(String.valueOf(leaveCreatedBy));
        history.setCreatedTime(LocalDateTime.now());
        if (file != null && !file.isEmpty()) {
            history.setFilePath(fileUrl);
        }
        historyRepository.saveAndFlush(history);
        // notification and email applying the condition of behalf of
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
        Long upperId = employeeRepository.findUpperIdByEmpId(loggedInEmpId);
        if (upperId != 0) {
            notifyHigherAuthority(employeeId, leaveCreatedBy, appliedDays, dto.getStartDate(), dto.getEndDate(), dto.getEmpId() != null);
        }
    }

    private void notifyHigherAuthority(Long employeeId, Long leaveCreatedBy, long appliedDays, LocalDate startDate, LocalDate endDate, boolean isOnBehalf) {
        // Getting logged-in employee ID and name
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
        String loggedInEmpName = tokenService.getEmployeeFromToken().getEmployee().getEmpName();

        // Finding the supervisor (upper authority) of the logged-in employee
        Long upperId = employeeRepository.findUpperIdByEmpId(loggedInEmpId);
        if (upperId == null) {
          //  throw new IllegalStateException("No supervisor found for employee ID: " + loggedInEmpId);
            logger.error("No supervisor found for employee ID: "+loggedInEmpId +" to send notification");
            return;
        }

        String employeeName = null;
        if (isOnBehalf) {
            Employee employee = employeeRepository.findById(employeeId)
                    .orElseThrow(() -> new IllegalArgumentException("Employee not found for ID: " + employeeId));
            employeeName = employee.getEmpName();  // Get the employee name for the given employeeId
        }

        // Notify based on whether the leave application is on behalf of another employee
        String notificationMessage;
        String emailMessage;
        if (isOnBehalf) {
            notificationMessage = "Employee " + loggedInEmpName + " has applied for " + appliedDays + " days of leave on behalf of Employee " + employeeName + ".";
            emailMessage = "Employee " + loggedInEmpName + " has applied for " + appliedDays + " days of leave on behalf of Employee " + employeeName + ".";
        } else {
            notificationMessage = "Employee " + loggedInEmpName + " has applied for " + appliedDays + " days of leave.";
            emailMessage = "Employee " + loggedInEmpName + " has applied for " + appliedDays + " days of leave.";
        }

        // Send notification to higher authority
        String higherAuthorityToken = String.valueOf(upperId);
        if (higherAuthorityToken != null) {
            NotificationDTO notificationDTO = new NotificationDTO(
                    "Leave Application Submitted",
                    notificationMessage,
                    "Leave_Request",
                    String.valueOf(leaveCreatedBy),
                    LocalDateTime.now().toInstant(ZoneOffset.UTC)
            );

            notificationService.sendNotificationToEmployee(upperId, notificationDTO.getTitle(), notificationDTO.getBody(), "leave", String.valueOf(loggedInEmpId));
        } else {
            System.out.println("Higher authority token not found for employee ID: " + loggedInEmpId);
        }

        // Send email notification
        String emailId = employeeRepository.getReferenceById(upperId).getEmail();
        EmailData data = new EmailData();
        data.setEmailId(emailId);
        data.setSubject("Leave Application Submitted");
        data.setMessage(emailMessage);
        emailService.sendEmail(data);
    }



    @Transactional
    public void approveLeave(Long leaveHistoryId, String comment) {
        EmployeeLeaveHistory leaveHistory = historyRepository.findById(leaveHistoryId)
                .orElseThrow(() -> new EntityNotFoundException("Leave history not found"));

        if (!"PENDING".equals(leaveHistory.getApprovalStatus())) {
            throw new IllegalStateException("Can only approve pending leave requests");
        }

        leaveHistory.setApprovalStatus("APPROVED");
        leaveHistory.setComment(comment);
        leaveHistory.setUpdatedTime(LocalDateTime.now());
        leaveHistory.setUpdatedBy(tokenService.getEmployeeIdFromToken().toString());
        historyRepository.saveAndFlush(leaveHistory);

        // mail sending and notification sending work for approve
        notifyEmployeeOnLeaveApproval(leaveHistory, comment);
    }

    // mail sending and notification sending work for approve
    private void notifyEmployeeOnLeaveApproval(EmployeeLeaveHistory leaveHistory, String comment) {

        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();

        // Retrieve employee information for notification and email
        Long empId = leaveHistory.getEmpId();
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));
        String empName = employee.getEmpName();

        // Construct the notification and email messages
       /* String notificationMessage = "Your leave request from " + leaveHistory.getStartDate() + " to " + leaveHistory.getEndDate() + " has been approved.";
        String emailMessage = "Dear " + empName + ",\n\nYour leave request from " + leaveHistory.getStartDate() + " to " + leaveHistory.getEndDate() + " has been approved.";
*/
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");

        String notificationMessage = "Your leave request from "
                + leaveHistory.getStartDate().format(formatter)
                + " to "
                + leaveHistory.getEndDate().format(formatter)
                + " has been denied.";

        String emailMessage = "Dear " + empName + ",\n\nYour leave request from "
                + leaveHistory.getStartDate().format(formatter)
                + " to "
                + leaveHistory.getEndDate().format(formatter)
                + " has been denied.";

        // Send notification to the employee
        NotificationDTO notificationDTO = new NotificationDTO(
                "Leave Request Approved",
                notificationMessage,
                "leave",
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        //leave type ==== leave_approval
        notificationService.sendNotificationToEmployee(empId, notificationDTO.getTitle(), notificationDTO.getBody(), "leave", String.valueOf(LoggedInEmpId));

        // Send email notification to the employee
        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Leave Request Approved");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);


        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }
    }



    @Transactional
    public void denyLeave(Long leaveHistoryId, String comment) {
        EmployeeLeaveHistory leaveHistory = historyRepository.findById(leaveHistoryId)
                .orElseThrow(() -> new EntityNotFoundException("Leave history not found"));

        if (!"PENDING".equals(leaveHistory.getApprovalStatus())) {
            throw new IllegalStateException("Can only deny pending leave requests");
        }

        leaveHistory.setApprovalStatus("DENIED");
        leaveHistory.setComment(comment);
        historyRepository.saveAndFlush(leaveHistory);

        // Restore leave balance
        EmployeeLeaveBalance leaveBalance = balanceRepository.findById(leaveHistory.getEmpLeaveId())
                .orElseThrow(() -> new EntityNotFoundException("Leave balance not found"));
       // leaveBalance.setBalanceLeave(leaveBalance.getBalanceLeave() + leaveHistory.getAppliedLeaveCount());
        leaveBalance.setBalanceLeave(leaveBalance.getBalanceLeave() + leaveHistory.getLeaveCount());
        balanceRepository.saveAndFlush(leaveBalance);

        //notification and email sending if leave deny
        // Send notification and email to the employee about leave denial
        notifyEmployeeOnLeaveDenial(leaveHistory, comment);
    }

    private void notifyEmployeeOnLeaveDenial(EmployeeLeaveHistory leaveHistory, String comment) {

        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();
        // Retrieve employee information for notification and email
        Long empId = leaveHistory.getEmpId();
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));
        String empName = employee.getEmpName();

        // Construct the notification and email messages

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");

        String notificationMessage = "Your leave request from "
                + leaveHistory.getStartDate().format(formatter)
                + " to "
                + leaveHistory.getEndDate().format(formatter)
                + " has been denied.";

        String emailMessage = "Dear " + empName + ",\n\nYour leave request from "
                + leaveHistory.getStartDate().format(formatter)
                + " to "
                + leaveHistory.getEndDate().format(formatter)
                + " has been denied.";

       /* String notificationMessage = "Your leave request from " + leaveHistory.getStartDate() + " to " + leaveHistory.getEndDate() + " has been denied.";
        String emailMessage = "Dear " + empName + ",\n\nYour leave request from " + leaveHistory.getStartDate() + " to " + leaveHistory.getEndDate() + " has been denied.";*/

        // Send notification to the employee
        NotificationDTO notificationDTO = new NotificationDTO(
                "Leave Request Denied",
                notificationMessage,
                "leave",
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        // leave type ====leave_denial
        notificationService.sendNotificationToEmployee(empId, notificationDTO.getTitle(), notificationDTO.getBody(), "leave", String.valueOf(LoggedInEmpId));

        // Send email notification to the employee
        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Leave Request Denied");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }


    }


    @Transactional
    public void pullbackLeave(Long leaveHistoryId, String comment,Long empId) {
        EmployeeLeaveHistory leaveHistory = historyRepository.findById(leaveHistoryId)
                .orElseThrow(() -> new EntityNotFoundException("Leave history not found"));

       /* if ("APPROVED".equals(leaveHistory.getApprovalStatus())) {
            throw new IllegalStateException("Cannot pull back approved leave requests");
        }*/


        if (empId != null) {
            // Supervisor is pulling back leave
            if (!leaveHistory.getEmpId().equals(empId)) {
                throw new IllegalStateException("Leave history does not belong to the specified employee");
            }
            }

        // Check for approved leave
        if ("APPROVED".equals(leaveHistory.getApprovalStatus())) {
            // Ensure leave can be canceled any time before the start date
            LocalDate startDate = leaveHistory.getStartDate();
            if (startDate.isBefore(LocalDate.now())) {
                throw new IllegalStateException("Cannot pull back leave after it has started");
            }

            // Update status and restore leave balance
            leaveHistory.setApprovalStatus("PULLED_BACK");
            leaveHistory.setReason(comment);
            historyRepository.saveAndFlush(leaveHistory);

            EmployeeLeaveBalance leaveBalance = balanceRepository.findById(leaveHistory.getEmpLeaveId())
                    .orElseThrow(() -> new EntityNotFoundException("Leave balance not found"));
            leaveBalance.setBalanceLeave(leaveBalance.getBalanceLeave() + leaveHistory.getLeaveCount());
            balanceRepository.saveAndFlush(leaveBalance);

            //notifyEmployeeOnLeavePullbackSupervisorSendingNotification(leaveHistory, comment,empId);

            if (empId != null) {
                notifyEmployeeOnLeavePullbackSupervisorSendingNotification(leaveHistory, comment, empId);
            }

            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
            Long upperId = employeeRepository.findUpperIdByEmpId(loggedInEmpId);
            if (upperId != 0 && upperId != null) {
                notifyEmployeeOnLeavePullbackEmployee(leaveHistory, comment);
            }

            return;
        }


        if ("PENDING".equals(leaveHistory.getApprovalStatus())) {
            leaveHistory.setApprovalStatus("PULLED_BACK");
            leaveHistory.setReason(comment);
            historyRepository.saveAndFlush(leaveHistory);

            // Restore leave balance
            EmployeeLeaveBalance leaveBalance = balanceRepository.findById(leaveHistory.getEmpLeaveId())
                    .orElseThrow(() -> new EntityNotFoundException("Leave balance not found"));
            leaveBalance.setBalanceLeave(leaveBalance.getBalanceLeave() + leaveHistory.getLeaveCount());
            balanceRepository.saveAndFlush(leaveBalance);


                notifyEmployeeOnLeavePullbackPending(leaveHistory, comment);

        } else {
            throw new IllegalStateException("Can only pull back pending or approved leave requests");
        }
    }

    //send mail and notification
    private void notifyEmployeeOnLeavePullbackSupervisorSendingNotification(EmployeeLeaveHistory leaveHistory, String comment,Long empId) {
     //supervisor
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
        String loggedInEmpName = tokenService.getEmployeeFromToken().getEmployee().getEmpName();


        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + leaveHistory.getEmpId()));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");

        String notificationMessage = "The leave request of " +
                loggedInEmpName + " (from " +
                leaveHistory.getStartDate().format(formatter) + " to " +
                leaveHistory.getEndDate().format(formatter) +
                ") has been pulled back" +   "Reason: " + comment;


        NotificationDTO notificationDTO = new NotificationDTO(
                "Leave Request Pulled Back",
                notificationMessage,
                "leave",
                String.valueOf(leaveHistory.getEmpId()),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(empId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "leave",
                String.valueOf(loggedInEmpId));

        String emailMessage = "The leave request of " +
                loggedInEmpName + " (from " +
                leaveHistory.getStartDate().format(formatter) + " to " +
                leaveHistory.getEndDate().format(formatter) +
                ") has been pulled back" +   "Reason: " + comment;

        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Leave Request Pulled Back");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);


        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }

    }



    private void notifyEmployeeOnLeavePullbackPending(EmployeeLeaveHistory leaveHistory, String comment) {
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
        String loggedInEmpName = tokenService.getEmployeeFromToken().getEmployee().getEmpName();

        Employee employee = employeeRepository.findById(loggedInEmpId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + leaveHistory.getEmpId()));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");

        String notificationMessage = "The leave request of " +
                loggedInEmpName + " (from " +
                leaveHistory.getStartDate().format(formatter) + " to " +
                leaveHistory.getEndDate().format(formatter) +
                ") has been pulled back" +   "Reason: " + comment;


        NotificationDTO notificationDTO = new NotificationDTO(
                "Leave Request Pulled Back",
                notificationMessage,
                "leave",
                String.valueOf(leaveHistory.getEmpId()),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(leaveHistory.getEmpId(),
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "leave",
                String.valueOf(loggedInEmpId));

        String emailMessage = "The leave request of " +
                loggedInEmpName + " (from " +
                leaveHistory.getStartDate().format(formatter) + " to " +
                leaveHistory.getEndDate().format(formatter) +
                ") has been pulled back" +   "Reason: " + comment;

        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Leave Request Pulled Back");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        //finding the leave pull back  for sending this sms

        Employee employee1 = employeeRepository.findById(leaveHistory.getEmpId())
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + leaveHistory.getEmpId()));


        if (employee1.getMobileNo() != null && !employee1.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee1.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }

    }

    private void notifyEmployeeOnLeavePullbackEmployee(EmployeeLeaveHistory leaveHistory, String comment) {
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
        String loggedInEmpName = tokenService.getEmployeeFromToken().getEmployee().getEmpName();

        // Finding the supervisor (upper authority) of the logged-in employee
        Long upperId = employeeRepository.findUpperIdByEmpId(loggedInEmpId);
        if (upperId == null) {
            //  throw new IllegalStateException("No supervisor found for employee ID: " + loggedInEmpId);
            logger.error("No supervisor found for employee ID1: "+loggedInEmpId +" to send notification");
            return;
        }

        Employee supervisor = employeeRepository.findById(upperId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + leaveHistory.getEmpId()));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");

        String notificationMessage = "The leave request of " +
                loggedInEmpName + " (from " +
                leaveHistory.getStartDate().format(formatter) + " to " +
                leaveHistory.getEndDate().format(formatter) +
                ") has been pulled back" +   "Reason: " + comment;


        NotificationDTO notificationDTO = new NotificationDTO(
                "Leave Request Pulled Back",
                notificationMessage,
                "leave",
                String.valueOf(leaveHistory.getEmpId()),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(upperId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "leave",
                String.valueOf(loggedInEmpId));

        String emailMessage = "The leave request of " +
                loggedInEmpName + " (from " +
                leaveHistory.getStartDate().format(formatter) + " to " +
                leaveHistory.getEndDate().format(formatter) +
                ") has been pulled back" +   "Reason: " + comment;

        String emailId = supervisor.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Leave Request Pulled Back");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        if (supervisor.getMobileNo() != null && !supervisor.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", supervisor.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(supervisor.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }

    }

   /* public List<EmployeeLeaveHistory> getAllLeaveHistory() {
        return employeeLeaveHistoryRepository.findAll();
    }*/

    // new

    /*public List<LeaveHistoryResponseDTO> getAllLeaveHistoryWithEmployeeDetails() {

        List<EmployeeLeaveHistory> leaveHistories = employeeLeaveHistoryRepository.findAllByOrderByIdDesc();

        return leaveHistories.stream().map(history -> {
            LeaveHistoryResponseDTO dto = new LeaveHistoryResponseDTO();

            Optional<Employee> employee = employeeRepository.findById(history.getEmpId());

            // Setting the basic fields
            dto.setLeaveHistoryId(history.getId());
            dto.setEmpLeaveId(history.getEmpLeaveId());
            if(employee.isPresent()){
                dto.setEmployeeId(employee.get().getEmpId());
                dto.setEmpName(employee.get().getEmpName());
                dto.setDesignationName(employee.get().getDesignation().getName());
                dto.setImageUrl(employee.get().getImgUre());
            }else{
                logger.info("employee not found for empId {}",history.getEmpId());
            }

            dto.setAppliedLeaveCount(history.getAppliedLeaveCount());
            dto.setStartDate(history.getStartDate());
            dto.setEndDate(history.getEndDate());
            dto.setApprovalStatus(history.getApprovalStatus());
            dto.setReason(history.getReason());

            // Setting CreatedBy details
            dto.setCreatedBy(history.getCreatedBy());

            // Fetch createdByName using createdBy field, assuming Employee repository/service has a method to get Employee by ID
            Employee createdByEmployee = employeeRepository.findById(Long.parseLong(history.getCreatedBy())).orElse(null);
            dto.setCreatedByName(createdByEmployee != null ? createdByEmployee.getEmpName() : null);
            dto.setCreatedTime(history.getCreatedTime());

            // Conditional logic for updatedBy and updatedByName
            if (history.getUpdatedBy() != null) {
                dto.setUpdatedBy(history.getUpdatedBy());

                // Fetch updatedByName using updatedBy field, assuming Employee repository/service has a method to get Employee by ID
                Employee updatedByEmployee = employeeRepository.findById(Long.parseLong(history.getUpdatedBy())).orElse(null);
                dto.setUpdatedByName(updatedByEmployee != null ? updatedByEmployee.getEmpName() : null);
            } else {
                // If updatedBy is null, updatedByName should also be null
                dto.setUpdatedBy(null);
                dto.setUpdatedByName(null);
            }

            dto.setUpdatedTime(history.getUpdatedTime());

            // Setting the comment field
            dto.setComment(history.getComment());

            return dto;
        }).collect(Collectors.toList());
    }*/

    public List<LeaveHistoryResponseDTO> getAllLeaveHistoryWithEmployeeDetails(LocalDate date, String status) {
        Long loggedInEmpId = getCurrentAuthenticatedEmployeeId();
        // Fetch all employees whose upperId matches the logged-in employee ID
        List<Employee> employeesUnderLoggedIn = employeeRepository.findByUpperId(loggedInEmpId);
        // Extract the employee IDs from the list of employees under the logged-in employee
        List<Long> employeeIdsUnderLoggedIn = employeesUnderLoggedIn.stream()
                .map(Employee::getEmpId)
                .collect(Collectors.toList());


        // Fetch leave histories for employees under the logged-in employee
      //  List<EmployeeLeaveHistory> leaveHistories = employeeLeaveHistoryRepository.findAllByEmpIdInOrderByIdDesc(employeeIdsUnderLoggedIn);

        // Fetch leave histories based on conditions
        List<EmployeeLeaveHistory> leaveHistories;

        if (date != null && status != null) {
            // Filter by createdTime (date only) and approvalStatus
            leaveHistories = employeeLeaveHistoryRepository.findByEmpIdInAndCreatedTimeAndApprovalStatus(
                    employeeIdsUnderLoggedIn, date, status);
        } else if (date != null) {
            // Filter by createdTime (date only)
            leaveHistories = employeeLeaveHistoryRepository.findByEmpIdInAndCreatedTime(
                    employeeIdsUnderLoggedIn, date);
        } else if (status != null) {
            // Filter by approvalStatus only
            leaveHistories = employeeLeaveHistoryRepository.findByEmpIdInAndApprovalStatus(
                    employeeIdsUnderLoggedIn, status);
        } else {
            // Fetch all leave histories for employees under the logged-in employee
            leaveHistories = employeeLeaveHistoryRepository.findAllByEmpIdInOrderByIdDesc(employeeIdsUnderLoggedIn);
        }
        return leaveHistories.stream()
                .map(history -> {
                    LeaveHistoryResponseDTO dto = new LeaveHistoryResponseDTO();

                    Optional<Employee> employee = employeeRepository.findById(history.getEmpId());

                    // Fetch and set leave type using empLeaveId
                    dto.setLeaveId(history.getLeaveId());
                    leaveMasterRepository.findByLeaveId(history.getLeaveId()).ifPresent(leaveMaster -> {
                        dto.setLeaveTypeName(leaveMaster.getType());
                    });
                    // Setting the basic fields
                    dto.setLeaveHistoryId(history.getId());
                    dto.setEmpLeaveId(history.getEmpLeaveId());
                    // Fetch and set leave type using empLeaveId
                    leaveMasterRepository.findByLeaveId(history.getEmpLeaveId()).ifPresent(leaveMaster -> {
                        dto.setEmpLeaveName(leaveMaster.getType());
                    });
                    if (employee.isPresent()) {
                        dto.setEmployeeId(employee.get().getEmpId());
                        dto.setEmpName(employee.get().getEmpName());
                        dto.setDesignationName(employee.get().getDesignation().getName());
                        dto.setEmpCode(employee.get().getEmpCode());
                        dto.setImageUrl(employee.get().getImgUre());
                    } else {
                        logger.info("Employee not found for empId {}", history.getEmpId());
                    }

                    dto.setAppliedLeaveCount(history.getAppliedLeaveCount());
                    dto.setStartDate(history.getStartDate());
                    dto.setEndDate(history.getEndDate());
                    dto.setApprovalStatus(history.getApprovalStatus());
                    dto.setReason(history.getReason());

                    // Setting CreatedBy details
                    dto.setCreatedBy(history.getCreatedBy());

                    // Fetch createdByName using createdBy field
                    Employee createdByEmployee = employeeRepository.findById(Long.parseLong(history.getCreatedBy())).orElse(null);
                    dto.setCreatedByName(createdByEmployee != null ? createdByEmployee.getEmpName() : null);
                    dto.setCreatedTime(history.getCreatedTime());

                    // Conditional logic for updatedBy and updatedByName
                    if (history.getUpdatedBy() != null) {
                        dto.setUpdatedBy(history.getUpdatedBy());

                        // Fetch updatedByName using updatedBy field
                        Employee updatedByEmployee = employeeRepository.findById(Long.parseLong(history.getUpdatedBy())).orElse(null);
                        dto.setUpdatedByName(updatedByEmployee != null ? updatedByEmployee.getEmpName() : null);
                    } else {
                        dto.setUpdatedBy(null);
                        dto.setUpdatedByName(null);
                    }

                    dto.setUpdatedTime(history.getUpdatedTime());
                    dto.setComment(history.getComment());
                    dto.setFilePath(history.getFilePath());
                    return dto;
                })
                .collect(Collectors.toList());
    }



    //self history
    public List<LeaveHistoryResponseDTO> getSelfLeaveHistory(LocalDate date, String status) {
        Long currentEmpId = getCurrentAuthenticatedEmployeeId();
        // Find leave history records only for the current employee
       // List<EmployeeLeaveHistory> leaveHistories = employeeLeaveHistoryRepository.findByEmpIdOrderByIdDesc(currentEmpId);
        List<EmployeeLeaveHistory> leaveHistories;

        if (date != null && status != null) {
            leaveHistories = employeeLeaveHistoryRepository.findByEmpIdAndCreatedDateAndApprovalStatus(
                    currentEmpId, date, status);
        } else if (status != null) {
            // Filter only by status
            leaveHistories = employeeLeaveHistoryRepository.findByEmpIdAndApprovalStatus(
                    currentEmpId, status);
        } else {
            // Fetch all leave history if no filters are provided
            leaveHistories = employeeLeaveHistoryRepository.findByEmpIdOrderByIdDesc(currentEmpId);
        }


        return leaveHistories.stream().map(history -> {
            LeaveHistoryResponseDTO dto = new LeaveHistoryResponseDTO();

            // Set fields similar to getAllLeaveHistoryWithEmployeeDetails
            dto.setLeaveHistoryId(history.getId());
            dto.setEmpLeaveId(history.getEmpLeaveId());
            // Fetch and set leave type using empLeaveId
            leaveMasterRepository.findByLeaveId(history.getEmpLeaveId()).ifPresent(leaveMaster -> {
                dto.setEmpLeaveName(leaveMaster.getType());
            });
            dto.setLeaveId(history.getLeaveId());
            leaveMasterRepository.findByLeaveId(history.getLeaveId()).ifPresent(leaveMaster -> {
                dto.setLeaveTypeName(leaveMaster.getType());
            });
            dto.setAppliedLeaveCount(history.getAppliedLeaveCount());
            dto.setStartDate(history.getStartDate());
            dto.setEndDate(history.getEndDate());
            dto.setApprovalStatus(history.getApprovalStatus());
            dto.setReason(history.getReason());
            dto.setCreatedBy(history.getCreatedBy());
            // Fetch createdByName using createdBy field
            Employee createdByEmployee = employeeRepository.findById(Long.parseLong(history.getCreatedBy())).orElse(null);
            dto.setCreatedByName(createdByEmployee != null ? createdByEmployee.getEmpName() : null);
            dto.setCreatedTime(history.getCreatedTime());
            dto.setUpdatedBy(history.getUpdatedBy());
            dto.setUpdatedTime(history.getUpdatedTime());
            dto.setComment(history.getComment());


            // Set employee details
            Employee employee = employeeRepository.findById(history.getEmpId()).orElse(null);
            if (employee != null) {
                dto.setEmployeeId(employee.getEmpId());
                dto.setEmpName(employee.getEmpName());
                dto.setEmpCode(employee.getEmpCode());
                dto.setDesignationName(employee.getDesignation().getName());
                dto.setImageUrl(employee.getImgUre());
            }
            dto.setFilePath(history.getFilePath());

            return dto;
        }).collect(Collectors.toList());
    }

    // Helper method to get the current employee ID
    private Long getCurrentAuthenticatedEmployeeId() {
        // Assuming you use Spring Security to get the authenticated username
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        return userRepository.findByUsername(username).getEmployee().getEmpId();
    }



    public List<LeaveTypeDTO> getAllLeaveTypes() {
    List<LeaveMaster> leaveMasters = leaveMasterRepository.findAllActiveLeaveTypes();
    return leaveMasters.stream()
            .map(lm -> new LeaveTypeDTO(lm.getLeaveId(), lm.getType() ,lm.getLeaveCount(),lm.getAllowFileUpload(),lm.getMinimumCount(),lm.getDaysAppliedBefore(),lm.getIsActive()))
            .collect(Collectors.toList());
}
    public List<LeaveTypeDTO> getAllLeaveTypesByEmpId() {
        Long empId = tokenService.getEmployeeIdFromToken();
        List<LeaveMaster> leaveMasters = leaveMasterRepository.findAllActiveLeaveTypesByEmpid(empId);
        return leaveMasters.stream()
                .map(lm -> new LeaveTypeDTO(lm.getLeaveId(), lm.getType() ,lm.getLeaveCount(), lm.getAllowFileUpload(),lm.getMinimumCount(),lm.getDaysAppliedBefore(),lm.getIsActive()))
                .collect(Collectors.toList());
    }
    public LeaveCountDTO getLeaveRequestCounts() {
        return employeeLeaveHistoryRepository.getLeaveRequestCounts();
    }
    public LeaveCountDTO getLeaveRequestCounts(LocalDate startDate, LocalDate endDate) {
        return employeeLeaveHistoryRepository.getLeaveRequestCounts(startDate, endDate);
    }


    //ritesh

    @Transactional
    public LeaveMaster saveLeave(LeaveMaster leaveMaster) {
        try {
            Long empId = tokenService.getEmployeeIdFromToken();

            // Check if the type already exists in DB (for new entry only)
            Optional<LeaveMaster> existingType = leaveMasterRepository.findByType(leaveMaster.getType());
            if (existingType.isPresent() && leaveMaster.getLeaveId() == null) {
                throw new RuntimeException("Leave type '" + leaveMaster.getType() + "' already exists.");
            }

            leaveMaster.setCreatedBy(empId);
            leaveMaster.setCreatedTime(LocalDateTime.now());

            // Validate Leave Credit Basis Enum (if applicable)
            try {
                LeaveCreditBasis.fromString(leaveMaster.getLeaveCreditBasis());
            } catch (IllegalArgumentException e) {
                throw new RuntimeException("Invalid Leave Credit Basis: '" + leaveMaster.getLeaveCreditBasis() +
                        "'. Allowed values are: joiningDate, immediateAfterProbationPeriod, workedAfterProbationPeriod.");
            }

            if (leaveMaster.getLeaveCredited() != null) {
                leaveMaster.setLeaveCount(leaveMaster.getLeaveCredited());
            } else {
                leaveMaster.setLeaveCount(BigDecimal.ZERO);
            }

            boolean isNewEntry = (leaveMaster.getLeaveId() == null);
            if (leaveMaster.getEffectiveLeaveDate() != null && isNewEntry &&
                    leaveMaster.getEffectiveLeaveDate().isAfter(LocalDate.now())) {
                leaveMaster.setIsActive(false);
            } else {
                leaveMaster.setIsActive(true);
            }

            if (leaveMaster.getAllowFileUpload() != null && leaveMaster.getAllowFileUpload()) {
                leaveMaster.setMinimumCount(leaveMaster.getMinimumCount());
            }

            if ("lumpsum based".equalsIgnoreCase(leaveMaster.getLeaveCreditMethod())) {
                leaveMaster.setLeaveCreditInterval(null);
            }

            if (leaveMaster.getLeaveExclusion() != null && !leaveMaster.getLeaveExclusion().isEmpty()) {
                String exclusions = String.join(",", leaveMaster.getLeaveExclusion());
                leaveMaster.setLeaveExclusion(exclusions);
            }

            return leaveMasterRepository.saveAndFlush(leaveMaster);

        } catch (RuntimeException e) {
            // Optionally log the exception
            System.err.println("Error: " + e.getMessage());

            // You can either re-throw or return a special LeaveMaster with an error message
            throw new RuntimeException(e.getMessage());
        } catch (Exception e) {
            // Catch any other unexpected exceptions
            System.err.println("Unexpected error: " + e.getMessage());
            throw new RuntimeException("Something went wrong while saving the leave.");
        }
    }



   /* @Transactional
    public List<LeaveMaster> getAllLeaves(Boolean isActive) {
        if (isActive == null) {
            return leaveMasterRepository.findAllByOrderByLeaveIdDesc(); // Fetch all records
        } else if (isActive) {
            return leaveMasterRepository.findByIsActiveOrderByLeaveIdDesc(true);
        } else {
            return leaveMasterRepository.findInactiveLeaves();
        }
    }*/

    @Transactional
    public List<LeaveMaster> getAllLeaves(Boolean isActive) {
        List<Object[]> results;

        if (isActive == null) {
            results = leaveMasterRepository.findAllLeavesWithNames();
        } else if (isActive) {
            results = leaveMasterRepository.findActiveLeavesWithNames();
        } else {
            results = leaveMasterRepository.findInactiveLeavesWithNames();
        }

        List<LeaveMaster> leaveList = new ArrayList<>();
        for (Object[] row : results) {
            LeaveMaster leave = (LeaveMaster) row[0];
            leave.setCreatedByName((String) row[1]); // Set CreatedBy Name
            leave.setUpdatedByName((String) row[2]); // Set UpdatedBy Name
            leaveList.add(leave);
        }
        return leaveList;
    }




    // update

    @Transactional
    public LeaveMaster updateLeave(Long id, LeaveMaster leaveMaster) {
        Long empId = tokenService.getEmployeeIdFromToken();
        LeaveMaster existingLeaveMaster = leaveMasterRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Leave record not found"));

        // Update the existing record with the new values
        existingLeaveMaster.setType(leaveMaster.getType());
        existingLeaveMaster.setLeaveCount(leaveMaster.getLeaveCredited());
        existingLeaveMaster.setUpdatedBy(empId);
        existingLeaveMaster.setUpdatedTime(LocalDateTime.now());
        existingLeaveMaster.setDaysAppliedBefore(leaveMaster.getDaysAppliedBefore());
        existingLeaveMaster.setDescription(leaveMaster.getDescription());

        // Check and update allowFileUpload if the field is provided
        if (leaveMaster.getAllowFileUpload() != null) {
            existingLeaveMaster.setAllowFileUpload(leaveMaster.getAllowFileUpload());
        }

        // Check and update minimumCount if the field is provided
        if (leaveMaster.getMinimumCount() != null) {
            existingLeaveMaster.setMinimumCount(leaveMaster.getMinimumCount());
        }

        if (leaveMaster.getIsActive() != null) {
            existingLeaveMaster.setIsActive(leaveMaster.getIsActive());
        } else {
            // If isActive is missing from request, set it to true
            existingLeaveMaster.setIsActive(true);
        }
//new filed
        if (leaveMaster.getLeaveCreditMethod() != null) {
            existingLeaveMaster.setLeaveCreditMethod(leaveMaster.getLeaveCreditMethod());
        }

        if (leaveMaster.getLeaveCreditBasis() != null) {
            existingLeaveMaster.setLeaveCreditBasis(leaveMaster.getLeaveCreditBasis());
        }

        if (leaveMaster.getCarryForward() != null) {
            existingLeaveMaster.setCarryForward(leaveMaster.getCarryForward());
        }


        if (leaveMaster.getLeaveCreditInterval() != null) {
            existingLeaveMaster.setLeaveCreditInterval(leaveMaster.getLeaveCreditInterval());
        }

      /*  if (leaveMaster.getLeaveCredited() != null) {
            existingLeaveMaster.setLeaveCredited(leaveMaster.getLeaveCredited());
        }*/

        if (leaveMaster.getLeaveExclusion() != null && !leaveMaster.getLeaveExclusion().isEmpty()) {
            String exclusions = String.join(",", leaveMaster.getLeaveExclusion());
            existingLeaveMaster.setLeaveExclusion(exclusions);
        }


        // Save the updated record
        return leaveMasterRepository.saveAndFlush(existingLeaveMaster);
    }

    // delete

    @Transactional
    public boolean deleteLeave(Long id) {
        Optional<LeaveMaster> leaveMasterOptional = leaveMasterRepository.findById(id);
        if (leaveMasterOptional.isPresent()) {
            LeaveMaster leaveMaster = leaveMasterOptional.get();
            leaveMaster.setIsActive(false); // Set isActive to false
            leaveMasterRepository.saveAndFlush(leaveMaster); // Save the updated entity
            return true;
        }
        return false;
    }

    @Transactional
    public List<LeaveMaster> getAllLeavesByActive(boolean isActive) {
        logger.info("Inside LeaveApplicationService: getAllLeavesByActiveisActive: "+isActive);
        if (isActive) {
            return leaveMasterRepository.findByIsActiveTrue();
        } else {
            return leaveMasterRepository.findByIsActiveFalse();
        }
    }


}