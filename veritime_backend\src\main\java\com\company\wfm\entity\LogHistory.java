package com.company.wfm.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_log_history")
public class LogHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // Auto-incremented ID

    @Column(name = "empId", nullable = false)
    private Long empId; // Employee ID

    @Column(name = "fb_token", nullable = true)
    private String fbToken; // Facebook token

    @Column(name = "session_id", nullable = true)
    private String sessionId; // Session ID

    @Column(name = "created_at", nullable = true)
    private LocalDateTime createdAt; // Creation timestamp

    @Column(name = "updated_at", nullable = true)
    private LocalDateTime updatedAt; // Last update timestamp

    @Column(name = "status", nullable = true)
    private String status; // Status

    @Column(name = "remark", nullable = true)
    private String remark; // Optional remark

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "lat_long", nullable = true)
    private String latLong; // Latitude and Longitude

    @Column(name = "ip_address", nullable = true)
    private String ipAddress; // IP Address

}
