"use client";
import React, { useEffect, useState } from "react";
import "./Admin.css";
import "../../css/statuscard.css";
import Team from "../../components/Team/Team";
import Layout from "../../components/Layout";
import Devices from "../../modules/AdminDashboard/Devices/device";
import Calenderr from "../../components/Calender/Calenderr";
import { appConstants } from "../../constants/appConstants";
import useLocalStorage from "@/services/localstorage";
import TopFacilities from "../../modules/AdminDashboard/TopFacilities/topFacilities";
import CountComponent1 from "../../modules/AdminDashboard/CommomCountCard/countComponentWithIcon";
import CountComponent2 from "../../components/Tickets/countComponentWithoutIcon";
import ScheduleReport from "../../modules/AdminDashboard/ScheduleWiseReport/scheduleReport";
import LeaveBalanceChart from "@/modules/EmployeeDashboard/LeaveChart";
import WeeklyAttendance from "@/modules/EmployeeDashboard/Weeklywise";
import WeeklyWorkingHours from "@/modules/EmployeeDashboard/HoursCheck";
import TicketsChart from "@/modules/EmployeeDashboard/TicketComponent";
import EmployeeCalender from "@/modules/EmployeeDashboard/EmployeeCalender";
import PieChartComponent from "@/modules/Dashboard/pieChartComponent";
import TotalDepartmentsChart from "@/modules/Dashboard/TotalDepartmentsChart";
import { getRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showErrorAlert, showSuccessAlert2 } from "@/services/alertService.js";
import PopupModal from "@/modules/AdminDashboard/CommomCountCard/PopupModal";
import axios from "axios";
import Avatar from "@/components/Avatar";

const AdminDashboard = () => {
  let [popupModalTitle, setPopupModalTitle] = useState("");
  const [viewWidgetData, setViewWidgetData] = useState<any>([]);
  const [columnNames, setcolumnNames] = useState<any>([]);
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;
  const [username, setusername] = useLocalStorage(appConstants?.username, "");
  const [isMounted, setIsMounted] = useState(false);
  const [data, setData] = useState<any>([]);
  const [openModal, setOpenModal] = useState<any>(false);
  const [blocksData, setBlocksData] = useState<any>([]);

  useEffect(() => {
    getDashboardData();

    if (typeof window !== "undefined") {
      setTimeout(() => {
        const chart = document.querySelectorAll("[data-percentage]");
        chart.forEach((element) => {
          const percentage = element.getAttribute("data-percentage");
          if (percentage) {
            (element as HTMLElement).style.setProperty(
              "--percentage",
              percentage
            );
          }
        });
      }, 100);
    }
  }, []);

  const getDashboardData = async () => {
    try {
      // Primary API Call
      const res = await getRequest(API_URLS.DASHBOARDS, true);
      //console.log("widget API Response:", res);

      // If primary call is empty, use fallback
      if (!res || res.length === 0) {
        console.warn("Empty response, calling fallback API...");
        const resFallback = await getRequest(
          `${API_URLS.DASHBOARDS}?type=myDashboard`
        );
        console.log("Fallback API Response:", resFallback);
        setBlocksData(resFallback);
      } else {
        // Use primary call if data is present
        setBlocksData(res);
        //console.log("dash abhay",res)
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);

      // Call fallback if the primary request fails
      try {
        const resFallback = await getRequest(
          `${API_URLS.DASHBOARDS}?type=myDashboard`
        );
        console.log("Fallback API Response (Error Case):", resFallback);
        setBlocksData(resFallback);
      } catch (fallbackError) {
        console.error("Fallback API also failed:", fallbackError);
      }
    }
  };
  useEffect(() => {
    //console.log("Updated blocksData:", blocksData);

    // Find if "Facility Count" exists
    const facilityCount = blocksData.find(
      (item: any) =>
        item.type === "countComponentWithIcon" &&
        item.value.name === "Facility Count"
    );

    //console.log("Facility Count Found:", facilityCount);
  }, [blocksData]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  interface WidgetData {
    widgetName: string; // Add this line to include widgetName
    downloadParameter: string | null;
    type: string;
    value: {
      name: string;
      count: number;
      icon: string;
      onClickPath: string;
    };
  }
  const getInitials = (name: string) => {
    if (!name) return "U";
    const nameParts = name.trim().split(" ");
    if (nameParts.length === 1) return nameParts[0].charAt(0).toUpperCase();
    return nameParts[0].charAt(0).toUpperCase();
  };

  const handleViewClick = (widgetData: WidgetData) => {
    setPopupModalTitle(widgetData.widgetName);
    const token = localStorage.getItem("accessToken");

    if (!widgetData.downloadParameter) {
      console.warn("No download parameter provided for this widget.");
      //setOpenModal(true);
      return;
    }

    try {
      const responseData = axios.post(
        API_URLS.DOWNLOAD_WIDGET_DATA(widgetData.downloadParameter, false),
        {},
        {
          headers: { responseType: "blob", Authorization: `Bearer ${token}` },
        }
      );
      // const responseData=getRequest(
      //   API_URLS.DOWNLOAD_WIDGET_DATA(widgetData.downloadParameter, false)
      // );
      responseData.then((response) => {
        //console.log("download",response);
        if (response.data.length > 0) {
          const data = response.data.map((row: any, index: any) => ({
            id: index,
            ...row,
          }));
          setViewWidgetData(data);
          console.log("this is view data", data);
          //extract key name from object
          const extractKeysName = Object.keys(response.data[0]);

          //set header based on specied syntax
          const header = extractKeysName.map((value) => {
            //set the image in the data popup if EmployeeImage exists
            if (value === "EmployeeImage") {
              return {
                field: value,
                headerName: "",
                width: 60,
                renderCell: (params: {
                  row: {
                    EmployeeImage: any;
                    EmployeeName: any;
                    designationName: any;
                  };
                }) => {
                  const imageUrl = params.row.EmployeeImage;
                  const name = params.row.EmployeeName;
                  console.log("Employee Name:", name); // ADD THIS LINE

                  return (
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <Avatar
                        intial={getInitials(name)}
                        profileImg={imageUrl ? `${S3URL}${imageUrl}` : ""}
                        className="rounded-circle me-2"
                        styleInital={{
                          width: "45px",
                          height: "45px",
                          backgroundColor: "#AABFD5",
                          color: "#000",
                          fontSize: 18,
                        }}
                        styleImg={{ width: "45px", height: "45px" }}
                      />
                    </div>
                  );
                },
              };
            } else {
              return { field: value, headerName: value, width: 190 };
            }
          });
          // console.log(
          //   "header",
          //   Object.keys(response.data[0]).includes("EmployeeImage")
          // );
          setcolumnNames(header);
        } else {
          setViewWidgetData([]);
          setcolumnNames([{ field: "1", headerName: "", width: 1050 }]);
        }
      });

      // const testData=[{'name':"Deepak", "id":123},{'name':"Abhay", "id":13}];
      //     console.log(Object.keys(testData[0]))
      //     setViewWidgetData(testData);
      //     const header=Object.keys(testData[0]).map(value=>{return (
      //       { field: value, headerName: value, width: 150 }
      //     )})
      //     setcolumnNames(header);
      setOpenModal(true);
    } catch (error) {
      console.log(error);
    }

    setOpenModal(true);
  };

  const handleModalClose = () => {
    setOpenModal(false);
  };
  return (
    <Layout>
      <div className="container-fluid bg-custom p-4 pt-5">
        <div className="row mb-3">
          <div className="col-12 col-md-9">
            {/* <p className="small" style={{ textTransform: "capitalize" }}>
              Hi, {username ? username?.toLowerCase() : ""}. Welcome back to
              Veritime Workforce Management System!
            </p> */}
          </div>
          {/* <Calenderr /> */}
        </div>
        <div className="row">
          {openModal && (
            <PopupModal
              show={openModal}
              title={popupModalTitle}
              columns={columnNames}
              rows={viewWidgetData}
              handleClose={handleModalClose}
            />
          )}
          {blocksData?.map((item: any, index: number): any => {
            switch (item.type) {
              case "countComponentWithIcon":
                return (
                  <>
                    <CountComponent1
                      isDownload={item.downloadParameter as string | null}
                      handleViewClick={() => handleViewClick(item)}
                      data={item.value}
                      key={item.type + index}
                    />
                  </>
                );
              //card to show count
              case "countcomponent":
                return (
                  <CountComponent2 data={item.value} key={item.type + index} />
                );
              case "activeDevicesCount":
                return <Devices data={item.value} key={item.type + index} />;
              case "top3branches":
                return (
                  <TopFacilities data={item.value} key={item.type + index} />
                );
              case "scheduleWiseReport":
                return <ScheduleReport data={item} key={item.type + index} />;
              case "pieChartComponent":
                return (
                  <PieChartComponent
                    data={item.value}
                    key={item.type + index}
                  />
                );
              case "totalDepartments":
                const departmentsData = Array.isArray(item.value)
                  ? item.value
                  : [];
                return (
                  <TotalDepartmentsChart
                    data={departmentsData}
                    key={item.type + index}
                  />
                );
              case "teamsCount":
                return <Team data={item.value} key={item.type + index} />;
              case "leaveBalanceChart":
                return <LeaveBalanceChart key={item.type + index} />;
              case "weeklyAttendance":
                return <WeeklyAttendance key={item.type + index} />;
              case "weeklyWorkingHours":
                return <WeeklyWorkingHours key={item.type + index} />;
              case "employeeCalender":
                return <EmployeeCalender key={item.type + index} />;
              default:
                return null;
            }
          })}
        </div>
      </div>
    </Layout>
  );
};

export default AdminDashboard;
