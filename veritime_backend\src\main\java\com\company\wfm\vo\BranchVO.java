package com.company.wfm.vo;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BranchVO {

	private long branchId;
	private String branchName;
	private long hospitalId;
	private String hospitalName;
	private int isActive;
	private String cluster;
	private String branchCode;
	private String branchType;
	private String branchHeadName;
	private Long branchHeadId;
	private Integer leaveCreditDay; // new Field
	private String timeZone; // new Field
	private LocalDateTime updatedAt;
	private LocalDateTime createdAt;
	private String createdBy;   // Add this field for the creator's name
	private String updatedBy;


	private List<DepartmentVO> departments;

}
