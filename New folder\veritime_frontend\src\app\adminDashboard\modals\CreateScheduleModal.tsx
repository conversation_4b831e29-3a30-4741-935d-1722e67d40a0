import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form } from "react-bootstrap";
import { getRequest, postRequest, putRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showErrorAlert, showSuccessAlert2 } from "@/services/alertService";
import Select from "react-select";

const CreateScheduleModal = ({ show, handleClose, rowData }: any) => {
  const [deptList, setDeptList] = useState<any>([]); // Department list state

  const [branchList, setBranchList] = useState<any>([]); // Branch list state
  const [errors, setErrors] = useState<any>([]);
  const [data, setData] = useState({ branch: "", department: "" });
  const [timeSlots, setTimeSlots] = useState<any>([
    { startTime: "", endTime: "", isDefault: 1 },
  ]);

  useEffect(() => {
    const fetchBranchList = async () => {
      const payload = {
        // deptId:'',
        // branchId:'',
        // isActive:1,
        // offset:0,
        // limit:10
        type: "select",
      };
      const data = await postRequest(API_URLS.GET_BRANCH, payload);
      console.log("data from branch select", data);
      if (data) setBranchList(data);
    };

    fetchBranchList();
  }, []);

  const handleSelectBranchChange = async (selectedOption: any) => {
    const branchId = selectedOption ? selectedOption.value : "";
    setData({ ...data, branch: branchId, department: "" });
    setErrors({ ...errors, branch: "", department: "" });

    if (branchId) {
      try {
        const deptData = await postRequest(API_URLS.DEPARTMENTS_BY_BRANCH, {
          branchId,
        });
        console.log("Department API Response:", deptData);
        // Fix: Ensure `setDeptList` gets an array
        if (Array.isArray(deptData)) {
          setDeptList(deptData); // Set the department list directly
        } else {
          setDeptList([]); // Ensure reset if no departments found
        }
      } catch (error) {
        showErrorAlert("Error fetching departments");
      }
    }
  };

  const handleChange = (e: any) => {
    setData({ ...data, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: "" });
  };

  const handleInputChange = (index: any, event: any) => {
    const { name, value } = event.target;
    const newTimeSlots = [...timeSlots];
    newTimeSlots[index][name] = value;
    setTimeSlots(newTimeSlots);
  };

  const handleDefaultChange = (index: any) => {
    const updatedTimeSlots = timeSlots.map((slot: any, i: any) => ({
      ...slot,
      isDefault: i === index ? 1 : 0,
    }));
    setTimeSlots(updatedTimeSlots);
  };

  const addTimeSlot = () => {
    setTimeSlots([...timeSlots, { startTime: "", endTime: "", isDefault: 0 }]);
  };

  const deleteTimeSlot = (index: any) => {
    const newTimeSlots = [...timeSlots];
    newTimeSlots.splice(index, 1);
    setTimeSlots(newTimeSlots);
  };
  useEffect(() => {
    if (rowData) {
      setData({
        branch: rowData?.branchId || "",
        department: rowData?.departmentId || "",
      });

      const formattedTimeSlots = rowData.timeSlots?.map((slot: any) => ({
        ...slot,
        startTime: slot.startTime.slice(0, 5),
        endTime: slot.endTime.slice(0, 5),
      })) || [{ startTime: "", endTime: "", isDefault: 1 }];

      setTimeSlots(formattedTimeSlots);

      if (rowData.branchId && deptList.length === 0) {
        handleSelectBranchChange({ value: rowData.branchId });
      }
    }
  }, [rowData, deptList]);

  const validateForm = () => {
    let formErrors: any = {};
    if (!data.department) formErrors.department = "Department Name is required";
    if (!data.branch) formErrors.branch = "Branch Name is required";
    timeSlots.forEach((slot: any, index: any) => {
      if (!slot.startTime)
        formErrors[`startTime${index}`] = "Start Time is required";
      if (!slot.endTime) formErrors[`endTime${index}`] = "End Time is required";
    });

    if (timeSlots.length === 0)
      formErrors.timeSlots = "At least one time slot is required";
    return formErrors;
  };

  const handleSubmit = async (event: any) => {
    event.preventDefault();

    const formErrors = validateForm();

    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
    } else {
      let payload;
      if (rowData) {
        // This is an edit (update) case, we need to include 'id' for each time slot
        payload = {
          branchId: String(data.branch),
          departmentId: String(data.department),
          timeSlots: timeSlots.map((slot: any) => {
            const timeSlotPayload: any = {
              startTime: slot.startTime,
              endTime: slot.endTime,
              isDefault: slot.isDefault,
            };

            if (slot.id !== undefined) {
              timeSlotPayload.id = String(slot.id); // Ensure ID is passed as a string
            }

            return timeSlotPayload;
          }),
          // scheduleId: rowData.scheduleId, // Add the scheduleId for update request
        };
      } else {
        // This is a create case, 'id' is not required
        payload = {
          branchId: String(data.branch),
          departmentId: String(data.department),
          timeSlots: timeSlots.map((slot: any) => ({
            startTime: slot.startTime,
            endTime: slot.endTime,
            isDefault: slot.isDefault,
          })),
        };
      }

      try {
        let response: any;
        if (rowData) {
          // Update existing schedule
          response = await putRequest(
            `${API_URLS.UPDATE_SCHEDULE}/${rowData.scheduleId}`,
            payload
          );
          showSuccessAlert2("Schedule updated successfully!").then(() =>
            handleClose(response)
          );
        } else {
          // Create new schedule
          response = await postRequest(API_URLS.CREATE_SCHEDULE, payload);
          showSuccessAlert2("Schedule created successfully!").then(() =>
            handleClose(response)
          );
        }

        if (response) {
          handleClose(response);
        }
      } catch (error: any) {
        showErrorAlert(error?.response?.data);
      }
    }
  };

  const handleSelectChange = (selectedOption: any) => {
    setData({
      ...data,
      department: selectedOption ? selectedOption.value.toString() : "",
    });
    setErrors({ ...errors, department: "" });
  };

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>
          {rowData ? "Edit Schedule" : "Create Schedule"}
        </Modal.Title>
      </Modal.Header>
      <label
        htmlFor="ticketSubject"
        className="ticket-text-primary"
        style={{ marginTop: "10px", marginLeft: "14px" }}
      >
        <span className="text-danger">
          The fields with * marks are mandatory
        </span>
      </label>
      <Modal.Body>
        <Form>
          <Form.Group className="mb-3" controlId="branch">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Facility <span className="text-danger">*</span>
              </label>
            </Form.Label>
            <Select
              options={branchList.map((branch: any) => ({
                value: branch?.branchId,
                label: branch?.branchName,
              }))}
              value={
                branchList.find(
                  (branch: any) => branch.branchId == parseInt(data?.branch)
                )
                  ? {
                      value: branchList.find(
                        (branch: any) =>
                          branch.branchId == parseInt(data?.branch)
                      )?.branchId,
                      label: branchList.find(
                        (branch: any) =>
                          branch.branchId == parseInt(data?.branch)
                      )?.branchName,
                    }
                  : null
              }
              onChange={handleSelectBranchChange}
              placeholder="Select Facility"
              isClearable
              classNamePrefix="react-select"
              className={errors.branch ? "is-invalid" : ""}
            />
            {errors.branch && (
              <div className="invalid-feedback d-block">{errors.branch}</div>
            )}
          </Form.Group>

          <Form.Group className="mb-3" controlId="department">
            <Form.Label>
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Department <span className="text-danger">*</span>
              </label>
            </Form.Label>

            {/* <Select
              options={
                Array.isArray(deptList)
                  ? deptList.map((dept: any) => ({
                      value: dept.departmentId,
                      label: dept.departmentName,
                    }))
                  : []
              }
              value={
                deptList.length > 0 &&
                deptList.find(
                  (dept: any) => dept.departmentId === parseInt(data.department)
                ) // Match departmentId
                  ? {
                      value: deptList.find(
                        (dept: any) =>
                          dept.departmentId === parseInt(data.department)
                      )?.departmentId,
                      label: deptList.find(
                        (dept: any) =>
                          dept.departmentId === parseInt(data.department)
                      )?.departmentName,
                    }
                  : null
              }
              onChange={handleSelectChange}
              placeholder="Select Department"
              isClearable
              classNamePrefix="react-select"
              className={errors.department ? "is-invalid" : ""}
            /> */}

            <Select
              options={deptList.map((dept: any) => ({
                value: dept.departmentId,
                label: dept.departmentName,
              }))}
              value={
                deptList.find(
                  (dept: any) => dept.departmentId == data.department
                )
                  ? {
                      value: data.department,
                      label: deptList.find(
                        (dept: any) => dept.departmentId == data.department
                      )?.departmentName,
                    }
                  : null
              }
              onChange={handleSelectChange}
              placeholder="Select Department"
              isClearable
              classNamePrefix="react-select"
              className={errors.department ? "is-invalid" : ""}
            />

            {errors.department && (
              <div className="invalid-feedback d-block">
                {errors.department}
              </div>
            )}
          </Form.Group>

          {timeSlots.map((slot: any, index: any) => (
            <div
              key={index}
              style={{
                display: "flex",
                marginBottom: "8px",
                alignItems: "center",
              }}
            >
              <Form.Group
                controlId={`startTime${index}`}
                style={{ marginRight: "8px" }}
              >
                <Form.Label>
                  <label
                    htmlFor="ticketSubject"
                    className="ticket-text-primary"
                    style={{ marginTop: "10px", marginLeft: "14px" }}
                  >
                    Start Time <span className="text-danger">*</span>
                  </label>
                </Form.Label>
                <br />
                <input
                  type="time"
                  name="startTime"
                  value={slot.startTime}
                  onChange={(event) => handleInputChange(index, event)}
                  style={inputStyle}
                />
                {errors[`startTime${index}`] && (
                  <div style={{ color: "red" }}>
                    {errors[`startTime${index}`]}
                  </div>
                )}
              </Form.Group>
              <Form.Group
                controlId={`endTime${index}`}
                style={{ marginRight: "8px" }}
              >
                <Form.Label>
                  <label
                    htmlFor="ticketSubject"
                    className="ticket-text-primary"
                    style={{ marginTop: "10px", marginLeft: "14px" }}
                  >
                    End Time <span className="text-danger">*</span>
                  </label>
                </Form.Label>
                <br />
                <input
                  type="time"
                  name="endTime"
                  value={slot.endTime}
                  onChange={(event) => handleInputChange(index, event)}
                  style={inputStyle}
                />
                {errors[`endTime${index}`] && (
                  <div style={{ color: "red" }}>
                    {errors[`endTime${index}`]}
                  </div>
                )}
              </Form.Group>
              <Form.Group
                controlId={`isDefault${index}`}
                style={{ marginLeft: "8px", marginRight: "8px" }}
              >
                <br />
                <input
                  type="checkbox"
                  name="isDefault"
                  checked={slot.isDefault === 1}
                  onChange={() => handleDefaultChange(index)}
                  style={checkboxStyle}
                />
              </Form.Group>
              {slot.isDefault !== 1 && (
                <Button
                  variant="black"
                  onClick={() => deleteTimeSlot(index)}
                  style={{ height: "35px", marginLeft: "0px" }}
                >
                  X
                </Button>
              )}
            </div>
          ))}
          <Button
            variant="link"
            onClick={addTimeSlot}
            style={{
              marginBottom: "18px",
              color: "#0275d8",
              textDecoration: "underline",
            }}
          >
            Add More
          </Button>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={handleSubmit}>
          {rowData ? "Save Changes" : "Create Schedule"}
        </Button>
        <Button onClick={handleClose}>Cancel</Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateScheduleModal;

const inputStyle = {
  padding: "8px",
  marginRight: "8px",
  borderRadius: "4px",
  border: "1px solid #ccc",
};

const checkboxStyle = {
  width: "20px",
  height: "20px",
  cursor: "pointer",
  borderRadius: "4px",
  marginTop: "30px",
};
