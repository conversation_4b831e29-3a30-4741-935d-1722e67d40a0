package com.company.wfm.service.impl;

import com.company.wfm.vo.EmployeeVO;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Service
public class DownloadReportServiceImpl {


//    public byte[] generateEmployeesOnLeaveTodayExcelFile(List<Map<String, Object>> employees) {
//        try (Workbook workbook = new XSSFWorkbook()) {
//            Sheet sheet = workbook.createSheet("EmployeesOnLeaveToday");
//
//            // Create Header Row
//            String[] headers = {
//                    "Employee Code", "Employee Name", "Department Name", "Designation Name", "Start Date",
//                    "End Date", "Leave Days", "Leave Type"
//            };
////            "SELECT e.EMP_ID AS empId, e.EMP_CODE AS empCode, e.EMP_NAME AS empName, e.IMG_URE AS empImage, d.DEPARTMENT_NAME AS deptName, dg.designation_name AS desgnName, lm.type AS leavetype, " +
////                    "CONVERT(VARCHAR(10), l.START_DATE, 120) AS startDate, " +
////                    "CONVERT(VARCHAR(10), l.END_DATE, 120) AS endDate, " +
////                    "l.applied_leave_count " +
////                    "FROM t_employee e " +
////                    "JOIN t_employee_leave_history l ON e.EMP_ID = l.EMP_ID " +
////                    "JOIN t_leave_master lm ON l.leave_id = lm.leave_id " +
////                    "JOIN t_department d ON d.DEPARTMENT_ID = e.DEPARTMENT_ID " +
////                    "JOIN t_designation dg ON dg.designation_id = e.DESIGNATION_ID " +
////                    "WHERE CAST(GETDATE() AS DATE) BETWEEN l.START_DATE AND l.END_DATE " +
////                    "AND l.APPROVAL_STATUS = 'APPROVED' " +
////                    "AND e.UPPER_ID = ? "
//
//            Row headerRow = sheet.createRow(0);
//            for (int i = 0; i < headers.length; i++) {
//                headerRow.createCell(i).setCellValue(headers[i]);
//            }
//
//            // Populate Employee Data
//            int rowNum = 1;
//            for (Map<String, Object> employee : employees) {
//                Row row = sheet.createRow(rowNum++);
//                int col = 0;
//                row.createCell(col++).setCellValue(employee.get("empCode") != null ? employee.get("empCode").toString() : "");
//                row.createCell(col++).setCellValue(employee.get("empName") != null ? employee.get("empName").toString() : "");
//                row.createCell(col++).setCellValue(employee.get("deptName") != null ? employee.get("deptName").toString() : "");
//                row.createCell(col++).setCellValue(employee.get("desgnName") != null ? employee.get("desgnName").toString() : "");
//                row.createCell(col++).setCellValue(employee.get("startDate") != null ? employee.get("startDate").toString() : "");
//                row.createCell(col++).setCellValue(employee.get("endDate") != null ? employee.get("endDate").toString() : "");
//                row.createCell(col++).setCellValue(employee.get("leaveDays") != null ? employee.get("leaveDays").toString() : "");
//                row.createCell(col++).setCellValue(employee.get("leavetype") != null ? employee.get("leavetype").toString() : "");
//
//            }
//
//            // System.out.println("Number of employees being downloaded: " + employees.size());
//
//            // Write workbook to ByteArrayOutputStream
//            ByteArrayOutputStream bos = new ByteArrayOutputStream();
//            workbook.write(bos);
//            workbook.close();
//            return bos.toByteArray();
//        } catch (IOException e) {
//            throw new RuntimeException("Error generating Excel file", e);
//        }
//    }

public byte[] generateEmployeesOnLeaveTodayExcelFile(List<Map<String, Object>> employees) {
    try (Workbook workbook = new XSSFWorkbook()) {
        Sheet sheet = workbook.createSheet("Sheet1");

        if (employees == null || employees.isEmpty()) {
            throw new IllegalArgumentException("Employee list is empty.");
        }

        // Extract dynamic headers from the first entry
        Map<String, Object> firstEmployee = employees.get(0);
        List<String> headers = new ArrayList<>(firstEmployee.keySet());

        // Create Header Row
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.size(); i++) {
            headerRow.createCell(i).setCellValue(headers.get(i));
        }

        // Populate Employee Data
        int rowNum = 1;
        for (Map<String, Object> employee : employees) {
            Row row = sheet.createRow(rowNum++);
            int col = 0;
            for (String key : headers) {
                Object value = employee.get(key);
                row.createCell(col++).setCellValue(value != null ? value.toString() : "");
            }
        }

        // Write workbook to ByteArrayOutputStream
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        workbook.close();
        return bos.toByteArray();
    } catch (IOException e) {
        throw new RuntimeException("Error generating Excel file", e);
    }
}

}
