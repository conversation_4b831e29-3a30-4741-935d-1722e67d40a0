import React, { useEffect, useState } from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import "../TicketRaise/Ticketinfo.css";
import Select from "react-select";
import { API_URLS } from "@/constants/apiConstants";
import { postRequest, getRequest } from "@/services/apiService";

const TicketInformation = ({ formData, setFormData }) => {
  const [priority, setPriority] = useState("");
  const [deptNames, setDeptNames] = useState([]);
  const [categories, setCategories] = useState([]);
  const [filteredCategories, setFilteredCategories] = useState([]);

  // Fetch Department Names from the API
  const fetchDeptNames = async () => {
    const data = await getRequest(API_URLS.DEPT_LOOKUP);
    if (data) {
      setDeptNames(data);
    } else {
    }
  };

  // Fetch Categories based on selected Department
  const fetchCategoryNames = async (departmentId) => {
    const data = await postRequest(API_URLS.CATEGORY_LOOKUP, {
      departmentId,
    });
    if (data) {
      setCategories(data);
      setFilteredCategories(data);
    }
  };

  useEffect(() => {
    fetchDeptNames();
  }, []);

  // Handle Department Selection
  const handleDepartmentChange = (selectedOption) => {
    const departmentId = selectedOption ? selectedOption.value : "";

    setFormData({
      ...formData,
      departmentId: departmentId,
      category: "", // Reset category when department changes
      priority: "", // Reset priority
    });

    if (departmentId) {
      fetchCategoryNames(departmentId); // Fetch categories if a department is selected
    } else {
      setFilteredCategories([]); // Clear categories if no department is selected
    }
  };

  // Handle Category Selection
  const handleCategoryChange = (selectedOption) => {
    const selectedCategory = categories.find(
      (category) => category.id === selectedOption?.value
    );

    setFormData({
      ...formData,
      category: selectedOption ? selectedOption.value : "",
      priority: selectedCategory ? selectedCategory.priority : "",
    });

    setPriority(selectedCategory ? selectedCategory.priority : "");
  };

  const getPriorityClass = (priority) => {
    switch (priority) {
      case "High":
        return "bg-danger text-light";
      case "Medium":
        return "bg-warning text-dark";
      case "Low":
        return "bg-success text-light";
      default:
        return "text-dark";
    }
  };

  return (
    <div className="container mt-3 custom-container">
      <h5 className="font-weight-bold ticket-ticket-text-primary mb-4">
        Create Ticket
      </h5>
      <label htmlFor="ticketSubject" className="ticket-text-primary">
        <span className="text-danger">
          The fields with * marks are mandatory
        </span>
      </label>
      <form>
        <div className="row">
          {/* Department Select */}
          <div className="form-group col-md-4">
            <label htmlFor="department" className="ticket-text-primary mb-1">
              Department <span className="text-danger">*</span>
            </label>
            <Select
              id="department"
              options={deptNames.map((dept) => ({
                value: dept.departmentId,
                label: dept.departmentName,
              }))}
              value={
                formData.departmentId
                  ? {
                      value: formData.departmentId,
                      label: deptNames.find(
                        (dept) =>
                          dept.departmentId === parseInt(formData.departmentId)
                      )?.departmentName,
                    }
                  : null
              }
              onChange={handleDepartmentChange}
              placeholder="Select Department"
              isClearable
              classNamePrefix="react-select"
            />
          </div>

          {/* Issue Types / Category Select */}
          <div className="form-group col-md-4">
            <label htmlFor="category" className="ticket-text-primary mb-1">
              Issue Types <span className="text-danger">*</span>
            </label>
            <Select
              id="category"
              options={filteredCategories.map((category) => ({
                value: category.id,
                label: category.category,
              }))}
              value={
                formData.category
                  ? {
                      value: formData.category,
                      label: filteredCategories.find(
                        (category) => category.id === formData.category
                      )?.category,
                    }
                  : null
              }
              onChange={handleCategoryChange}
              placeholder="Issue Type"
              isClearable
              classNamePrefix="react-select"
            />
          </div>

          {/* Priority Display */}
          <div className="form-group col-md-4">
            <label htmlFor="priority" className="ticket-txt-primary mb-1">
              Priority <span className="text-danger">*</span>
            </label>
            {formData.priority ? (
              <label
                className={`label ${getPriorityClass(formData.priority)}`}
                style={{
                  padding: "7px 10px",
                  borderRadius: "5px",
                  fontSize: "14px",
                  display: "inline-block",
                  fontWeight: "bold",
                  width: "270px",
                }}
              >
                {formData.priority}
              </label>
            ) : (
              <span className="text-muted"></span>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default TicketInformation;
