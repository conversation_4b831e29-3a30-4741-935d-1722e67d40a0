package com.company.wfm.service;

import com.company.wfm.dto.EmailTemplateDto;
import com.company.wfm.dto.WelcomeEmailTemplateDto;

public interface EmailTemplateService {
	public String getTicketCreateTemplate(EmailTemplateDto emailTemplate);
	public String getTicketResolveTemplate(EmailTemplateDto emailTemplate);
	public String getTicketUpdateTemplate(EmailTemplateDto emailTemplate);
	public String getPasswordResetTemplate(EmailTemplateDto emailTemplate);
	public String getTicketAssingedTemplate(EmailTemplateDto emailTemplate);

	public String getWelcomeTemplate(WelcomeEmailTemplateDto welcomeEmailTemplateDto);
}
