import React, { useEffect, useState } from "react";

const PersonalDetailsForm = ({ data, goToNextTab, onSubmit,setProfileImg }) => {
  const [formData, setFormData] = useState({
    firstName: "",
    middleName: "",
    lastName: "",
    gender: "",
    married: "",
    birthday: "",
    ethnicity: "",
    base64EncodeFile: "",
    fileName: "",
    fileSize: 0,
    imgUre: "",
    forpostfile: "",
    nationalId: "",
  });

  useEffect(() => {
    if (data) setFormData(data);
  }, []);

  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // If the user is typing in the National ID field, we validate the length
    if (name === "nationalId") {
      if (value.length < 13) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          nationalId: "National ID must be exactly 13 numeric digits",
        }));
      } else if (value.length > 13) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          nationalId: "National ID must be exactly 13 numeric digits",
        }));
      } else {
        setErrors((prevErrors) => ({
          ...prevErrors,
          nationalId: "",
        }));
      }
    }

    setFormData({
      ...formData,
      [name]: value || "",
    });

    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prevFormData) => ({
        ...prevFormData,
        forpostfile: file,
      }));
      setProfileImg(file);
      const reader = new FileReader();

      reader.onloadend = () => {
        const base64String = reader.result.split(",")[1];

        setFormData((prevFormData) => ({
          ...prevFormData,
          base64EncodeFile: base64String,
          fileName: file.name,
          fileSize: file.size,
          imgUre: null,
        }));
      };

      reader.readAsDataURL(file);
    }
  };

  const [maxDate, setMaxDate] = useState("");
  useEffect(() => {
    const today = new Date();
    const year = today.getFullYear() - 15;
    const month = today.getMonth() + 1;
    const day = today.getDate();

    const formattedMaxDate = `${year}-${month.toString().padStart(2, "0")}-${day
      .toString()
      .padStart(2, "0")}`;
    setMaxDate(formattedMaxDate);
  }, []);

  const validate = () => {
    let newErrors = {};
    const today = new Date();
    const selectedBirthday = new Date(formData.birthday);
    const ageLimitDate = new Date(
      today.getFullYear() - 15,
      today.getMonth(),
      today.getDate()
    );

    if (!formData.firstName || formData.firstName.trim() === "") {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName || formData.lastName.trim() === "") {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.gender) {
      newErrors.gender = "Gender is required";
    }
    if (!formData.married) {
      newErrors.married = "Status is required";
    }

    const nationalIdRegex = /^[0-9]{13}$/;
    if (!formData.nationalId || formData.nationalId.trim() === "") {
      newErrors.nationalId = "National ID is required";
    } else if (!nationalIdRegex.test(formData.nationalId)) {
      newErrors.nationalId = "National ID must be exactly 13 numeric digits";
    }

    if (!formData.birthday) {
      newErrors.birthday = "Date of Birth is required";
    } else if (selectedBirthday > ageLimitDate) {
      newErrors.birthday = "You must be at least 15 years old";
    }

    return newErrors;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const validationErrors = validate();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
    } else {
      const cleanedData = { ...formData };

      Object.keys(cleanedData).forEach((key) => {
        if (cleanedData[key] === "" || cleanedData[key] === undefined) {
          delete cleanedData[key];
        }
      });

      onSubmit(cleanedData);
      console.log("Form data submitted: ", cleanedData);
    }
  };

  return (
    <form onSubmit={handleSubmit} style={formStyle}>
      <h3 style={headingStyle}>Personal Details</h3>
      <label
        htmlFor="ticketSubject"
        className="ticket-text-primary"
        style={{ marginTop: "10px", marginLeft: "14px" }}
      >
        <span className="text-danger">
          The fields with * marks are mandatory
        </span>
      </label>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          width: "100%",
          marginBottom: "13px",
        }}
      >
        <div>
          <div style={inputGroupStyle}>
            <label htmlFor="firstName">
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                First Name <span className="text-danger">*</span>
              </label>
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              placeholder="Enter Name"
              value={formData.firstName}
              onChange={handleInputChange}
              style={inputStyle}
            />
            {errors.firstName && (
              <span style={errorStyle}>{errors.firstName}</span>
            )}
          </div>
          <div style={inputGroupStyle}>
            <label htmlFor="firstName">
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Middle Name
              </label>{" "}
            </label>
            <input
              type="text"
              id="middleName"
              name="middleName"
              placeholder="Middle Name"
              value={formData.middleName}
              onChange={handleInputChange}
              style={inputStyle}
            />
            {errors.middleName && (
              <span style={errorStyle}>{errors.middleName}</span>
            )}
          </div>
          <div style={inputGroupStyle}>
            <label htmlFor="firstName">
              <label
                htmlFor="ticketSubject"
                className="ticket-text-primary"
                style={{ marginTop: "10px", marginLeft: "14px" }}
              >
                Last Name <span className="text-danger">*</span>
              </label>{" "}
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              placeholder="Last Name"
              value={formData.lastName}
              onChange={handleInputChange}
              style={inputStyle}
            />
            {errors.lastName && (
              <span style={errorStyle}>{errors.lastName}</span>
            )}
          </div>
          <div style={formRowStyle}>
            <div style={inputGroupStyle}>
              <label htmlFor="firstName">
                <label htmlFor="ticketSubject" className="ticket-text-primary">
                  Gender <span className="text-danger">*</span>
                </label>{" "}
              </label>
              <select
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                style={selectStyle}
              >
                <option value="">
                  {" "}
                  <label htmlFor="firstName">
                    <label
                      htmlFor="ticketSubject"
                      className="ticket-text-primary"
                      style={{ marginTop: "10px", marginLeft: "14px" }}
                    >
                      Select <span className="text-danger"></span>
                    </label>{" "}
                  </label>
                </option>
                <option value="Male">
                  {" "}
                  <label htmlFor="firstName">
                    <label
                      htmlFor="ticketSubject"
                      className="ticket-text-primary"
                      style={{ marginTop: "10px", marginLeft: "14px" }}
                    >
                      Male <span className="text-danger"></span>
                    </label>{" "}
                  </label>
                </option>
                <option value="Female">
                  {" "}
                  <label htmlFor="firstName">
                    <label
                      htmlFor="ticketSubject"
                      className="ticket-text-primary"
                      style={{ marginTop: "10px", marginLeft: "14px" }}
                    >
                      Female <span className="text-danger"></span>
                    </label>{" "}
                  </label>
                </option>
                Other
              </select>
              {errors.gender && <span style={errorStyle}>{errors.gender}</span>}
            </div>
            <div style={inputGroupStyle}>
              <label htmlFor="ethnicity">
                <label htmlFor="ticketSubject" className="ticket-text-primary">
                  Ethnicity
                </label>
              </label>
              <select
                id="ethnicity"
                name="ethnicity"
                value={formData.ethnicity}
                onChange={handleInputChange}
                style={selectStyle}
              >
                <option value="">Select</option>
                <option value="Black">Black</option>
                <option value="Colored">Colored</option>
                <option value="Indian">Indian</option>
                <option value="White">White</option>
              </select>
              {errors.ethnicity && (
                <span style={errorStyle}>{errors.ethnicity}</span>
              )}
            </div>

            <div style={inputGroupStyle}>
              <label htmlFor="married">
                {" "}
                <label htmlFor="firstName">
                  <label
                    htmlFor="ticketSubject"
                    className="ticket-text-primary"
                  >
                    is Married <span className="text-danger">*</span>
                  </label>{" "}
                </label>
              </label>
              <select
                id="married"
                name="married"
                value={formData.married}
                onChange={handleInputChange}
                style={selectStyle}
              >
                <option value="">Select</option>
                <option value={true}>Yes</option>
                <option value={false}>No</option>
              </select>
              {errors.married && (
                <span style={errorStyle}>{errors.married}</span>
              )}
            </div>

            <div style={inputGroupStyle}>
              <label htmlFor="birthday">
                {" "}
                <label htmlFor="married">
                  {" "}
                  <label htmlFor="firstName">
                    <label
                      htmlFor="ticketSubject"
                      className="ticket-text-primary"
                    >
                      Date Of Birth <span className="text-danger">*</span>
                    </label>{" "}
                  </label>
                </label>
              </label>
              <input
                type="date"
                id="birthday"
                name="birthday"
                placeholder="Enter DOB"
                value={formData.birthday}
                onChange={handleInputChange}
                style={inputStyle}
                max={maxDate}
              />
              {errors.birthday && (
                <span style={errorStyle}>{errors.birthday}</span>
              )}
            </div>
          </div>
          <div style={{ marginBottom: "15px" }}>
            <label
              htmlFor="nationalId"
              className="ticket-text-primary"
              style={{ marginTop: "10px", marginLeft: "14px" }}
            >
              National ID <span className="text-danger">*</span>
            </label>
            <input
              type="text"
              name="nationalId"
              value={formData.nationalId}
              onChange={handleInputChange}
              placeholder="Add 13 digit Numeric Number"
              style={{ width: "100%", padding: "8px", marginBottom: "5px" }}
            />
            {errors.nationalId && (
              <span style={errorStyle}>{errors.nationalId}</span>
            )}
          </div>
        </div>
        <div style={profilePicStyle}>
          <div style={picWrapperStyle}>
            <img
              src={
                formData?.imgUre
                  ? formData?.imgUre
                  : formData?.base64EncodeFile
                  ? `data:image/jpeg;base64,${formData.base64EncodeFile}`
                  : "https://static.vecteezy.com/system/resources/previews/005/544/718/non_2x/profile-icon-design-free-vector.jpg"
              }
              alt="Profile Pic"
              style={picStyle}
            />
            <label
              htmlFor="profilePic"
              style={picLabelStyle}
              onClick={(e) => {
                if (formData.base64EncodeFile || formData.imgUre) {
                  setFormData((prev) => ({
                    ...prev,
                    base64EncodeFile: "",
                    imgUre: "",
                    fileName: "",
                    fileSize: 0,
                  }));
                }
              }}
            >
              + Add Profile Pic
            </label>

            <input
              type="file"
              id="profilePic"
              name="profilePic"
              accept="image/jpeg, image/png, image/jpg"
              onChange={handleFileChange}
              style={{ display: "none" }}
            />
            {errors.profilePic && (
              <span style={errorStyle}>{errors.profilePic}</span>
            )}
          </div>
        </div>
      </div>

      <div style={formRowStyle}>
        <button type="submit" style={submitButtonStyle}>
          Save
        </button>
      </div>
    </form>
  );
};

const formStyle = {
  width: "60%",
  margin: "5px auto",
  // padding: '20px',
  borderRadius: "8px",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
};

const headingStyle = {
  marginBottom: "20px",
  fontWeight: "bold",
};

const formRowStyle = {
  width: "100%",
  display: "flex",
  marginBottom: "-16px",
};

const inputGroupStyle = {
  flex: "1",
  marginRight: "10px",
  display: "flex",
  flexDirection: "column",
  marginBottom: "13px",
};

const inputStyle = {
  padding: "10px",
  borderRadius: "4px",
  border: "1px solid #ccc",
  fontSize: "16px",
};

const selectStyle = {
  padding: "10px",
  borderRadius: "4px",
  border: "1px solid #ccc",
  fontSize: "16px",
};

const profilePicStyle = {
  display: "flex",
  alignItems: "top",
  justifyContent: "center",
  width: "100%",
};

const picWrapperStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
};

const picStyle = {
  width: "110px",
  height: "100px",
  borderRadius: "50%",
  marginBottom: "10px",
  objectFit: "cover",
};

const picLabelStyle = {
  cursor: "pointer",
  color: "#007bff",
  width: "140px",
};

const errorStyle = {
  color: "red",
  fontSize: "12px",
  marginTop: "5px",
};

const submitButtonStyle = {
  width: "150px",
  padding: "10px 20px",
  borderRadius: "4px",
  border: "none",
  backgroundColor: "#007bff",
  color: "#fff",
  fontSize: "16px",
  cursor: "pointer",
  textAlign: "center",
};

export default PersonalDetailsForm;
