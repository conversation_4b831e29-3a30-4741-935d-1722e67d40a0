package com.company.wfm.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import com.company.wfm.dto.ErrorResponse;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final long MAX_FILE_SIZE1 = 2 * 1024 * 1024;

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ErrorResponse> handleMaxUploadSizeExceeded(MaxUploadSizeExceededException ex) {

        // Dynamically set the error message using the value from the properties
        // Dynamically set the error message using MAX_FILE_SIZE1
        String errorMessage = "File size exceeds the maximum limit of " + (MAX_FILE_SIZE1 / (1024 * 1024)) + " MB.";  // Convert bytes to MB

        // Create the error response object
        ErrorResponse errorResponse = new ErrorResponse(errorMessage);
        // Return the error response with the appropriate HTTP status
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(errorResponse);
    }
}
