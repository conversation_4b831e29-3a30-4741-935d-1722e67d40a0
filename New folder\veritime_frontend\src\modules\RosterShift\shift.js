import React, { useEffect, useState } from "react";
import ScheduleForm from "./ScheduleForm";

import "../RosterShift/shift.css";
import { colors } from "@constants/colors";
import { getRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";

const EmployeeRoster = ({ fromDate, toDate, triggerFetch }) => {
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState(null);
  const [employees, setEmployees] = useState([]);
  const [weekdays, setWeekdays] = useState([]);

  const fetchRoster = async () => {
    try {
      const response = await getRequest(
        API_URLS.GET_ROSTER(fromDate, toDate),
        true
      );

      setEmployees(response.employees);
      setWeekdays(response.weekdays);
    } catch (error) {}
  };

  useEffect(() => {
    fetchRoster();
  }, [triggerFetch]);

  const getBorderStyle = (type) => {
    return {
      borderLeft: "3px solid #3AB3EA",
      minHeight: "80px",
      borderRadius: "0",
      borderRight: "0",
      borderTop: "0",
      borderBottom: "0",
      backgroundColor: "#EDF0F1",
    };
  };
  const handleCardClick = (schedule, department_id) => {
    setSelectedSchedule({ ...schedule, department_id });
    setIsPopupVisible(true);
  };

  const handleClosePopup = () => {
    setIsPopupVisible(false);
    setSelectedSchedule(null);
  };

  const [imageErrors, setImageErrors] = useState({});

  const getInitials = (name) => {
    return name?.charAt(0)?.toUpperCase() || "U";
  };

  const handleImageError = (index) => {
    setImageErrors((prevErrors) => ({
      ...prevErrors,
      [index]: true,
    }));
  };
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  return (
    <div className="container-fluid">
      <div className="d-flex justify-content-end align-items-center mb-4">
        {/* <button className="btn create-button-roaster">Shift 1</button>
        <button className="btn create-button-roaster">Shift 2</button>
        <button className="btn create-button-roaster">Shift 3</button> */}
      </div>

      <div className="d-flex" style={{ overflowY: "auto" }}>
        <div className="col-2">
          <div className="fw-bold text-center mb-2">Employee</div>
          <div>
            {employees.map((employee, index) => {
              //console.log("Employee Data:", employee); // Debugging log
              if (employee && employee.name) {
                employee.name = employee.name.replace(/undefined/g, " ");
              }
              return employee.schedule.length > 0 ? (
                <div
                  key={index}
                  className="d-flex align-items-center"
                  style={{
                    backgroundColor: colors.cyan2,
                    minHeight: "81px",
                    maxHeight: "81px",
                    borderRadius: "10px",
                    padding: "10px",
                    width: "90%",
                    marginBottom: 23,
                  }}
                >
                  {!employee.avatar || imageErrors[index] ? (
                    <div
                      className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                      style={{
                        width: "50px",
                        height: "50px",
                        backgroundColor: "#ccc",
                        color: "#fff",
                        fontWeight: "bold",
                      }}
                    >
                      {getInitials(employee.name)}
                    </div>
                  ) : (
                    <img
                      src={`${S3URL}${employee.avatar}`}
                      alt={employee.name}
                      className="rounded-circle me-2"
                      style={{ width: "50px", height: "50px" }}
                      onError={() => handleImageError(index)}
                    />
                  )}

                  <div title={`${employee.name} - ${employee.department_name}`} >
                    <div style={{ fontSize: "13px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",display:"block",maxWidth:"90px" }} className="fw-bold h8">{employee.name}</div>
                    <div style={{ fontSize: "11px", marginTop: "5px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",display:"block",maxWidth:"90px" }} className="text-muted h8">
                      {employee.department_name}
                      {/* <div>{employee.designation}</div> */}
                    </div>
                  </div>
                </div>
              ) : null;
            })}
          </div>
        </div>
        <div className="col" style={{ overflowX: "auto" }}>
          <div
            className="d-flex fw-bold text-center"
            style={{ flex: "1 1 auto" }}
          >
            {weekdays.map((weekday, index) => (
              <div
                key={index}
                className="h8"
                style={{
                  minWidth: "180px",
                  maxWidth: "180px",
                  marginRight: index < weekdays.length - 1 ? "20px" : "0",
                }}
              >
                {weekday.day}
                <br />
                <small>{weekday.date}</small>
              </div>
            ))}
          </div>
          <div>
            {employees.map((employee, index) => (
              <div key={index} className="d-flex mb-4 align-items-center">
                {employee.schedule.map((shift, idx) => (
                  <div
                    key={idx}
                    className="flex-shrink-0"
                    style={{
                      maxWidth: "180px",
                      minWidth: "180px",
                      marginRight: "20px",
                    }}
                  >
                    <div
                      className="card p-2 h-100"
                      // onClick={() => handleCardClick(shift, employee?.department_id)}
                      style={getBorderStyle(shift.type)}
                    >
                      <div className="small">{shift.shift || "Holiday"}</div>
                      {shift.designation && (
                        <div className="small">{shift.designation}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>

      {isPopupVisible && (
        <div
          className="position-absolute top-0 left-0 h-100"
          style={{
            width: "50%",
            display: "flex",
            alignItems: "center",
            paddingLeft: "20%",
          }}
        >
          <ScheduleForm
            schedule={selectedSchedule}
            onClose={handleClosePopup}
          />
        </div>
      )}
    </div>
  );
};

export default EmployeeRoster;
