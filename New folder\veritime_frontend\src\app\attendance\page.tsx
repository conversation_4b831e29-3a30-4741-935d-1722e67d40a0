"use client"
import React, { useEffect, useState } from 'react'
import Layout from '../../components/Layout';
import AttendanceList from '../../components/AttendanceList/attendance';

const Attendance = () => {
    const [isMounted, setIsMounted] = useState(false);
    useEffect(() => {
    setIsMounted(true);
    }, []);
      
    if (!isMounted) return null;
    return (
        <Layout>
            <AttendanceList/>
          
        </Layout>
    );
}

export default Attendance;
