package com.company.wfm.dto;

import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TicketDetailsResponseDTO {
    private Long ticketId;
    private Long empId;
    private String ticketSubject;
    private String ticketMessage;
    private String ticketCode;
    private String status;
    private String category;
    private String filePath;
    private Long updatedBy;
    private LocalDateTime updatedTime;
    private Long createdBy;
    private String createdByName;
    private LocalDateTime createdTime;
    private Long departmentId;
    private String departmentName;
    private Long assigned_to;
    private String assignedToName;
    private Long assigned_by;
    private String assignedByName;
    private String internal_ticket_status;
    private Long categoryId;
    private String categoryName;
    private String priority;
    private LocalDateTime lastEscalationTime;
    List<TicketAssignDTO> history;
    private List<EscalationDetailsDTO> escalationDetails;



}
