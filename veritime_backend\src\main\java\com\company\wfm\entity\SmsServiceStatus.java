package com.company.wfm.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "t_sms_service_status")
public class SmsServiceStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // Primary Key (Optional, based on your use case)

    @Column(name = "action")
    private String action;

    @Column(name = "key_value")
    private String key;

    @Column(name = "result")
    private String result;

    @Column(name = "number")
    private String number;

    @Column(name = "error")
    private String error;
}
