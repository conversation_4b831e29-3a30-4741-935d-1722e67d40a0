"use client";
import { useState, useEffect, ChangeEvent } from "react";
import { Container, Row, Col, Button, Table, Form } from "react-bootstrap";
import SwipeableViews from "react-swipeable-views";
import { useTheme } from "@material-ui/core/styles";
import Layout from "@/components/Layout";
import { TabPanel } from "../../../common-components/utils.js";
import { API_URLS } from "@/constants/apiConstants";
import { getRequest, deleteRequest, postRequest } from "@/services/apiService";
import CreateHolidayModal from "../modals/CreateHolidayModal";
import HolidayDetailModal from "../modals/HolidayDetailModal";

import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import TableFilter from "../../../common-components/TableFilter2.js";
import { showSuccessAlert2, showError<PERSON>lert } from "@/services/alertService.js";
import CreateExcelModal from "../modals/CreateExcelModal";
import moment from "moment";
import VisibilityIcon from "@mui/icons-material/Visibility";

import useLocalStorage from "@/services/localstorage";
import { appConstants } from "@/constants/appConstants.js";

const HolidayPage = ({ toggleMenu, expanded }: any) => {
  const [holidays, setHolidays] = useState<any>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [showExcelUploadModal, setShowExcelUploadModal] = useState(false);
  const [selectedHolidayForEdit, setSelectedHolidayForEdit] = useState(null);
  const [selectedHoliday, setSelectedHoliday] = useState(null);
  const [selectedHolidayForDetail, setSelectedHolidayForDetail] =
    useState(null);
  const [showHolidayDetailModal, setShowHolidayDetailModal] = useState(false);
  const [username, setUsername] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [role, setRole] = useLocalStorage("role", "");
  const [rowToEdit, setRowToEdit] = useState<any>({});
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);

  const handlePageChange = (newPage: any) => {
    setOffset(newPage?.page);
    setLimit(newPage?.pageSize);

    if (isFiltered) {
      handleSearch(newPage?.pageSize, newPage?.page); // Fetch filtered paginated data
    } else {
      fetchHolidays(newPage?.pageSize, newPage?.page); // Fetch normal paginated data
    }
  };

  const theme = useTheme();
  const [value, setValue] = useState(0);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedRole = localStorage.getItem(appConstants?.role);
      if (storedRole) {
        setRole(storedRole);
      }
    }
  }, []);

  useEffect(() => {
    fetchHolidays(limit, offset);
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem("username");
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
  }, []);

  useEffect(() => {
    fetchHolidays(limit ?? 10, offset ?? 0);
  }, [limit, offset]);

  const fetchHolidays = async (limit: any, offset: any) => {
    try {
      const request = {
        offset: offset ?? 0,
        limit: limit ?? 10,
      };

      const response = await postRequest(API_URLS.HOLIDAY_LIST, request, true);
      console.log(response, "holiday list");
      if (response?.content?.length > 0) {
        setTotalItems(response?.totalElements);
        const rows = response?.content?.map((holiday: any, index: any) => ({
          id: holiday?.id,

          date: holiday?.date
            ? moment(holiday.date).format("DD-MM-YYYY")
            : "N/A",

          holiday: holiday?.holiday,
          isOptional: holiday?.isOptional,
          isActive: holiday?.isActive, // Directly pass the boolean value

          createdByName: holiday?.createdByName || "N/A",
          updatedByName1: holiday?.modifiedByName || "N/A",

          createdAt: holiday?.createdAt
            ? `${holiday.createdAt[0]}-${String(holiday.createdAt[1]).padStart(
                2,
                "0"
              )}-${String(holiday.createdAt[2]).padStart(2, "0")}`
            : "N/A",

          updatedAt: holiday?.updatedAt
            ? `${holiday.updatedAt[0]}-${String(holiday.updatedAt[1]).padStart(
                2,
                "0"
              )}-${String(holiday.updatedAt[2]).padStart(2, "0")}`
            : "Never Updated",

          srno: request?.offset * request?.limit + (index + 1),
        }));
        setHolidays(rows);
        console.log(setHolidays);
      }
    } catch (error) {}
  };

  // const handleSearch = async () => {
  //   const searchFromDate = fromDate || "";
  //   let searchToDate = toDate || fromDate || "";
  //   if (!toDate && fromDate) {
  //     searchToDate = moment(fromDate).add(1, "days").format("DD-MM-YYYY");
  //   }
  //   try {
  //     const request = {
  //       fromDate: searchFromDate,
  //       toDate: searchToDate,
  //       query: searchQuery,
  //     };

  //     const response = await postRequest(
  //       API_URLS.DATE_FILTER_HOLIDAY,
  //       request,
  //       true
  //     );
  //     if (response?.content?.length > 0) {
  //       const rows = response?.content?.map((holiday: any, index: any) => ({
  //         date: holiday?.date
  //           ? moment(holiday.date).format("DD-MM-YYYY") // Formats the date correctly
  //           : "N/A",

  //         id: holiday?.id,
  //         // date: holiday?.date,
  //         holiday: holiday?.holiday,
  //         isOptional: holiday?.isOptional,
  //         srno: offset * limit + (index + 1),
  //       }));
  //       setHolidays(rows);
  //       console.log("holiday date", rows);
  //     }
  //   } catch (error) {}
  // };

  const [filteredHolidays, setFilteredHolidays] = useState<any>([]);
  const [isFiltered, setIsFiltered] = useState(false);
  const handleSearch = async (newLimit = limit, newOffset = offset) => {
    if (!fromDate || !toDate) {
      showErrorAlert("Please select a valid date range");
      return;
    }

    const request = {
      fromDate: moment(fromDate).format("YYYY-MM-DD"),
      toDate: moment(toDate).format("YYYY-MM-DD"),
      query: searchQuery,
      limit: newLimit,
      offset: newOffset,
    };

    try {
      const response = await postRequest(
        API_URLS.DATE_FILTER_HOLIDAY,
        request,
        true
      );
      console.log("API Response:", response);

      if (response?.content?.length > 0) {
        setTotalItems(response?.totalElements || response?.content?.length); // ✅ Fix total count
        const rows = response.content.map((holiday: any, index: any) => ({
          id: holiday?.id,
          date: holiday?.date
            ? moment(holiday.date.toString()).format("DD-MM-YYYY")
            : "N/A",
          holiday: holiday?.holiday,
          isOptional: holiday?.isOptional,
          createdByName: holiday?.createdByName || "N/A",
          srno: newOffset * newLimit + (index + 1),
        }));
        setFilteredHolidays(rows);
        setIsFiltered(true);
      } else {
        setFilteredHolidays([]);
        setTotalItems(0); // ✅ Ensure totalItems is set to zero
        setIsFiltered(true);
        console.log("No holidays found for the selected range.");
      }
    } catch (error) {
      console.error("Error fetching holidays:", error);
    }
  };

  useEffect(() => {
    if (!isFiltered) {
      fetchHolidays(limit, offset);
    }
  }, [limit, offset, isFiltered]);

  const handleEdit = (holiday: any) => {
    setSelectedHoliday(holiday);
    setShowModal(true);
  };

  const handleOpen = () => {
    setSelectedHoliday(null);
    setShowModal(true);
  };

  const handleDetail = (holiday: any) => {
    setSelectedHolidayForDetail(holiday);
    setShowHolidayDetailModal(true);
  };

  const handleModalClose = () => {
    setSelectedHolidayForEdit(null);
    setSelectedHolidayForDetail(null);
    setShowModal(false);
    setShowHolidayDetailModal(false);
    setShowExcelUploadModal(false);
  };

  const handleReset = () => {
    setFromDate("");
    setToDate("");
    setSearchQuery("");
    setIsFiltered(false);
    setFilteredHolidays([]);
    setTotalItems(0); // ✅ Explicitly reset total items
    fetchHolidays(10, 0); // ✅ Reset pagination
  };

  const columns = [
    {
      field: "srno",
      headerName: "Sr.no",
      width: 150,
    },
    { field: "date", headerName: "Date", width: 250 },
    { field: "holiday", headerName: "Holiday", width: 250 },
    {
      field: "isOptional",
      headerName: "Is Optional",
      width: 250,
      renderCell: (params: any) => (params.row.isOptional ? "Yes" : "No"),
    },
    ...(role !== "admin" && role !== "superadmin"
      ? [
          {
            field: "actions",
            headerName: "Actions",
            width: 250,
            renderCell: (params: any) => (
              <div>
                <Button
                  // onClick={() => handleDetail(params.row)} // Ensure this triggers correctly
                  style={{
                    backgroundColor: "transparent",
                    border: "none",
                    color: "black",
                  }}
                ></Button>
                <Button
                  onClick={() => handleEdit(params.row)}
                  style={{
                    backgroundColor: "transparent",
                    border: "none",
                    color: "black",
                  }}
                >
                  <VisibilityIcon
                    style={{ cursor: "pointer", marginRight: "10px" }}
                    onClick={() => handleDetail(params.row)}
                  />
                  <EditIcon />
                </Button>{" "}
              </div>
            ),
          },
        ]
      : []),
  ];

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const haldleFromDateChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (moment(e.target.value).year().toString().length > 4) {
      showErrorAlert(
        "The maximum allowed year is 4 digits. Please enter a valid year"
      );
      //setFromDate("");
    } else {
      setFromDate(e.target.value);
    }
  };

  const haldleToDateChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (moment(e.target.value).year().toString().length > 4) {
      showErrorAlert(
        "The maximum allowed year is 4 digits. Please enter a valid year"
      );
      //setToDate("");
    } else {
      setToDate(e.target.value);
    }
  };

  // const selectedFromYear = moment(fromDate).year();
  //     const selectedToYear = moment(toDate).year();
  //     if (selectedFromYear.toString().length > 4) {
  //       alert(`${selectedFromYear} is invalid From year`);
  //     }

  //     if (selectedToYear.toString().length > 4) {
  //       alert(`${selectedToYear} is invalid To year`);
  //     }

  if (!isMounted) return null;

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <Row className="my-3">
          <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
            <h4>{"Holiday List"}</h4>
            {/* <span style={{ fontSize: "12px", color: "grey" }}>
              Hi, {username ? username.toLowerCase() : ""}. Your organization's
              holidays are listed here
            </span> */}
          </Col>
          <Col md={8}>
            <Row style={{ display: "flex", justifyContent: "flex-end" }}>
              <Button
                style={{
                  width: "150px",
                  background: "green",
                  marginRight: "15px",
                }}
                onClick={() => setShowExcelUploadModal(true)}
              >
                Upload Holidays
              </Button>
              <Button
                style={{
                  width: "150px",
                  background: "green",
                  marginRight: "15px",
                }}
                onClick={handleOpen}
              >
                + Add Holiday
              </Button>
            </Row>
          </Col>
        </Row>

        <Row
          className="align-items-center justify-content-center"
          style={{ marginLeft: "10px" }}
        >
          <Col md={2}>
            <Form.Group controlId="fromDate" className="mb-3">
              <p style={{ marginBottom: "5px" }}>From Date</p>
              <Form.Control
                type="date"
                value={fromDate}
                onChange={(e) => haldleFromDateChange(e)}
                style={{ width: "100%" }}
              />
            </Form.Group>
          </Col>

          <Col md={2}>
            <Form.Group controlId="toDate" className="mb-3">
              <p style={{ marginBottom: "5px" }}>To Date</p>
              <Form.Control
                type="date"
                value={toDate}
                onChange={(e) => haldleToDateChange(e)}
                style={{ width: "100%" }}
              />
            </Form.Group>
          </Col>

          <Col md={3}>
            <Form.Group controlId="searchQuery" className="mb-3">
              <p style={{ marginBottom: "5px" }}>Search Holidays</p>
              <Form.Control
                type="text"
                placeholder="Search by holiday name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                style={{ width: "100%" }}
              />
            </Form.Group>
          </Col>

          <Col
            md={2}
            className="d-flex align-items-center mb-3"
            style={{ marginTop: "25px" }}
          >
            <Button
              variant="primary"
              onClick={() => handleSearch(limit, offset)}
            >
              Apply
            </Button>
            <Button
              variant="secondary"
              onClick={handleReset}
              style={{ marginLeft: "10px" }}
            >
              Reset
            </Button>
          </Col>
        </Row>

        <Row>
          <Col md={12}>
            {/* <SwipeableViews
              axis={theme.direction === "rtl" ? "x-reverse" : "x"}
            > */}
            <TabPanel value={value} index={0}>
              {/* <TableFilter
                rows={holidays}
                columns={columns}
                totalCount={totalItems}
                onPageChange={handlePageChange}
                pageSize={limit}
              /> */}
              <TableFilter
                rows={isFiltered ? filteredHolidays : holidays}
                columns={columns}
                totalCount={totalItems}
                onPageChange={handlePageChange}
                pageSize={limit}
              />
            </TabPanel>
            {/* </SwipeableViews> */}
          </Col>
        </Row>

        {showModal && (
          <CreateHolidayModal
            show={showModal}
            handleClose={handleModalClose}
            holiday={selectedHoliday}
            fetchHolidays={fetchHolidays}
          />
        )}
        {showHolidayDetailModal && selectedHolidayForDetail && (
          <HolidayDetailModal
            show={showHolidayDetailModal}
            handleClose={handleModalClose}
            holidayDetails={selectedHolidayForDetail}
          />
        )}

        {showExcelUploadModal && (
          <CreateExcelModal
            show={showExcelUploadModal}
            fetchHolidays={fetchHolidays} // ✅ Pass this function
            handleClose={handleModalClose}
            downloadType="holiday"
          />
        )}
      </Container>
    </Layout>
  );
};

export default HolidayPage;
