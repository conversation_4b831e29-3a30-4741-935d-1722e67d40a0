package com.company.wfm.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

import com.company.wfm.dto.EmployeeDocumentVO;
import com.company.wfm.dto.EmployeeLeaveBalanceDto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EmployeeVO {
    private Long empId;
    private Long companyId;
    private String department;
    private Long departmentId;
    private String designation;
    private Long designationId;
    private String empCode;
    private String empName;
    private String uid;
    private String workZone;
    private String idNo;
    private Long orgId;
    private Long upperId;
    private Long regionId;
    private String subDistrictName;
    private Long countryId;
    private Long provinceId;
    private String provinceName;
    private String city;
    private String districtName;
    private String upperName;
    private Date hireDate;
    private String gender;
    private Date birthday;
    private String nation;
    private Boolean married;
    private String phoneNo;
    private String mobileNo;
    private String email;
    private String nativePlace;
    private String zipCode;
    private Boolean isHistory;
    private Boolean inService;
    private String remark;
    private Long createdBy;
    private LocalDateTime createdTime;
    private Long updatedBy;
    private LocalDateTime updatedTime;
    private Long version;
    private String nativeLanguage;
    private String foreignLanguages;
    private Integer workYears;
    private String graduateSchool;
    private Date graduateTime;
    private String highestDegree;
    private String imgUre;
    private Long defaultTimeSlotId;
    private String defaultShift;
    private String leaveOnDays;
    private String street;
    private String unitNumber;
    private String alternateNumber;
    private String alternateEmail;
    private String emergencyContact1;
    private String emergencyContact2;
    private String emergencyContactname1;
    private String emergencyContactname2;
    private String branchName; // from Branch Table
    private Long branchId;
    private String biometricID;
    private String workingDayCountInWeek;
    private Integer totalLeaves;
    private Integer pendingLeaves;
    private Integer overTimeHours;
    private String ethnicity;
    private String companyName;
    private String role;
    private LocalTime startTime;
    private LocalTime endTime;
    private String nationalId;
    private List<EmployeeDocumentVO> documents;
    private List<EmployeeLeaveBalanceDto> leaveBalances;
    private BigDecimal probationPeriod;
    private String isInProbation;
    private LocalDateTime probationEndDate;

}
