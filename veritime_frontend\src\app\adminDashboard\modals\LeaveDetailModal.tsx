import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

// Define the type for leave details
interface LeaveDetails {
  updatedByName: string;
  createdByName: string;
  leaveName?: string;
  descriptionList?: string;
  availableDays?: number;
  availedDays?: number;
  createdDate?: string;
  modifiedDate?: string;
  leaveCreditMethod?: string;
  leaveCreditBasis?: string;
}

// Define the props for the LeaveDetailModal
interface LeaveDetailModalProps {
  show: boolean;
  handleClose: () => void;
  leaveDetails: LeaveDetails;
}

const LeaveDetailModal: React.FC<LeaveDetailModalProps> = ({
  show,
  handleClose,
  leaveDetails,
}) => {
  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>Leave Details</Modal.Title>
      </Modal.Header>

      <Modal.Body style={{ overflowY: "auto", maxHeight: "400px" }}>
        <div style={{ padding: "10px" }}>
          {/* Leave Name */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Leave Type
            </div>
            <div>{leaveDetails?.leaveName || "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Description */}
          {/* <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Description
            </div>
            <div>{leaveDetails?.descriptionList || "N/A"}</div>
          </div> */}
          {/* <hr style={{ borderColor: "#a0a0a0" }} /> */}

          {/* Available Days */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Available Days
            </div>
            <div>{leaveDetails?.availableDays ?? "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Availed Days */}
          <div style={{ marginBottom: "10px" }}>
            <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
              Availed Days
            </div>
            <div>{leaveDetails?.availedDays ?? "N/A"}</div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                leave Credit Method
              </div>
              <div>{leaveDetails?.leaveCreditMethod || "N/A"}</div>
            </div>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                leave Credit Basis
              </div>
              <div>{leaveDetails?.leaveCreditBasis || "N/A"}</div>
            </div>
          </div>
          <hr style={{ borderColor: "#a0a0a0" }} />

          {/* Created Date and Modified Date structured similarly */}
          <div style={{ display: "flex", marginBottom: "10px" }}>
            <div style={{ flex: 1, marginRight: "20px" }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Created Date
              </div>
              <div>{leaveDetails?.createdDate || "N/A"}</div>
            </div>
            

            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Created by
              </div>
              <div>{leaveDetails?.createdByName || "N/A"}</div>
            </div>

          </div>
          {(leaveDetails.modifiedDate!=="N/A"|| leaveDetails.updatedByName!=="N/A")&&<hr style={{ borderColor: "#a0a0a0" }} /> }
          

          <div style={{ display: "flex", marginBottom: "10px" }}>
            {leaveDetails.modifiedDate!=="N/A"&&<div style={{ flex: 1,marginRight: "20px"  }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Modified Date
              </div>
              <div>{leaveDetails?.modifiedDate || "N/A"}</div>
            </div>}
            {leaveDetails.updatedByName!=="N/A"&&<div style={{ flex: 1 }}>
              <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                Updated by
              </div>
              <div>{leaveDetails?.updatedByName || "N/A"}</div>
            </div>}
            
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="primary" onClick={handleClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default LeaveDetailModal;
