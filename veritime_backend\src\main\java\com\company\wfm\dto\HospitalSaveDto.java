package com.company.wfm.dto;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

@Data
public class HospitalSaveDto {
    private String hospitalName;
    private String shortCode;
    private String provinceName;
    private String districtName;
    private String subDistrictName;
    private String addressLine1;
    private String addressLine2;
    private Double lat;
    private Double lng;
    private LocalDateTime validity;
    private Long createdBy;
    private Long updatedBy;
    private String hospitalType;
    private String clusterName;
    private List<Long> departmentIds;
   // private Long branchHeadId;
    private List<String> departmentNames;
    private List<String> branchNames;
    private List<BranchSaveDto> branches;
    private String branchName;
}
