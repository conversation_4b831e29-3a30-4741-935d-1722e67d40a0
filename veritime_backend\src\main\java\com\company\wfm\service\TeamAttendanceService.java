package com.company.wfm.service;

import java.time.LocalDate;
import java.time.DayOfWeek;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.company.wfm.entity.Employee;
import com.company.wfm.repository.EmployeeRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * Service class for managing team attendance data.
 * 
 * This service provides functionality to retrieve and process attendance records
 * for team members, including handling week-off days and attendance status.
 * It supports filtering by date ranges and specific employee IDs.
 */
@Service
@Slf4j
public class TeamAttendanceService {

    private static final Logger logger = LoggerFactory.getLogger(TeamAttendanceService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserTokenService tokenService;
    
    @Autowired
    private EmployeeRepository employeeRepository;

    /**
     * Retrieves attendance records for team members within a specified date range.
     * 
     * This method processes attendance data for team members and handles special cases:
     * - If an employee is marked as absent on a week-off day, their status is updated to "Week-off"
     * - If an employee is present on any day (including week-off days), they are marked as "Present"
     * 
     * @param userIds List of employee IDs to filter by. If null or empty, returns data for all team members.
     * @param fromDate Start date for the attendance records (format: yyyy-MM-dd)
     * @param toDate End date for the attendance records (format: yyyy-MM-dd)
     * @return List of attendance records with employee details and attendance status
     */
    public List<Map<String, Object>> getTeamAttendanceRequest(List<Long> userIds, String fromDate, String toDate) {
        List<Map<String, Object>> attendanceList = new ArrayList<>();
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        try {
            // Step 1: Get team members
            List<Map<String, Object>> teamMembers;
            if (userIds == null || userIds.isEmpty()) {
                String teamQuery = """
                    SELECT EMP_ID, EMP_NAME
                    FROM t_employee
                    WHERE UPPER_ID = ? AND IN_SERVICE = 1
                """;
                teamMembers = jdbcTemplate.queryForList(teamQuery, loggedInEmpId);
            } else {
                String teamQuery = """
                    SELECT EMP_ID, EMP_NAME
                    FROM t_employee
                    WHERE EMP_ID IN (%s) AND IN_SERVICE = 1
                """.formatted(userIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                teamMembers = jdbcTemplate.queryForList(teamQuery);
            }

            if (teamMembers.isEmpty()) {
                return attendanceList;
            }

            // Step 2: Extract employee IDs
            List<Long> empIds = new ArrayList<>();
            for (Map<String, Object> member : teamMembers) {
                empIds.add(((Number) member.get("EMP_ID")).longValue());
            }

            if (!empIds.isEmpty()) {
                // Step 3: Get attendance data for the date range
                String attendanceQuery = """
                    SELECT a.EMP_ID, e.EMP_NAME, CONVERT(VARCHAR, a.[DATE], 23) AS [DATE], a.CHECK_IN_TIME, a.CHECK_OUT_TIME, a.CREATED_TIME, a.MODE_TYPE, a.OVERTIME, a.actual_shift_start_time, a.actual_shift_end_time 
                    FROM t_attendance a JOIN t_employee e ON a.EMP_ID = e.EMP_ID 
                    WHERE a.EMP_ID IN (%s) AND a.[DATE] BETWEEN '%s' AND '%s' AND a.is_active = 1;
                """.formatted(
                        empIds.stream().map(String::valueOf).collect(Collectors.joining(",")),
                        fromDate, toDate
                );

                List<Map<String, Object>> attendanceData = jdbcTemplate.queryForList(attendanceQuery);
                
                // Step 4: Generate date range
                List<String> dateRange = generateDateRange(fromDate, toDate);
                
                // Step 5: Group attendance data by employee
                Map<Long, Map<String, Map<String, Object>>> employeeAttendanceMap = new HashMap<>();
                
                // Initialize the map with all employees
                for (Map<String, Object> member : teamMembers) {
                    Long empId = ((Number) member.get("EMP_ID")).longValue();
                    String empName = (String) member.get("EMP_NAME");
                    
                    // Get employee's week-off days
                    Employee employee = employeeRepository.findById(empId).orElse(null);
                    Set<String> weekOffDays = new HashSet<>();
                    if (employee != null && employee.getLeaveOnDays() != null && !employee.getLeaveOnDays().isEmpty()) {
                        String[] days = employee.getLeaveOnDays().split(",");
                        for (String day : days) {
                            // Normalize the day name to match the format from LocalDate.getDayOfWeek()
                            String normalizedDay = day.trim();
                            // Convert to title case (first letter uppercase, rest lowercase)
                            normalizedDay = normalizedDay.substring(0, 1).toUpperCase() + normalizedDay.substring(1).toLowerCase();
                            weekOffDays.add(normalizedDay);
                        }
                        // Log the employee's week-off days
                        logger.info("Employee {} has week-off days: {}", empName, weekOffDays);
                    } else {
                        logger.info("Employee {} has no week-off days configured", empName);
                    }
                    
                    Map<String, Map<String, Object>> dateMap = new HashMap<>();
                    for (String date : dateRange) {
                        Map<String, Object> record = new HashMap<>();
                        record.put("empid", empId);
                        record.put("empname", empName);
                        record.put("date", date);
                        record.put("checkin", null);
                        record.put("checkout", null);
                        record.put("modetype", null);
                        record.put("overtime", null);
                        record.put("createdAt", null);
                        record.put("actualShiftStartTime", null);
                        record.put("actualShiftEndTime", null);
                        
                        // Check if this date is a week-off day for the employee
                        LocalDate localDate = LocalDate.parse(date);
                        String dayOfWeek = localDate.getDayOfWeek().toString();
                        
                        // Normalize the day of week to match the format in the database
                        // The database might store "Saturday" but LocalDate.getDayOfWeek() returns "SATURDAY"
                        String normalizedDayOfWeek = dayOfWeek.substring(0, 1).toUpperCase() + dayOfWeek.substring(1).toLowerCase();
                        
                        // Debug log to see what's happening
                        logger.debug("Employee: {}, Date: {}, Day of Week: {}, Normalized Day: {}, Week-off Days: {}", 
                            empName, date, dayOfWeek, normalizedDayOfWeek, weekOffDays);
                        
                        // Check if this is a week-off day for the employee
                        boolean isWeekOffDay = false;
                        for (String weekOffDay : weekOffDays) {
                            if (weekOffDay.equalsIgnoreCase(normalizedDayOfWeek)) {
                                isWeekOffDay = true;
                                break;
                            }
                        }
                        
                        if (isWeekOffDay) {
                            record.put("status", "Week-off");
                            logger.debug("Marking {} as Week-off for employee {}", date, empName);
                        } else {
                            record.put("status", "Absent");
                            logger.debug("Marking {} as Absent for employee {}", date, empName);
                        }
                        
                        dateMap.put(date, record);
                    }
                    employeeAttendanceMap.put(empId, dateMap);
                }
                
                // Step 6: Update the map with actual attendance data
                for (Map<String, Object> row : attendanceData) {
                    Long empId = ((Number) row.get("EMP_ID")).longValue();
                    String date = (String) row.get("DATE");
                    
                    if (employeeAttendanceMap.containsKey(empId) && employeeAttendanceMap.get(empId).containsKey(date)) {
                        Map<String, Object> record = employeeAttendanceMap.get(empId).get(date);
                        
                        record.put("checkin", row.get("CHECK_IN_TIME") != null ? row.get("CHECK_IN_TIME").toString() : null);
                        record.put("checkout", row.get("CHECK_OUT_TIME") != null ? row.get("CHECK_OUT_TIME").toString() : null);
                        record.put("modetype", row.get("MODE_TYPE"));
                        record.put("overtime", row.get("OVERTIME"));
                        record.put("createdAt", row.get("CREATED_TIME") != null ? row.get("CREATED_TIME").toString() : null);
                        record.put("actualShiftStartTime", row.get("actual_shift_start_time") != null ? row.get("actual_shift_start_time").toString() : null);
                        record.put("actualShiftEndTime", row.get("actual_shift_end_time") != null ? row.get("actual_shift_end_time").toString() : null);
                        
                        // Always mark as Present if they have attendance record, regardless of week-off day
                        record.put("status", "Present");
                    }
                }
                
                // Step 7: Flatten the map into a list
                for (Map<String, Map<String, Object>> dateMap : employeeAttendanceMap.values()) {
                    attendanceList.addAll(dateMap.values());
                }
            }
        } catch (DataAccessException e) {
            logger.error("Database error occurred while fetching team attendance data: {}", e.getMessage());
            attendanceList.clear();
        } catch (Exception e) {
            logger.error("unknown error occurred while fetching team attendance data .: {}", e.getMessage());
        }

        return attendanceList;
    }

    /**
     * Generates a summary of attendance data for team members within a specified date range.
     * 
     * This method provides aggregated attendance information, including the total number of
     * team members and daily attendance counts.
     * 
     * @param userIds List of employee IDs to filter by. If null or empty, returns data for all team members.
     * @param fromDate Start date for the attendance records (format: yyyy-MM-dd)
     * @param toDate End date for the attendance records (format: yyyy-MM-dd)
     * @return Map containing total member count and daily attendance counts
     */
    public Map<String, Object> getAttendanceSummary(List<Long> userIds, String fromDate, String toDate) {
        Map<String, Object> response = new HashMap<>();
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        try {
            List<Long> empIds = new ArrayList<>();
            if (userIds == null || userIds.isEmpty()) {
                String teamQuery = """
                    SELECT EMP_ID
                    FROM t_employee
                    WHERE UPPER_ID = ?
                """;
                empIds = jdbcTemplate.queryForList(teamQuery, Long.class, loggedInEmpId);
            } else {
                empIds.addAll(userIds);
            }

            if (empIds.isEmpty()) {
                response.put("totalMembers", 0);
                response.put("attendanceList", Collections.emptyList());
                return response;
            }

            response.put("totalMembers", empIds.size());

            String attendanceQuery = """
                SELECT CONVERT(VARCHAR, [DATE], 23) AS date, COUNT(DISTINCT EMP_ID) AS count
                FROM wfmmanager.dbo.t_attendance
                WHERE EMP_ID IN (%s) AND [DATE] BETWEEN '%s' AND '%s'
                GROUP BY [DATE]
                ORDER BY [DATE]
            """.formatted(
                    empIds.stream().map(String::valueOf).collect(Collectors.joining(",")),
                    fromDate, toDate
            );

            List<Map<String, Object>> attendanceList = jdbcTemplate.queryForList(attendanceQuery);

            response.put("attendanceList", attendanceList);
        } catch (Exception e) {
			log.error("Exception while calling getAttendanceSummary", e);
            response.put("totalMembers", 0);
            response.put("attendanceList", Collections.emptyList());
        }

        return response;
    }

    /**
     * Generates a list of dates between the start and end dates (inclusive).
     * 
     * @param fromDate Start date (format: yyyy-MM-dd)
     * @param toDate End date (format: yyyy-MM-dd)
     * @return List of date strings in yyyy-MM-dd format
     */
    private List<String> generateDateRange(String fromDate, String toDate) {
        List<String> dateRange = new ArrayList<>();
        LocalDate startDate = LocalDate.parse(fromDate);
        LocalDate endDate = LocalDate.parse(toDate);

        while (!startDate.isAfter(endDate)) {
            dateRange.add(startDate.toString());
            startDate = startDate.plusDays(1);
        }

        return dateRange;
    }
}