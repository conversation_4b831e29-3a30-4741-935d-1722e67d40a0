package com.company.wfm.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmployeeDepartmentDTO {

    //private Long empId;
    //private String empName;

    /*private Long departmentBranchId; // this should be the ID of the department branch
    private Long departmentId;        // department ID
    private Long branchId;
    private String departmentName;
    private String branchName;
    private List<UserDTO> userList;*/

    private Long departmentBranchId;
    private Long departmentId;
    private String departmentName;
    private Long branchId;
    private String branchName;
    private List<UserDTO> userList;
}
