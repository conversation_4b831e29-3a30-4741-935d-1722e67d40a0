package com.company.wfm.repository;

import java.time.LocalDate;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.company.wfm.entity.HolidaySchedule;

public interface HolidayScheduleRepository extends JpaRepository<HolidaySchedule, Long> {

   // Page<HolidaySchedule> findByDate(LocalDate date, Pageable pageable);
    @Override
	Page<HolidaySchedule> findAll(Pageable pageable);

   /* @Query("SELECT h FROM HolidaySchedule h WHERE h.date BETWEEN :fromDate AND :toDate")
    Page<HolidaySchedule> findByDateBetween(@Param("fromDate") LocalDate fromDate, @Param("toDate") LocalDate toDate, Pageable pageable);


    //filter work
    @Query("SELECT h FROM HolidaySchedule h WHERE LOWER(h.holiday) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<HolidaySchedule> findByHolidayNameContaining(@Param("name") String name, Pageable pageable);*/

 @Query("SELECT h FROM HolidaySchedule h WHERE h.date BETWEEN :fromDate AND :toDate")
 Page<HolidaySchedule> findByDateBetween(@Param("fromDate") LocalDate fromDate, @Param("toDate") LocalDate toDate, Pageable pageable);

 @Query("SELECT h FROM HolidaySchedule h WHERE LOWER(h.holiday) LIKE LOWER(CONCAT('%', :name, '%'))")
 Page<HolidaySchedule> findByHolidayNameContaining(@Param("name") String name, Pageable pageable);

 @Query("SELECT h FROM HolidaySchedule h WHERE h.date BETWEEN :fromDate AND :toDate AND LOWER(h.holiday) LIKE LOWER(CONCAT('%', :name, '%'))")
 Page<HolidaySchedule> findByDateBetweenAndHolidayNameContaining(@Param("fromDate") LocalDate fromDate, @Param("toDate") LocalDate toDate, @Param("name") String name, Pageable pageable);

 @Query("SELECT h FROM HolidaySchedule h WHERE h.date = :date AND h.isActive = :isActive AND h.isOptional = :isOptional")
 Optional<HolidaySchedule> findByDateAndIsActiveAndIsOptional(
     @Param("date") LocalDate date,
     @Param("isActive") int isActive,
     @Param("isOptional") int isOptional);
}

