package com.company.wfm.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.BranchDTO;

@Repository
public class BranchJDBCRepository {

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    String sql = "select * from t_branch b where b.branch_id=:branchId and b.company_id=:companyId";

    public BranchDTO validateHospitalBranch(Long branchId, Long companyId) {


        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("branchId", branchId);
        params.addValue("companyId", companyId);

        List<BranchDTO> results = namedParameterJdbcTemplate.query(sql, params, (rs, rowNum) -> {
            BranchDTO branchDTO = new BranchDTO();
            branchDTO.setId(rs.getLong("branch_id"));
            branchDTO.setCompanyId(rs.getInt("company_id"));
            branchDTO.setBranchName(rs.getString("branch_name"));
            return branchDTO;
        });

        return results.isEmpty() ? null : results.get(0);
    }
}
