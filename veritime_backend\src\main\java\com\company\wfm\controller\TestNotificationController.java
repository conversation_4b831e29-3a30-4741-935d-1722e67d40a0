package com.company.wfm.controller;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.Notification;
import com.company.wfm.service.NotificationService;

@RestController
public class TestNotificationController {

    @Autowired
    private NotificationService notificationService;

    @GetMapping("/trigger-notification")
    public String triggerNotification() {
        Notification notification = new Notification("New Alert", "This is a test notification", LocalDateTime.now().toString());
        notificationService.sendNotification(notification);
        return "Notification Sent!";
    }
}