package com.company.wfm.scheduler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> @date
 */
@Component
@Slf4j
public class EmployeeScheduleExecutor {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     *
     * @param empId
     * @param hireDate
     * @param leaveOnDays
     * @param defaultTimeSlotId
     */
    public void executeEmployeeSchedule(Long empId, String hireDate, String leaveOnDays, String defaultTimeSlotId) {
        try {
            String storedProc = "{call InsertEmployeeSchedule(?, ?, ?, ?)}";

            jdbcTemplate.update(
                    storedProc,
                    empId,
                    hireDate,
                    leaveOnDays,
                    defaultTimeSlotId
            );

           log.info("Stored procedure executed successfully for employee {} ", empId);

        } catch (Exception ex) {
        	log.error("Error executing stored procedure: {}" , ex);
        }
    }
}