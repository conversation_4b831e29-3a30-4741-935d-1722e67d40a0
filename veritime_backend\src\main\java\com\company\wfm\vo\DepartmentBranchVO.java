package com.company.wfm.vo;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DepartmentBranchVO {

	private long departmentId;
	private String departmentName;
	private String departmentCode;
	private String category;
	private int isActive;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Long createdBy;
	private String createdByName;
	private LocalDateTime createdAt;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Long updatedBy;
	private String updatedByName;
	private LocalDateTime updatedAt;
	private List<BranchDetailVO> branchList;

}
