package com.company.wfm.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.ResignationStatus;

@Repository
public interface ResignationStatusRepository extends JpaRepository<ResignationStatus, Long> {

    @Query("SELECT rs FROM ResignationStatus rs " +
            "JOIN Employee e ON rs.empId = e.empId " +
            "WHERE e.branch.id = :branchId")
    Page<ResignationStatus> findByBranchId(@Param("branchId") Long branchId, Pageable pageable);


    Optional<ResignationStatus> findByEmpId(Long empId);

    Page<ResignationStatus> findByEmpId(Long empId, Pageable pageable);

    @Query("SELECT rs FROM ResignationStatus rs WHERE rs.empId IN :empIds")
    Page<ResignationStatus> findByEmpIdIn(@Param("empIds") List<Long> empIds, Pageable pageable);

    @Query("SELECT rs FROM ResignationStatus rs WHERE rs.empId = :empId ORDER BY rs.createdTime DESC")
    List<ResignationStatus> findLatestResignationByEmpId(@Param("empId") Long empId);

}
