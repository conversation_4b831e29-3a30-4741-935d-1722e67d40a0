package com.company.wfm.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_ticket_category_master")
public class TicketCategoryMaster {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "category", nullable = true)
    private String category;

    @Column(name = "priority", nullable = true)
    private String priority;

    @Column(name = "created_by", nullable = true)
    private Long createdBy;

    @Column(name = "created_at", nullable = true)
    private LocalDateTime createdAt;

    @Column(name = "updated_by", nullable = true)
    private Long updatedBy;

    @Column(name = "updated_at", nullable = true)
    private LocalDateTime updatedAt;

    public Boolean getActive() {
        return isActive;


    }

    public void setTicketActive(Boolean active) {
        this.isActive = active;
    }

    @Column(name = "is_active", nullable = true)
    private Boolean isActive;

    @Column(name = "ETA", nullable = true)
    private BigDecimal eta;


    @Column(name = "branch_id", nullable = true)
    private Long branchId;

    @Column(name = "department_id", nullable = true)
    private Long departmentId;


}
