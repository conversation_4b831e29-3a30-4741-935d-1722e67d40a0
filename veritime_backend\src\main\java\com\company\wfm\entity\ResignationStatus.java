package com.company.wfm.entity;


import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "t_resignation_status")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ResignationStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;  // Auto-increment primary key

    @Column(name = "emp_id")
    private Long empId;  // Employee ID (Foreign Key)

    @Column(name = "notice_start_date")
    private LocalDate noticeStartDate;  // Notice period start date

    @Column(name = "last_working_date")
    private LocalDate lastWorkingDate;  // Last working day

    @Column(name = "status")
    private String status;  // Resignation status

    @Column(name = "created_by")
    private Long createdBy;  // User who created the record

    @Column(name = "created_time")
    private LocalDateTime createdTime;   // Creation timestamp

    @Column(name = "updated_by")
    private Long updatedBy;  // User who last updated the record

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;


    @Column(name = "remark")
    private String remark;

    @Column(name = "terminate_id")
    private Long terminateId;


}
