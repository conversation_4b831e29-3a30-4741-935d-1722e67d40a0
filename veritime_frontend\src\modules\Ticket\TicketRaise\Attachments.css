
.custom-container-attachment {
    background-color: var(--red14); 
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    /* width:1080px; */
  }
  
  .text-primary {
    color: var(--blue12) !important; 
  }
  
  .custom-file-input-container {
    display: flex;
    align-items: center;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px;
    position: relative;
  }
  
  .custom-file-label {
    font-weight: bold;
    color: var(--grey3);
    cursor: pointer;
    margin-right: 10px;
    flex-shrink: 0;
  }
  .separator {
    margin-right: 10px;
    /* font-weight: bold; */
    color: grey;
  }
  
  .custom-file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  
  .file-selected {
    color: var(--blue3);
    flex-grow: 1;
  }
  
  /* Target screens below 768px (mobile) */
@media (max-width: 768px) {
  .custom-container-attachment {
    padding: 15px; 
    width: 100%; 
    max-width: 600px; 
    margin-left: 10px; 
    
  }

  .form-group {
    margin-bottom: 1rem; 
    width: 100%; 
  }

  .custom-file-input-container {
    display: flex; 
    flex-direction: column; 
    align-items: flex-start; 
    padding: 5px; 
  }

  .custom-file-label {
    margin-bottom: 5px; 
    font-size: 12px; 
  }
}

/* Target screens between 768px and 1024px (tablet ) */
@media (min-width: 768px) and (max-width: 1024px) {
  .custom-container-attachment {
    padding: 20px; 
    width: 100%; 
    max-width: 600px; 
    margin-left: 15px; 
   
  }

  .form-group {
    margin-bottom: 1rem; 
    width: 100%; 
  }

  .custom-file-input-container {
    display: flex; 
    flex-direction: row; 
    align-items: center; 
    padding: 10px; 
  }

  .custom-file-label {
    margin-bottom: 0;
    font-size: 14px; 
  }
}

