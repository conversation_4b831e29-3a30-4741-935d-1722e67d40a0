import React, { useEffect, useState } from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import "../ProfileCard/ProfileCard.css";
import { appConstants } from "../../../constants/appConstants";
import Link from "next/link";
import OneSignal from 'react-onesignal';
import { decryptData } from "@/utils/encryption";
import { postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";

const ProfileCard = () => {
  const [username, setusername] = useState("");
  useEffect(() => {
    setusername(localStorage.getItem(appConstants.username));
  }, []);

  const logout = async () => {
    try {
      const response = await postRequest(API_URLS.LOGOUT,{logHistoryId:localStorage.getItem("logHistoryId")},true)
      if(response){
        await OneSignal.logout(localStorage.getItem("fb"))
        localStorage.clear()
        window.location.href = "/login";
      }
    } catch (error) {
      
    }
  };

  const [dashboard, setDashboard] = useState("");

  useEffect(() => {
    const role = localStorage.getItem(appConstants.role);
    switch (role) {
      case "ceo":
        setDashboard("/dashboards");
        break;
      case "superadmin":
        setDashboard("/dashboards");
        break;
      case "admin":
        setDashboard("/dashboards");
        break;
      case "supervisor":
        setDashboard("/dashboards");
        break;
      case "employee":
        setDashboard("/dashboards");
        break;
      default:
        setDashboard("/login");
    }
  }, []);

  return (
    <div className="profilecard-container">
      <div className="card profile-card" style={{ borderRadius: "14px" }}>
        <div className="card-body p-4">
          {/* <div className="text-center mb-2 d-flex justify-content-center align-items-center">
            <img src="/image/profile.png" className="rounded-circle mb-1" style={{ width: '50px', height: '50px', marginRight: '10px' }} alt="Profile" />
            <div className="text-muted" style={{ fontSize: '15px', fontWeight: '800' }}>{username}</div>
          </div> */}
          {/* <hr style={{ border: '1px solid #C4C4C4' }} /> */}
          <div className="d-flex align-items-center mb-4">
            <div className="bg-light rounded-circle d-flex justify-content-center align-items-center icon-container">
              <img
                src="/image/personicon.png"
                style={{ width: "40px", height: "40px" }}
                alt="My Profile"
              />
            </div>
            <Link href ={ "/ProfileDetails"}>
            <div
              className="ms-3 text-primary"
              style={{
                fontSize: "13px",
                fontWeight: "700",
                paddingLeft: "10px",
              }}
            >
              My Profile
            </div>
            </Link>
          </div>
          <hr style={{ border: "1px solid #C4C4C4" }} />
          <div className="d-flex align-items-center mb-4">
            <div className="bg-light rounded-circle d-flex justify-content-center align-items-center icon-container">
              <img
                src="/image/icons8-attendance-50 (1).png"
                style={{ width: "24px", height: "24px" }}
                alt="Help"
              />
            </div>
            <Link href ={"/myDashboard"}>
            <div
              className="ms-3 text-primary"
              style={{
                fontSize: "13px",
                fontWeight: "700",
                paddingLeft: "10px",
              }}
            >
              My Attendance
            </div>
            </Link>
          </div>
          <hr style={{ border: "1px solid #C4C4C4" }} />
          <div className="d-flex align-items-center mb-4">
            <div className="bg-light rounded-circle d-flex justify-content-center align-items-center icon-container">
              <img
                src="/image/help.png"
                style={{ width: "40px", height: "40px" }}
                alt="Help"
              />
            </div>
            <Link href={"/ticket"} prefetch={false}>
            <div
              className="ms-3 text-primary"
              style={{
                fontSize: "13px",
                fontWeight: "700",
                paddingLeft: "10px",
              }}
            >
              Help
            </div>
            </Link>
          </div>
          <hr style={{ border: "1px solid #C4C4C4" }} />
          <div className="d-flex align-items-center">
            <div className="bg-light rounded-circle d-flex justify-content-center align-items-center icon-container">
              <img
                src="/image/logout.png"
                style={{ width: "40px", height: "40px" }}
                alt="Logout"
              />
            </div>
            <div
              className="ms-3 text-primary"
              style={{
                fontSize: "13px",
                fontWeight: "700",
                paddingLeft: "10px",
              }}
              onClick={() => logout()}
            >
              Logout
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileCard;
