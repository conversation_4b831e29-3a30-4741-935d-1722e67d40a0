package com.company.wfm.dto;


import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BranchUpdateResponseDTO {
    private Long id;
    private String branchName;
    private String branchCode;
    private String branchHeadName;
    private String branchType;
    private Long hospitalId;
    private String hospitalName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long createdBy;
    private String createdByName;
    private Long updatedBy;
    private String updatedByName;
    private Integer isActive;
    private String cluster;
    private Boolean active;
    private Integer leaveCreditDay; // new Field
    private String timeZone; // new Field
}
