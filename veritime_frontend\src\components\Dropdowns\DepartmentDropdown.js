// components/DepartmentDropdown.js

import React, { useEffect, useState } from "react";
import Select from "react-select";
import { API_URLS } from "@/constants/apiConstants";
import { getRequest, postRequest } from "@/services/apiService";

const DepartmentDropdown = ({
  empDetail,
  formData = "",
  onChange,
  error = "",
  className = "",
  hideLabel = false,
}) => {
  const [departments, setDepartments] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState(null);

  // useEffect(() => {
  //   const fetchDepartments = async () => {
  //     const cachedDepartments = JSON.parse(localStorage.getItem("departments"));

  //     if (cachedDepartments && cachedDepartments.length > 0) {
  //       setDepartments(cachedDepartments);
  //     } else {
  //       try {
  //         const response = await getRequest(API_URLS.DEPT_LOOKUP);
  //         if (response) {
  //           setDepartments(response);
  //           localStorage.setItem("departments", JSON.stringify(response));
  //         }
  //       } catch (error) {
  //         console.error("Error fetching departments:", error);
  //       }
  //     }
  //   };

  //   fetchDepartments();
  // }, []);

  // useEffect(() => {
  //   const fetchDepartments = async () => {
  //     const requestBody = {
  //       branchId: empDetail, //this empDetail is branchId
  //     };

  //     try {
  //       const response = await postRequest(
  //         API_URLS.DEPARTMENTS_BY_BRANCH,
  //         requestBody
  //       );
  //       console.log("🔹 API Response:", response); // Log API response
  //       if (response && Array.isArray(response.departments)) {
  //         setDepartments(response.departments);
  //         if (response && Array.isArray(response.departments)) {
  //           setDepartments(response.departments);
  //         }
  //       }
  //     } catch (error) {
  //       setDepartments([]);
  //     }
  //   };

  //   fetchDepartments();
  // }, [empDetail]);

  useEffect(() => {
    const fetchDepartments = async () => {
      const requestBody = { branchId: empDetail };

      try {
        const response = await postRequest(
          API_URLS.DEPARTMENTS_BY_BRANCH,
          requestBody
        );
        //console.log("🔹 API Response:", response);

        if (Array.isArray(response)) {
          setDepartments(response);
          //console.log(" Updated departments:", response);
        }
      } catch (error) {
        console.error(" Error fetching departments:", error);
        setDepartments([]);
      }
    };

    fetchDepartments();
  }, [empDetail]);

  useEffect(() => {
    if (formData.departmentId && departments.length > 0) {
      const preselected = departments.find(
        (dept) => dept.departmentId === formData.departmentId
      );
      setSelectedDepartment(
        preselected
          ? {
              value: preselected.departmentId,
              label: preselected.departmentName,
            }
          : null
      );
    }
    //console.log(" Updated Departments State:", departments);
  }, [formData.departmentId, departments]);

  const handleDepartmentChange = (selectedOption) => {
    setSelectedDepartment(selectedOption);
    onChange({
      target: {
        name: "departmentId",
        value: selectedOption ? selectedOption.value : "",
      },
    });
  };

  // const options = departments.map((dept) => ({
  //   value: dept.departmentId,
  //   label: dept.departmentName,
  // }));

  const options =
    departments?.map((dept) => ({
      value: dept.departmentId,
      label: dept.departmentName,
    })) || [];

  //console.log(" Dropdown Options:", options);

  return (
    <div style={{ marginBottom: "15px" }}>
      {!hideLabel && (
        <label>
          Department <span className="text-danger">*</span>{" "}
        </label>
      )}

      <Select
        value={selectedDepartment}
        onChange={handleDepartmentChange}
        options={options}
        placeholder="Select department"
        className={className}
        styles={{
          control: (base) => ({
            ...base,
            width: "100%",
            padding: "8px",
            height: "41px",
            padding: "4px",
          }),
        }}
        isClearable
      />
      {error && <span style={{ color: "red", fontSize: "13px" }}>{error}</span>}
    </div>
  );
};

export default DepartmentDropdown;
