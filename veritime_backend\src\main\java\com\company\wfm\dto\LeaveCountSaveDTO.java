package com.company.wfm.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class LeaveCountSaveDTO {
    private long leaveTypeId;
    private Integer assignedLeave;
    private Integer balanceLeave;
    private Long empId;

    public LeaveCountSaveDTO(long leaveTypeId, Integer assignedLeave, Long empId ) {
        this.leaveTypeId = leaveTypeId;
        this.assignedLeave = assignedLeave;
        this.balanceLeave = assignedLeave;
        this.empId = empId;
    }
    // No-Args Constructor
    public void LeaveTypeDTO() {
    }
}