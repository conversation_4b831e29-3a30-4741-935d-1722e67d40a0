package com.company.wfm.leave.service.impl;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.entity.AttendanceEntity;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.EmployeeLeaveBalance;
import com.company.wfm.entity.LeaveMaster;
import com.company.wfm.leave.service.LeaveManagementService;
import com.company.wfm.repository.AttendanceRepository;
import com.company.wfm.repository.EmployeeLeaveBalanceRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.HolidayScheduleRepository;
import com.company.wfm.repository.LeaveMasterRepository;
import com.company.wfm.util.CommonConstant;
import com.company.wfm.util.LeaveCreditBasis;

/**
 * LeaveManagementService handles the business logic for leave management, including
 * leave crediting, eligibility checks, and leave balance updates for employees.
 *
 * <p>This service is responsible for ensuring that leave calculations are performed
 * correctly based on various factors such as employee eligibility, attendance, and 
 * company-specific leave policies.</p>
 *
 * <h3>Key Responsibilities:</h3>
 * <ul>
 *   <li>Determine employee eligibility for leave crediting based on probation and joining rules.</li>
 *   <li>Credit leaves to employees on a monthly or yearly basis depending on policy.</li>
 *   <li>Handle leave carry-forward at the end of the year based on defined rules.</li>
 *   <li>Update leave balances and maintain employee leave records.</li>
 *   <li>Provide support for leave adjustments and cache management.</li>
 * </ul>
 *
 * <h3>Leave Crediting Logic:</h3>
 * <ul>
 *   <li>Checks if an employee is eligible for leave based on {@code leave_credit_basis} (e.g., Joining Date, After Probation, Worked After Probation).</li>
 *   <li>Calculates leave on a pro-rata basis if attendance-based leave credit is applicable.</li>
 *   <li>Handles different types of leave crediting methods, such as lumpsum (yearly) or month-on-month (attendance-based).</li>
 *   <li>Ensures that only active leave types (where {@code is_active = 1}) are considered for leave credit.</li>
 * </ul>
 *
 * <h3>Integration with Other Components:</h3>
 * <ul>
 *   <li>{@link LeaveMasterService} - Retrieves leave policies and configurations.</li>
 *   <li>{@link EmployeeRepository} - Fetches employee details for leave processing.</li>
 *   <li>{@link AttendanceRepository} - Retrieves attendance data for pro-rata calculations.</li>
 *   <li>{@link LeaveBalanceRepository} - Updates and maintains employee leave balances.</li>
 * </ul>
 *
 * <h3>Cache Management:</h3>
 * <ul>
 *   <li>Uses caching to store active leave types for efficient retrieval.</li>
 *   <li>Evicts and reloads the leave type cache whenever a new leave type is added or updated.</li>
 * </ul>
 *
 * <p>This service ensures accurate leave tracking while allowing for configurable and dynamic leave policies.</p>
 *
 * <AUTHOR>
 * @since 2025
 */
@Service
public class LeaveManagementServiceImpl implements LeaveManagementService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(LeaveManagementServiceImpl.class);

    @Autowired
    private AttendanceRepository attendanceRepository;
    
    @Autowired
    private HolidayScheduleRepository holidayScheduleRepository;
    
    @Autowired
    private LeaveMasterRepository leaveMasterRepository;
    
    @Autowired
    private EmployeeRepository employeeRepository;
    
    @Autowired
    private EmployeeLeaveBalanceRepository employeeLeaveBalanceRepository;

    @Override
    @Transactional
    public void creditLeavesToEmployee(Long empId, Long leaveId) {
        LOGGER.info("Starting leave credit process for Employee ID: {} and Leave ID: {}", empId, leaveId);

        LeaveMaster leaveMaster = leaveMasterRepository.findById(leaveId)
                .orElseThrow(() -> new RuntimeException("Leave type not found"));
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new RuntimeException("Employee not found"));
        EmployeeLeaveBalance leaveBalance = employeeLeaveBalanceRepository.findByEmpIdAndLeaveId(empId, leaveId)
                .orElseGet(() -> createNewLeaveBalance(empId, leaveId));

        LocalDate today = LocalDate.now();
        LocalDate hireDate = toLocalDate(employee.getHireDate());
        LocalDate probationEndDate = hireDate.plusDays(employee.getProbationPeriod().longValue());
        LocalDate lastCreditDate = Optional.ofNullable(leaveBalance.getCreditDate())
                .map(this::toLocalDate).orElse(null);

        if (!isEmployeeEligibleForLeave(today, hireDate, probationEndDate, leaveMaster)) {
            LOGGER.warn("Employee ID: {} is not eligible for leave credit.", empId);
            return;
        }

        if (lastCreditDate != null && lastCreditDate.getYear() < today.getYear()) {
            handleYearEndCarryForward(leaveBalance, leaveMaster);
        }

        if (leaveMaster.getLeaveCreditMethod().equalsIgnoreCase(CommonConstant.ATTENDANCE)) {
            processAttendanceBasedLeave(empId, leaveMaster, leaveBalance, lastCreditDate, today);
        } else {
            processLumpsumLeave(leaveMaster, leaveBalance, lastCreditDate, today);
        }

        leaveBalance.setUpdatedTime(LocalDateTime.now());
        employeeLeaveBalanceRepository.save(leaveBalance);
        LOGGER.info("Leave credit process completed for Employee ID: {}", empId);
    }

    private void processAttendanceBasedLeave(Long empId, LeaveMaster leaveMaster, EmployeeLeaveBalance leaveBalance,
                                             LocalDate lastCreditDate, LocalDate today) {
        YearMonth currentMonth = YearMonth.now();
        if (lastCreditDate == null || today.getMonthValue() > lastCreditDate.getMonthValue()) {
            int totalWorkingDays = getTotalWorkingDaysForMonth(currentMonth, leaveMaster.getLeaveExclusion());
            int employeeAttendanceDays = getEmployeeAttendanceForMonth(empId, currentMonth);

            if (employeeAttendanceDays > 0) {
                double proratedLeave = ((double) employeeAttendanceDays / totalWorkingDays) * leaveMaster.getLeaveCount().doubleValue();
                int roundedLeave = (int) Math.floor(proratedLeave);

                leaveBalance.setAssignedLeave(roundedLeave);
                leaveBalance.setBalanceLeave(roundedLeave + leaveBalance.getBalanceLeave());
                leaveBalance.setCreditDate(java.sql.Date.valueOf(today));
                LOGGER.info("Attendance-based leave credited: {} days for Employee ID: {}", roundedLeave, empId);
            }
        }
    }

    private void processLumpsumLeave(LeaveMaster leaveMaster, EmployeeLeaveBalance leaveBalance,
                                     LocalDate lastCreditDate, LocalDate today) {
        if (lastCreditDate == null || today.getYear() > lastCreditDate.getYear()) {
            leaveBalance.setAssignedLeave(leaveMaster.getLeaveCount().intValue());
            leaveBalance.setBalanceLeave(leaveMaster.getLeaveCount().intValue() + leaveBalance.getBalanceLeave());
            leaveBalance.setCreditDate(java.sql.Date.valueOf(today));
            LOGGER.info("Lumpsum yearly leave credited: {} days", leaveMaster.getLeaveCount().intValue());
        }
    }

    private void handleYearEndCarryForward(EmployeeLeaveBalance leaveBalance, LeaveMaster leaveMaster) {
        if (leaveMaster.getCarryForward()) {
            leaveBalance.setBalanceLeave(leaveBalance.getBalanceLeave());
        } else {
            leaveBalance.setAssignedLeave(0);
            leaveBalance.setBalanceLeave(0);
        }
        leaveBalance.setCreditDate(java.sql.Date.valueOf(LocalDate.now()));
        LOGGER.info("Year-end carry forward applied. New balance: {} days", leaveBalance.getBalanceLeave());
    }

    private boolean isEmployeeEligibleForLeave(LocalDate today, LocalDate hireDate, LocalDate probationEndDate,
                                               LeaveMaster leaveMaster) {
        LeaveCreditBasis creditBasis = leaveMaster.getLeaveCreditBasisEnum();
        return switch (creditBasis) {
            case JOINING_DATE -> !today.isBefore(hireDate);
            case IMMEDIATE_AFTER_PROBATION_PERIOD -> !today.isBefore(probationEndDate);
            case WORKED_AFTER_PROBATION_PERIOD -> !today.isBefore(probationEndDate.plusDays(Long.parseLong(
                    Optional.ofNullable(leaveMaster.getLeaveCreditInterval()).orElse("0"))));
        };
    }

    private int getTotalWorkingDaysForMonth(YearMonth month, String exclusionRule) {
        return (int) month.atEndOfMonth().datesUntil(month.atEndOfMonth().plusDays(1))
                .filter(date -> !isWeekendOrHoliday(date, exclusionRule)).count();
    }

    private boolean isWeekendOrHoliday(LocalDate date, String exclusionRule) {
        return (exclusionRule.contains("Holiday") && isPublicHoliday(date)) ||
               (exclusionRule.contains("Weekoff") && isWeekend(date));
    }

    private boolean isWeekend(LocalDate date) {
        return date.getDayOfWeek() == DayOfWeek.SATURDAY || date.getDayOfWeek() == DayOfWeek.SUNDAY;
    }

    private boolean isPublicHoliday(LocalDate date) {
        return holidayScheduleRepository.findByDateAndIsActiveAndIsOptional(date, 1, 0).isPresent();
    }

    private LocalDate toLocalDate(java.util.Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    private EmployeeLeaveBalance createNewLeaveBalance(Long empId, Long leaveId) {
        EmployeeLeaveBalance leaveBalance = new EmployeeLeaveBalance();
        leaveBalance.setEmpId(empId);
        leaveBalance.setLeaveId(leaveId);
        leaveBalance.setAssignedLeave(0);
        leaveBalance.setBalanceLeave(0);
        leaveBalance.setCreatedTime(LocalDateTime.now());
        return leaveBalance;
    }
    
    /**
     * Calculates the total number of unique working days an employee attended in a given month.
     * Attendance is only counted if both check-in and check-out times are recorded.
     *
     * @param empId      The employee's unique identifier.
     * @param yearMonth  The year and month for which attendance is being calculated.
     * @return The count of distinct days the employee was present in the given month.
     */
    private int getEmployeeAttendanceForMonth(Long empId, YearMonth yearMonth) {
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();

        // Fetch only active attendance records for the given employee and date range
        List<AttendanceEntity> attendanceRecords = attendanceRepository
            .findByEmpIdAndDateBetweenAndIsActive(empId, startDate, endDate);

        // Use a Set to store unique attendance days, ensuring no duplicate counts
        return (int) attendanceRecords.stream()
            .filter(record -> record.getCheckOutTime() != null) // Consider only completed attendance records
            .map(AttendanceEntity::getDate) // Extract the date
            .distinct() // Remove duplicates
            .count(); // Count unique attendance days
    }

}