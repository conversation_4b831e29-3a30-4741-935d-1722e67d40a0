package com.company.wfm.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2018/8/9
 */
@ToString
@Getter
@Setter
public class Result<T> implements Serializable{
    private static final long serialVersionUID = -432433894613972712L;

    private Integer errorCode;

    private Integer code;

    private String msg;

    private String message;

    private T data;

    public Result() {
        this.errorCode = 0;
        this.msg = "success";
    }

    public Result(T data){
        this.data = data;
        this.errorCode = 0;
        this.msg = "success";
    }

    public Result(Integer code,String message,T data){
        this.message = message;
        this.code = code;
        this.data = data;
    }

    public Result(Integer errorCode,String msg){
        this.msg = msg;
        this.errorCode = errorCode;
    }

    public Result(T data, Integer code, String message) {
        this.data = data;
        this.code = code;
        this.message = message;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
