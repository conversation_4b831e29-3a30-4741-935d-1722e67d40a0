package com.company.wfm.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.EmailData;
import com.company.wfm.dto.EmployeeDepartmentDTO;
import com.company.wfm.dto.EmployeeDetailsDto;
import com.company.wfm.dto.EmployeeLeaveBalanceDto;
import com.company.wfm.dto.EscalationDetailsDTO;
import com.company.wfm.dto.NotificationDTO;
import com.company.wfm.dto.TicketCategoryResponseDTO;
import com.company.wfm.dto.TicketDTO;
import com.company.wfm.dto.TicketDetailsResponseDTO;
import com.company.wfm.dto.TicketEscalationRequestDTO;
import com.company.wfm.dto.TicketEscalationResponseDTO;
import com.company.wfm.dto.TicketFilterRequestDTO;
import com.company.wfm.dto.TicketResponseDTO;
import com.company.wfm.dto.UserDTO;
import com.company.wfm.dto.VendorEmployeeRequestDTO;
import com.company.wfm.entity.AssignedTickets;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.DepartmentBranch;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.Ticket;
import com.company.wfm.entity.TicketCategoryMaster;
import com.company.wfm.entity.TicketEscalation;
import com.company.wfm.entity.TicketUserMapping;
import com.company.wfm.entity.User;
import com.company.wfm.entity.Vendor;
import com.company.wfm.repository.AssignedTicketRepository;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentBranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.EmployeeLeaveBalanceRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.TicketCategoryMasterRepository;
import com.company.wfm.repository.TicketEscalationRepository;
import com.company.wfm.repository.TicketJdbcTemplateRepository;
import com.company.wfm.repository.TicketRepository;
import com.company.wfm.repository.TicketUserMappingRepository;
import com.company.wfm.repository.UserRepository;
import com.company.wfm.repository.VendorRepository;
import com.company.wfm.service.AmazonS3Service;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.EmailTemplateService;
import com.company.wfm.service.TicketService;
import com.company.wfm.service.UserTokenService;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;


@Service
public class TicketServiceImpl implements TicketService {

    private static final Logger logger = LoggerFactory.getLogger(TicketServiceImpl.class);

    @Autowired
    private TicketRepository ticketRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TicketJdbcTemplateRepository ticketJdbcTemplateRepository;

    @Autowired
    private DepartmentBranchRepository departmentBranchRepository;

    @Autowired
    private TicketUserMappingRepository ticketUserMappingRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private  DepartmentRepository departmentRepository;

    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private VendorRepository vendorRepository;

    @Autowired
    private EmployeeLeaveBalanceRepository employeeLeaveBalanceRepository;

    @Autowired
    private AssignedTicketRepository assignedTicketRepository;

    @Autowired
    private CommonNotificationService notificationService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private UserTokenService userTokenService;

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private  TicketCategoryMasterRepository ticketCategoryMasterRepository;

    @Autowired
    AmazonS3Service s3Service;

    @Autowired
    TicketEscalationRepository ticketEscalationRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private EmailTemplateService template;

    private static final String BASE_UPLOAD_DIRECTORY = "uploads/";
    private static final List<String> ALLOWED_FILE_TYPES = Arrays.asList("image/jpeg", "image/png");
    private static final long MAX_FILE_SIZE = 1 * 1024 * 1024; // 1 MB size limit
    private String filePath = null;
    private Long empId;
    private String uniqueFileName=null;

    @Override
    @Transactional
    public TicketResponseDTO createTicket(TicketDTO ticketDTO, MultipartFile[] files) {
    	filePath = "";
        uniqueFileName="";
    	empId = null;
        Ticket ticket = new Ticket();
        empId = ticketDTO.getEmpId();
        Long branchId;
        if (empId == null) {
            empId = getLoggedInUserId();
            branchId = getLoggedInUserBranchId();
           // System.out.println("branchid"+branchId);
        }else{
            branchId = findBranchIdByEmpId(empId);
            //System.out.println("branchid"+branchId);
        }


        ticket.setEmpId(empId);
        ticket.setTicketSubject(ticketDTO.getTicketSubject());
        ticket.setTicketMessage(ticketDTO.getTicketMessage());
        ticket.setStatus(ticketDTO.getStatus());
        ticket.setCategory(ticketDTO.getCategory());
        ticket.setCreatedBy(empId);
        ticket.setCreatedTime(LocalDateTime.now());
        //ticket creation time set internal automatic assign
        ticket.setCurrent_type("internal");
        ticket.setDepartmentId(ticketDTO.getDepartmentId());
        ticket.setBranchId(branchId);

        if (files != null && files.length > 0) {
            // Initialize a StringBuilder to concatenate file paths
            StringBuilder filePaths = new StringBuilder();

            for (MultipartFile file : files) {
                if (file != null && !file.isEmpty()) {
                    // Generate a unique file name
                    String fileName = file.getOriginalFilename();
                    String uniqueFileName = "ticket/" + empId + "/" + System.currentTimeMillis() + "_" + fileName;

                    // Upload the file to S3
                    s3Service.uploadFile(file, uniqueFileName);

                    // Append the file path to the StringBuilder
                    if (!filePaths.isEmpty()) {
                        filePaths.append(";"); // Separate paths with a semicolon
                    }
                    filePaths.append(uniqueFileName);
                }
            }

            // Save the concatenated file paths in the ticket, or null if no valid files
            ticket.setFilePath(!filePaths.isEmpty() ? filePaths.toString() : null);
        }
        // Save the ticket in the database
        Ticket savedTicket = ticketRepository.save(ticket);
        assignSupportPersonsToTicket(savedTicket);

        // Fetch category details using category ID
        TicketCategoryResponseDTO categoryDetails = ticketCategoryMasterRepository.findCategoryDetailsById(
                Long.parseLong(savedTicket.getCategory())
        );
        // Prepare the response DTO with the saved ticket details
        TicketResponseDTO responseDTO = new TicketResponseDTO();
        responseDTO.setTicketId(savedTicket.getTicketId());
        responseDTO.setTicketSubject(savedTicket.getTicketSubject());
        responseDTO.setTicketMessage(savedTicket.getTicketMessage());
        responseDTO.setStatus(savedTicket.getStatus());
        responseDTO.setFilePath(savedTicket.getFilePath());
        responseDTO.setCreatedBy(savedTicket.getCreatedBy());
        responseDTO.setUpdatedBy(savedTicket.getUpdatedBy());
        responseDTO.setDepartmentId(savedTicket.getDepartmentId());
        responseDTO.setBranchId(savedTicket.getBranchId());
        responseDTO.setTicketCode(savedTicket.getTicketCode());
        // Include category details in the response
        responseDTO.setCategoryDetails(categoryDetails);
        // separate method to send email and notification
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();



        Long upperId = employeeRepository.findUpperIdByEmpId(loggedInEmpId);
        if (upperId != 0) {
            sendTicketCreationNotifications(savedTicket, ticketDTO);
        }

        return responseDTO;
    }
    private void assignSupportPersonsToTicket(Ticket ticket) {
        Long employeeIdFromToken = tokenService.getEmployeeIdFromToken();

        Long ticketId = ticket.getTicketId();

        Ticket ticket1 = ticketRepository.findById(ticketId)
                .orElseThrow(() -> new EntityNotFoundException("Ticket not found"));

        Long departmentId=ticket1.getDepartmentId();
        Long branchId=ticket1.getBranchId();

        List<TicketUserMapping> supportPersons = ticketUserMappingRepository.findByDepartmentIdAndBranchId(departmentId,branchId);
        if (!supportPersons.isEmpty()) {
            // Pick a random support person from the list
            Random random = new Random();
            TicketUserMapping randomMapping = supportPersons.get(random.nextInt(supportPersons.size())); // Get random element

            // Create a ticket assignment for the randomly selected support person
            AssignedTickets ticketAssignment = new AssignedTickets();
            ticketAssignment.setTicket(ticket1);
            Employee employee1 = randomMapping.getUser().getEmployee();
            ticketAssignment.setEmployee(employee1);
            ticketAssignment.setType("internal");
            ticketAssignment.setCreatedAt(LocalDateTime.now());
            ticketAssignment.setCreatedBy(employeeIdFromToken);
            String remark = "A ticket has been assigned to " + (employee1.getEmpName() != null ? employee1.getEmpName() : "Unknown Employee") +
                    ". Issue: " + (ticket.getTicketMessage() != null ? ticket.getTicketMessage() : "No details provided");
            ticketAssignment.setRemark(remark);

            // Save the assignment to the database
            assignedTicketRepository.save(ticketAssignment);

            // Update ticket table
            String remark1 = "A ticket has been assigned to " + employee1.getEmpName();
            ticket.setAssigned_by(employeeIdFromToken);
            ticket.setAssigned_to(employee1.getEmpId()); // Assign to randomly selected employee
            ticket.setInternal_ticket_status("assigned");
            ticket.setLatest_remark(remark1);
            ticketRepository.save(ticket);

            // Send email notification to employee
            String emailId = employeeRepository.getReferenceById(employee1.getEmpId()).getEmail();
            EmailData data = new EmailData();
            data.setEmailId(emailId);
            data.setSubject("Ticket Assignment");
            data.setMessage("Ticket number: " + ticketId + " has been assigned to you.");
            emailService.sendEmail(data);

            // Send push notification
            sendTicketAssignmentNotification(employee1.getEmpId(), ticketId, remark, employeeIdFromToken);
        } else {
            // Handle the case where no support persons are found
            throw new RuntimeException("No support persons found.");
        }
    }

    private void sendTicketAssignmentNotification(Long empId, Long ticketId, String remark, Long assignedById) {
        // Define notification title and message
        String notificationTitle = "Ticket Assigned";
        String notificationMessage = "Ticket number: " + ticketId + " has been assigned to you."
                + (remark != null ? " Remark: " + remark : "");

        // Create NotificationDTO with required details
        NotificationDTO notificationDTO = new NotificationDTO(
                notificationTitle,
                notificationMessage,
                "ticket/details/" + ticketId,
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        // Send the notification using notificationService
        notificationService.sendNotificationToEmployee(
                empId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "ticket/details/" + ticketId,
                String.valueOf(assignedById)
        );
    }




    public String saveUploadedFile(MultipartFile file, String empId) throws IOException {
        // Create the directory if it doesn't exist
        Path folderPath = Paths.get(BASE_UPLOAD_DIRECTORY, empId);  // Using comma instead of string concatenation for better path management
        if (!Files.exists(folderPath)) {
            Files.createDirectories(folderPath);
        }

        // Save the file
        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.isEmpty()) {
            throw new IllegalArgumentException("File name cannot be empty.");
        }

        Path filePath = folderPath.resolve(fileName);
        Files.write(filePath, file.getBytes());

        // Return the file URL or path
        return "/api/v1/tickets/files/" + empId + "/" + fileName;
    }
    // creation of ticket send mail and notification
private void sendTicketCreationNotifications(Ticket ticket, TicketDTO ticketDTO) {
   Long empId=ticket.getEmpId();
    Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

    Long upperId = employeeRepository.findUpperIdByEmpId(empId);
    if (upperId == null) {
       // throw new IllegalStateException("No supervisor found for employee ID: " + empId);
        logger.error("No supervisor found for employee ID: "+empId+" to send notification");
        return;
    }
    String emailSubject = "New Ticket Created: " + ticket.getTicketSubject();
    String emailMessage = "A new ticket has been created with ID " + ticket.getTicketId() + ".\n\nDetails:\n" +
            "Subject: " + ticket.getTicketSubject() + "\n" +
            "Message: " + ticket.getTicketMessage() + "\n" +
            "Status: " + ticket.getStatus();

    String emailId = employeeRepository.getReferenceById(upperId).getEmail();
    EmailData emailData = new EmailData();
    emailData.setEmailId(emailId); // Assuming ticketDTO has an email field
    emailData.setSubject(emailSubject);
    emailData.setMessage(emailMessage);
    emailService.sendEmail(emailData);

    String ticketDetailsUrl = "ticket/details/" + ticket.getTicketId();

    // Notification sending logic
    NotificationDTO notificationDTO = new NotificationDTO(
            "New Ticket Created",
            "A new ticket with ID " + ticket.getTicketId() + " has been created.",
            ticketDetailsUrl,
            String.valueOf(loggedInEmpId),
            LocalDateTime.now().toInstant(ZoneOffset.UTC)
    );

    notificationService.sendNotificationToEmployee(
            upperId,
            notificationDTO.getTitle(),
            notificationDTO.getBody(),
            ticketDetailsUrl,
            String.valueOf(loggedInEmpId)
    );
}




    // Method to get the currently logged-in user's ID
    private Long getLoggedInUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();  // Assuming username is the login credential
        return findEmployeeIdByUsername(username);
    }

    private Long findEmployeeIdByUsername(String username) {
        User user = userRepository.findByUsername(username);
        if (user != null && user.getEmployee() != null) {
            return user.getEmployee().getEmpId();
        }

        throw new IllegalArgumentException("User or Employee not found for username: " + username);
    }

    private Long getLoggedInUserBranchId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();  // Assuming username is the login credential
        return findBranchIdByUsername(username);
    }

    private Long findBranchIdByUsername(String username) {
        User user = userRepository.findByUsername(username);
        if (user != null && user.getEmployee() != null && user.getEmployee().getBranch() != null) {
            return user.getEmployee().getBranch().getId();
        }
        throw new IllegalArgumentException("Branch not found for the logged-in user");
    }

    // Helper method to find branch ID by employee ID
    private Long findBranchIdByEmpId(Long empId) {
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new IllegalArgumentException("Employee not found for ID: " + empId));

        if (employee.getBranch() != null) {
            return employee.getBranch().getId();
        }
        throw new IllegalArgumentException("Branch not found for employee ID: " + empId);
    }




    // listing
    @Override
	public Page<Ticket> listAllTickets(Pageable pageable) {
        try {

            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
            Long departmentId = employeeRepository.findById(loggedInEmpId)
                    .orElseThrow(() -> new RuntimeException("Employee not found"))
                    .getDepartment()
                    .getDepartmentId();
        //    return ticketJdbcTemplateRepository.findAllTickets(pageable,departmentId);
            // Fetch tickets where the logged-in employee is either the creator or assignee, and department matches
            return ticketJdbcTemplateRepository.findAllTickets(pageable, departmentId, loggedInEmpId);

        } catch (Exception e) {
            // Log the error message
            logger.error("Error fetching all tickets: {}", e.getMessage(), e);
            // Re-throwing the exception
            throw new RuntimeException("Could not fetch tickets", e);
        }
    }

    //new listing

    @Override
	public Page<Ticket> listAllTickets1(Pageable pageable, TicketFilterRequestDTO ticketFilterRequestDTO) {
        try {

            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
            Long departmentId = employeeRepository.findById(loggedInEmpId)
                    .orElseThrow(() -> new RuntimeException("Employee not found"))
                    .getDepartment()
                    .getDepartmentId();
            //    return ticketJdbcTemplateRepository.findAllTickets(pageable,departmentId);
            // Fetch tickets where the logged-in employee is either the creator or assignee, and department matches
            return ticketJdbcTemplateRepository.findAllTickets1(pageable, departmentId, loggedInEmpId, ticketFilterRequestDTO);

        } catch (Exception e) {
            // Log the error message
            logger.error("Error fetching all tickets: {}", e.getMessage(), e);
            // Re-throwing the exception
            throw new RuntimeException("Could not fetch tickets", e);
        }
    }




    //update
    @Override
    public TicketResponseDTO updateTicket(Long ticketId, TicketDTO ticketDTO, MultipartFile file) {
        Long empId = getLoggedInUserId();

        // Fetch the existing ticket
        Ticket existingTicket = ticketRepository.findById(ticketId)
                .orElseThrow(() -> new RuntimeException("Ticket not found"));

        // Check if status is provided and valid
        if (ticketDTO.getStatus() != null) {
            List<String> validStatuses = Arrays.asList("Pending", "Resolved", "Closed", "Cancelled");
            if (!validStatuses.contains(ticketDTO.getStatus())) {
                throw new RuntimeException("Invalid status: " + ticketDTO.getStatus());
            }
            existingTicket.setStatus(ticketDTO.getStatus());
        } else {
            existingTicket.setStatus(existingTicket.getStatus());
        }

        existingTicket.setUpdatedBy(empId);
        existingTicket.setUpdatedTime(LocalDateTime.now());

        // Handle file upload if a new file is provided
       /* if (file != null && !file.isEmpty()) {
            String filePath = saveFile(file); // Reuse the saveFile method to save the new file
            existingTicket.setFilePath(filePath); // Update the file path in the ticket
        }*/

        // Save the updated ticket in the database
        Ticket savedTicket = ticketRepository.save(existingTicket);

        // Prepare the response DTO with the updated ticket details
        TicketResponseDTO responseDTO = new TicketResponseDTO();
        responseDTO.setTicketId(savedTicket.getTicketId());
        responseDTO.setTicketSubject(savedTicket.getTicketSubject()); // Retaining original subject
        responseDTO.setTicketMessage(savedTicket.getTicketMessage()); // Retaining original message
        responseDTO.setCreatedBy(savedTicket.getCreatedBy());
        responseDTO.setUpdatedBy(savedTicket.getUpdatedBy());
        responseDTO.setStatus(savedTicket.getStatus());
        responseDTO.setFilePath(savedTicket.getFilePath());

        return responseDTO;
    }

    @Override
	@Transactional
    public void saveMappings(Long branchId, Long departmentId, List<Long> userIds) {

        ticketUserMappingRepository.deleteByBranchIdAndDepartmentId(branchId, departmentId);
        Branch branch = branchRepository.findById(branchId)
                .orElseThrow(() -> new IllegalArgumentException("Invalid branch ID"));

        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new IllegalArgumentException("Invalid department ID"));

        for (Long employeeId : userIds) {
            User user = userRepository.findUserIdByEmployeeId(employeeId)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid user ID: " + employeeId));
            TicketUserMapping mapping = new TicketUserMapping();
            mapping.setBranch(branch);
            mapping.setDepartment(department);
            mapping.setUser(user);
            ticketUserMappingRepository.save(mapping);
        }
    }



    @Override
	public Page<EmployeeDepartmentDTO> getAllEmployeesByDepartmentBranch(Pageable pageable,String type) {

        // Fetch all department branches with pagination
        Page<DepartmentBranch> departmentBranches = null;
        User employee = userTokenService.getEmployeeFromToken();
        if (employee != null
                && (employee.getRole().equalsIgnoreCase("supervisor") || employee.getRole().equals("employee"))) {
            throw new RuntimeException("You are not authorized to view the branch details");
        }

        if (employee.getRole().equalsIgnoreCase("superadmin")) {

            Optional<List<Branch>> branchData = branchRepository.findByHospitalId(employee.getEmployee().getCompanyId());
            List<Long> branchId = branchData.get().stream().map(Branch::getId).collect(Collectors.toList());
            departmentBranches = departmentBranchRepository.findByBranchIdIn(branchId, pageable);

        } else if (employee.getRole().equalsIgnoreCase("admin")) {
            Optional<Branch> branchData = branchRepository.findByBranchId(employee.getEmployee().getBranch().getId());
            departmentBranches = departmentBranchRepository.findByBranchId(branchData.get().getId(), pageable);
        }


        // Create a list to hold the result
        List<EmployeeDepartmentDTO> employeeDepartmentDTOList = new ArrayList<>();
        if ("select".equalsIgnoreCase(type)) {
            for (DepartmentBranch departmentBranch : departmentBranches) {
                Long departmentId = departmentBranch.getDepartmentId();
                String departmentName = departmentRepository.findById(departmentId)
                        .map(Department::getDepartmentName)
                        .orElse("Unknown Department");

                // Create a simplified DTO with only department details
                EmployeeDepartmentDTO departmentDTO = new EmployeeDepartmentDTO();
               // departmentDTO.setDepartmentId(departmentId);
                departmentDTO.setDepartmentName(departmentName);
                employeeDepartmentDTOList.add(departmentDTO);
            }
        } else {
            // Fetch employees based on department branches
            for (DepartmentBranch departmentBranch : departmentBranches) {
                Long branchId = departmentBranch.getBranchId();
                Long departmentId = departmentBranch.getDepartmentId();
               // Long branchId= 1427L;
              //  Long departmentId= 176L;
                String departmentName = departmentRepository.findById(departmentId)
                        .map(Department::getDepartmentName)
                        .orElse("Unknown Department");

                String branchName = branchRepository.findById(branchId)
                        .map(Branch::getBranchName)
                        .orElse("Unknown Branch");

                // Fetch employees based on branchId and departmentId
                List<Employee> employees = employeeRepository.findEmployeesByBranchAndDepartment(branchId, departmentId);
                // Create the UserDTO list for each department branch
                List<UserDTO> userList = new ArrayList<>();
                for (Employee employee1 : employees) {
                    Long empId1=employee1.getEmpId();
                    // new changes
                    Long empId=userRepository.findIdByEmployeeId(empId1)
                            .orElseThrow(() -> new IllegalArgumentException("No user found for employee ID: " + empId1));
                    boolean isSupportPerson = ticketUserMappingRepository.existsByUser_IdAndBranch_IdAndDepartment_DepartmentId(empId, branchId, departmentId);
                    logger.info("EmpId: {}, BranchId: {}, DeptId: {}, isSupportPerson: {}", empId, branchId, departmentId, isSupportPerson);
                    userList.add(new UserDTO(employee1.getEmpId(), employee1.getEmpName(), employee1.getImgUre(),isSupportPerson));
                   // userList.add(new UserDTO(employee1.getEmpId(), employee1.getEmpName(), employee1.getImgUre()));
                }

                // Create the DTO for this department branch and add to the result list
                EmployeeDepartmentDTO employeeDepartmentDTO = new EmployeeDepartmentDTO(
                        departmentBranch.getId(),
                        departmentId,
                        departmentName,
                        branchId,
                        branchName,
                        userList
                );
                employeeDepartmentDTOList.add(employeeDepartmentDTO);
            }
        }


        // Convert the list of DTOs into a paginated response and return
        return new PageImpl<>(employeeDepartmentDTOList, pageable, departmentBranches.getTotalElements());


    }

    @Override
    public Page<EmployeeDetailsDto> getAllEmployees(VendorEmployeeRequestDTO request,Pageable pageable) {

        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
       // Page<Employee> employeePage = employeeRepository.findAll(pageable);
        Long branchId = employeeRepository.findById(loggedInEmpId)
                .map(Employee::getBranch)
                .map(Branch::getId)
                .orElseThrow(() -> new IllegalArgumentException("Logged-in user's branch ID not found."));

        // Determine the department ID to filter
        Long departmentId = request.getDepartmentId() != null
                ? request.getDepartmentId()
                : employeeRepository.findById(loggedInEmpId)
                .map(Employee::getDepartment)
                .map(Department::getDepartmentId)
                .orElse(null);

        Page<Employee> employeePage;

        if (departmentId != null) {
            employeePage = employeeRepository.findByBranchIdAndDepartmentId(branchId,departmentId, pageable);
        } else {
            // Fetch all employees if no department ID is available
            employeePage = employeeRepository.findAll(pageable);
        }

        // Map to EmployeeDetailsDto
        List<EmployeeDetailsDto> employeeDtos = employeePage.stream().map(employee -> {
            List<EmployeeLeaveBalanceDto> leaveBalances = employeeLeaveBalanceRepository.findByEmpId(employee.getEmpId())
                    .stream()
                    .map(leaveBalance -> new EmployeeLeaveBalanceDto(
                            leaveBalance.getEmpLeaveId(),
                            leaveBalance.getEmpId(),
                            leaveBalance.getLeaveId(),
                            leaveBalance.getAssignedLeave(),
                            leaveBalance.getBalanceLeave(),
                            leaveBalance.getCreatedTime(),
                            leaveBalance.getUpdatedTime()
                    ))
                    .collect(Collectors.toList());

            return EmployeeDetailsDto.builder()
                    .empId(employee.getEmpId())
                    .companyId(employee.getCompanyId())
                    .departmentId(employee.getDepartment() != null ? employee.getDepartment().getDepartmentId() : null)
                    .deptName(employee.getDepartment() != null ? employee.getDepartment().getDepartmentName() : null)
                    .designationId(employee.getDesignation() != null ? employee.getDesignation().getId() : null)
                    .designationName(employee.getDesignation() != null ? employee.getDesignation().getName() : null)
                    .branchId(employee.getBranch() !=null? employee.getBranch().getId() : null)
                    .branchName(employee.getBranch() != null ? employee.getBranch().getBranchName() : null)
                    .empCode(employee.getEmpCode())
                    .empName(employee.getEmpName())
                    .uid(employee.getBiometricID())
                    .idNo(employee.getIdNo())
                    .orgId(employee.getOrgId())
                    .upperId(employee.getUpperId())
                    .regionId(employee.getRegionId())
                    .countryId(employee.getCountryId())
                    .provinceId(employee.getProvinceId())
                    .city(employee.getCity())
                    .upperName(employee.getUpperName())
                    .hireDate(employee.getHireDate())
                    .gender(employee.getGender())
                    .birthday(employee.getBirthday())
                    .nation(employee.getNation())
                    .married(employee.getMarried())
                    .phoneNo(employee.getPhoneNo())
                    .mobileNo(employee.getMobileNo())
                    .email(employee.getEmail())
                    .nativePlace(employee.getNativePlace())
                    .zipCode(employee.getZipCode())
                    .isHistory(employee.getIsHistory())
                    .inService(employee.getInService())
                    .remark(employee.getRemark())
                    .createdBy(employee.getCreatedBy())
                    .createdTime(employee.getCreatedTime())
                    .updatedBy(employee.getUpdatedBy())
                    .updatedTime(employee.getUpdatedTime())
                    .version(employee.getVersion())
                    .nativeLanguage(employee.getNativeLanguage())
                    .foreignLanguages(employee.getForeignLanguages())
                    .leaveOnDays(employee.getLeaveOnDays())
                    .workYears(employee.getWorkYears())
                    .graduateSchool(employee.getGraduateSchool())
                    .graduateTime(employee.getGraduateTime())
                    .highestDegree(employee.getHighestDegree())
                    .imgUre(employee.getImgUre())
                    .defaultTimeSlot(employee.getDefaultTimeSlotId())
                    .unitNumber(employee.getUnitNumber())
                    .street(employee.getStreet())
                    .leaveBalances(leaveBalances) // Include leave balances
                    .build();
        }).collect(Collectors.toList());

        // Return a Page<EmployeeDetailsDto>
        return new PageImpl<>(employeeDtos, pageable, employeePage.getTotalElements());
    }

    @Override
    public Page<Vendor> getAllVendors(Pageable pageable) {
        return vendorRepository.findAll(pageable);
    }

    @Override
    public Optional<TicketDetailsResponseDTO> getTicketDetails(Long ticketId) {
        Optional<Ticket> ticket = ticketRepository.findById(ticketId);

        if (ticket.isPresent()) {
            Ticket ticketData = ticket.get();
            String departmentName = "null";
            if (ticketData.getDepartmentId() != null) {
                Optional<Department> department = departmentRepository.findById(ticketData.getDepartmentId());
                departmentName = department.map(Department::getDepartmentName).orElse("null");
            }
            // Fetch assigned-to employee name using assigned_to empId
            String assignedToName = "null";
            if (ticketData.getAssigned_to() != null) {
                Optional<Employee> assignedEmployee = employeeRepository.findById(ticketData.getAssigned_to());
                assignedToName = assignedEmployee.map(Employee::getEmpName).orElse("null");
            }
            // Fetch assigned-by employee name using assigned_by empId
            String assignedByName = "null";
            if (ticketData.getAssigned_by() != null) {
                Optional<Employee> assignedByEmployee = employeeRepository.findById(ticketData.getAssigned_by());
                assignedByName = assignedByEmployee.map(Employee::getEmpName).orElse("null");
            }

            // Fetch created-by employee name using createdBy empId
            String createdByName = "null";
            if (ticketData.getCreatedBy() != null) {
                Optional<Employee> createdByEmployee = employeeRepository.findById(ticketData.getCreatedBy());
                createdByName = createdByEmployee.map(Employee::getEmpName).orElse("null");
            }


            Long categoryId = null;
            String categoryName = "null";
            String priority = "null";
            if (ticketData.getCategory() != null) {
                Optional<TicketCategoryMaster> category = ticketCategoryMasterRepository.findById(Long.valueOf(ticketData.getCategory()));
                if (category.isPresent()) {
                    TicketCategoryMaster categoryData = category.get();
                    categoryId = categoryData.getId();
                    categoryName = categoryData.getCategory();
                    priority = categoryData.getPriority();
                }
            }

            // Fetch escalation details
            List<TicketEscalation> escalations = ticketEscalationRepository.findByTicketId(ticketId);

            List<EscalationDetailsDTO> escalationDetailsDTOs = escalations.stream()
                    .map(escalation -> {
                        String escalationCreatedByName = employeeRepository.findById(escalation.getCreatedBy())
                                .map(Employee::getEmpName)
                                .orElse("null");
                        Long upperid = employeeRepository.findUpperIdByEmpId(escalation.getEmpId());
                        String escalatedToName = employeeRepository.findEmployeeNameById(upperid);


                        return new EscalationDetailsDTO(
                                escalation.getCreatedBy(),
                                escalationCreatedByName,
                                escalation.getCreatedAt(),
                                upperid,
                                escalatedToName
                        );
                    }).toList();


            // Build the response DTO
            TicketDetailsResponseDTO responseDTO = new TicketDetailsResponseDTO(
                    ticketData.getTicketId(),
                    ticketData.getEmpId(),
                    ticketData.getTicketSubject(),
                    ticketData.getTicketMessage(),
                    ticketData.getTicketCode(),
                    ticketData.getStatus(),
                    ticketData.getCategory(),
                    ticketData.getFilePath(),
                    ticketData.getUpdatedBy(),
                    ticketData.getUpdatedTime(),
                    ticketData.getCreatedBy(),
                    createdByName,
                    ticketData.getCreatedTime(),
                    ticketData.getDepartmentId(),
                    departmentName,
                    ticketData.getAssigned_to(),
                    assignedToName,
                    ticketData.getAssigned_by(),
                    assignedByName,
                    ticketData.getInternal_ticket_status(),
                    categoryId,
                    categoryName,
                    priority,
                    ticketData.getLastEscalationTime(),
                    assignedTicketRepository.findAssignmentsByTicketId(ticketId) ,// Assuming this returns a list of assignments
                    escalationDetailsDTOs
            );
            return Optional.of(responseDTO);
        }
        return Optional.empty();
    }

    public boolean existsByUserIdBranchIdAndDepartmentId(Long userId, Long branchId, Long departmentId) {
        return ticketUserMappingRepository.existsByUser_IdAndBranch_IdAndDepartment_DepartmentId(userId, branchId, departmentId);
    }


    //listing with filter generic listing
    @Override
    public Page<Ticket> listTicketsWithFilters(Pageable pageable, TicketFilterRequestDTO filterRequest) {
        try {
            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
            Long loggedInBranchId = getLoggedInUserBranchId();

            System.out.println("Department ID: " + filterRequest.getDepartmentId());
           System.out.println("Logged-in Employee ID: " + loggedInEmpId);
            System.out.println("Logged-in branch ID: " + loggedInBranchId);

            // Check if all filters are empty or null
            boolean areFiltersEmpty = (filterRequest.getDepartmentId() == null) &&
                    (filterRequest.getMyDepartmentTicket() == null || filterRequest.getMyDepartmentTicket().trim().isEmpty()) &&
                    (filterRequest.getStatus() == null || filterRequest.getStatus().trim().isEmpty()) &&
                    (filterRequest.getAssignedTo() == null) &&
                    (filterRequest.getCreatedBy() == null) &&
                    (filterRequest.getQuery() == null || filterRequest.getQuery().trim().isEmpty());

            // If all filters are empty, fetch all tickets
                if (areFiltersEmpty) {

                    String sql = "SELECT t.*, c.category AS category_name " +
                            "FROM t_ticket t " +
                            "LEFT JOIN t_ticket_category_master c ON t.category = c.id " +
                            "WHERE t.branch_id = ? " +
                            "ORDER BY ticket_id DESC OFFSET ? ROWS FETCH NEXT ? ROWS ONLY";
                    String countSql = "SELECT COUNT(*) FROM t_ticket WHERE branch_id = ?";

                    // Count Query (For Total Rows)
                    int totalRows = jdbcTemplate.queryForObject(countSql, new Object[]{loggedInBranchId}, Integer.class);

                    // Fetch Tickets for Branch
                    List<Ticket> tickets = jdbcTemplate.query(sql,
                            new Object[]{loggedInBranchId, pageable.getOffset(), pageable.getPageSize()},
                            ticketRowMapper);

                    return new PageImpl<>(tickets, pageable, totalRows);
                }
            StringBuilder sql = new StringBuilder("SELECT t.*, c.category AS category_name " +
                    "FROM t_ticket t " +
                    "LEFT JOIN t_ticket_category_master c ON t.category = c.id " +
                    "WHERE t.branch_id = " + loggedInBranchId);




            // Apply Department Filter
            if (filterRequest.getDepartmentId() != null) {
                sql.append(" AND t.department_id = ").append(filterRequest.getDepartmentId());  // Use t.department_id
            }

            // Apply Created By or Assigned To Filters
            if ("createdByMe".equalsIgnoreCase(filterRequest.getMyDepartmentTicket())) {
                sql.append(" AND t.created_by = ").append(loggedInEmpId);
            } else if ("assignedToMe".equalsIgnoreCase(filterRequest.getMyDepartmentTicket())) {
                sql.append(" AND t.assigned_to = ").append(loggedInEmpId);

            }

            // Apply Status Filter
            if (filterRequest.getStatus() != null && !filterRequest.getStatus().trim().isEmpty()) {
                sql.append(" AND t.status = '").append(filterRequest.getStatus()).append("'");
            }

            // Apply Assigned To Filter
            if (filterRequest.getAssignedTo() != null) {
                sql.append(" AND t.assigned_to = ").append(filterRequest.getAssignedTo());
            }

            // Apply Created By Filter
            if (filterRequest.getCreatedBy() != null) {
                sql.append(" AND t.created_by = ").append(filterRequest.getCreatedBy());
            }

            // Apply Text-Based Filter
            if (filterRequest.getQuery() != null && !filterRequest.getQuery().trim().isEmpty()) {
                sql.append(" AND (CAST(ticket_id AS VARCHAR) LIKE '%").append(filterRequest.getQuery()).append("%'")
                        .append(" OR ticket_subject LIKE '%").append(filterRequest.getQuery()).append("%'")
                        .append(" OR ticket_message LIKE '%").append(filterRequest.getQuery()).append("%'")
                        .append(" OR c.category LIKE '%").append(filterRequest.getQuery()).append("%')");
            }

            // Count Query (For Total Rows)
            String countSql = sql.toString().replace("SELECT t.*, c.category AS category_name", "SELECT COUNT(*)");

            int totalRows = jdbcTemplate.queryForObject(countSql, Integer.class);

            // Add Pagination and Sorting to SQL
            sql.append(" ORDER BY ticket_id DESC")
                    .append(" OFFSET ").append(pageable.getOffset()).append(" ROWS")
                    .append(" FETCH NEXT ").append(pageable.getPageSize()).append(" ROWS ONLY");

            // Execute Query for Ticket Data
            List<Ticket> tickets = jdbcTemplate.query(sql.toString(), ticketRowMapper);

            // Return Paginated Result
            return new PageImpl<>(tickets, pageable, totalRows);
        } catch (Exception e) {
            logger.error("Error fetching filtered tickets: {}", e.getMessage(), e);
            throw new RuntimeException("Could not fetch filtered tickets", e);
        }
    }


    private final RowMapper<Ticket> ticketRowMapper = new RowMapper<>() {
        @Override
        public Ticket mapRow(ResultSet rs, int rowNum) throws SQLException {
            Ticket ticket = new Ticket();
            ticket.setTicketId(rs.getLong("TICKET_ID")); // Adjusted to match your entity column names
            ticket.setEmpId(rs.getLong("EMP_ID"));
            ticket.setTicketCode(rs.getString("ticket_code"));
            ticket.setTicketSubject(rs.getString("TICKET_SUBJECT"));
            ticket.setTicketMessage(rs.getString("TICKET_MESSAGE"));
            ticket.setStatus(rs.getString("STATUS"));
            ticket.setCategory(rs.getString("CATEGORY"));
            ticket.setFilePath(rs.getString("FILE_PATH"));
            ticket.setCreatedBy(rs.getLong("CREATED_BY")); // Changed to Long type
            ticket.setCreatedTime(rs.getTimestamp("CREATED_TIME").toLocalDateTime());
            ticket.setUpdatedBy(rs.getLong("UPDATED_BY"));
            ticket.setUpdatedTime(rs.getTimestamp("UPDATED_TIME") != null ? rs.getTimestamp("UPDATED_TIME").toLocalDateTime() : null); // Check for null before conversion
            ticket.setBranchId(rs.getLong("BRANCH_ID")); // Map branchId
            ticket.setDepartmentId(rs.getLong("DEPARTMENT_ID")); // Map departmentId
            ticket.setAssigned_to(rs.getLong("ASSIGNED_TO")); // Map assignedTo
            ticket.setAssigned_by(rs.getLong("ASSIGNED_BY")); // Map assignedBy
            ticket.setLatest_remark(rs.getString("LATEST_REMARK")); // Map latestRemark
            ticket.setCurrent_type(rs.getString("CURRENT_TYPE"));
            // Set the categoryName directly
            ticket.setCategoryName(rs.getString("category_name"));

            return ticket;
        }
    };

    @Override
    public void escalateTicket(TicketEscalationRequestDTO request, HttpServletRequest request1) {

      try{ //url
        String frontendUrl = (String) request1.getAttribute("frontendUrl");

        // Construct the reset URL with frontend URL, falling back to server URL if needed
        String resetUrl;
        if (frontendUrl != null) {
            resetUrl = frontendUrl + "ticket/details/";
        } else {
            resetUrl = request1.getRequestURL().toString().replace(request1.getRequestURI(), "") + "ticket/details/";
        }
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        Ticket ticket = ticketRepository.findById(request.getTicketId())
                .orElseThrow(() -> new EntityNotFoundException("Ticket not found with ID: " + request.getTicketId()));

        String type1=ticket.getCurrent_type();
        System.out.println("type ID: " +type1);

        if(type1==null || type1.isEmpty()){
            throw new IllegalArgumentException("Ticket type is not available.");
        }


        // Validate escalation conditions
        if (ticket.getStatus().equalsIgnoreCase("close")) {
            throw new IllegalArgumentException("Cannot escalate a closed ticket.");
        }

        //ticket table update
        ticket.setLatest_remark(request.getEscalationRemark());
        ticket.setInternal_ticket_status("open");
        ticketRepository.save(ticket);

        //creating log
        AssignedTickets logEntry = new AssignedTickets();
        logEntry.setTicket(ticket);
        logEntry.setEmployee(null);
        logEntry.setRemark(request.getEscalationRemark());

        logEntry.setType(type1);
        logEntry.setReferenceId(null);
        logEntry.setCreatedBy(loggedInEmpId);
        logEntry.setCreatedAt(LocalDateTime.now());
        assignedTicketRepository.save(logEntry);

        if (ticket.getLastEscalationTime() == null) {
            //ticket update
            ticket.setLastEscalationTime(LocalDateTime.now());
            ticketRepository.save(ticket);

            // First escalation, send notification and email
            sendEscalationNotificationAndEmail(ticket, request, loggedInEmpId,resetUrl);
        } else {
            // Get the ticket category ID
            Long ticketCategoryId = Long.valueOf(ticket.getCategory());
            // Fetch the ticket category from the database
            Optional<TicketCategoryMaster> ticketCategoryMaster = ticketCategoryMasterRepository.findById(ticketCategoryId);

            // Check if the ticket category exists
            if (ticketCategoryMaster.isPresent()) {

                BigDecimal etaValueInBigDecimal = ticketCategoryMaster.get().getEta();
                double etaValueInHours = etaValueInBigDecimal.doubleValue();
                // Convert ETA from hours to minutes
                long etaValueInMinutes = (long) (etaValueInHours * 60);
                // Calculate the allowed escalation time by adding the ETA to the last escalation time
                LocalDateTime allowedEscalationTime = ticket.getLastEscalationTime().plusMinutes(etaValueInMinutes);

                // Check if the current time is after the allowed escalation time
                if (LocalDateTime.now().isAfter(allowedEscalationTime)) {

                    // Check the number of escalations for the ticket
                    ticketEscalationRepository.countByTicketId(ticket.getTicketId());

                     //checking the latest entry and finding supervisorId
                    TicketEscalation latestEscalation = getLatestEscalationForTicket(ticket.getTicketId());
                    Long latestEmpId = latestEscalation.getEmpId();
                   // System.out.println("Latest escalation details: " + latestEscalation);
                    logger.info("Latest escalation details: {}", latestEscalation);
                    Long upperOfUpperID = employeeRepository.findUpperIdByEmpId(latestEmpId);

                    if(upperOfUpperID != null && upperOfUpperID != 0){
                        sendEscalationNotificationAndEmailSecondTime(upperOfUpperID,ticket, request, loggedInEmpId,resetUrl);
                    }else{
                         sendFinalNotificationAndEmail(ticket,request, latestEmpId,resetUrl) ;
                    }
                    ticket.setLastEscalationTime(LocalDateTime.now());
                    ticketRepository.save(ticket);
//

                } else {
                    // If escalation is not allowed yet, throw an exception with a message
                    throw new IllegalArgumentException("Escalation is not allowed yet. Please wait until " + allowedEscalationTime);
                }
            } else {
                // If the ticket category doesn't exist, throw an exception
                throw new EntityNotFoundException("Ticket category not found for ID: " + ticketCategoryId);
            }
        }

      }catch (EntityNotFoundException ex) {
          logger.error("Ticket not found", ex);
          throw ex; // Re-throw to be caught in controller
      } catch (IllegalArgumentException ex) {
          logger.error("Invalid argument error", ex);
          throw ex; // Re-throw to be caught in controller
      } catch (Exception ex) {
          logger.error("Unexpected error while escalating ticket", ex);
          throw new RuntimeException("An unexpected error occurred during ticket escalation.", ex);
      }
    }

    public TicketEscalation getLatestEscalationForTicket(Long ticketId) {
       /* Optional<TicketEscalation> latestEscalation = ticketEscalationRepository.findLatestEscalationByTicketId(ticketId);
        return latestEscalation.orElseThrow(() ->
                new EntityNotFoundException("No escalation entry found for ticket ID: " + ticketId));*/

        Pageable pageable = PageRequest.of(0, 1); // Fetch only the first result
        Page<TicketEscalation> latestEscalationPage = ticketEscalationRepository.findLatestEscalationByTicketId(ticketId, pageable);

        if (!latestEscalationPage.isEmpty()) {
            TicketEscalation latestEscalation = latestEscalationPage.getContent().get(0);
            logger.info("Latest escalation for ticket ID {}: {}, empId: {}, createdAt: {}",
                    ticketId, latestEscalation, latestEscalation.getEmpId(), latestEscalation.getCreatedAt());
            return latestEscalation;
        } else {
            logger.warn("No escalation found for ticket ID: {}", ticketId);
            throw new EntityNotFoundException("No escalation entry found for ticket ID: " + ticketId);
        }
    }

    //update notification logic
    private void sendEscalationNotificationAndEmail(Ticket ticket, TicketEscalationRequestDTO request, Long loggedInEmpId, String resetUrl) {

        Long upperId = employeeRepository.findUpperIdByEmpId(loggedInEmpId);
        // First time escalation: Notify direct supervisor
        if (upperId != null) {
            sendNotificationToSupervisor(upperId, ticket, request, loggedInEmpId,resetUrl);
        }

    }

  private  void sendEscalationNotificationAndEmailSecondTime(Long upperOfUpperID,Ticket ticket, TicketEscalationRequestDTO request, Long loggedInEmpId, String resetUrl){
      logger.info("Sending escalation notification to supervisorOfSupervisor ID: {}", upperOfUpperID);
      // Create and save ticket escalation record
      TicketEscalation ticketEscalation = new TicketEscalation();
      ticketEscalation.setTicketId(ticket.getTicketId());
      ticketEscalation.setEmpId(upperOfUpperID);
      ticketEscalation.setCreatedBy(loggedInEmpId);
      ticketEscalation.setCreatedAt(LocalDateTime.now());
      ticketEscalationRepository.save(ticketEscalation);


      String emailSubject = "Ticket Escalation: " + ticket.getTicketSubject();
       /* String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated.\n\nDetails:\n" +
                "Reason: " + request.getEscalationRemark();*/
      String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated.\n\n" +
              "Details:\n" +
              "Reason: " + request.getEscalationRemark() + "\n\n" +
              "You can view the escalation details by clicking the link below:\n" +
              resetUrl + ticket.getTicketId();

      String emailId = employeeRepository.getReferenceById(upperOfUpperID).getEmail();
      EmailData emailData = new EmailData();
      emailData.setEmailId(emailId);
      emailData.setSubject(emailSubject);
      emailData.setMessage(emailMessage);
      emailService.sendEmail(emailData);

      // Sending notification to supervisor
      String notificationTitle = "Ticket Escalated";
      String notificationBody = "Ticket ID " + ticket.getTicketId() + " has been escalated.\n" +
              "Reason: " + request.getEscalationRemark();
      String ticketDetailsUrl = "ticket/details/" + ticket.getTicketId();

      notificationService.sendNotificationToEmployee(
              upperOfUpperID,
              notificationTitle,
              notificationBody,
              ticketDetailsUrl,
              String.valueOf(loggedInEmpId)
      );

  }


    private void sendNotificationToSupervisor(Long supervisorId, Ticket ticket, TicketEscalationRequestDTO request, Long loggedInEmpId,String resetUrl) {
        logger.info("Sending escalation notification to supervisor ID: {}", supervisorId);

        // Create and save ticket escalation record
        TicketEscalation ticketEscalation = new TicketEscalation();
        ticketEscalation.setTicketId(ticket.getTicketId());
        ticketEscalation.setEmpId(supervisorId);
        ticketEscalation.setCreatedBy(loggedInEmpId);
        ticketEscalation.setCreatedAt(LocalDateTime.now());
        ticketEscalationRepository.save(ticketEscalation);

        String emailSubject = "Ticket Escalation: " + ticket.getTicketSubject();
       /* String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated.\n\nDetails:\n" +
                "Reason: " + request.getEscalationRemark();*/
        String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated.\n\n" +
                "Details:\n" +
                "Reason: " + request.getEscalationRemark() + "\n\n" +
                "You can view the escalation details by clicking the link below:\n" +
                resetUrl + ticket.getTicketId();

        String emailId = employeeRepository.getReferenceById(supervisorId).getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject(emailSubject);
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        // Sending notification to supervisor
        String notificationTitle = "Ticket Escalated";
        String notificationBody = "Ticket ID " + ticket.getTicketId() + " has been escalated.\n" +
                "Reason: " + request.getEscalationRemark();
        String ticketDetailsUrl = "ticket/details/" + ticket.getTicketId();

        notificationService.sendNotificationToEmployee(
                supervisorId,
                notificationTitle,
                notificationBody,
                ticketDetailsUrl,
                String.valueOf(loggedInEmpId)
        );
    }

    private void sendFinalNotificationAndEmail(Ticket ticket, TicketEscalationRequestDTO request, Long lastSupervisorId,String resetUrl ) {
        // Final notification and email if no upper supervisor found
        logger.info("No further escalation possible. Sending final notification to supervisor ID: {}", lastSupervisorId);

        String emailSubject = "Final Escalation Notice: " + ticket.getTicketSubject();
        String emailMessage = "The ticket with ID " + ticket.getTicketId() + " has been escalated to its final level.\n\nDetails:\n" +
                "Reason: " + request.getEscalationRemark()+ "\n\n" +
                "You can view the escalation details by clicking the link below:\n" +
                resetUrl + ticket.getTicketId();

        String emailId = employeeRepository.getReferenceById(lastSupervisorId).getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject(emailSubject);
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        // Final notification
        String notificationTitle = "Ticket Escalated - Final Level";
        String notificationBody = "Ticket ID " + ticket.getTicketId() + " has been escalated to the final level.\n" +
                "Reason: " + request.getEscalationRemark();
        String ticketDetailsUrl = "ticket/details/" + ticket.getTicketId();

        notificationService.sendNotificationToEmployee(
                lastSupervisorId,
                notificationTitle,
                notificationBody,
                ticketDetailsUrl,
                String.valueOf(ticket.getCreatedBy())
        );
    }


    //ticket listing esclation
    @Override
    public Page<TicketEscalationResponseDTO> getTicketsWithDetailsByEmployeeId(int page, int size) {
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        // Create Pageable object with sorting by ticketId in descending order
        Pageable pageable = PageRequest.of(page, size);

        // Fetch the tickets using the repository method with pagination
        Page<TicketEscalation> ticketEscalations = ticketEscalationRepository.findByEmpIdOrderedByTicketIdDesc(loggedInEmpId, pageable);

        // Convert TicketEscalation to TicketEscalationResponseDTO
        List<TicketEscalationResponseDTO> ticketEscalationResponseDTOs = ticketEscalations.stream().map(ticketEscalation -> {
            Long ticketId = ticketEscalation.getTicketId();

            Optional<Ticket> ticketOptional = ticketRepository.findByTicketId(ticketId);

            if (ticketOptional.isPresent()) {
                Ticket ticket = ticketOptional.get();
                TicketEscalationResponseDTO ticketEscalationResponseDTO = new TicketEscalationResponseDTO();

                // Map the Ticket entity fields to TicketEscalationResponseDTO
                ticketEscalationResponseDTO.setTicketId(ticket.getTicketId());
                ticketEscalationResponseDTO.setTicketCode(ticket.getTicketCode());
                ticketEscalationResponseDTO.setTicketSubject(ticket.getTicketSubject());
                ticketEscalationResponseDTO.setTicketMessage(ticket.getTicketMessage());
                ticketEscalationResponseDTO.setStatus(ticket.getStatus());
                ticketEscalationResponseDTO.setCategory(ticket.getCategory());
                ticketEscalationResponseDTO.setFilePath(ticket.getFilePath());
                ticketEscalationResponseDTO.setCreatedBy(ticket.getCreatedBy());
                ticketEscalationResponseDTO.setCreatedTime(ticket.getCreatedTime());
                ticketEscalationResponseDTO.setUpdatedBy(ticket.getUpdatedBy());
                ticketEscalationResponseDTO.setUpdatedTime(ticket.getUpdatedTime());
                ticketEscalationResponseDTO.setDepartmentId(ticket.getDepartmentId());
                ticketEscalationResponseDTO.setAssignedBy(ticket.getAssigned_by());
                ticketEscalationResponseDTO.setAssignedTo(ticket.getAssigned_to());
                ticketEscalationResponseDTO.setInternalTicketStatus(ticket.getInternal_ticket_status());
                ticketEscalationResponseDTO.setLatestRemark(ticket.getLatest_remark());
                ticketEscalationResponseDTO.setCurrentType(ticket.getCurrent_type());
                ticketEscalationResponseDTO.setLastEscalationTime(ticket.getLastEscalationTime());

                // Fetch the category name from the master table
                Optional<String> categoryNameOptional = ticketCategoryMasterRepository.findCategoryNameById(Long.valueOf(ticket.getCategory()));
                ticketEscalationResponseDTO.setCategoryName(categoryNameOptional.orElse("Unknown Category"));


                return ticketEscalationResponseDTO;
            } else {
                throw new IllegalArgumentException("Ticket with ID " + ticketId + " not found.");
            }
        }).collect(Collectors.toList());

        // Return the result as a Page object
        return new PageImpl<>(ticketEscalationResponseDTOs, pageable, ticketEscalations.getTotalElements());
    }
}







