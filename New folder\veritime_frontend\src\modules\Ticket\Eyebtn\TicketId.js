import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import '../Eyebtn/TicketId.css'
import moment from 'moment';
import { convertToSouthAfricaTime } from '@/services/utils';
// import TicketId from './TicketId';

const TicketInfo = ({ ticketNumber, status, priority, date }) => {
  return (
    <div className="container-id mt-2 " >
      <div className="d-flex flex-wrap p-1" >
        <div style={{ marginRight: '60px' }}>
          <span className="text-muted">Ticket: </span>
          <a href="#" className="text-primary font-weight-bold">{ticketNumber}</a>
        </div>
        <div style={{ marginRight: '90px' }}>
          <span className="text-muted">Status: </span>
          <span className="font-weight-bold" style={{ color: status === 'OPEN' ? 'brown' : 'green' }}>{status}</span>
        </div>
        {/* <div style={{ marginRight: '90px' }}>
          <span className="text-muted">Priority: </span>
          <span className="font-weight-bold">{priority}</span>
        </div> */}
        <div className="ml-auto">
          <span className="text-muted">Date: </span>
          <span className="font-weight-bold">{convertToSouthAfricaTime(date)}</span>
        </div>
      </div>
    </div>
  );
};

const TicketId = ({ticketNumber}) => {
  
    const ticketInfo = {
      ticketNumber: ticketNumber?.ticketCode ,
      status: ticketNumber?.status,
      priority: ticketNumber?.priority,
      date: ticketNumber?.createdTime,
    };


  return (
    <div>
      <TicketInfo {...ticketInfo} />
    </div>
  );
};

export default TicketId;
