package com.company.wfm.service.impl;


import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.HospialBranchUpdateDTO;
import com.company.wfm.dto.HospitalDTO;
import com.company.wfm.dto.HospitalExcelDTO;
import com.company.wfm.dto.HospitalSaveDto;
import com.company.wfm.dto.HospitalUpdateDetailsDTO;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.DepartmentBranch;
import com.company.wfm.entity.District;
import com.company.wfm.entity.FeederHospital;
import com.company.wfm.entity.Hospital;
import com.company.wfm.entity.Province;
import com.company.wfm.entity.SubDistrict;
import com.company.wfm.entity.User;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentBranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.DistrictRepository;
import com.company.wfm.repository.FeederHospitalRepository;
import com.company.wfm.repository.HospitalRepository;
import com.company.wfm.repository.ProvinceRepository;
import com.company.wfm.repository.SubDistrictRepository;
import com.company.wfm.repository.UserRepository;

import jakarta.persistence.EntityNotFoundException;

@Service
public class HospitalService {

    private static final Logger logger = LoggerFactory.getLogger(HospitalService.class);

    @Autowired
    private HospitalRepository hospitalRepository;
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private FeederHospitalRepository feederHospitalRepository;

    @Autowired
    private ProvinceRepository provinceRepository;  // Added repository for Province

    @Autowired
    private DistrictRepository districtRepository;

    @Autowired
    private SubDistrictRepository subDistrictRepository;

    @Autowired
    private DepartmentBranchRepository departmentBranchRepository;

    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

   
// listing get employee

    public Page<HospitalDTO> filterHospitals(List<Long> provinceIds, List<Long> districtIds,
                                             List<Long> subDistrictIds, String query,
                                             PageRequest pageRequest) {

        if (provinceIds == null || provinceIds.isEmpty()) {
            provinceIds = null;  // Set to null if the list is empty
        }
        if (districtIds == null || districtIds.isEmpty()) {
            districtIds = null;
        }
        if (subDistrictIds == null || subDistrictIds.isEmpty()) {
            subDistrictIds = null;
        }
        if (query == null || query.trim().isEmpty()) {
            query = null;  // Treat empty query as null
        }
        Page<Hospital> hospitals = hospitalRepository.findHospitalByFilters(provinceIds, districtIds, subDistrictIds, query, pageRequest);
        return hospitals.map(this::convertToDTO);
    }
//old
   /* private HospitalDTO convertToDTO(Hospital hospital) {
        HospitalDTO dto = new HospitalDTO();
        dto.setId(hospital.getId());
        dto.setName(hospital.getHospitalName());
        dto.setAddress(hospital.getAddressLine1() + " " + (hospital.getAddressLine2() != null ? hospital.getAddressLine2() : ""));
        dto.setType(hospital.getHospitalType());
        dto.setFacility(hospital.getClusterName());  // Assuming 'clusterName' is the facility
        dto.setProvinceId(hospital.getProvince().getId());
        dto.setProvince(hospital.getProvince().getName()); // Assuming Province entity has 'name'
        dto.setDistrictId(hospital.getDistrict().getId());
        dto.setDistrict(hospital.getDistrict().getName()); // Assuming District entity has 'name'
        dto.setSubDistrictId(hospital.getSubDistrict().getId());
        dto.setSubDistrict(hospital.getSubDistrict().getName()); // Assuming SubDistrict entity has 'name'
        dto.setCreatedAt(hospital.getCreatedAt());
        dto.setUpdatedAt(hospital.getUpdatedAt());
        return dto;
    }*/

    private HospitalDTO convertToDTO(Hospital hospital) {
        HospitalDTO dto = new HospitalDTO();
        dto.setId(hospital.getId());
        dto.setName(hospital.getHospitalName());
        dto.setAddress(hospital.getAddressLine1() + " " + (hospital.getAddressLine2() != null ? hospital.getAddressLine2() : ""));
        dto.setType(hospital.getHospitalType());
        dto.setFacility(hospital.getClusterName());  // Assuming 'clusterName' is the facility
        if (hospital.getProvince() != null) {
            dto.setProvinceId(hospital.getProvince().getId());
            dto.setProvince(hospital.getProvince().getName());
        } else {
            dto.setProvinceId(null); // or some default value
        }

        if (hospital.getDistrict() != null) {
            dto.setDistrictId(hospital.getDistrict().getId());
            dto.setDistrict(hospital.getDistrict().getName());
        } else {
            dto.setDistrictId(null); // or some default value
        }

        if (hospital.getSubDistrict() != null) {
            dto.setSubDistrictId(hospital.getSubDistrict().getId());
            dto.setSubDistrict(hospital.getSubDistrict().getName());
        } else {
            dto.setSubDistrictId(null); // or some default value
        }
        dto.setCreatedAt(hospital.getCreatedAt());
        dto.setUpdatedAt(hospital.getUpdatedAt());
        return dto;
    }


    //delete
    @Transactional
    public boolean deleteHospital(Long id) {

        Hospital hospital = hospitalRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Hospital not found for ID: " + id));
        hospital.setIsActive(false);
        hospitalRepository.saveAndFlush(hospital);

        return true;
    }

   // new update work

   /* @Transactional
    public boolean updateHospital(Long id, HospitalUpdateDetailsDTO updatedHospitalDTO) {

        // Step 1: Authenticate the user and retrieve employee ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        Long employeeId = findEmployeeIdByUsername(username);
        System.out.println("Authenticated Employee ID: " + employeeId);

        // Step 2: Retrieve hospital data through hospital id
        Optional<Hospital> hospitalOptional = hospitalRepository.findById(id);

        if (hospitalOptional.isPresent()) {
            Hospital hospital = hospitalOptional.get();

            // Step 3: Delete existing branches before updating
            deleteExistingBranches(hospital.getId());

            // Update hospital data
            updateHospitalDetails(hospital, updatedHospital, employeeId);

            // Step 4: Update branches based on provided data
            handleBranchUpdates(hospital, updatedHospital.getBranches(), employeeId);

            // Step 5: Save the hospital after updating all branches and departments
            hospitalRepository.saveAndFlush(hospital);
            return true;
        }
        return false;
    }

    private void deleteExistingBranches(Long hospitalId) {
        List<Branch> branches = branchRepository.findByHospitalIdWise(hospitalId);
        for (Branch branch : branches) {
            // Delete associated departments for the branch before deleting the branch
            deleteExistingDepartments(branch.getId());
        }
        branchRepository.deleteByHospitalId(hospitalId);
    }

    private void updateHospitalDetails(Hospital hospital, Hospital updatedHospital, Long employeeId) {
        hospital.setHospitalName(updatedHospital.getHospitalName());
        hospital.setShortCode(updatedHospital.getShortCode());
        hospital.setProvince(updatedHospital.getProvince());
        hospital.setDistrict(updatedHospital.getDistrict());
        hospital.setSubDistrict(updatedHospital.getSubDistrict());
        hospital.setAddressLine1(updatedHospital.getAddressLine1());
        hospital.setAddressLine2(updatedHospital.getAddressLine2());
        hospital.setLat(updatedHospital.getLat());
        hospital.setLng(updatedHospital.getLng());
        hospital.setValidity(updatedHospital.getValidity());
        hospital.setUpdatedBy(employeeId);
        hospital.setUpdatedAt(LocalDateTime.now());
        hospital.setHospitalType(updatedHospital.getHospitalType());
        hospital.setClusterName(updatedHospital.getClusterName());
    }

    private void handleBranchUpdates(Hospital hospital, List<Branch> updatedBranches, Long employeeId) {
        for (Branch updatedBranch : updatedBranches) {
            Branch newBranch = new Branch(); //

            // Update branch details
            newBranch.setBranchName(updatedBranch.getBranchName());
            newBranch.setBranchCode(updatedBranch.getBranchCode());
           // newBranch.setCompany(hospital); // Associate with the hospital

            branchRepository.saveAndFlush(newBranch);

            // Save associated departments for the new branch using department IDs
           *//* if (updatedBranch.getDepartment() != null) {
                saveDepartmentForBranch(newBranch, updatedBranch.getDepartmentIds());
            }*//*
        }
    }


    private void saveDepartmentForBranch(Branch branch, List<Long> departmentIds) {
        for (Long departmentId : departmentIds) {
            DepartmentBranch newDepartment = new DepartmentBranch();
            newDepartment.setBranchId(branch.getId());
            newDepartment.setDepartmentId(departmentId); // Set the department ID directly

            departmentBranchRepository.saveAndFlush(newDepartment);
        }
    }


    private void deleteExistingDepartments(Long branchId) {
        departmentBranchRepository.deleteByBranchId(branchId);
    }*/


    @Transactional
    public boolean updateHospital(Long id, HospitalUpdateDetailsDTO updatedHospitalDTO) {
        // Step 1: Authenticate the user and retrieve employee ID
        Long employeeId = getAuthenticatedEmployeeId();
        System.out.println("Authenticated Employee ID: " + employeeId);

        // Step 2: Retrieve hospital data through hospital id
        Optional<Hospital> hospitalOptional = hospitalRepository.findById(id);
        if (hospitalOptional.isPresent()) {
            Hospital hospital = hospitalOptional.get();

            // Step 3: Update hospital data
            updateHospitalDetails(hospital, updatedHospitalDTO, employeeId);

            // Step 4: Manage branches and associated departments
            updateBranchesAndDepartments(hospital, updatedHospitalDTO.getBranches(), employeeId);

            // Step 5: Save the hospital after all updates
            hospitalRepository.saveAndFlush(hospital);
            return true;
        }
        return false;

    }

    private Long getAuthenticatedEmployeeId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        return findEmployeeIdByUsername(username);
    }

    private void updateHospitalDetails(Hospital hospital, HospitalUpdateDetailsDTO updatedHospitalDTO, Long employeeId) {
        hospital.setHospitalName(updatedHospitalDTO.getHospitalName());
        hospital.setShortCode(updatedHospitalDTO.getShortCode());
        hospital.setProvince(provinceRepository.findById(updatedHospitalDTO.getProvinceId()).orElse(null));
        hospital.setDistrict(districtRepository.findById(updatedHospitalDTO.getDistrictId()).orElse(null));
        hospital.setSubDistrict(subDistrictRepository.findById(updatedHospitalDTO.getSubDistrictId()).orElse(null));
        hospital.setAddressLine1(updatedHospitalDTO.getAddressLine1());
        hospital.setAddressLine2(updatedHospitalDTO.getAddressLine2());
        hospital.setLat(updatedHospitalDTO.getLat());
        hospital.setLng(updatedHospitalDTO.getLng());
        hospital.setValidity(updatedHospitalDTO.getValidity());
        hospital.setUpdatedBy(employeeId);
        hospital.setUpdatedAt(LocalDateTime.now());
        hospital.setHospitalType(updatedHospitalDTO.getHospitalType());
        hospital.setClusterName(updatedHospitalDTO.getClusterName());
    }

    private void updateBranchesAndDepartments(Hospital hospital, List<HospialBranchUpdateDTO> updatedBranchDTOs, Long employeeId) {

        for (HospialBranchUpdateDTO updatedBranchDTO : updatedBranchDTOs) {
            deleteExistingBranches(updatedBranchDTO.getBranchName());
        }

        // Add new branches
        for (HospialBranchUpdateDTO updatedBranchDTO : updatedBranchDTOs) {
            Branch newBranch = new Branch();
            newBranch.setBranchName(updatedBranchDTO.getBranchName());
            newBranch.setBranchCode(updatedBranchDTO.getBranchCode());
           // newBranch.setHospital(hospital); // Associate with the hospital
            // Save the new branch
            Branch savedBranch =branchRepository.saveAndFlush(newBranch);
            Long branchId = savedBranch.getId();

            // Step 3: Update department associations for the branch
            if (updatedBranchDTO.getDepartmentIds() != null && !updatedBranchDTO.getDepartmentIds().isEmpty()) {

                List<Long> departmentIds = updatedBranchDTO.getDepartmentIds();
                // Save the department associations for the branch
                saveDepartmentForBranch(branchId, departmentIds);
            }
        }
    }

    private void saveDepartmentForBranch(Long branchId, List<Long> departmentIds) {
        for (Long departmentId : departmentIds) {
            DepartmentBranch departmentBranch = departmentBranchRepository.findByBranchIdAndDepartmentId(branchId, departmentId);

            if (departmentBranch != null) {
                departmentBranchRepository.save(departmentBranch);
            } else {
                DepartmentBranch newDepartmentBranch = new DepartmentBranch();
                newDepartmentBranch.setBranchId(branchId);  // Set the branchId
                newDepartmentBranch.setDepartmentId(departmentId);  // Set the departmentId
                departmentBranchRepository.save(newDepartmentBranch);
            }
        }
    }
  
    private void deleteExistingBranches(String branchName) {
        List<Long> branchIds = branchRepository.findBranchIdsByBranchName(branchName);
        if (branchIds != null && !branchIds.isEmpty()) {
            for (Long branchId : branchIds) {
                deleteExistingDepartments(branchId);
                branchRepository.deleteById(branchId);
            }
        } else {
            throw new EntityNotFoundException("Branch not found with name: " + branchName);
        }
    }


    private void deleteExistingDepartments(Long branchId) {
        departmentBranchRepository.deleteByBranchId(branchId);
    }


    private Long findEmployeeIdByUsername(String username) {
        User user = userRepository.findByUsername(username);
        if (user != null && user.getEmployee() != null) {
            return user.getEmployee().getEmpId();
        }
        throw new IllegalArgumentException("User or Employee not found for username: " + username);
    }



    //save from form

   public void saveHospitalFromForm(HospitalSaveDto hospitalSaveDto) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            Long employeeId = findEmployeeIdByUsername(username);

            try {
                // Step 1: Validate and fetch the related entities (Province, District, SubDistrict)
                Province province = provinceRepository.findByName(hospitalSaveDto.getProvinceName())
                        .orElseThrow(() -> new RuntimeException("Province not found"));

                District district = districtRepository.findByName(hospitalSaveDto.getDistrictName())
                        .orElseThrow(() -> new RuntimeException("District not found"));

                SubDistrict subDistrict = subDistrictRepository.findByName(hospitalSaveDto.getSubDistrictName())
                        .orElseThrow(() -> new RuntimeException("Sub-District not found"));

                // Step 2: Create new Hospital entity and set details
                Hospital hospital = new Hospital();
                hospital.setHospitalName(hospitalSaveDto.getHospitalName());
                hospital.setShortCode(hospitalSaveDto.getShortCode());
                hospital.setProvince(province);
                hospital.setDistrict(district);
                hospital.setSubDistrict(subDistrict);
                hospital.setAddressLine1(hospitalSaveDto.getAddressLine1());
                hospital.setAddressLine2(hospitalSaveDto.getAddressLine2());
                hospital.setLat(hospitalSaveDto.getLat());
                hospital.setLng(hospitalSaveDto.getLng());
                hospital.setValidity(hospitalSaveDto.getValidity());
                hospital.setCreatedBy(employeeId);
                hospital.setHospitalType(hospitalSaveDto.getHospitalType());
                hospital.setClusterName(hospitalSaveDto.getClusterName());
                hospital.setCreatedAt(LocalDateTime.now());
                hospital.setIsActive(true);

                // Step 3: Save the hospital to the database
                Hospital savedHospital = hospitalRepository.saveAndFlush(hospital);

                // Step 4: Create a new Branch and associate it with the Hospital
                Branch branch = new Branch();
                branch.setBranchName(savedHospital.getHospitalName());
                branch.setBranchCode(generateBranchCode(savedHospital.getHospitalName(), savedHospital.getId()));
                branch.setBranchHead(null);  // Set the branch head if applicable
                branch.setBranchHeadName(null);
                branch.setCreatedAt(LocalDateTime.now());

                // Step 5: Save the branch and get the branch ID
                Branch savedBranch = branchRepository.saveAndFlush(branch);

                if (hospitalSaveDto.getDepartmentIds() != null && !hospitalSaveDto.getDepartmentIds().isEmpty()) {
                    for (Long departmentId : hospitalSaveDto.getDepartmentIds()) {
                        try {
                            // Check if the department exists
                            Department existingDepartment = departmentRepository.findById(departmentId)
                                    .orElseThrow(() -> new RuntimeException("Department not found for ID: " + departmentId));

                            // Create and save the DepartmentBranch entry
                            DepartmentBranch departmentBranch = new DepartmentBranch();
                            departmentBranch.setBranchId(savedBranch.getId());
                            departmentBranch.setDepartmentId(existingDepartment.getDepartmentId());

                            // Save the association in the DepartmentBranch table
                            departmentBranchRepository.saveAndFlush(departmentBranch);
                        } catch (Exception e) {
                            logger.error("Error processing department ID: {}, error: {}", departmentId, e.getMessage(), e);
                        }
                    }
                }

            } catch (Exception e) {

                logger.error("Error processing hospital or branch creation: {}", e.getMessage(), e);
            }

        } catch (Exception e) {
            // Handle unexpected errors
            logger.error("An error occurred while saving the hospital: {}", e.getMessage(), e);
        }
    }




    public String generateBranchCode(String hospitalName, Long hospitalId) {
        return hospitalName.substring(0, 3).toUpperCase() + "-" + hospitalId;
    }

    public String generateDepartmentCode(String departmentName) {
        return departmentName.substring(0, 3).toUpperCase() + "-" + System.currentTimeMillis();
    }

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm");

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void uploadAndSaveHospitals(MultipartFile file) {
        List<String> errorMessages = new ArrayList<>();
        try (InputStream is = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0);

            List<HospitalExcelDTO> hospitalExcelDTOList = new ArrayList<>();

            // Read the Excel data
            for (int i = 1; i <= sheet.getLastRowNum(); i++) { // Skip header (row 0)
                Row row = sheet.getRow(i);
                try {
                    HospitalExcelDTO dto = new HospitalExcelDTO();

                    // Counter variable to represent column index
                    int counter = 0;

                    dto.setHospitalType(getCellValue(row, counter++));
                    dto.setHospitalName(getCellValue(row, counter++));
                    dto.setFacilityName(getCellValue(row, counter++));
                    dto.setProvinceName(getCellValue(row, counter++));
                    dto.setDistrictName(getCellValue(row, counter++));
                    dto.setSubDistrictName(getCellValue(row, counter++));
                    dto.setClusterName(getCellValue(row, counter++));
                    dto.setAddressLine1(getCellValue(row, counter++));
                    dto.setAddressLine2(getCellValue(row, counter++)); // Column 8: address_line2

                    // Handle optional fields for latitude and longitude
                    String latString = getCellValue(row, counter++); // Column 9: lat
                    if (!latString.isEmpty()) {
                        dto.setLat(Double.parseDouble(latString));
                    }

                    String lngString = getCellValue(row, counter++); // Column 10: lng
                    if (!lngString.isEmpty()) {
                        dto.setLng(Double.parseDouble(lngString));
                    }

                    String validityString = getCellValue(row, counter++); // Column 11: validity
                    if (validityString != null && !validityString.isEmpty()) {
                        dto.setValidity(LocalDateTime.parse(validityString, DATE_FORMATTER));
                    } else {
                        dto.setValidity(null);
                    }

                    String departmentNamesString = getCellValue(row, counter++); // Column 12: departmentName
                    if (departmentNamesString != null && !departmentNamesString.isEmpty()) {
                     //   dto.setDepartmentNames(Arrays.asList(departmentNamesString.split(","))); // Assuming department names are comma-separated
                        List<String> departmentNamesList = Arrays.stream(departmentNamesString.split(","))
                                .map(String::trim)  // Trim spaces
                                .collect(Collectors.toList());
                        dto.setDepartmentNames(departmentNamesList);

                    } else {
                        dto.setDepartmentNames(new ArrayList<>()); // Set empty list if no names provided
                    }

                    hospitalExcelDTOList.add(dto);
                } catch (Exception e) {
                    logger.error("Error processing row {}: {}", i + 1, e.getMessage());
                    errorMessages.add("Error processing row " + (i + 1) + ": " + e.getMessage());
                }
            }

            // Process each hospital entry
            for (HospitalExcelDTO hospitalExcelDTO : hospitalExcelDTOList) {
                try {
                    saveHospitalFromExcelDto(hospitalExcelDTO);
                } catch (Exception e) {
                    logger.error("Error saving hospital data: {}", e.getMessage());
                    errorMessages.add("Error saving hospital data: " + e.getMessage());
                }
            }

        } catch (IOException e) {
            logger.error("Error reading the Excel file: {}", e.getMessage());
            errorMessages.add("Error reading the Excel file: " + e.getMessage());
            throw new RuntimeException(e);
        } catch (Exception e) {
            logger.error("An unexpected error occurred: {}", e.getMessage());
            errorMessages.add("An unexpected error occurred: " + e.getMessage());
            throw new RuntimeException(e);
        } finally {
            // Log all error messages at once
            if (!errorMessages.isEmpty()) {
                logger.error("Errors occurred during processing: {}", String.join(", ", errorMessages));
                // Optionally throw a custom exception or handle the errors as required
            }
        }
    }
    
    /**
     * 
     * @param hospitalSaveDto
     */
    @Transactional
    private void saveHospitalFromExcelDto(HospitalExcelDTO hospitalSaveDto) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            Long employeeId = findEmployeeIdByUsername(username);

            // Step 1: Validate and fetch the related entities (Province, District, SubDistrict)
            Province province = provinceRepository.findByName(hospitalSaveDto.getProvinceName())
                    .orElseThrow(() -> new EntityNotFoundException("Province not found: " + hospitalSaveDto.getProvinceName()));

            District district = districtRepository.findByName(hospitalSaveDto.getDistrictName())
                    .orElseThrow(() -> new EntityNotFoundException("District not found: " + hospitalSaveDto.getDistrictName()));

            SubDistrict subDistrict = subDistrictRepository.findByName(hospitalSaveDto.getSubDistrictName())
                    .orElseThrow(() -> new EntityNotFoundException("Sub-District not found: " + hospitalSaveDto.getSubDistrictName()));

            // Step 2: Create new Hospital entity and set details

            List<Hospital> existingHospitals = hospitalRepository.findByHospitalName1(hospitalSaveDto.getHospitalName());
            Hospital hospital;
            if (!existingHospitals.isEmpty()) {
                // Use the first found hospital if multiple exist with the same name
                hospital = existingHospitals.get(0);
            } else {
                //Hospital hospital = new Hospital();
                hospital = new Hospital();
                hospital.setHospitalType(hospitalSaveDto.getHospitalType());
                hospital.setHospitalName(hospitalSaveDto.getHospitalName());
                //  hospital.setShortCode(hospitalSaveDto.getShortCode());
                String hospitalName = hospitalSaveDto.getHospitalName();
                if (hospitalName != null && !hospitalName.isEmpty()) {
                    // Split the hospital name into words
                    String[] words = hospitalName.split("\\s+");
                    StringBuilder shortCodeBuilder = new StringBuilder();

                    for (String word : words) {
                        if (!word.isEmpty()) {
                            shortCodeBuilder.append(word.substring(0, 1).toUpperCase()); // Append the first letter of each word in uppercase
                        }
                    }

                    hospital.setShortCode(shortCodeBuilder.toString()); // Set the generated short code
                } else {
                    hospital.setShortCode("");
                }

                hospital.setProvince(province);
                hospital.setDistrict(district);
                hospital.setSubDistrict(subDistrict);
                hospital.setClusterName(hospitalSaveDto.getClusterName());
                hospital.setAddressLine1(hospitalSaveDto.getAddressLine1());
                hospital.setAddressLine2(hospitalSaveDto.getAddressLine2());
                hospital.setLat(hospitalSaveDto.getLat());
                hospital.setLng(hospitalSaveDto.getLng());
                hospital.setValidity(hospitalSaveDto.getValidity());
                hospital.setCreatedBy(employeeId);
                hospital.setCreatedAt(LocalDateTime.now());
                hospital.setIsActive(true);

                // Step 3: Save the hospital to the database
                  //Hospital savedHospital = hospitalRepository.saveAndFlush(hospital);
                hospital = hospitalRepository.saveAndFlush(hospital);
                 //  logger.info("Saved Hospital: {}", savedHospital);
                logger.info("Saved Hospital: {}", hospital);
            }


            Hospital savedHospital = hospital;
            String branchNameFromExcel = hospitalSaveDto.getFacilityName();
            String branchHeadNameFromExcel = hospitalSaveDto.getFacilityHeadName();

            List<Branch> branches = branchRepository.findAllByBranchName(branchNameFromExcel);

            Branch branch;
            if (branches.isEmpty()) {
                // If no branch exists, create a new one
                branch = new Branch();
                branch.setBranchName(branchNameFromExcel);
                branch.setBranchCode(generateBranchCode(branchNameFromExcel, savedHospital.getId()));
               FeederHospital feederHospital = feederHospitalRepository.findById(savedHospital.getId())
                      .orElseThrow(() -> new EntityNotFoundException("FeederHospital not found with ID: " + savedHospital.getId()));
               branch.setCompany(feederHospital);

                if (branchHeadNameFromExcel != null && !branchHeadNameFromExcel.trim().isEmpty()) {
                    branch.setBranchHeadName(branchHeadNameFromExcel);
                }
                branch.setCluster(hospitalSaveDto.getClusterName());
                branch.setBranchType(hospitalSaveDto.getHospitalType());
                branch.setCreatedBy(employeeId);
                branch.setActive(true);

                branch.setCreatedAt(LocalDateTime.now());
                branch = branchRepository.saveAndFlush(branch);
            } else {
                // If multiple branches exist, handle it appropriately (e.g., choose the first, log a warning, etc.)
                branch = branches.get(0); // Handle according to your business logic
                logger.warn("Multiple branches found with the same name, picking the first one: " + branch.getId());
            }

            Long branchId = branch.getId();


            // Step 6: Loop through department names and check if they exist, otherwise create departments
            if (hospitalSaveDto.getDepartmentNames() != null && !hospitalSaveDto.getDepartmentNames().isEmpty()) {
                for (String departmentName : hospitalSaveDto.getDepartmentNames()) {
                    try {
                        // Check if the department already exists
                        Department existingDepartment = departmentRepository.findByDepartmentName(departmentName);

                        Department department;
                        if (existingDepartment != null) {
                            department = existingDepartment;
                        } else {
                            // Create new Department entity if not found
                            department = new Department();
                            department.setDepartmentName(departmentName);
                            department.setDepartmentCode(generateDepartmentCode(departmentName)); // Generate a code
                            department.setIsActiveDepartment(true);
                            department.setCreatedAt(LocalDateTime.now());

                            // Save the new department and get its ID
                            department = departmentRepository.saveAndFlush(department);
                        }
                        // Step 7: Create and save the DepartmentBranch entry
                        DepartmentBranch existingDepartmentBranch = departmentBranchRepository.findByBranchIdAndDepartmentId(branchId, department.getDepartmentId());

                        if (existingDepartmentBranch == null) {
                            DepartmentBranch departmentBranch = new DepartmentBranch();
                            departmentBranch.setBranchId(branchId);
                            departmentBranch.setDepartmentId(department.getDepartmentId());

                            // Save the association in the DepartmentBranch table only if it doesn't exist
                            departmentBranchRepository.saveAndFlush(departmentBranch);
                        } else {
                            logger.info("DepartmentBranch entry for branchId: {} and departmentId: {} already exists. Skipping insert.", branchId, department.getDepartmentId());
                        }


                    } catch (Exception e) {
                        logger.error("Error processing department: {}, error: {}", departmentName, e.getMessage());
                        throw new RuntimeException("Error processing department: " + departmentName + ": " + e.getMessage());
                    }
                }
            }

        } catch (EntityNotFoundException e) {
            logger.error("Error processing hospital creation: {}", e.getMessage());
            throw new RuntimeException("Error processing hospital creation: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Error processing hospital creation: {}", e.getMessage());
            throw new RuntimeException("An error occurred while saving the hospital: " + e.getMessage());
        }
    }


    private String getCellValue(Row row, int cellIndex) {
        Cell cell = row.getCell(cellIndex);
        if (cell != null) {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getLocalDateTimeCellValue().toString();
                    } else {
                        return String.valueOf(cell.getNumericCellValue());
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return "";
            }
        }
        return "";
    }
}
