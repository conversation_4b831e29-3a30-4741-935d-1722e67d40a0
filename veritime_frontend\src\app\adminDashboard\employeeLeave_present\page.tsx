"use client";
import { useEffect, useState } from "react";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, But<PERSON> } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter2.js";
import { styled } from "@mui/system";
import Switch from "@mui/material/Switch";
import EmailIcon from "@mui/icons-material/Email";
import CallIcon from "@mui/icons-material/Call";
import CreateDesignationModal from "../modals/CreateDesignationModal";
import CreateScheduleModal from "../modals/CreateScheduleModal";
import Layout from "@/components/Layout";
import { API_URLS } from "@/constants/apiConstants.js";
import { getRequest, postRequest, postRequestWithSecurity , } from "@/services/apiService.js";
import { appConstants } from "@/constants/appConstants.js";
import BranchDropdown from "@/components/Dropdowns/BranchDropdown";
import DepartmentDropdown from "@/components/Dropdowns/DeptDropdown";
import DesignationDropdown from "@/components/Dropdowns/DesignationDropdown";
import CreateExcelModal from "../modals/CreateEmployeeModal";
import EditIcon from "@mui/icons-material/Edit";
import Link from "next/link.js";
import DocumentPopup from "../modals/DocumentPopUP";
import "../../../css/style.css";
import VisibilityIcon from "@mui/icons-material/Visibility";
import axios from "axios";
import BulkUploadModal from "../modals/Pngupload";

const Employee = ({ toggleMenu, expanded }: any) => {
  const [username, setUsername] = useState("");
  const [role, setRole] = useState("");
  const [limit, setLimit] = useState(10);
  const [offset, setOffset] = useState(0);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedUsername = localStorage.getItem(appConstants?.username);
      if (storedUsername) {
        setUsername(storedUsername);
      }
    }
  }, []);
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedRole = localStorage.getItem(appConstants?.role);

      if (storedRole) {
        setRole(storedRole);
      }
    }
  }, []);

  const [branches, setBranches] = useState([]);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);
  const [departments, setDepartments] = useState([]);
  const [designations, setDesignations] = useState([]);
  const [selectedBranch, setSelectedBranch] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState([]);
  const [selectedDesignation, setSelectedDesignation] = useState([]);
  const [showExcelUploadModal, setShowExcelUploadModal] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [employeeId, setEmployeeId] = useState(123); // Example Employee ID
  const [documentData, setDocumentData] = useState([]); // Replace with actual data
  const [refreshFlag, setRefreshFlag] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [uploadErrors, setUploadErrors] = useState({});
  const [file, setFile] = useState(null);
  const [docType, setDocType] = useState({ documentId: 1 });

  // const [data, setData] = useState<any[]>([]);
  const toggleModal = () => {
    setIsModalOpen(!isModalOpen);
  };

  const handleRefresh = () => {
    setRefreshFlag(!refreshFlag);
    // Add additional logic if needed
  };
  const handleBulkUploadClose = () => setShowBulkUploadModal(false);
  const handleBulkUploadOpen = () => setShowBulkUploadModal(true);
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const payload = {
          // deptId:'',
          // branchId:'',
          // isActive:1,
          // offset:0,
          // limit:10
          type: "select",
        };

        const branchesData = await postRequest(API_URLS.GET_BRANCH, payload);
        // console.log("branchesDataconsole.log('::: ', );", branchesData);
        if (branchesData) setBranches(branchesData);
        const departmentsData = await getRequest(API_URLS.DEPT_LOOKUP);
        if (departmentsData) setDepartments(departmentsData);
        const designationsData = await getRequest(API_URLS.DESIGNATION_LOOKUP);
        if (designationsData) setDesignations(designationsData);
      } catch (error) {
        // console.error("Error fetching filter options:", error);
      }
    };

    fetchFilterOptions();
  }, []);

  useEffect(() => {
    const fetchFilteredData = async () => {
      try {
        const filterParams = {
          branch: selectedBranch,
          department: selectedDepartment,
          designation: selectedDesignation,
        };
        // const response = await postdata('employee/filter', filterParams);
        // setData(response.data);
      } catch (error) {
      }
    };

    fetchFilteredData();
  }, [selectedBranch, selectedDepartment, selectedDesignation]);

  const handleBranchChange = (selected: any) => setSelectedBranch(selected);
  const handleDepartmentChange = (selected: any) =>
    setSelectedDepartment(selected);
  const handleDesignationChange = (selected: any) =>
    setSelectedDesignation(selected);

  const [anchorEl, setAnchorEl] = useState(null);
  const [data, setData] = useState<any[]>([]);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");

  const handleSearchChange = (e: any) => {
    setSearchTerm(e.target.value);
  };

  useEffect(() => {
    fetchItems();
  }, [offset, limit]);

  const fetchItems = async () => {
    try {
      const request = {
        offset: offset,
        limit: limit,
      };
      const data = await postRequest (API_URLS.EMPLOYEE_LIST_LEAVEONPRESENTS, request, true);

      if (data) {
        setData(data.content);
        setTotalItems(data.total);
      
      }
    } catch (error) {
    }
  };
  

  const [openDesigntionModal, setDesigntionModal] = useState(false);
  const handleDesigntionModalOpen = () => setDesigntionModal(true);
  const handleDesigntionModalClose = () => setDesigntionModal(false);
  const [selectedEmpId, setSelectedEmpId] = useState<any>(null);

  const [openSchedulenModal, setScheduleModal] = useState(false);
  const handleScheduleModalOpen = () => setScheduleModal(true);
  const handleScheduleModalClose = () => setScheduleModal(false);
  const [isLoading, setIsLoading] = useState(false);
  const [empId, setEmpId] = useState("");

  const handleResetFilters = () => {
    setSelectedBranch([]);
    setSelectedDepartment([]);
    setSelectedDesignation([]);
    setSearchTerm("");
  };

  const handleEditEmp = (row: any) => {
    localStorage.setItem("empDetailsToEdit", JSON.stringify(row?.allData));
    window.location.href = "./employee/empDetails";
  };
  const handleOpenPopup = (row: any) => {
    setShowPopup(true);
    setSelectedEmpId(row); 
  };
  const handleUploadSuccess = () => {
  };

 


  

  const handleClosePopup = () => setShowPopup(false);

  const columns = [
    {
      field: "srNo",
      headerName: "Sr.no",
      width: 61,
      renderCell: (params: any) => {
        return <span>{params.row.srNo}</span>; // Show the srNo value
      },
    },
    {
      field: "empName",
      headerName: "Name",
      width: 150,
      // renderCell: (params:any) => (
      //   <div style={{ display: 'flex', alignItems: 'center' }}>
      //     <img
      //       src={params.row.imgUre}
      //       alt="Employee"
      //       style={{ width: 30, height: 30, borderRadius: '50%', marginRight: 10 }}
      //     />
      //     {params.value}
      //   </div>
      // ),
    },

    {
      field: "contact",
      headerName: "Contact",
      width: 80,
      renderCell: (params: any) => {
        return (
          <>
            {/* <span>{params.row.empName}</span> &nbsp; */}
            <a
              style={{ textDecoration: "none" }}
              href={`mailto:${params.row.email}`}
            >
              &nbsp;
              <EmailIcon sx={{ color: "blue" }} />
            </a>
            <a
              style={{ textDecoration: "none" }}
              href={`tel:${params.row.mobileNo}`}
            >
              &nbsp;
              <CallIcon sx={{ color: "blue" }} />
            </a>
          </>
        );
      },
    },
    { field: "branch", headerName: "Facility", width: 150 },
    { field: "department", headerName: "Department", width: 150 },
    { field: "designation", headerName: "Designation", width: 150 },
    { field: "upperName", headerName: "Reporting To", width: 150 },
    { field: "overTimeHours", headerName: "OverTime", width: 80 },
   
    

  ];

  const rows = data.map((employee, index) => ({
    id: index,
    srNo: (offset) * limit + (index + 1),
    empName: employee.designationName
      ? `${employee.empName} (${employee.designationName})`
      : employee.empName,
    branch: employee.branchName,
    department: employee.department,
    designation: employee.designation,
    upperName: employee.upperName,
    attendance: employee.attendance ? employee.attendance : "75%",
    email: employee.email,
    mobileNo: employee.mobileNo,
    allData: employee,
    empId: employee?.empId,
    overTimeHours: employee.overTimeHours,
  }));
  const handlePageChange = (newPage: any) => {
    // const newOffset = newPage.page * limit;  // Calculate offset based on page number
    setOffset(newPage.page);
    setLimit(newPage.pageSize);  // Handle changing page size
  };

  const handleModalClose = () => {
    setShowExcelUploadModal(false);
  };

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <Row className="my-3">
          <Col md={12} sm={12}>
            <Row>
              <Col
                style={{
                  width: "30%",
                  marginLeft: "20px",
                  marginBottom: "20px",
                }}
              >
                <h4>{"Employee List"}</h4>
                {/* <span style={{ fontSize: "12px", color: "grey" }}>
                  Hi,{" "}
                  {username
                    ? username?.charAt(0).toUpperCase() + username.slice(1)
                    : ""}
                  . Your organization's Employees are listed here
                </span> */}
              </Col>
             

                 
                
            </Row>
            
          </Col>
          
        </Row>

        <Row className="my-3" style={{ marginLeft: "10px" }}>
          <TableFilter
            columns={columns}
            rows={rows}
            onPageChange={handlePageChange}
            totalCount={totalItems}
            pageSize={limit}
          />
        </Row>
        {openDesigntionModal && (
          <CreateDesignationModal
            show={openDesigntionModal}
            handleClose={handleDesigntionModalClose}
          />
        )}
        {openSchedulenModal && (
          <CreateScheduleModal
            show={openSchedulenModal}
            handleClose={handleScheduleModalClose}
          />
        )}
        {showExcelUploadModal && (
          <CreateExcelModal
            show={showExcelUploadModal}
            handleClose={handleModalClose}
            downloadType="Employee"
          />
        )}
        <DocumentPopup show={showPopup} handleClose={handleClosePopup}    empId={selectedEmpId}
        />
         <BulkUploadModal
        show={showBulkUploadModal}
        handleClose={handleBulkUploadClose}
        onUploadSuccess={handleUploadSuccess}
      />
      </Container>
    </Layout>
  );
};

export default Employee;

const PopupBody = styled("div")(
  ({ theme }) => `
    width: max-content;
    padding: 12px 16px;
    margin: 8px;
    border-radius: 8px;
    border: 1px solid ${theme.palette.mode === "dark" ? grey[700] : grey[200]};
    background-color: ${theme.palette.mode === "dark" ? grey[900] : "#fff"};
    box-shadow: ${
      theme.palette.mode === "dark"
        ? `0px 4px 8px rgb(0 0 0 / 0.7)`
        : `0px 4px 8px rgb(0 0 0 / 0.1)`
    };
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    z-index: 1;
  `
);

const FilterButton = styled("button")(
  ({ theme }) => `
    // font-family: 'IBM Plex Sans', sans-serif;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: ${blue[500]};
    padding: 8px 16px;
    border-radius: 8px;
    color: white;
    transition: all 150ms ease;
    cursor: pointer;
    border: 1px solid ${blue[500]};
    box-shadow: 0 2px 4px ${
      theme.palette.mode === "dark"
        ? "rgba(0, 0, 0, 0.5)"
        : "rgba(0, 127, 255, 0.5)"
    }, inset 0 1.5px 1px ${blue[400]}, inset 0 -2px 1px ${blue[600]};
  
    &:hover {
      background-color: ${blue[600]};
    }
  
    &:active {
      background-color: ${blue[700]};
      box-shadow: none;
    }
  
    &:focus-visible {
      box-shadow: 0 0 0 4px ${
        theme.palette.mode === "dark" ? blue[300] : blue[200]
      };
      outline: none;
    }
  
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
      box-shadow: none;
      &:hover {
        background-color: ${blue[500]};
      }
    }
  `
);

const grey = {
  50: "#F3F6F9",
  100: "#E5EAF2",
  200: "#DAE2ED",
  300: "#C7D0DD",
  400: "#B0B8C4",
  500: "#9DA8B7",
  600: "#6B7A90",
  700: "#434D5B",
  800: "#303740",
  900: "#1C2025",
};

const blue = {
  200: "#99CCFF",
  300: "#66B2FF",
  400: "#3399FF",
  500: "#007FFF",
  600: "#0072E5",
  700: "#0066CC",
};
