
.custom-container {
    background-color: var(--red14); 
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    /* width:1080px; */
  }
  
  .ticket-text-primary {
    color: var(--blue12) !important; 
  }
  
  .form-control {
    border-radius: 4px;
  }
  
  .form-group {
    margin-bottom: 1.5rem; 
  }
  
  .form-control.bg-danger {
    background-color: var(--red6) !important;
    color: var(--red14) !important;
  }
  
  .form-control.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
  }
  
  .form-control.bg-success {
    background-color: var(--green2) !important;
    color: var(--red14) !important;
  }
 
  @media (max-width: 768px) {
    .custom-container {
      padding: 15px;
      width: 100%;
      max-width: 600px;  
      margin-left: 10px;
      margin-right: auto;
    }
  
    .form-group {
      margin-bottom: 1rem;
      width: 100%;
    }
  
    .react-select__control {
      width: 100%;  
    }
  
    .label {
      width: auto;
      padding: 5px 8px;
      font-size: 12px;  
    }
  }
  
 @media (min-width: 768px) and (max-width: 1024px) {
  .custom-container {
    padding: 15px; 
    width: 100%; 
    max-width: 600px; 
    margin-left: 18px;
    margin-right: auto; 
  }

  .form-group {
    margin-bottom: 1rem; 
    width: 100%; 
  }

  .react-select__control {
    width: 100%; 
  }

  .label {
    width: auto; 
    padding: 5px 8px; 
    font-size: 12px; 
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .label {
    width: 200px !important; 
    padding: 5px 8px; 
    font-size: 12px; 
  }
}
