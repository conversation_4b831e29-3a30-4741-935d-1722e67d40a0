import { CategoryScale } from "chart.js";

export const BASE_URL = process.env.NEXT_PUBLIC_API_URL;
export const BASE_URL2 = process.env.NEXT_PUBLIC_API_URL2;

export const API_URLS = {
  CREATE_REGULARIZE_ATTENDANCE: `${BASE_URL}/attendance/regularize2`,
  APPLY_LEAVE: `${BASE_URL}/employees/leave/applications`,
  FETCH_LEAVE_TYPES: `${BASE_URL}/leave-applications/types`,
  SHIFT_CHANGE: `${BASE_URL}/shift-change/request`,
  LEAVE_HISTORY: `${BASE_URL}/leave-applications/history`,
  PULL_BACK_LEAVE: (leaveHistoryId) =>
    `${BASE_URL}/leave-applications/${leaveHistoryId}/pullback`,
  MODIFICATION_HISTORY: `${BASE_URL}/attendance/regularizations`,
  APPROVE_LEAVE: (leaveHistoryId) =>
    `${BASE_URL}/leave-applications/${leaveHistoryId}/approve`,
  DENY_LEAVE: (leaveHistoryId) =>
    `${BASE_URL}/leave-applications/${leaveHistoryId}/deny`,
  APPROVE_SHIFT: (swapRequestId) =>
    `${BASE_URL}/shift-change/${swapRequestId}/approve`,
  DENY_SHIFT: (swapRequestId) =>
    `${BASE_URL}/shift-change/${swapRequestId}/deny`,
  GET_SCHDEULES: `${BASE_URL}/schedules`,
  GET_DESIGNATION_DEPTID: (deptid) => `${BASE_URL}/designation?dept=${deptid}`,
  GET_DESIGNATION: `${BASE_URL}/designation`,
  GET_DEPARTMENT: `${BASE_URL}/departments/names`,
  GET_BRANCH: `${BASE_URL}/departments/branches`,
  SHIFT_CHANGE_LIST: `${BASE_URL}/shift-change/data`,
  EMPLOYEE_LIST: `${BASE_URL}/employee`,
  EMPLOYEE_LIST2: `${BASE_URL}/employee1`,
  EMPLOYEE_LIST_PRESENT: `${BASE_URL}/employees/present`,
  EMPLOYEE_LIST_LEAVEONPRESENTS: `${BASE_URL}/employees/onLeave`,

  CREATE_EMPLOYEE_DETAILS: `${BASE_URL}/employee`,
  GET_PROVIENCE: `${BASE_URL}/provinces`,
  GET_DISTRICT: (provincename) =>
    `${BASE_URL}/provinces/${provincename}/districts`, //.toLowerString()
  GET_SUB_DISTRICT: (provinceId, districtId) =>
    `${BASE_URL}/provinces/${provinceId}/districts/${districtId}/subdistricts`,
  LOGIN: `${BASE_URL2}/auth/login`,
  USER_CREATION: `${BASE_URL}/users/create`,
  DEPT_ALL: `${BASE_URL}/departments/all`,
  DEPT_LIST: `${BASE_URL}/departments/allDepartments`,
  CREATE_BRANCH: `${BASE_URL}/branches/create-branch-dept`,
  UPDATE_BRANCH: `${BASE_URL}/branches/update`,
  CREATE_DEPARTMENT: `${BASE_URL}/departments/create-dept-branch`,
  //CREATE_DEPARTMENT: `${BASE_URL}/departments/create`,
  CREATE_DESIGNATION: `${BASE_URL}/designation/create`,
  // Add the new UPDATE_HOSPITAL endpoint
  UPDATE_HOSPITAL: (hospitalId) => `${BASE_URL}/hospitals/update/${hospitalId}`,

  UPDATE_SCHEDULE: `${BASE_URL}/schedule/update`,
  CREATE_LEAVE_BALANCE: `${BASE_URL}/employee/leave-balance`,
  DEPT_LOOKUP: `${BASE_URL}/departments/branchWiseDepartment`,
  DEPT_WISE_DESIGNATION: (deptid) =>
    `${BASE_URL}/designation/getDesignationByDepartment?departmentId=${deptid}`,
  DESIGNATION_LOOKUP: `${BASE_URL}/designation/lookupDesgination`,
  ATTENDANCE_APPROVE: `${BASE_URL}/attendance/update-status`,
  ATTENDANCE_DENY: `${BASE_URL}/attendance/update-status`,
  EMPLOYEE_NAME: `${BASE_URL}/employees/ids-names`,
  LEAVE_COUNT: `${BASE_URL}/leave-applications/counts`,
  PROFILE_DETAILS: `${BASE_URL}/current`,
  SCHEDULE_LIST: `${BASE_URL}/schedule/list`,
  // CEO_SCHEDULE_LIST: (branchId, deptId) =>
  //   `${BASE_URL}/schedule/list?hospitalId=${hospitalId}&branchId=${branchId}&departmentId=${deptId}`,
  // SUPERADMIN_SCHEDULE_LIST: (branchId, deptId) =>
  //   `${BASE_URL}/schedule/list?branchId=${branchId}&departmentId=${deptId}`,
  //SUPERADMIN_SCHEDULE_LIST: `${BASE_URL}/schedule/list`,
  DESIGNATION_LIST: `${BASE_URL}/designation/list`,
  DESIGNATION_DELETE: `${BASE_URL}/designation/delete`,
  MASTER_LEAVE_LIST: `${BASE_URL}/employees/leave/getAll`,
  MASTER_LEAVE_CREATE: `${BASE_URL}/employees/leave/save`,
  UPDATE_LEAVE_LIST: (leaveId) => `${BASE_URL}/employees/leave/${leaveId}`,
  DELETE_LEAVE_LIST: (leaveId) => `${BASE_URL}/employees/leave/${leaveId}`,
  HOLIDAY_LIST: `${BASE_URL}/holiday-schedule/list`,
  HOLIDAY_SEARCH: (query) => `${BASE_URL}/holiday-schedule/list?query=${query}`,
  DELETE_HOLIDAY: (id) => `${BASE_URL}/holiday-schedule/delete/${id}`,
  UPLOAD_EXCEL: `${BASE_URL}/holiday-schedule/upload-excel`,
  UPLOAD_EXCEL_HOSPITAL: `${BASE_URL}/hospitals/upload`,
  UPLOAD_EXCEL_EMPLOYEE: `${BASE_URL}/employee/upload-excel`,

  // UPLOAD_EXCEL_LEAVE: `${BASE_URL}/leave/upload-excel`,
  UPLOAD_EXCEL_NEW: `${BASE_URL}/upload-excel`,

  CREATE_SCHEDULE: `${BASE_URL}/schedule/create`,
  UPDATE_HOLIDAY: (id) => `${BASE_URL}/holiday-schedule/update/${id}`,
  CREATE_HOLIDAY: `${BASE_URL}/holiday-schedule/save`,
  // New API for updating a department
  UPDATE_DEPARTMENT: (id) => `${BASE_URL}/departments/update/${id}`,
  // NEW API FOR DELETING DEPARTMENT
  DELETE_DEPARTMENT: (id) => `${BASE_URL}/departments/delete/${id}`,
  GET_ROSTER: (fromDate, toDate) =>
    `${BASE_URL}/roster?fromDate=${fromDate}&toDate=${toDate}`,

  DATE_FILTER_HOLIDAY: `${BASE_URL}/holiday-schedule/list`,

  // DATE_FILTER_HOLIDAY: `${BASE_URL}/holiday-schedule/list`,
  // DOWNLOAD_WIDGET_DATA:(name,istrue)=>`${BASE_URL}/report/widgets?report=${name}&download=${istrue}}`,
  DOWNLOAD_WIDGET_DATA: (name, istrue) =>
    `${BASE_URL}/report/widgets?report=${name}&download=${istrue}`,

  SHIFTCHANGE_TIMESLOT: `${BASE_URL}/timeslots`,
  SHIFTCHANGE_TIMESLOT_ROSTER: (deptid) =>
    `${BASE_URL}/timeslots?deptId=${deptid}`,
  GET_RECOMMENDATION_LIST: (timeSlotId, fromDate, toDate) =>
    `${BASE_URL}/employees/by-timeslot?time_slot_id=${timeSlotId}&from_date=${fromDate}&to_date=${toDate}`,
  GET_ATTENDANCE_LIST: (fromDate, toDate) =>
    `${BASE_URL}/attendance/listByEmployee?fromDate=${fromDate}&toDate=${toDate}`,
  BIOMETRIC_LIST: (fromDate, toDate) =>
    `${BASE_URL}/attendance/audit/listByEmployee?fromDate=${fromDate}&toDate=${toDate}`,
  GET_SHIFTSWAPPING_LIST: (deptId, fromDate) =>
    `${BASE_URL}/shiftswapping?departmentId=${deptId}&date=${fromDate}`,
  SHIFT_SWAP: `${BASE_URL}/shiftswapping/swap`,
  GET_HOSPITAL_LIST: `${BASE_URL}/hospitals/getHospitals`,
  GET_ADMIN_DASHBOARD: `${BASE_URL}/dashboard/admin`,
  GET_SUPERVISOR_DASHBOARD: `${BASE_URL}/dashboard/supervisor`,
  DASHBOARDS: `${BASE_URL}/dashboard/widgets`,
  CREATE_TICKET: `${BASE_URL}/tickets/create`,
  VIEW_TICKETS: `${BASE_URL}/tickets/list`,
  UPDATE_TICKET: `${BASE_URL}/tickets/respond`,
  DESIGNATION_UPDATE: `${BASE_URL}/designation/update`,
  NOTIFICATION_LIST: `${BASE_URL}/employees/notifications`,
  EMAIL_SENT: `${BASE_URL}/users/forgot-password`,
  RESET_PASSWORD: `${BASE_URL}/users/reset-password`,
  VALID_TOKEN: `${BASE_URL}/users/validate-reset-token`,
  TICKET_BRANCHWISE_DEPARTMENT: `${BASE_URL}/tickets/employees/branches`,
  USER_MAPPING_SAVE: `${BASE_URL}/tickets/department_branch_user/save`,
  empVendorList: `${BASE_URL}/tickets/empVendorList`,
  EMPLOYEE_CALENDER: `${BASE_URL}/employee/calendar-month`,
  SIDEMENU_LIST: `${BASE_URL}/sidemenu`,
  EMPLOYEE_UPDATE: (empId) => `${BASE_URL}/employee/update/${empId}`,
  TICKET_DETAILS: `${BASE_URL}/tickets/getById`,
  Assign_Update: `${BASE_URL}/tickets/assign`,
  Status_Update: `${BASE_URL}/tickets/respond`,
  Reply_Update: `${BASE_URL}/tickets/respond`,
  MY_TEAMS: `${BASE_URL}/teams`,
  DOCUMENT_LIST: `${BASE_URL}/documentMaster/list`,
  CREATE_DOCUMENT: `${BASE_URL}/documentMaster/save`,
  TEAM_MEMBERS: `${BASE_URL}/teams/get-hierarchy-data`,
  EMPLOYEE_DOC_UPLOAD: `${BASE_URL}/employee-documents/upload`,
  DOWNLOAD_EMPLOYEE_FILE: `${BASE_URL}/employee-documents/download/employeefile?name=`,
  Leave_Download: `${BASE_URL}/leave-applications/download/leaveuploadfile?name=`,
  LOGOUT: `${BASE_URL}/employees/logoutUser`,
  READ_NOTIFIACTION: `${BASE_URL}/employees/notifications/read`,
  TICKET_DOWNLOAD: `${BASE_URL}/tickets/file/download?name=`,
  CATEGORY_LIST: `${BASE_URL}/categories/list`,
  CATEGORY_LOOKUP: `${BASE_URL}/categories/categoryLookup`,
  UPDATE_CATEGORY: `${BASE_URL}/categories/update/`,
  CREATE_CATEGORY: `${BASE_URL}/categories/save`,
  FILTER_TICKETS: `${BASE_URL}/tickets/genericList`,
  ASSIGNED_TO: `${BASE_URL}/employee/by-branch`,
  GET_EMPLOYEES_BY_BRANCH: `${BASE_URL}/employee/by-branch`,
  // New department endpoint without branchId in the URL
  DEPARTMENTS_BY_BRANCH: `${BASE_URL}/departments/branchWiseDepartment1`,
  EXCALATE_TICKET: `${BASE_URL}/tickets/escalate`,
  EXCALATE_LIST: `${BASE_URL}/tickets/escalationList`,
  UPDATE_DOCUMENT: `${BASE_URL}/documentMaster/update/`,
  DELETE_DOCUMENT: `${BASE_URL}/employee-documents/delete?`,
  TEAM_ATTENDANCE: `${BASE_URL}/attendance/teamAttendance`,
  NOTIFICATION_UPDATE: `${BASE_URL}/employees/updateToken`,
  EMPLOYEE_DETAIL: (employeeId) => `${BASE_URL}/employee/${employeeId}`,
  BULK_UPLOAD: `${BASE_URL}/uploadBulkImages`,
  WEEKLY_WORKING_HOUR: `${BASE_URL}/working-hours`,
  WEEKLY_ATTENDANCE: `${BASE_URL}/attendance/activity`,
  DEACTIVATION: `${BASE_URL}/employee/terminate`,
  RESIGNATION: `${BASE_URL}/employee/resign`,
  RESIGNATION_LIST: `${BASE_URL}/Resignation/status/list`,
  EMPLOYEE_APPROVE: (leaveHistoryId) =>
    `${BASE_URL}/Resignation/status/approve/${leaveHistoryId}`,
  EMPLOYEE_REJECT: (leaveHistoryId) =>
    `${BASE_URL}/Resignation/status/reject/${leaveHistoryId}`,
  EMPLOYEE_CANCEL: (leaveHistoryId) =>
    `${BASE_URL}/Resignation/status/reject/${leaveHistoryId}`,
  // New API for fetching attendance audit details
  ATTENDANCE_AUDIT_DETAILS: `${BASE_URL}/attendance/audit/details`,
  // Integrate the generateSchedule API below:
  // SCHEDULE_WITH_EMP_CODE: `${BASE_URL}/scheduler/generateSchedule`,

  SCHEDULE_WITH_EMP_CODE: `${BASE_URL}/scheduler/generateSchedule`,
};
