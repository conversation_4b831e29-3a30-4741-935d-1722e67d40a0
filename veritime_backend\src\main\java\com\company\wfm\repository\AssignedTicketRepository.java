package com.company.wfm.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.company.wfm.dto.TicketAssignDTO;
import com.company.wfm.entity.AssignedTickets;

public interface AssignedTicketRepository extends JpaRepository<AssignedTickets, Long> {

	@Query("SELECT new com.company.wfm.dto.TicketAssignDTO( " + "at.createdBy, " + // createdBy (from AssignedTickets)
			"createdByEmp.empName, " + // createdByName (from Employee table for created_by)
			"createdByEmp.imgUre, " + // image (from Employee table for created_by)
			"at.remark, " + // remark (from AssignedTickets)
			"at.createdAt, " + // createdAt (from AssignedTickets)
			"at.files, " + // files (from AssignedTickets)
			"CASE WHEN emp.empId IS NULL THEN NULL ELSE emp.empId END, " + // emp_id (conditionally check if empId is
																			// present)
			"CASE WHEN emp.empId IS NULL THEN NULL ELSE emp.empName END) " + // emp_id_name (conditionally check if
																				// empId is present)
			"FROM AssignedTickets at " + "JOIN Employee createdByEmp ON createdByEmp.empId = at.createdBy " + // Join
																												// for
																												// created_by
			"LEFT JOIN Employee emp ON emp.empId = at.employee.empId " + // LEFT JOIN for emp_id (optional)
			"WHERE at.ticket.ticketId = :ticketId " + "ORDER BY at.createdAt DESC")
	List<TicketAssignDTO> findAssignmentsByTicketId(@Param("ticketId") Long ticketId);

	@Query("SELECT at FROM AssignedTickets at WHERE at.ticket.ticketId = :ticketId")
	List<AssignedTickets> findByTicketId(@Param("ticketId") Long ticketId);
}
