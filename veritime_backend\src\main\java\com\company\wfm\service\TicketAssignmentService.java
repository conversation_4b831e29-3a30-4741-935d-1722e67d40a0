package com.company.wfm.service;

import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.TicketAssignmentDTO;

import jakarta.servlet.http.HttpServletRequest;

public interface TicketAssignmentService {

    public void assignTicket(TicketAssignmentDTO ticketAssignmentDTO) throws Exception;
    public void closeTicketByHR(Long ticketId, String finalRemark) throws Exception;
    public String respondAndCloseInternalTicket(Long ticketId, String responseRemark, String status, boolean notifyCreator, MultipartFile[] files, boolean escalate, HttpServletRequest httpRequest) throws Exception;
}
