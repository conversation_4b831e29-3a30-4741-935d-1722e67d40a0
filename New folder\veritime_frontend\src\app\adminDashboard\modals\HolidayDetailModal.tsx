import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

interface HolidayDetailModalProps {
  show: boolean;
  handleClose: () => void;
  holidayDetails: {
    date?: string;
    holiday?: string;
    isOptional?: boolean;
    isActive?: boolean;
    createdByName?: string;
    updatedByName1?: string;

    modifiedByName?: string;
    createdAt?: string;
    updatedAt?: string;
  };
}

const HolidayDetailModal: React.FC<HolidayDetailModalProps> = ({
  show,
  handleClose,
  holidayDetails,
}) => {
  React.useEffect(() => {
    console.log("Holiday details updated:", holidayDetails);
  }, [holidayDetails]);
  const formatDateOrNever = (date: string | null | undefined) => {
    return date ? new Date(date).toLocaleString() : "Never Updated";
  };
  return (
    <>
      <style>
        {`
      .custom-modal-backdrop {
        background-color: rgba(0, 0, 0, 0.2) 
      }
    `}
      </style>
      <Modal
        show={show}
        onHide={handleClose}
        backdropClassName="custom-modal-backdrop"
      >
        <Modal.Header closeButton>
          <Modal.Title>Holiday Details</Modal.Title>
        </Modal.Header>
        <Modal.Body style={{ overflowY: "auto", maxHeight: "400px" }}>
          <div style={{ padding: "10px" }}>
            <div style={{ display: "flex", marginBottom: "10px" }}>
              <div style={{ flex: 1, marginRight: "20px" }}>
                <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                  Date
                </div>
                <div>{holidayDetails?.date || ""}</div>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                  Holiday Name
                </div>
                <div>{holidayDetails?.holiday || ""}</div>
              </div>
            </div>
            <hr style={{ borderColor: "#a0a0a0" }} />

            {/* Is Optional  and Is Active*/}
            <div style={{ display: "flex", marginBottom: "10px" }}>
              <div style={{ flex: 1, marginRight: "20px" }}>
                <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                  Is Optional
                </div>

                <div>
                  {holidayDetails?.isOptional !== undefined
                    ? holidayDetails.isOptional
                      ? "Yes"
                      : "No"
                    : ""}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                  Status
                </div>
                <div>
                  {holidayDetails?.isActive == true ? (
                    <span style={{ color: "green" }}>Active</span>
                  ) : holidayDetails?.isActive == false ? (
                    <span style={{ color: "red" }}>Inactive</span>
                  ) : (
                    <span style={{ color: "orange" }}>N/A</span>
                  )}
                </div>
              </div>
            </div>
            <hr style={{ borderColor: "#a0a0a0" }} />

            {/* Created By and Updated By */}
            <div style={{ display: "flex", marginBottom: "10px" }}>
              <div style={{ flex: 1, marginRight: "20px" }}>
                <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                  Created By
                </div>
                <div>{holidayDetails?.createdByName || ""}</div>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                  Updated By
                </div>
                <div>{holidayDetails?.updatedByName1 || ""}</div>
              </div>
            </div>
            <hr style={{ borderColor: "#a0a0a0" }} />

            {/* Created At and Updated At */}
            <div style={{ display: "flex", marginBottom: "10px" }}>
              <div style={{ flex: 1, marginRight: "20px" }}>
                <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                  Created At
                </div>
                <div>
                  {holidayDetails?.createdAt
                    ? new Date(holidayDetails.createdAt).toLocaleString()
                    : ""}
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                  Updated At
                </div>
                <div>
                  {holidayDetails?.updatedAt === null
                    ? "Never Updated"
                    : holidayDetails?.updatedAt
                    ? new Date(holidayDetails.updatedAt).toLocaleString()
                    : ""}
                </div>
              </div>
            </div>
          </div>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" onClick={handleClose}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default HolidayDetailModal;
