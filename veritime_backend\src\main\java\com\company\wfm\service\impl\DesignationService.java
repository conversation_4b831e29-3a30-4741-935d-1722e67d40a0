package com.company.wfm.service.impl;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.company.wfm.dto.DesignationResponseDTO;
import com.company.wfm.repository.EmployeeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.dto.DesignationDTO;
import com.company.wfm.dto.DesignationIdNameDTO;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.Designation;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.DesignationRepository;
import com.company.wfm.service.UserTokenService;

import jakarta.persistence.EntityNotFoundException;

@Service
public class DesignationService {

    @Autowired
    private DesignationRepository designationRepository;

    @Autowired
    private DepartmentRepository departmentRepository;


    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private EmployeeRepository employeeRepository;


    @Transactional
    public Designation createDesignation(DesignationDTO designationDTO) {
        Long empId = tokenService.getEmployeeIdFromToken();

        if (designationRepository.existsByCode(designationDTO.getCode())) {
            throw new IllegalArgumentException("Designation code already exists");
        }
        // Check if designation name already exists
        if (designationRepository.existsByName(designationDTO.getName())) {
            throw new IllegalArgumentException("Designation name already exists");
        }

        Department department = departmentRepository.findById(designationDTO.getDepartmentId())
                .orElseThrow(() -> new EntityNotFoundException("Department not found"));

        Designation designation = new Designation();
        designation.setName(designationDTO.getName());

        // Ensure that designation code is provided
        if (designationDTO.getCode() == null || designationDTO.getCode().isEmpty()) {
            throw new IllegalArgumentException("Designation code is required");
        }
        designation.setCode(designationDTO.getCode());

        designation.setLevel(designationDTO.getLevel());
        designation.setDepartment(department);
        designation.setRole(designationDTO.getRole());
        designation.setActive(true);
        designation.setNoticePeriod(designationDTO.getNoticePeriod());
        // Set the role
//        if (designationDTO.getRole() != null && !designationDTO.getRole().isEmpty()) {
//            try {
//                DesignationRole role = DesignationRole.fromString(designationDTO.getRole());
//                designation.setRole(role);
//            } catch (IllegalArgumentException e) {
//                throw new IllegalArgumentException("Invalid role. Allowed values are: admin, supervisor, employee");
//            }
//        }

        designation.setCreatedAt(LocalDateTime.now());
        designation.setCreatedBy(empId);
       // designation.setUpdatedAt(LocalDateTime.now());

        return designationRepository.saveAndFlush(designation);
    }
    public List<String> getAllDesignationLevel() {
        return designationRepository.findAllDesignationLevel();
    }

//    @Transactional
//    public Designation updateDesignation(Long id, DesignationDTO designationDTO) {
//        Designation designation = getDesignationById(id);
//
//        Department department = departmentRepository.findById(designationDTO.getDepartmentId())
//                .orElseThrow(() -> new EntityNotFoundException("Department not found"));
//
//        designation.setName(designationDTO.getName());
//        designation.setCode(designationDTO.getCode());
//        designation.setLevel(designationDTO.getLevel());
//        designation.setDepartment(department);
//        designation.setUpdatedAt(LocalDateTime.now());
//
//        return designationRepository.saveAndFlush(designation);
//    }

//    @Transactional
//    public void deleteDesignation(Long id) {
//        Designation designation = getDesignationById(id);
//        designationRepository.delete(designation);
//    }
//
	public List<DesignationDTO> getAllDesignations() {
		List<Designation> designations = designationRepository.findAll();
        designations.sort(Comparator.comparing(Designation::getId).reversed());
		return designations.stream().map(this::convertToDesignationDTO).collect(Collectors.toList());
	}

	public List<DesignationDTO> getAllDesignationsByDepartmentId(Long departmentId) {
		List<Designation> designations = designationRepository.findAllByDepartmentId(departmentId);
		return designations.stream().map(this::convertToDesignationDTO).collect(Collectors.toList());
	}

	private DesignationDTO convertToDesignationDTO(Designation designation) {
        DesignationDTO dto = new DesignationDTO();
        dto.setId(designation.getId());
        dto.setName(designation.getName());
        dto.setCode(designation.getCode());
        dto.setLevel(designation.getLevel());
        dto.setCreatedBy(designation.getCreatedBy());
        dto.setCreatedAt(designation.getCreatedAt());
        dto.setUpdatedBy(designation.getUpdatedBy());
        dto.setUpdatedAt(designation.getUpdatedAt());
        dto.setRole(designation.getRole());
        dto.setNoticePeriod(designation.getNoticePeriod() != null ? designation.getNoticePeriod() : null);
        dto.setDepartmentId(designation.getDepartment().getDepartmentId());
        dto.setDepartmentName(designation.getDepartment().getDepartmentName());

        // Fetch employee name for createdBy
        if (designation.getCreatedBy() != null) {
            dto.setCreatedByName(employeeRepository.findEmployeeNameById(designation.getCreatedBy()));
        } else {
            dto.setCreatedByName(null);
        }

        // Fetch employee name for updatedBy
        if (designation.getUpdatedBy() != null) {
            dto.setUpdatedByName(employeeRepository.findEmployeeNameById(designation.getUpdatedBy()));
        } else {
            dto.setUpdatedByName(null);
        }
        return dto;
    }

    public List<DesignationIdNameDTO> getDesignationIdsAndNamesByDepartmentId() {
        return designationRepository.findDesignationIdsAndNamesByDepartmentId();
    }

    public List<DesignationIdNameDTO> getDesignationsKeyPairDepartmentId(Long departmentId) {
        return designationRepository.findDesignationByDepartmentId(departmentId);
    }


    //update and delete degination

    @Transactional
    public DesignationResponseDTO updateDesignation(Long id, DesignationDTO designationDTO) {

        Long empId = tokenService.getEmployeeIdFromToken();

        Designation designation = designationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Designation not found"));

        // Update fields
        designation.setName(designationDTO.getName());
        designation.setCode(designationDTO.getCode());
        designation.setLevel(designationDTO.getLevel());

        // Optional: Update the department if necessary
        if (designationDTO.getDepartmentId() != null) {
            Department department = departmentRepository.findById(designationDTO.getDepartmentId())
                    .orElseThrow(() -> new EntityNotFoundException("Department not found"));
            designation.setDepartment(department);
        }
         designation.setUpdatedBy(empId);
        designation.setUpdatedAt(LocalDateTime.now());
        designation.setRole(designationDTO.getRole());
        designation.setNoticePeriod(designationDTO.getNoticePeriod());

        designation = designationRepository.saveAndFlush(designation);
        DesignationResponseDTO responseDTO = new DesignationResponseDTO();
        responseDTO.setId(designation.getId());
        responseDTO.setName(designation.getName());
        responseDTO.setCode(designation.getCode());
        responseDTO.setLevel(designation.getLevel());
        responseDTO.setRole(designation.getRole());
        responseDTO.setNoticePeriod(designation.getNoticePeriod());
        responseDTO.setActive(designation.isActive());
        responseDTO.setCreatedAt(designation.getCreatedAt());
        responseDTO.setUpdatedAt(designation.getUpdatedAt());
        responseDTO.setCreatedBy(designation.getCreatedBy());
        responseDTO.setUpdatedBy(designation.getUpdatedBy());

        // Set department details
        if (designation.getDepartment() != null) {
            responseDTO.setDepartmentId(designation.getDepartment().getDepartmentId());
            responseDTO.setDepartmentName(designation.getDepartment().getDepartmentName());
        }

        return responseDTO;
    }


    @Transactional
    public void softDeleteDesignation(Long id) {
        Designation designation = designationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Designation not found"));

        // Soft delete by setting isActive to false
        designation.setActive(false);
        designationRepository.saveAndFlush(designation);
    }

}
