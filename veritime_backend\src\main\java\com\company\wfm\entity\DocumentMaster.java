package com.company.wfm.entity;


import java.io.Serializable;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_document_master")
public class DocumentMaster implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DOCUMENT_ID")
    private Long documentId; // Auto-incremented primary key

    @Column(name = "NAME", nullable = false)
    private String name; // Name of the document

    @Column(name = "TYPE", nullable = false)
    private String type; // Type of the document

    @Column(name = "IS_MANDATORY", nullable = false)
    private Boolean isMandatory; // Checkbox indicating if the document is mandatory

    @Column(name = "CREATED_BY", nullable = false)
    private Long createdBy; // ID of the user who created the record

    @Column(name = "CREATED_AT", nullable = false)
    private LocalDateTime createdAt; // Timestamp for when the record was created

    @Column(name = "UPDATED_BY")
    private Long updatedBy; // ID of the user who last updated the record

    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt; // Timestamp for the last update

    @Column(name = "IS_ACTIVE", nullable = false)
    private Boolean isActive; // Active status (1 for active, 0 for inactive)


}
