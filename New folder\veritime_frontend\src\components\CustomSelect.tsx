import React from 'react';
import Select from 'react-select';

interface Option {
  value: string | number;
  label: string;
}

interface CustomSelectProps {
  options: any;
  onChange: (selectedOption: Option | null) => void;
  placeholder?: string;
}

const CustomSelect: React.FC<CustomSelectProps> = ({ options, onChange, placeholder = "Select Branch" }) => {
  return (
    <Select
      options={options}
      onChange={onChange}
      placeholder={placeholder}
      isSearchable={true}
      classNamePrefix="custom-select"
    />
  );
};

export default CustomSelect;
