# Veritime Frontend Application Flow Documentation

## Table of Contents

1. [Complete User Authentication Journey](#complete-user-authentication-journey)
2. [Application Initialization Flow](#application-initialization-flow)
3. [Page-by-Page Navigation Flow](#page-by-page-navigation-flow)
4. [State Management and Data Flow](#state-management-and-data-flow)
5. [Role-Specific Feature Access](#role-specific-feature-access)
6. [Technical Implementation Details](#technical-implementation-details)

## Complete User Authentication Journey

### 1. Login Page Interaction (`/login`)

#### Initial Page Load

```javascript
// pages/login/index.js
useEffect(() => {
  // Check if user is already logged in
  if (localStorage.getItem("accessToken") && localStorage.getItem("role")) {
    // Redirect to appropriate dashboard based on role
    roleBasedRedirect(window.location.pathname);
  }
}, []);
```

#### Form Validation

```javascript
const validateForm = () => {
  const errors = {};

  if (!formData.username) {
    errors.username = "Username is required";
  }

  if (!formData.password) {
    errors.password = "Password is required";
  }

  if (!formData.captcha) {
    errors.captcha = "Captcha is required";
  }

  return errors;
};
```

#### Login API Call Process

```javascript
const handleLogin = async (e) => {
  e.preventDefault();

  try {
    // 1. Validate form data
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // 2. Prepare encrypted payload
    const loginPayload = {
      username: formData.username,
      password: formData.password,
    };

    // 3. Make API call to login endpoint
    const response = await postRequest(
      API_URLS.LOGIN,
      loginPayload,
      true // Show loader
    );

    // 4. Process successful response
    if (response && response.accessToken) {
      // Store authentication data
      localStorage.setItem("accessToken", response.accessToken);
      localStorage.setItem("role", response.role);
      localStorage.setItem("empId", response.empId);
      localStorage.setItem("employeeName", response.employeeName);
      localStorage.setItem("logHistoryId", response.logHistoryId);

      // 5. OneSignal user login
      await oneSignalService.loginUser(response.accessToken);

      // 6. Role-based redirection
      roleBasedRedirect("/");
    } else {
      showErrorAlert("Invalid credentials");
    }
  } catch (error) {
    console.error("Login error:", error);
    showErrorAlert("Login failed. Please try again.");
  }
};
```

### 2. Token Storage and Management

#### localStorage Structure

```javascript
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "role": "admin",
  "empId": "12345",
  "employeeName": "John Doe",
  "logHistoryId": "67890",
  "currentPath": "/dashboards"
}
```

#### Token Validation on Each Request

```javascript
// services/apiService.js
const getRequestWithSecurity = async (url, showLoader = false) => {
  const token = localStorage.getItem("accessToken");

  if (!token) {
    // Redirect to login if no token
    window.location.href = "/login";
    return;
  }

  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(url, { headers });

    if (response.status === 401) {
      // Token expired, redirect to login
      localStorage.clear();
      window.location.href = "/login?sessionExpired=1";
      return;
    }

    return await response.json();
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
};
```

### 3. Role-Based Redirection Logic

#### roleBasedRedirect.ts Implementation

```javascript
const roleBasedRedirect = (currentPath) => {
  const role = localStorage.getItem("role");
  const accessToken = localStorage.getItem("accessToken");

  // Check authentication
  if (!accessToken || !role) {
    window.location.href = "/login";
    return;
  }

  // Role-based access control
  switch (role.toLowerCase()) {
    case "ceo":
      // CEO has access to all pages
      if (currentPath === "/" || currentPath === "/login") {
        window.location.href = "/dashboards";
      }
      break;

    case "superadmin":
    case "admin":
      // Admin access to most pages except CEO-specific
      if (currentPath.startsWith("/ceo-reports")) {
        window.location.href = "/404";
      } else if (currentPath === "/" || currentPath === "/login") {
        window.location.href = "/dashboards";
      }
      break;

    case "supervisor":
      // Supervisor access to team-related pages
      if (
        currentPath.startsWith("/masters") ||
        currentPath.startsWith("/ceo-reports")
      ) {
        window.location.href = "/404";
      } else if (currentPath === "/" || currentPath === "/login") {
        window.location.href = "/dashboards";
      }
      break;

    case "employee":
      // Employee access to limited pages
      const allowedPaths = ["/dashboards", "/profile", "/leave", "/attendance"];
      const isAllowed = allowedPaths.some((path) =>
        currentPath.startsWith(path)
      );

      if (!isAllowed && currentPath !== "/") {
        window.location.href = "/404";
      } else if (currentPath === "/" || currentPath === "/login") {
        window.location.href = "/dashboards";
      }
      break;

    default:
      window.location.href = "/login";
  }
};
```

## Application Initialization Flow

### 1. \_app.js Global Setup

#### OneSignal SDK Initialization

```javascript
// pages/_app.js
import { useEffect } from "react";
import OneSignal from "react-onesignal";

let initializationAttempted = false;

function MyApp({ Component, pageProps }) {
  useEffect(() => {
    // Initialize OneSignal only once on client side
    if (typeof window !== "undefined" && !initializationAttempted) {
      initializationAttempted = true;

      OneSignal.init({
        appId: "************************************",
        safari_web_id:
          "web.onesignal.auto.************************************",
        allowLocalhostAsSecureOrigin: true,
      })
        .then(() => {
          // OneSignal is now ready - set up global listeners
          OneSignal.Notifications.requestPermission().catch((error) => {
            console.log("Notification permission not granted:", error.message);
          });
          OneSignal.Notifications.addEventListener("click", () => {});
          OneSignal.Notifications.addEventListener(
            "foregroundWillDisplay",
            () => {}
          );
          OneSignal.Notifications.addEventListener(
            "permissionChange",
            () => {}
          );
        })
        .catch((error) => {
          console.error("OneSignal initialization failed:", error);
        });
    }
  }, []);

  return <Component {...pageProps} />;
}
```

### 2. Layout Component Initialization

#### Authentication Check

```javascript
// components/Layout.tsx
useEffect(() => {
  if (typeof window !== "undefined") {
    // Validate authentication
    if (!localStorage.getItem("accessToken") || !localStorage.getItem("role")) {
      localStorage.clear();
      localStorage.setItem("currentPath", window.location.href);
      window.location.href = "/login?sessionExpired=1";
      return;
    }

    // Apply role-based redirection
    roleBasedRedirect(window.location.pathname);
  }
}, []);
```

### 3. Sidebar Menu Generation

#### Dynamic Menu Loading

```javascript
// components/Sidemenu.tsx
const [menuData, setMenuData] = useState([]);

useEffect(() => {
  const fetchSideMenu = async () => {
    try {
      // Fetch role-based menu from backend
      const response = await getRequest(API_URLS.SIDEMENU_LIST);

      if (response && Array.isArray(response)) {
        // Filter active menu items and sort by sequence
        const activeMenus = response
          .filter((item) => item.is_active)
          .sort((a, b) => a.sequence - b.sequence);

        setMenuData(activeMenus);
      }
    } catch (error) {
      console.error("Error fetching side menu:", error);
    }
  };

  fetchSideMenu();
}, []);

// Render menu items
const renderMenuItems = (items) => {
  return items.map((item) => (
    <Link href={item.menu_path} key={item.id}>
      <div className={`menu-item ${isActive(item.menu_path) ? "active" : ""}`}>
        <img src={item.icon_path} alt={item.menu_label} />
        <span>{item.menu_label}</span>
      </div>
    </Link>
  ));
};
```

## Page-by-Page Navigation Flow

### 1. Dashboard Loading Process

#### Widget Data Fetching

```javascript
// app/dashboards/page.tsx
const [dashboardData, setDashboardData] = useState({});

useEffect(() => {
  const loadDashboardData = async () => {
    try {
      // Fetch multiple dashboard widgets
      const [employeeCount, attendanceData, leaveRequests, notifications] =
        await Promise.all([
          postRequest(API_URLS.WIDGETS + "?report=EmployeeCount"),
          postRequest(API_URLS.WIDGETS + "?report=EmployeePresent"),
          postRequest(API_URLS.WIDGETS + "?report=LeaveApprovalsPending"),
          getRequest(API_URLS.NOTIFICATIONS),
        ]);

      setDashboardData({
        employeeCount,
        attendanceData,
        leaveRequests,
        notifications,
      });
    } catch (error) {
      console.error("Dashboard data loading failed:", error);
    }
  };

  loadDashboardData();
}, []);
```

### 2. Employee Management Workflow

#### Employee List Loading

```javascript
// app/masters/employee/page.tsx
const [employees, setEmployees] = useState([]);
const [pagination, setPagination] = useState({ offset: 0, limit: 10 });

const fetchEmployees = async () => {
  try {
    const response = await postRequest(API_URLS.EMPLOYEE_PRESENT, pagination);

    if (response && response.employees) {
      setEmployees(response.employees);
    }
  } catch (error) {
    console.error("Error fetching employees:", error);
  }
};

// CRUD Operations
const createEmployee = async (employeeData) => {
  try {
    const response = await postRequest(
      API_URLS.CREATE_EMPLOYEE_DETAILS,
      employeeData
    );

    if (response.success) {
      showSuccessAlert("Employee created successfully");
      fetchEmployees(); // Refresh list
    }
  } catch (error) {
    showErrorAlert("Failed to create employee");
  }
};
```

### 3. Attendance Tracking Flow

#### Team Attendance View

```javascript
// app/attendance/team/page.tsx
const fetchTeamAttendance = async (dateRange) => {
  try {
    const payload = {
      userid: localStorage.getItem("empId"),
      fromDate: dateRange.startDate,
      toDate: dateRange.endDate,
    };

    const response = await postRequest(API_URLS.TEAM_ATTENDANCE, payload);

    setAttendanceData(response);
  } catch (error) {
    console.error("Error fetching team attendance:", error);
  }
};

// Attendance Approval
const approveAttendance = async (attendanceId) => {
  try {
    const payload = {
      attendanceId,
      status: "APPROVED",
      remarks: "Approved by supervisor",
    };

    await postRequest(API_URLS.ATTENDANCE_APPROVE, payload);
    showSuccessAlert("Attendance approved");
    fetchTeamAttendance(); // Refresh data
  } catch (error) {
    showErrorAlert("Failed to approve attendance");
  }
};
```

### 4. Leave Application Process

#### Leave Application Form

```javascript
// app/leave/apply/page.tsx
const submitLeaveApplication = async (leaveData) => {
  try {
    const payload = {
      leaveId: leaveData.leaveType,
      startDate: leaveData.startDate,
      endDate: leaveData.endDate,
      appliedLeaveCount: leaveData.days,
      reason: leaveData.reason,
      uploadedFileName: leaveData.attachmentName,
    };

    const response = await postRequest(API_URLS.APPLY_LEAVE, payload);

    if (response.success) {
      showSuccessAlert("Leave application submitted");
      router.push("/leave/history");
    }
  } catch (error) {
    showErrorAlert("Failed to submit leave application");
  }
};
```

### 5. Notification Handling

#### Real-time Notification Processing

```javascript
// components/Header/Header.js
useEffect(() => {
  const handleForegroundNotification = (event) => {
    const newNotification = event?.notification?.title;

    // Check and prevent duplicate notifications
    if (!displayedNotifications.current.has(newNotification)) {
      displayedNotifications.current.add(newNotification);

      showSuccessAlert("New Notification: " + newNotification);
      setTimeout(() => {
        fetchNotifications(); // Refresh notification list
      }, 2000);
    }
  };

  // Add OneSignal event listener for this component
  if (window.OneSignal && window.OneSignal.Notifications) {
    OneSignal.Notifications.addEventListener(
      "foregroundWillDisplay",
      handleForegroundNotification
    );
  }

  // Cleanup on component unmount
  return () => {
    if (window.OneSignal && window.OneSignal.Notifications) {
      OneSignal.Notifications.removeEventListener(
        "foregroundWillDisplay",
        handleForegroundNotification
      );
    }
  };
}, []);
```

## State Management and Data Flow

### 1. localStorage Usage Patterns

#### Authentication State

```javascript
// Stored after successful login
localStorage.setItem("accessToken", token);
localStorage.setItem("role", userRole);
localStorage.setItem("empId", employeeId);
localStorage.setItem("employeeName", name);

// Retrieved for API calls and role checks
const token = localStorage.getItem("accessToken");
const role = localStorage.getItem("role");
const empId = localStorage.getItem("empId");
```

#### Navigation State

```javascript
// Store current path for post-login redirect
localStorage.setItem("currentPath", window.location.href);

// Retrieve and redirect after login
const savedPath = localStorage.getItem("currentPath");
if (savedPath) {
  window.location.href = savedPath;
  localStorage.removeItem("currentPath");
}
```

### 2. Component State Management

#### React State Patterns

```javascript
// Typical component state structure
const [data, setData] = useState([]);
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);
const [pagination, setPagination] = useState({ offset: 0, limit: 10 });

// Data fetching pattern
const fetchData = async () => {
  setLoading(true);
  setError(null);

  try {
    const response = await getRequest(API_URL);
    setData(response);
  } catch (err) {
    setError(err.message);
  } finally {
    setLoading(false);
  }
};
```

### 3. API Call Patterns

#### Standard Request Flow

```javascript
// services/apiService.js
export const postRequest = async (url, data, showLoader = false) => {
  if (showLoader) {
    // Show loading indicator
    setGlobalLoader(true);
  }

  try {
    const token = localStorage.getItem("accessToken");
    const response = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ encryptedData: encrypt(data) }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    return decrypt(result.encryptedData);
  } catch (error) {
    console.error("API Error:", error);
    throw error;
  } finally {
    if (showLoader) {
      setGlobalLoader(false);
    }
  }
};
```

### 4. Error Handling and User Feedback

#### Global Error Handler

```javascript
// utils/errorHandler.js
export const handleApiError = (error, context = "") => {
  console.error(`${context} Error:`, error);

  if (error.status === 401) {
    // Unauthorized - redirect to login
    localStorage.clear();
    window.location.href = "/login?sessionExpired=1";
  } else if (error.status === 403) {
    // Forbidden - show access denied
    showErrorAlert("Access denied. Insufficient permissions.");
  } else if (error.status >= 500) {
    // Server error
    showErrorAlert("Server error. Please try again later.");
  } else {
    // Client error
    showErrorAlert(error.message || "An error occurred");
  }
};
```

#### User Feedback System

```javascript
// utils/alerts.js
export const showSuccessAlert = (message) => {
  // Implementation using toast library or custom modal
  toast.success(message, {
    position: "top-right",
    autoClose: 3000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  });
};

export const showErrorAlert = (message) => {
  toast.error(message, {
    position: "top-right",
    autoClose: 5000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  });
};
```

## Role-Specific Feature Access

### 1. Component-Level Role Checks

#### Conditional Rendering

```javascript
// components/EmployeeActions.tsx
const EmployeeActions = ({ employee }) => {
  const userRole = localStorage.getItem("role");
  const currentEmpId = localStorage.getItem("empId");

  const canEdit = () => {
    // CEO and SuperAdmin can edit anyone
    if (["ceo", "superadmin"].includes(userRole.toLowerCase())) {
      return true;
    }

    // Admin can edit employees in their branch
    if (userRole.toLowerCase() === "admin") {
      return employee.branchId === currentUserBranchId;
    }

    // Supervisor can edit direct reports
    if (userRole.toLowerCase() === "supervisor") {
      return employee.upperId === currentEmpId;
    }

    // Employee can only edit themselves
    return employee.empId === currentEmpId;
  };

  return (
    <div>
      {canEdit() && (
        <button onClick={() => editEmployee(employee.empId)}>
          Edit Employee
        </button>
      )}

      {["ceo", "superadmin", "admin"].includes(userRole.toLowerCase()) && (
        <button onClick={() => deactivateEmployee(employee.empId)}>
          Deactivate
        </button>
      )}
    </div>
  );
};
```

### 2. Route Protection

#### Page-Level Access Control

```javascript
// components/ProtectedRoute.tsx
const ProtectedRoute = ({ children, requiredRoles = [] }) => {
  const userRole = localStorage.getItem("role");

  useEffect(() => {
    if (!userRole) {
      window.location.href = "/login";
      return;
    }

    if (
      requiredRoles.length > 0 &&
      !requiredRoles.includes(userRole.toLowerCase())
    ) {
      window.location.href = "/404";
      return;
    }
  }, [userRole, requiredRoles]);

  return children;
};

// Usage in pages
const MastersPage = () => {
  return (
    <ProtectedRoute requiredRoles={["ceo", "superadmin", "admin"]}>
      <MastersContent />
    </ProtectedRoute>
  );
};
```

### 3. Data Filtering by Role

#### API Response Processing

```javascript
// utils/dataFilters.js
export const filterEmployeesByRole = (employees, userRole, userEmpId) => {
  switch (userRole.toLowerCase()) {
    case "ceo":
    case "superadmin":
      // Return all employees
      return employees;

    case "admin":
      // Filter by branch
      return employees.filter((emp) => emp.branchId === userBranchId);

    case "supervisor":
      // Filter by direct reports
      return employees.filter((emp) => emp.upperId === userEmpId);

    case "employee":
      // Return only self
      return employees.filter((emp) => emp.empId === userEmpId);

    default:
      return [];
  }
};
```

## Technical Implementation Details

### 1. Environment Configuration

#### API Base URLs

```javascript
// .env.local
NEXT_PUBLIC_API_URL=https://www.xpertlyte.tech/api/v1
NEXT_PUBLIC_API_URL2=https://www.xpertlyte.tech/api

// constants/apiConstants.js
export const BASE_URL = process.env.NEXT_PUBLIC_API_URL;
export const BASE_URL2 = process.env.NEXT_PUBLIC_API_URL2;
```

### 2. Security Measures

#### Data Encryption

```javascript
// utils/encryption.js
export const encrypt = (data) => {
  // Implementation of AES encryption
  const encrypted = CryptoJS.AES.encrypt(
    JSON.stringify(data),
    process.env.NEXT_PUBLIC_ENCRYPTION_KEY
  ).toString();

  return encrypted;
};

export const decrypt = (encryptedData) => {
  // Implementation of AES decryption
  const decrypted = CryptoJS.AES.decrypt(
    encryptedData,
    process.env.NEXT_PUBLIC_ENCRYPTION_KEY
  );

  return JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
};
```

#### Token Security

```javascript
// Token validation on each request
const validateToken = () => {
  const token = localStorage.getItem("accessToken");

  if (!token) {
    return false;
  }

  try {
    // Decode JWT to check expiration
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Date.now() / 1000;

    if (payload.exp < currentTime) {
      // Token expired
      localStorage.clear();
      return false;
    }

    return true;
  } catch (error) {
    // Invalid token format
    localStorage.clear();
    return false;
  }
};
```

### 3. Performance Optimization

#### Lazy Loading

```javascript
// Dynamic imports for code splitting
const DashboardPage = dynamic(() => import("../app/dashboards/page"), {
  loading: () => <LoadingSpinner />,
  ssr: false,
});

const MastersPage = dynamic(() => import("../app/masters/page"), {
  loading: () => <LoadingSpinner />,
  ssr: false,
});
```

#### Data Caching

```javascript
// Simple cache implementation
const cache = new Map();

export const getCachedData = async (key, fetchFunction, ttl = 300000) => {
  const cached = cache.get(key);

  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data;
  }

  const data = await fetchFunction();
  cache.set(key, { data, timestamp: Date.now() });

  return data;
};
```

### 4. Error Boundaries

#### React Error Boundary

```javascript
// components/ErrorBoundary.tsx
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error Boundary caught an error:", error, errorInfo);
    // Log to error reporting service
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>Something went wrong.</h2>
          <button onClick={() => window.location.reload()}>Reload Page</button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## Advanced Features and Integrations

### 1. File Upload and Management

#### Document Upload Process

```javascript
// components/FileUpload.tsx
const uploadDocument = async (file, employeeId) => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("empId", employeeId);
  formData.append("documentType", selectedDocType);

  try {
    const response = await fetch(API_URLS.EMPLOYEE_DOC_UPLOAD, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
      },
      body: formData,
    });

    if (response.ok) {
      showSuccessAlert("Document uploaded successfully");
      refreshDocumentList();
    }
  } catch (error) {
    showErrorAlert("Upload failed");
  }
};
```

#### File Download Handling

```javascript
const downloadFile = async (fileName) => {
  try {
    const response = await fetch(
      `${API_URLS.DOWNLOAD_EMPLOYEE_FILE}${fileName}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
        },
      }
    );

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    showErrorAlert("Download failed");
  }
};
```

### 2. Real-time Data Updates

#### WebSocket Integration (if implemented)

```javascript
// services/websocketService.js
class WebSocketService {
  constructor() {
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const token = localStorage.getItem("accessToken");
    this.ws = new WebSocket(`wss://api.example.com/ws?token=${token}`);

    this.ws.onopen = () => {
      console.log("WebSocket connected");
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };

    this.ws.onclose = () => {
      this.reconnect();
    };
  }

  handleMessage(data) {
    switch (data.type) {
      case "ATTENDANCE_UPDATE":
        // Refresh attendance data
        window.dispatchEvent(
          new CustomEvent("attendanceUpdate", { detail: data })
        );
        break;
      case "LEAVE_APPROVAL":
        // Show notification
        showSuccessAlert(`Leave request ${data.status}`);
        break;
    }
  }
}
```

#### Polling for Updates

```javascript
// hooks/usePolling.js
const usePolling = (fetchFunction, interval = 30000) => {
  const [data, setData] = useState(null);
  const [isPolling, setIsPolling] = useState(true);

  useEffect(() => {
    let intervalId;

    const poll = async () => {
      try {
        const result = await fetchFunction();
        setData(result);
      } catch (error) {
        console.error("Polling error:", error);
      }
    };

    if (isPolling) {
      poll(); // Initial fetch
      intervalId = setInterval(poll, interval);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [fetchFunction, interval, isPolling]);

  return { data, setIsPolling };
};

// Usage in components
const AttendanceList = () => {
  const { data: attendanceData } = usePolling(
    () => getRequest(API_URLS.TEAM_ATTENDANCE),
    60000 // Poll every minute
  );

  return (
    <div>
      {attendanceData?.map((record) => (
        <AttendanceRow key={record.id} data={record} />
      ))}
    </div>
  );
};
```

### 3. Advanced Form Handling

#### Dynamic Form Generation

```javascript
// components/DynamicForm.tsx
const DynamicForm = ({ formConfig, onSubmit }) => {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});

  const renderField = (field) => {
    switch (field.type) {
      case "text":
        return (
          <input
            type="text"
            value={formData[field.name] || ""}
            onChange={(e) =>
              setFormData({
                ...formData,
                [field.name]: e.target.value,
              })
            }
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case "select":
        return (
          <select
            value={formData[field.name] || ""}
            onChange={(e) =>
              setFormData({
                ...formData,
                [field.name]: e.target.value,
              })
            }
            required={field.required}
          >
            <option value="">Select {field.label}</option>
            {field.options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case "date":
        return (
          <input
            type="date"
            value={formData[field.name] || ""}
            onChange={(e) =>
              setFormData({
                ...formData,
                [field.name]: e.target.value,
              })
            }
            required={field.required}
          />
        );

      default:
        return null;
    }
  };

  const validateForm = () => {
    const newErrors = {};

    formConfig.fields.forEach((field) => {
      if (field.required && !formData[field.name]) {
        newErrors[field.name] = `${field.label} is required`;
      }

      if (field.validation) {
        const validationResult = field.validation(formData[field.name]);
        if (!validationResult.isValid) {
          newErrors[field.name] = validationResult.message;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {formConfig.fields.map((field) => (
        <div key={field.name} className="form-group">
          <label>{field.label}</label>
          {renderField(field)}
          {errors[field.name] && (
            <span className="error">{errors[field.name]}</span>
          )}
        </div>
      ))}
      <button type="submit">Submit</button>
    </form>
  );
};
```

### 4. Data Export and Reporting

#### Export Functionality

```javascript
// utils/exportUtils.js
export const exportToCSV = (data, filename) => {
  const csvContent = convertToCSV(data);
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

export const exportToExcel = async (data, filename) => {
  // Using a library like xlsx
  const XLSX = await import("xlsx");
  const ws = XLSX.utils.json_to_sheet(data);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
  XLSX.writeFile(wb, filename);
};

const convertToCSV = (data) => {
  if (!data.length) return "";

  const headers = Object.keys(data[0]);
  const csvHeaders = headers.join(",");

  const csvRows = data.map((row) =>
    headers
      .map((header) => {
        const value = row[header];
        return typeof value === "string" && value.includes(",")
          ? `"${value}"`
          : value;
      })
      .join(",")
  );

  return [csvHeaders, ...csvRows].join("\n");
};
```

#### Report Generation

```javascript
// components/ReportGenerator.tsx
const ReportGenerator = () => {
  const [reportType, setReportType] = useState("");
  const [dateRange, setDateRange] = useState({ start: "", end: "" });
  const [filters, setFilters] = useState({});
  const [isGenerating, setIsGenerating] = useState(false);

  const generateReport = async () => {
    setIsGenerating(true);

    try {
      const params = {
        report: reportType,
        fromDate: dateRange.start,
        toDate: dateRange.end,
        ...filters,
        download: true,
      };

      const response = await postRequest(API_URLS.WIDGETS, params);

      if (response.downloadUrl) {
        // Direct download
        window.open(response.downloadUrl, "_blank");
      } else {
        // Export client-side
        exportToExcel(response.data, `${reportType}_${Date.now()}.xlsx`);
      }

      showSuccessAlert("Report generated successfully");
    } catch (error) {
      showErrorAlert("Report generation failed");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="report-generator">
      <div className="form-group">
        <label>Report Type</label>
        <select
          value={reportType}
          onChange={(e) => setReportType(e.target.value)}
        >
          <option value="">Select Report</option>
          <option value="EmployeePresent">Present Employees</option>
          <option value="LeaveApproved">Approved Leaves</option>
          <option value="AttendanceSummary">Attendance Summary</option>
        </select>
      </div>

      <div className="date-range">
        <input
          type="date"
          value={dateRange.start}
          onChange={(e) =>
            setDateRange({ ...dateRange, start: e.target.value })
          }
          placeholder="Start Date"
        />
        <input
          type="date"
          value={dateRange.end}
          onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
          placeholder="End Date"
        />
      </div>

      <button onClick={generateReport} disabled={!reportType || isGenerating}>
        {isGenerating ? "Generating..." : "Generate Report"}
      </button>
    </div>
  );
};
```

### 5. Mobile Responsiveness and PWA Features

#### Responsive Design Patterns

```javascript
// hooks/useMediaQuery.js
const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addListener(listener);

    return () => media.removeListener(listener);
  }, [matches, query]);

  return matches;
};

// Usage in components
const ResponsiveLayout = () => {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const isTablet = useMediaQuery("(max-width: 1024px)");

  return (
    <div
      className={`layout ${
        isMobile ? "mobile" : isTablet ? "tablet" : "desktop"
      }`}
    >
      {isMobile ? <MobileNavigation /> : <DesktopSidebar />}
      <main className="content">{/* Content */}</main>
    </div>
  );
};
```

#### PWA Configuration

```javascript
// public/sw.js (Service Worker)
const CACHE_NAME = 'veritime-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
  );
});

// public/manifest.json
{
  "name": "Veritime Workforce Management",
  "short_name": "Veritime",
  "description": "Employee attendance and workforce management system",
  "start_url": "/",
  "display": "standalone",
  "theme_color": "#000000",
  "background_color": "#ffffff",
  "icons": [
    {
      "src": "icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### 6. Testing Strategies

#### Unit Testing Examples

```javascript
// __tests__/components/LoginForm.test.js
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import LoginForm from "../components/LoginForm";
import * as apiService from "../services/apiService";

jest.mock("../services/apiService");

describe("LoginForm", () => {
  beforeEach(() => {
    localStorage.clear();
    jest.clearAllMocks();
  });

  test("renders login form correctly", () => {
    render(<LoginForm />);

    expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /login/i })).toBeInTheDocument();
  });

  test("handles successful login", async () => {
    const mockResponse = {
      accessToken: "mock-token",
      role: "admin",
      empId: "123",
    };

    apiService.postRequest.mockResolvedValue(mockResponse);

    render(<LoginForm />);

    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: "password123" },
    });

    fireEvent.click(screen.getByRole("button", { name: /login/i }));

    await waitFor(() => {
      expect(localStorage.getItem("accessToken")).toBe("mock-token");
      expect(localStorage.getItem("role")).toBe("admin");
    });
  });

  test("displays error on failed login", async () => {
    apiService.postRequest.mockRejectedValue(new Error("Invalid credentials"));

    render(<LoginForm />);

    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: "wrongpassword" },
    });

    fireEvent.click(screen.getByRole("button", { name: /login/i }));

    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
    });
  });
});
```

#### Integration Testing

```javascript
// __tests__/integration/EmployeeWorkflow.test.js
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import App from "../App";
import * as apiService from "../services/apiService";

jest.mock("../services/apiService");

describe("Employee Management Workflow", () => {
  beforeEach(() => {
    // Mock authenticated user
    localStorage.setItem("accessToken", "mock-token");
    localStorage.setItem("role", "admin");
    localStorage.setItem("empId", "123");
  });

  test("complete employee creation workflow", async () => {
    // Mock API responses
    apiService.getRequest.mockImplementation((url) => {
      if (url.includes("sidemenu")) {
        return Promise.resolve([
          {
            id: 1,
            menu_label: "Masters",
            menu_path: "/masters",
            is_active: true,
          },
        ]);
      }
      if (url.includes("departments")) {
        return Promise.resolve([{ id: 1, name: "IT Department" }]);
      }
    });

    apiService.postRequest.mockResolvedValue({ success: true });

    render(
      <BrowserRouter>
        <App />
      </BrowserRouter>
    );

    // Navigate to employee creation
    fireEvent.click(screen.getByText(/masters/i));
    fireEvent.click(screen.getByText(/create employee/i));

    // Fill form
    fireEvent.change(screen.getByLabelText(/employee name/i), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: "<EMAIL>" },
    });

    // Submit form
    fireEvent.click(screen.getByRole("button", { name: /create/i }));

    await waitFor(() => {
      expect(
        screen.getByText(/employee created successfully/i)
      ).toBeInTheDocument();
    });
  });
});
```

This comprehensive documentation provides developers with detailed insights into the Veritime frontend application flow, covering authentication, navigation, state management, advanced features, and testing strategies. It serves as a complete guide for understanding the system architecture and implementing new features.
