"use client"
import React, { useEffect, useState } from 'react'
import Layout from "@/components/Layout";
import ApprovalsDashboardBootStrap from '../../../components/Approvalsdashboard1/ApprovalsDashboardBootStrap'
const Approvals = () => {
    const [isMounted, setIsMounted] = useState(false);
    useEffect(() => {
    setIsMounted(true);
    }, []);
      
    if (!isMounted) return null;
    return (
        <Layout>
            <ApprovalsDashboardBootStrap toggleMenu={undefined} expanded={undefined} isFromHistory={undefined} />
        </Layout>
    );
}

export default Approvals;
