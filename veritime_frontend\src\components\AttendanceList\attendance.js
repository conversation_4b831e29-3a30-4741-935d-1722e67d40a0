import React, { useState, useEffect } from "react";
import "../../css/style.css";
import { Container, Row, Col } from "react-bootstrap";
import { useTheme } from "@material-ui/core/styles";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import SwipeableViews from "react-swipeable-views";
import moment from "moment";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import TeamAttendance from "./teamAttendance";
import MyAttendance from "./myAttendance";
import AttendanceRegularization from "./attendanceRegularization";
// import AttendanceRegularization2 from "./attendanceRegularization";
import "./teamAttendance.css";
import { getRequest, getRequestWithSecurity } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import Select from "react-select";
import { showError<PERSON>lert } from "@/services/alertService.js";

function TabPanel(props) {
  const { children, value, index, ...other } = props;
  return (
    <div
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";

ChartJS.register(ArcElement, Tooltip, Legend);

function a11yProps(index) {
  return {
    id: `full-width-tab-${index}`,
    "aria-controls": `full-width-tabpanel-${index}`,
  };
}

const AttendanceList = () => {
  const [role, setRole] = useState("");
  // Set initial dates in DD-MM-YYYY format
  const [fromDate, setFromDate] = useState(moment().format("YYYY-MM-DD"));
  const [toDate, setToDate] = useState(moment().format("YYYY-MM-DD"));

  const [goButton, setGoButton] = useState(false);
  const [employees, setEmployees] = useState([]);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [statusFilter, setStatusFilter] = useState("All");

  // useEffect(() => {
  //   if (typeof window !== "undefined") {
  //     const storedRole = localStorage.getItem("role");
  //     if (storedRole) {
  //       setRole(storedRole);
  //     }

  //     fetchEmployees();
  //   }
  // }, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedRole = localStorage.getItem("role");
      if (storedRole) {
        setRole(storedRole);
      }

      // Get URL Parameters
      const params = new URLSearchParams(window.location.search);
      const tab = params.get("tab");
      const type = params.get("type");

      fetchEmployees();
      // Check if URL contains the required conditions
      if (tab === "TeamAttendance" && (type === "Present" || type === "ClockedIn")) {
        setValue(2);
        setStatusFilter("Present");
        // Set dates to one day prior (yesterday)
        const yesterday = moment().subtract(1, "days").format("YYYY-MM-DD");
        setFromDate(yesterday);
        setToDate(yesterday);
        setGoButton(true);
      }

      //04.04.25
      // if (tab === "TeamAttendance" && type === "ClockedIn") {
      //   setStatusFilter("Present");
      //   setFromDate(moment().format("YYYY-MM-DD"));
      //   setToDate(moment().format("YYYY-MM-DD"));
      //   setGoButton(true);
      // }
    }
  }, []);

  const fetchEmployees = async () => {
    try {
      const queryParams = window.location.search;
      const empId = new URLSearchParams(queryParams).get("empId");

      if (empId) {
        try {
          const data = await getRequest(
            `${API_URLS.TEAM_MEMBERS}/${empId}`,
            true
          );

          const employeesData = data.hierarchy.map((emp) => ({
            value: emp.emp_id,
            label: emp.name,
          }));

          setEmployees(employeesData);
        } catch (error) {
          // console.error("Error fetching employee details:", error);
          // alert("Error fetching employee details.");
        }
      } else {
        const currentUserData = await getRequestWithSecurity(
          `${API_URLS.PROFILE_DETAILS}`
        );
        setRole(currentUserData?.role);
        const currentEmpId = currentUserData?.empId;

        if (currentEmpId) {
          const teamData = await getRequest(
            `${API_URLS.TEAM_MEMBERS}/${currentEmpId}`,
            true
          );

          const employeesData = teamData.team.map((emp) => ({
            value: emp.emp_id,
            label: emp.name,
          }));

          setEmployees(employeesData);
        }
      }
    } catch (error) {
      // console.error("Error fetching details:", error);
    }
  };

  const theme = useTheme();
  const [value, setValue] = useState(0);
  const handleEmployeeChange = (selected) => {
    setSelectedEmployee(selected);
  };

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleChangeIndex = (index) => {
    setValue(index);
  };

  const handleStatusChange = (selected) => {
    setStatusFilter(selected.value);
  };

  const handleDateChange = (e, setter) => {
    const dateValue = e.target.value; // Input value (YYYY-MM-DD from native picker)

    // Ensure it's valid and store it in "YYYY-MM-DD" format
    if (moment(dateValue, "YYYY-MM-DD", true).isValid()) {
      setter(dateValue); // Save as "YYYY-MM-DD"
    } else {
      showErrorAlert("Invalid date. Please select a valid date.");
    }
  };

  const filterByDate = () => {
    if (fromDate && toDate) {
      const from = moment(fromDate, "YYYY-MM-DD");
      const to = moment(toDate, "YYYY-MM-DD");

      if (from.isAfter(to)) {
        showErrorAlert("From Date cannot be later than To Date");
        return;
      }

      console.log("Sending to API: ", { fromDate, toDate });
      setGoButton((prevState) => !prevState);
    } else {
      showErrorAlert("Please select both From Date and To Date");
    }
  };

  const resetFilters = () => {
    setFromDate(moment().format("YYYY-MM-DD"));
    setToDate(moment().format("YYYY-MM-DD"));
    setSelectedEmployee(null);
    setGoButton(false);
  };

  return (
    <Container fluid style={{ marginTop: "40px" }}>
      <h3 style={{ marginBottom: "20px", marginLeft: "15px" }}>
        Attendance List
      </h3>
      <div
        className="filter-container-center"
        style={{
          marginTop: "15px",
          justifyContent: "end",
          marginRight: "29px",
        }}
      >
        {/* From Date Input */}
        <label style={{ marginRight: "8px" }}>From Date</label>
        <input
          type="date"
          value={fromDate} // Already in "YYYY-MM-DD"
          onChange={(e) => handleDateChange(e, setFromDate)}
          style={{
            width: "200px",
            padding: "8px",
            fontSize: "14px",
            borderRadius: "4px",
            border: "1px solid #ccc",
            marginRight: "11px",
          }}
        />

        <label style={{ marginRight: "8px" }}>To Date</label>
        <input
          type="date"
          value={toDate} // Already in "YYYY-MM-DD"
          onChange={(e) => handleDateChange(e, setToDate)}
          style={{
            width: "180px",
            padding: "8px",
            fontSize: "14px",
            borderRadius: "4px",
            border: "1px solid #ccc",
            marginRight: "10px",
          }}
        />

        {value === 2 && (
          <div className="status-filter" style={{ marginTop: "-24px" }}>
            <label htmlFor="statusSelect"></label>
            <Select
              id="statusSelect"
              value={{ value: statusFilter, label: statusFilter }}
              onChange={handleStatusChange}
              options={[
                { value: "All", label: "All" },
                { value: "Present", label: "Present" },
                { value: "Absent", label: "Absent" },
              ]}
              placeholder="Select Status"
            />
          </div>
        )}
        {value === 2 && (
          <div className="employee-filter" style={{ marginTop: "-25px" }}>
            <label htmlFor="employeeSelect"></label>
            <Select
              id="employeeSelect"
              value={selectedEmployee}
              onChange={handleEmployeeChange}
              options={employees}
              isClearable
              placeholder="Select Employee"
            />
          </div>
        )}
        <button
          className="go-button"
          style={{ width: "100px" }}
          onClick={filterByDate}
        >
          Apply
        </button>
        {value === 2 && (
          <button
            onClick={resetFilters}
            className="go-button"
            style={{
              width: "83px",
              maxHeight: "80%",
              marginLeft: "3px",
            }}
          >
            Reset
          </button>
        )}
      </div>
      <Row className="my-0">
        <Col
          md={2}
          className="p-0"
          style={{ backgroundColor: "#6097b4", marginTop: "25px" }}
        >
          <Tabs
            value={value}
            onChange={handleChange}
            indicatorColor="secondary"
            textColor="inherit"
            variant="fullWidth"
            aria-label="full width tabs example"
            className="tab-pannel-test"
            style={{ backgroundColor: "#6097b4" }}
          >
            <Tab
              label="Attendance"
              {...a11yProps(0)}
              style={{
                color: "#000",
                backgroundColor: "#ccc",
                boxShadow: "none",
                // fontFamily: "inherit",
              }}
            />
            <Tab
              label="Audit"
              {...a11yProps(1)}
              style={{
                color: "#000",
                backgroundColor: "#ccc",
                boxShadow: "none",
                // fontFamily: "inherit",
              }}
            />
            {role !== "employee" && (
              <Tab
                label="Team Attendance"
                {...a11yProps(2)}
                style={{
                  color: "#000",
                  backgroundColor: "#ccc",
                  boxShadow: "none",
                }}
              />
            )}
            {/* {role !== "employee" && (
              <Tab
                label="Team Attendance"
                {...a11yProps(2)}
                style={{
                  color: "#000",
                  backgroundColor: "#ccc",
                  boxShadow: "none",
                }}
              />
            )} */}
          </Tabs>
        </Col>
        <Col md={10}>
          <SwipeableViews
            axis={theme.direction === "rtl" ? "x-reverse" : "x"}
            index={value}
            onChangeIndex={handleChangeIndex}
          >
            <TabPanel value={value} index={0} dir={theme.direction}>
              <TeamAttendance
                fromDate={fromDate}
                toDate={toDate}
                goButton={goButton}
              />
            </TabPanel>
            <TabPanel value={value} index={1} dir={theme.direction}>
              <MyAttendance
                fromDate={moment(fromDate).format("YYYY-MM-DDTHH:mm:ss")}
                toDate={moment(toDate).format("YYYY-MM-DDTHH:mm:ss")}
                goButton={goButton}
              />
            </TabPanel>
            <TabPanel value={value} index={2} dir={theme.direction}>
              <AttendanceRegularization
                fromDate={fromDate}
                toDate={toDate}
                goButton={goButton}
                statusFilter={statusFilter}
                selectedEmployee={selectedEmployee}
              />
              {/* Render Regularization Tab */}
            </TabPanel>
            {/* <TabPanel value={value} index={3} dir={theme.direction}>
              <AttendanceRegularization2
                fromDate={fromDate}
                toDate={toDate}
                goButton={goButton}
                statusFilter={statusFilter}
                selectedEmployee={selectedEmployee}
              />{" "} */}
            {/* Render Regularization Tab */}
            {/* </TabPanel> */}
          </SwipeableViews>
        </Col>
      </Row>
    </Container>
  );
};

export default AttendanceList;
