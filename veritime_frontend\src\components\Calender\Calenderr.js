import React, { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import './Calender.css';
import moment from 'moment';

const Calenderr = () => {
    const [showCalendar, setShowCalendar] = useState(false);
    const [fromDate, setFromDate] = useState(new Date());
    const [toDate, setToDate] = useState(new Date());
    const [displayText, setDisplayText] = useState("Today");

    const handleApply = () => {
        setDisplayText(`${moment(fromDate).format('MMM DD, YY')} - ${moment(toDate).format('MMM DD, YY')}`);
        setShowCalendar(false);
    };

    return (
        <div className="col-12 col-md-3 d-flex position-relative grid-layout justify-content-end">
            <div className='d-flex flex-row bg-white p-3 rounded-3 '>
            <img src="/image/Icon.png" alt="Filter Date" className="me-2" style={{ width: '25px', height: '25px'}} />
            <span className="d-flex flex-column fs-7">{displayText}</span>
            <img
                src="/image/down.png"
                alt="Expand"
                className="filter-down-icon"
                style={{ width: '35px', height: '20px',paddingLeft:"15px" }}
                onClick={() => setShowCalendar(!showCalendar)}
            />
            {showCalendar && (
                <div className="calendar-container p-3">
                    <div className="row">
                        <div className="col-12 col-md-6 mb-3">
                            <label className="form-label">From</label>
                            <div className="input-group">
                                <DatePicker
                                    selected={fromDate}
                                    onChange={date => setFromDate(date)}
                                    className="form-control "
                                />
                            </div>
                        </div>
                        <div className="col-12 col-md-6 mb-3">
                            <label className="form-label">To</label>
                            <div className="input-group">
                                <DatePicker
                                    selected={toDate}
                                    onChange={date => setToDate(date)}
                                    className="form-control"
                                />
                            </div>
                        </div>
                        <div className="col-12 text-left">
                            <button className="btn btn-primary" onClick={handleApply}>Apply</button>
                        </div>
                    </div>
                </div>
            )}
            </div>
          
        </div>
    );
}

export default Calenderr;
