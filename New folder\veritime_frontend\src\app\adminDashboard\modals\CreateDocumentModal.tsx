import React, { useState, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";
import { postRequest, putRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showSuccessAlert2 } from "@/services/alertService";

const CreateDocumentModal = ({ show, handleClose, onSave, document }: any) => {
  const [formData, setFormData] = useState({
    name: document?.name || "",
    type: document?.type || "",
    isMandatory: document?.isMandatory || false,
  });

  const [errors, setErrors] = useState<any>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (document) {
      // Reset form data if document is passed (for editing)
      setFormData({
        name: document?.name || "",
        type: document?.type || "",
        isMandatory: document?.isMandatory || false,
      });
    }
  }, [document]);

  const handleChange = (e: any) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const validateForm = () => {
    const newErrors: any = {};
    if (!formData.name) {
      newErrors.name = "Please enter a name";
    }
    if (!formData.type) {
      newErrors.type = "Please select a type";
    }
    setErrors(newErrors);
    return newErrors;
  };

  const handleSave = async () => {
    const formErrors = validateForm();
    if (Object.keys(formErrors).length === 0) {
      setLoading(true);
      try {
        const payload = {
          name: formData.name,
          type: formData.type,
          isMandatory: formData.isMandatory,
        };

        if (document) {
          // Edit existing document
          const url = `${API_URLS.UPDATE_DOCUMENT}${document.id}`;
          await putRequest(url, payload);
          showSuccessAlert2("Document updated successfully!").then(() => {
            onSave({ ...formData, id: document.id }); // Pass updated data to parent
            handleClose(); // Close the modal
          });
        } else {
          // Create new document
          const response = await postRequest(API_URLS.CREATE_DOCUMENT, payload);
          showSuccessAlert2("Document created successfully!").then(() => {
            // onSave({ ...formData, id: response.id }); // Pass new data to parent
            onSave({
              ...formData,
              id: response.id || response.documentId || new Date().getTime(),
            });

            handleClose(); // Close the modal
          });
        }
      } catch (error) {
        setErrors({
          apiError: "Failed to save the document. Please try again.",
        });
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>
          {document ? "Edit Document" : "Create Document"}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          <Form.Group controlId="nameInput">
            <Form.Label>
              Name <span className="text-danger">*</span>
            </Form.Label>
            <Form.Control
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              isInvalid={!!errors.name}
              style={{ marginBottom: "20px" }}
            />
            <Form.Control.Feedback type="invalid">
              {errors.name}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group controlId="selectType">
            <Form.Label>
              Select Type <span className="text-danger">*</span>
            </Form.Label>
            <Form.Control
              as="select"
              name="type"
              value={formData.type}
              onChange={handleChange}
              isInvalid={!!errors.type}
              style={{ marginBottom: "20px" }}
            >
              <option value="">Select...</option>
              <option value="Identification">Identification</option>
              <option value="Address Verification">Address Verification</option>
              <option value="Tax Identification">Tax Identification</option>
              <option value="Previous Employment Proof">
                Previous Employment Proof
              </option>
            </Form.Control>
            <Form.Control.Feedback type="invalid">
              {errors.type}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group controlId="isMandatory">
            <Form.Check
              type="checkbox"
              label="Is Mandatory"
              name="isMandatory"
              checked={formData.isMandatory}
              onChange={handleChange}
            />
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSave} disabled={loading}>
          {loading ? "Saving..." : "Save"}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateDocumentModal;
