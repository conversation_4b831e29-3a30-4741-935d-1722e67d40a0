import React, { useState, useEffect, forwardRef } from "react";
import { Container, Row, Col, Button, ButtonGroup } from "react-bootstrap";
import { postRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import { Bar } from "react-chartjs-2";
import "chart.js/auto";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { FiCalendar } from "react-icons/fi";
import { colors } from "@constants/colors";
import { subDays, startOfMonth, endOfMonth, subMonths, startOfWeek, endOfWeek } from "date-fns";
import { format } from "date-fns"; // Importing format from date-fns for correct date formatting

const WeeklyWorkingHours = () => {
  const [view, setView] = useState("week"); // Default view is 'week'
  const [selectedOption, setSelectedOption] = useState("Week");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [totalWorkingHours, setTotalWorkingHours] = useState(0);
  const [expectedWorkingHours, setExpectedWorkingHours] = useState(0);
  const [errorMessage, setErrorMessage] = useState("");

  // Fetch calendar data based on selected date range
  const fetchCalendarData = async (start, end) => {
    try {
      const response = await postRequest(API_URLS.WEEKLY_WORKING_HOUR, {
        startDate: start,
        endDate: end,
      });

      setTotalWorkingHours(response.totalWorkingHours);
      setExpectedWorkingHours(response.expectedWorkingHours);
      setErrorMessage(""); // Clear any previous error message
    } catch (error) {
      setErrorMessage("Failed to fetch calendar data. Please try again later.");
    }
  };

  // Handle the option change (Day, Week, Month)
  const handleOptionChange = (option) => {
    setSelectedOption(option);
    const today = new Date();

    let calculatedStartDate, calculatedEndDate;

    if (option === "Day") {
      calculatedStartDate = subDays(today, 1);
      calculatedEndDate = calculatedStartDate;
    } else if (option === "Week") {
      calculatedStartDate = startOfWeek(subDays(today, 7), { weekStartsOn: 0 }); // Week starts on Sunday
      calculatedEndDate = endOfWeek(subDays(today, 7), { weekStartsOn: 1 }); // End of the previous week (Sunday)
    } else if (option === "Month") {
      calculatedStartDate = startOfMonth(subMonths(today, 1));
      calculatedEndDate = endOfMonth(subMonths(today, 1));
    }

    setStartDate(calculatedStartDate);
    setEndDate(calculatedEndDate);

    // Fetch data when the view option changes
    fetchCalendarData(format(calculatedStartDate, "yyyy-MM-dd"), format(calculatedEndDate, "yyyy-MM-dd"));
  };

  // Custom date picker input component
  const CustomDateInput = forwardRef(({ value, onClick }, ref) => (
    <div className="input-group">
      <input
        ref={ref}
        type="text"
        className="form-control ch1"
        value={value}
        onClick={onClick}
        placeholder="Select Dates"
        readOnly
        style={{
          borderTopRightRadius: 0,
          borderBottomRightRadius: 0,
          width: 120,
        }}
      />
      <button
        type="button"
        className="btn btn-outline-secondary btnTxt1"
        onClick={onClick}
        style={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0 }}
      >
        <FiCalendar />
      </button>
    </div>
  ));

  // Bar chart data
  const data = {
    labels: ["Actual Hours", "Expected Hours"],
    datasets: [
      {
        label: "Hours",
        data: [totalWorkingHours, expectedWorkingHours],
        backgroundColor: ["#77a8f9", "#1E488E"],
        barThickness: 40,
        borderRadius: 20,
      },
    ],
  };

  // Bar chart options
  const options = {
    indexAxis: "y",
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          display: false,
        },
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 16,
            weight: "bold",
          },
        },
      },
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    barPercentage: 1,
    categoryPercentage: 1,
  };

  useEffect(() => {
    if (startDate && endDate) {
      fetchCalendarData(format(startDate, "yyyy-MM-dd"), format(endDate, "yyyy-MM-dd"));
    }
  }, [startDate, endDate]);

  // Set the default view on initial render
  useEffect(() => {
    handleOptionChange("Week"); // Set default option to "Week"
  }, []);

  return (
    <div
      className="col col-lg-12 col-md-12"
      style={{
        backgroundColor: colors.red14,
        marginBottom: "20px",
        borderRadius: "10px",
        boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
        padding: 30,
        width: "96.38%",
        marginLeft: "0.6%",
      }}
    >
      <Row className="mb-4 align-items-center">
        <Col>
          <h4 className="cardHeaderTxt">Weekly Working Hours</h4>
        </Col>
        <Col className="d-flex justify-content-end align-items-center">
          <div className="me-2" style={{ width: "161px" }}>
            <DatePicker
              selected={startDate}
              onChange={(date) => setStartDate(date)}
              customInput={<CustomDateInput />}
              dateFormat="dd/MM/yyyy"
            />
          </div>

          <div className="m-2" style={{ width: "170px" }}>
            <DatePicker
              selected={endDate}
              onChange={(date) => setEndDate(date)}
              customInput={<CustomDateInput />}
              dateFormat="dd/MM/yyyy"
            />
          </div>
          <ButtonGroup>
            <Button
              variant={selectedOption === "Day" ? "primary" : "outline-primary"}
              className="me-1 btnTxt1"
              onClick={() => handleOptionChange("Day")}
            >
              Day
            </Button>
            <Button
              variant={selectedOption === "Week" ? "primary" : "outline-primary"}
              className="me-1 btnTxt1"
              onClick={() => handleOptionChange("Week")}
            >
              Week
            </Button>
            <Button
              variant={selectedOption === "Month" ? "primary" : "outline-primary"}
              onClick={() => handleOptionChange("Month")}
              className="me-1 btnTxt1"
            >
              Month
            </Button>
          </ButtonGroup>
        </Col>
      </Row>

      {errorMessage && (
        <Row>
          <Col>
            <div style={{ color: "red", fontSize: "14px" }}>{errorMessage}</div>
          </Col>
        </Row>
      )}

      <Row className="align-items-center">
        <Col xs={12} md={10}>
          <Bar data={data} options={options} />
        </Col>
        <Col xs={12} md={2} className="text-center">
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                marginBottom: "35px",
              }}
            >
              <span
                style={{
                  fontSize: "16px",
                  fontWeight: "bold",
                  color: "#77a8f9",
                }}
              >
                {totalWorkingHours} hours
              </span>
            </div>
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
              }}
            >
              <span
                style={{
                  fontSize: "16px",
                  fontWeight: "bold",
                  color: "#1E488E",
                }}
              >
                {expectedWorkingHours} hours
              </span>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default WeeklyWorkingHours;
