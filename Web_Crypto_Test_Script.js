// Web Crypto API Test Script for Veritime Login
// Use this in Postman pre-request script to test the exact encryption method

// Your credentials
const aesPassword = "uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabqAU=";
const aesSalt = "uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabBA==";

// Test payload
const testPayload = {
    username: "<EMAIL>",
    password: "raj456123"
};

// Helper functions
const base64ToBytes = (base64) => {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
};

const bytesToBase64 = (bytes) => {
    let binaryString = '';
    for (let i = 0; i < bytes.length; i++) {
        binaryString += String.fromCharCode(bytes[i]);
    }
    return btoa(binaryString);
};

const stringToBytes = (str) => new TextEncoder().encode(str);
const bytesToString = (bytes) => new TextDecoder().decode(bytes);

// Constants matching frontend
const IV_LENGTH = 12;
const ITERATIONS = 100000;
const KEY_SIZE = 256;

async function testWebCryptoEncryption() {
    try {
        console.log("=== WEB CRYPTO API TEST ===");
        console.log("Original payload:", JSON.stringify(testPayload));
        
        const jsonString = JSON.stringify(testPayload);
        
        // Step 1: Derive key using PBKDF2
        const passwordBytes = stringToBytes(aesPassword);
        const saltBytes = base64ToBytes(aesSalt);
        
        console.log("Password bytes length:", passwordBytes.length);
        console.log("Salt bytes length:", saltBytes.length);
        
        // Import password as key material
        const keyMaterial = await crypto.subtle.importKey(
            'raw',
            passwordBytes,
            { name: 'PBKDF2' },
            false,
            ['deriveBits', 'deriveKey']
        );
        
        // Derive the actual encryption key
        const key = await crypto.subtle.deriveKey(
            {
                name: 'PBKDF2',
                salt: saltBytes,
                iterations: ITERATIONS,
                hash: 'SHA-256',
            },
            keyMaterial,
            { name: 'AES-GCM', length: KEY_SIZE },
            false,
            ['encrypt', 'decrypt']
        );
        
        console.log("✅ Key derived successfully");
        
        // Step 2: Generate random IV
        const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));
        console.log("Generated IV length:", iv.length);
        
        // Step 3: Encrypt data using AES-GCM
        const encryptedData = await crypto.subtle.encrypt(
            { name: 'AES-GCM', iv },
            key,
            stringToBytes(jsonString)
        );
        
        console.log("✅ Data encrypted with AES-GCM");
        
        // Step 4: Combine IV + encrypted data
        const combinedArray = new Uint8Array(iv.length + encryptedData.byteLength);
        combinedArray.set(iv);
        combinedArray.set(new Uint8Array(encryptedData), iv.length);
        
        // Step 5: Convert to base64
        const encryptedBase64 = bytesToBase64(combinedArray);
        
        console.log("Final encrypted data:");
        console.log("Length:", encryptedBase64.length);
        console.log("Data:", encryptedBase64);
        
        // Test decryption to verify
        console.log("\n=== TESTING DECRYPTION ===");
        
        // Decode the encrypted data
        const encryptedBytes = base64ToBytes(encryptedBase64);
        
        // Extract IV and encrypted data
        const extractedIv = encryptedBytes.slice(0, IV_LENGTH);
        const extractedEncryptedData = encryptedBytes.slice(IV_LENGTH);
        
        console.log("Extracted IV length:", extractedIv.length);
        console.log("Extracted encrypted data length:", extractedEncryptedData.length);
        
        // Decrypt using AES-GCM
        const decryptedData = await crypto.subtle.decrypt(
            { name: 'AES-GCM', iv: extractedIv },
            key,
            extractedEncryptedData
        );
        
        // Convert to string and parse JSON
        const decryptedText = bytesToString(new Uint8Array(decryptedData));
        const decryptedJson = JSON.parse(decryptedText);
        
        console.log("✅ Decryption successful:");
        console.log("Decrypted text:", decryptedText);
        console.log("Decrypted JSON:", decryptedJson);
        console.log("Round trip success:", JSON.stringify(decryptedJson) === JSON.stringify(testPayload));
        
        // Create request body
        const requestBody = {
            encryptedData: encryptedBase64
        };
        
        // Update Postman request
        pm.request.body.mode = 'raw';
        pm.request.body.raw = JSON.stringify(requestBody, null, 2);
        
        pm.request.headers.upsert({
            key: 'Content-Type',
            value: 'application/json'
        });
        
        console.log("\n✅ Postman request updated with Web Crypto encrypted payload");
        console.log("Request body:", JSON.stringify(requestBody, null, 2));
        
        return encryptedBase64;
        
    } catch (error) {
        console.error("❌ Web Crypto encryption failed:", error);
        throw error;
    }
}

// Execute the test
testWebCryptoEncryption().then(result => {
    console.log("\n🎉 Web Crypto API encryption test completed successfully!");
    console.log("Encrypted payload ready for backend");
}).catch(error => {
    console.error("\n💥 Web Crypto API encryption test failed:", error);
});
