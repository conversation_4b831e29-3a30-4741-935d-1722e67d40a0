"use client";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Form } from "react-bootstrap";
import Layout from "../../components/Layout";
import React, { useEffect, useState } from "react";
import "./roster.css";
import EmployeeRoster from "../../modules/RosterShift/shift";
import DateSelector from "../../modules/RosterShift/RosterCalender";
import { colors } from "@constants/colors";
import { getRequest } from "@/services/apiService";
// import Select from "react-select"; // Import react-select
// import { SingleValue } from "react-select";

import Select, { MultiValue, SingleValue } from "react-select"; // Import multi and single value types if needed

import { API_URLS } from "@/constants/apiConstants";
import moment from "moment";
import { showErrorAlert } from "@/services/alertService";

const Roaster = () => {
  const [isDropdownVisible, setDropdownVisible] = useState(false);
  const [isDropdownVisibleDesignation, setDropdownVisibleDesignation] =
    useState(false);
  const [isDropdownVisibleSchedule, setDropdownVisibleSchedule] =
    useState(false);

  // // const [selectedDept, setSelectedDept] = useState(null);
  // const [selectedDesignation, setSelectedDesignation] =
  //   useState("Select Designation");
  // const [selectedSchedule, setSelectedSchedule] = useState("Select Schedule");

  //const [dept, setdept] = useState(["HR", "Dept"]);

  const [selectedDesignation, setSelectedDesignation] = useState<{
    value: string;
    label: string;
  } | null>(null);

  const [selectedSchedule, setSelectedSchedule] = useState<{
    value: string;
    label: string;
  } | null>(null);

  const [dept, setdept] = useState<{ value: string; label: string }[]>([
    { value: "HR", label: "HR" },
    { value: "Dept", label: "Dept" },
  ]);
  const [designation, setDesignation] = useState<
    { value: string; label: string }[]
  >([]);

  const [schedule, setSchedule] = useState<{ value: string; label: string }[]>([
    { value: "09-07", label: "09-07" },
    { value: "06-02", label: "06-02" },
    { value: "01-09", label: "01-09" },
  ]);

  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [fetchRosterData, setFetchRosterData] = useState(false);
  const [selectedDept, setSelectedDept] = useState<{
    value: string;
    label: string;
  } | null>(null);

  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const departmentsData = await getRequest(API_URLS.GET_DEPARTMENT);
        // if (departmentsData) setdept(departmentsData);
        if (departmentsData)
          setdept(departmentsData.map((d: string) => ({ value: d, label: d })));

        // const designationsData = await getRequest(API_URLS.DESIGNATION_LOOKUP);
        // if (designationsData) setdesignation(designationsData);
        const designationsData = await getRequest(API_URLS.DESIGNATION_LOOKUP);
        if (designationsData)
          setDesignation(
            designationsData.map((d: any) => ({
              value: d.designationName,
              label: d.designationName,
            }))
          );
        // const scheduleData = await getRequest(API_URLS.GET_SCHDEULES);
        // if (scheduleData) setdesignation(scheduleData);
      } catch (error) { }
    };

    fetchFilterOptions();
  }, []);

  const toggleDropdownDept = () => {
    setDropdownVisible(!isDropdownVisible);
  };

  const toggleDropdownDesignation = () => {
    setDropdownVisibleDesignation(!isDropdownVisibleDesignation);
  };

  const toggleDropdownSchedule = () => {
    setDropdownVisibleSchedule(!isDropdownVisibleSchedule);
  };

  const handleOptionDept = (dept: any) => {
    setSelectedDept(dept);
    setDropdownVisible(false);
  };

  const handleDeptChange = (
    selectedOption: SingleValue<{ value: string; label: string }>
  ) => {
    setSelectedDept(selectedOption);
  };

  const handleDesignationChange = (
    selectedOption: SingleValue<{ value: string; label: string }>
  ) => {
    setSelectedDesignation(selectedOption);
  };

  const handleScheduleChange = (
    selectedOption: SingleValue<{ value: string; label: string }>
  ) => {
    setSelectedSchedule(selectedOption);
  };

  const handleOptionDesignation = (designation: any) => {
    setSelectedDesignation(designation);
    setDropdownVisibleDesignation(false);
  };

  const handleOptionschedule = (schedule: any) => {
    setSelectedSchedule(schedule);
    setDropdownVisibleSchedule(false);
  };

  const handleGoClick = () => {
    setFetchRosterData(!fetchRosterData);
  };

  // // Handle clearing selection on Backspace
  // const handleKeyDown = (e: React.KeyboardEvent) => {
  //   if (e.key === "Backspace") {
  //     if (selectedDept) setSelectedDept(null);
  //     if (selectedDesignation) setSelectedDesignation(null);
  //     if (selectedSchedule) setSelectedSchedule(null);
  //   }
  // };

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Layout>
      <div>
        <h3
          className="h3 text"
          style={{ marginTop: "20px", marginLeft: "25px" }}
        >
          Roster
        </h3>

        <div className="grid-layout-container-roaster">
          <Row
            className="controls p-2"
            style={{
              alignItems: "center", // Align items vertically in the center
              gap: "10px", // Add gap between components
              marginBottom: "15px", // bottom margin from row to keep components aligned
            }}
          >
            {/* <Col md="auto" style={{ display: "flex", alignItems: "center" }}>
            <Select
              value={selectedDept}
              onChange={handleDeptChange}
              options={dept}
              placeholder="Select Department"
              isClearable
              classNamePrefix="react-select"
              styles={{
                control: (base) => ({
                  ...base,
                  width: "190px", // Set fixed width for dropdown options
                  height: "40px", // Ensure consistent height
                  fontSize: "14px",

                  border: "1px solid #ddd",
                  borderRadius: "6px",
                  boxShadow: "none",
                }),
              }}
            />
          </Col>

          <Col md="auto" style={{ display: "flex", alignItems: "center" }}>
            <Select
              value={selectedDesignation}
              onChange={handleDesignationChange}
              options={designation}
              placeholder="Select Designation"
              isClearable
              classNamePrefix="react-select"
              styles={{
                control: (base) => ({
                  ...base,
                  width: "190px", // Set fixed width for dropdown options
                  height: "40px", // Ensure consistent height
                  fontSize: "14px",

                  border: "1px solid #ddd",
                  borderRadius: "6px",
                  boxShadow: "none",
                }),
              }}
            />
          </Col>

          <Col md="auto" style={{ display: "flex", alignItems: "center" }}>
            <Select
              value={selectedSchedule}
              onChange={handleScheduleChange}
              options={schedule}
              placeholder="Select Schedule"
              isClearable
              classNamePrefix="react-select"
              styles={{
                control: (base) => ({
                  ...base,
                  width: "170px", // Set fixed width for dropdown options
                  height: "40px", // Ensure consistent height
                  fontSize: "14px",

                  border: "1px solid #ddd",
                  borderRadius: "6px",
                  boxShadow: "none",
                }),
              }}
            />
          </Col> */}

            {/* <Col className="header-menu-roaster col-lg-2 col-md-3 col-sm-6 col-12 position-relative">
            <p className="m-0 font-weight-bold h8">{selectedDept}</p>
            <img
              className="ml-2"
              style={{ width: "18px", height: "15px", cursor: "pointer" }}
              src="./image/downArrow.png"
              onClick={toggleDropdownDept}
              alt="Dropdown Arrow"
            />
            {isDropdownVisible && (
              <div
                className="dropdown-menu show position-absolute"
                style={{
                  top: "100%",
                  left: 0,
                  zIndex: 1000,
                  backgroundColor: colors.blue13,
                  width: "98%",
                }}
              >
                {dept.map((dept, index) => (
                  <li
                    key={index}
                    className="px-3 py-2 cursor-pointer"
                    onClick={() => handleOptionDept(dept)}
                  >
                    {dept}
                  </li>
                ))}
              </div>
            )}
          </Col> */}

            {/* <Col className="header-menu-roaster col-lg-2 col-md-3 col-sm-6 col-12 position-relative">
            <p className="m-0 font-weight-bold h8">{selectedDesignation}</p>
            <img
              className="ml-2"
              style={{ width: "18px", height: "15px", cursor: "pointer" }}
              src="./image/downArrow.png"
              onClick={toggleDropdownDesignation}
              alt="Dropdown Arrow"
            />
            {isDropdownVisibleDesignation && (
              <div
                className="dropdown-menu show position-absolute"
                style={{
                  top: "100%",
                  left: 0,
                  zIndex: 1000,
                  backgroundColor: colors.blue13,
                  width: "98%",
                }}
              >
                {designation.map((designation: any, index) => (
                  <li
                    key={index}
                    className="px-3 py-2 cursor-pointer"
                    onClick={() =>
                      handleOptionDesignation(designation.designationName)
                    }
                  >
                    {designation.designationName}
                  </li>
                ))}
              </div>
            )}
          </Col> */}
            {/* <Col className="header-menu-roaster col-lg-2 col-md-3 col-sm-6 col-12 position-relative">
            <p className="m-0 font-weight-bold h8">{selectedSchedule}</p>
            <img
              className="ml-2"
              style={{ width: "18px", height: "15px", cursor: "pointer" }}
              src="./image/downArrow.png"
              onClick={toggleDropdownSchedule}
              alt="Dropdown Arrow"
            />
            {isDropdownVisibleSchedule && (
              <div
                className="dropdown-menu show position-absolute"
                style={{
                  top: "100%",
                  left: 0,
                  zIndex: 1000,
                  backgroundColor: colors.blue13,
                  width: "98%",
                }}
              >
                <ul className="list-unstyled m-0 py-2">
                  <li
                    className="px-3 py-2 cursor-pointer"
                    onClick={() => handleOptionschedule("09-07")}
                  >
                    09-07
                  </li>
                  <li
                    className="px-3 py-2 cursor-pointer"
                    onClick={() => handleOptionschedule("06-02")}
                  >
                    06-02
                  </li>
                  <li
                    className="px-3 py-2 cursor-pointer"
                    onClick={() => handleOptionschedule("01-09")}
                  >
                    01-09
                  </li>
                </ul>
              </div>
            )}
          </Col> */}
            {/* <Col className="header-menu-roaster col-lg-2 col-md-3 col-sm-6 col-12">
            <Select
              value={selectedDept}
              onChange={handleDeptChange} // Correctly typed
              options={dept} // Now contains { value, label } objects
              placeholder="Select Department"
            />
          </Col>

          <Col className="header-menu-roaster col-lg-2 col-md-3 col-sm-6 col-12">
            <Select
              value={selectedDesignation}
              onChange={handleDesignationChange}
              options={designation}
              placeholder="Select Designation"
            />
          </Col>
       
          <Col className="header-menu-roaster col-lg-2 col-md-3 col-sm-6 col-12">
            <Select
              value={selectedSchedule}
              onChange={handleScheduleChange}
              options={schedule}
              placeholder="Select Schedule"
            />
          </Col>
      */}
            {/* 
           <Col className="col-lg-4 col-md-3 col-sm-6">
            <DateSelector
              fromDate={fromDate}
              toDate={toDate}
              setFromDate={setFromDate}
              setToDate={setToDate}
            />
          </Col>  */}
            {/* From Date Field */}
            <Col md="auto" style={{ display: "flex", alignItems: "center" }}>
              <label style={{ marginRight: "8px" }}>From Date</label>
              <Form.Control
                type="date"
                value={fromDate}
                onChange={(e) => {
                  //console.log(1)
                  if (moment(e.target.value).year().toString().length > 4) {
                    showErrorAlert("The maximum allowed year is 4 digits. Please enter a valid year")
                    
                  }
                  const { value } = e.target;
                  const year = value.split("-")[0];
                  if (/^\d{4}$/.test(year) || value === "") {
                    setFromDate(value);
                  }
                }}
                style={{
                  width: "180px",
                  color: fromDate ? "black" : "#6c757d",
                }}
              />
            </Col>

            {/* To Date Field */}
            <Col md="auto" style={{ display: "flex", alignItems: "center" }}>
              <label style={{ marginRight: "8px" }}>To Date</label>
              <Form.Control
                type="date"
                value={toDate}
                onChange={(e) => {
                  if (moment(e.target.value).year().toString().length > 4) {
                    showErrorAlert("The maximum allowed year is 4 digits. Please enter a valid year");

                  }
                  const { value } = e.target;
                  const year = value.split("-")[0];
                  if (/^\d{4}$/.test(year) || value === "") {
                    setToDate(value);
                  }
                }}
                style={{
                  width: "180px",
                  color: toDate ? "black" : "#6c757d",
                }}
              />
            </Col>

            {/* Go Button */}

            <Col md="auto">
              <Button
                variant="primary"
                onClick={handleGoClick}
                style={{ width: "70px", marginLeft: "10px" }} // Fixed width for the Go button
              >
                Apply
              </Button>
            </Col>
          </Row>
          <div className="container-fluid bg-white p-4">
            <EmployeeRoster
              fromDate={fromDate}
              toDate={toDate}
              triggerFetch={fetchRosterData}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Roaster;
