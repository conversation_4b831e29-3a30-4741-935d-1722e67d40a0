package com.company.wfm.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.company.wfm.dto.TicketCategoryDTO;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.TicketCategoryMaster;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.TicketCategoryMasterRepository;
import com.company.wfm.service.TicketCategoryMasterService;
import com.company.wfm.service.UserTokenService;

import jakarta.persistence.EntityNotFoundException;


@Service
public class TicketCategoryMasterServiceImpl implements TicketCategoryMasterService {

    private static final Logger logger = LoggerFactory.getLogger(TicketCategoryMasterServiceImpl.class);

    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private TicketCategoryMasterRepository repository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Override
    public TicketCategoryMaster saveCategory(TicketCategoryMaster category) {
        try {
            Long employeeIdFromToken = tokenService.getEmployeeIdFromToken();
            Long branchId = employeeRepository.findBranchIdByEmployeeId(employeeIdFromToken);
            System.out.println("-========="+branchId);
            if (branchId == null) {
                throw new IllegalArgumentException("Branch ID not found for the logged-in user.");
            }
            logger.info("Saving new category: {}", category);
            if (category.getCategory() == null || category.getCategory().isBlank()) {
                logger.warn("Category name is null or empty");
                throw new IllegalArgumentException("Category name cannot be null or empty");
            }

            if (category.getPriority() == null || category.getPriority().isBlank()) {
                logger.warn("Priority is null or empty");
                throw new IllegalArgumentException("Priority cannot be null or empty");
            }

            category.setCreatedBy(employeeIdFromToken);
            category.setCreatedAt(LocalDateTime.now());
            category.setBranchId(branchId);
            category.setIsActive(true);
            category.setDepartmentId(category.getDepartmentId());
            // Save to the repository
            TicketCategoryMaster savedCategory = repository.save(category);
            logger.info("Category saved successfully with ID: {}", savedCategory.getId());
            return savedCategory;
        } catch (IllegalArgumentException ex) {
            logger.error("Validation error: {}", ex.getMessage());
            // Rethrow validation errors
            throw ex;
        } catch (Exception ex) {
            logger.error("Failed to save category: {}", ex.getMessage(), ex);
            // Log and rethrow unexpected errors
            throw new RuntimeException("Failed to save category: " + ex.getMessage(), ex);
        }

    }


   /* @Override
    public Page<TicketCategoryMaster> listCategories(Pageable pageable) {
        try {
            return repository.findAll(pageable);
        } catch (Exception ex) {
            logger.error("Error occurred while fetching categories: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error fetching categories", ex);
        }
    }*/

    @Override
    public Page<TicketCategoryDTO> listCategories(Pageable pageable) {
        try {
            Long employeeIdFromToken = tokenService.getEmployeeIdFromToken();
            Long branchId = employeeRepository.findBranchIdByEmployeeId(employeeIdFromToken);
           // System.out.println("-========="+branchId);
            logger.info("Logged-in Employee's Branch ID: {}", branchId);

           // Page<TicketCategoryMaster> categories = repository.findAll(pageable);
            Page<TicketCategoryMaster> categories = repository.findByBranchId(branchId, pageable);

            List<TicketCategoryDTO> dtoList = categories.stream()
                    .map(category -> {
                        String  createdByName=null;
                        if (category.getCreatedBy() != null) {
                            createdByName = getEmployeeNameById(category.getCreatedBy());
                        }

                        String updatedByName = null;
                        if (category.getUpdatedBy() != null) {
                            updatedByName = getEmployeeNameById(category.getUpdatedBy());
                        }
                        String departmentName = null;
                        if (category.getDepartmentId() != null) {
                            departmentName = getDepartmentNameById(category.getDepartmentId());
                        }

                        String branchName = null;
                        if (category.getBranchId() != null) {
                            branchName = getBranchNameById(category.getBranchId());
                        }
                        // Return a new TicketCategoryDTO
                        return new TicketCategoryDTO(
                                category.getId(),
                                category.getCategory(),
                                category.getPriority(),
                                category.getCreatedBy(),
                                createdByName,
                                category.getCreatedAt(),
                                category.getUpdatedBy(),
                                updatedByName,
                                category.getUpdatedAt(),
                                category.getIsActive(),
                                category.getEta(),
                                category.getBranchId(),
                                branchName,  // branchName is set here, it can be null
                                category.getDepartmentId(),
                                departmentName // departmentName is set here, it can be null
                        );
                    })
                    .collect(Collectors.toList());

            // Return as Page of DTOs
            return new PageImpl<>(dtoList, pageable, categories.getTotalElements());
        } catch (Exception ex) {
            logger.error("Error occurred while fetching categories: {}", ex.getMessage(), ex);
            throw new RuntimeException("Error fetching categories", ex);
        }
    }

    private String getBranchNameById(Long branchId) {
        // Assuming Branch has a `name` field
        return branchRepository.findById(branchId)
                .map(Branch::getBranchName)
                .orElse("Unknown Branch"); // Default value if branch is not found
    }

    private String getDepartmentNameById(Long departmentId) {
        // Assuming Department has a `name` field
        return departmentRepository.findById(departmentId)
                .map(Department::getDepartmentName)
                .orElse("Unknown Department"); // Default value if department is not found
    }
    private String getEmployeeNameById(Long employeeId) {
        return employeeRepository.findById(employeeId)
                .map(Employee::getEmpName) // Get the employee name
                .orElse("Unknown"); // If employee is not found, return "Unknown"
    }


    @Override
    public List<Object[]> listCategoriesForSelect() {
        try {
            Long employeeIdFromToken = tokenService.getEmployeeIdFromToken();
            Long branchId = employeeRepository.findBranchIdByEmployeeId(employeeIdFromToken);
            // System.out.println("-========="+branchId);
            logger.info("Logged-in Employee's Branch IDs: {}", branchId);
            return repository.findActiveCategoriesWithIdCategoryPriorityOrderedByIdDesc(branchId);
        } catch (Exception ex) {
            logger.error("Error occurred while fetching categories for 'select': {}", ex.getMessage(), ex);
            throw new RuntimeException("Error fetching categories for 'select'", ex);
        }
    }


    //update

    @Override
    public TicketCategoryMaster updateCategory(Long id, TicketCategoryMaster updatedCategory) {
        try {

            Long employeeIdFromToken = tokenService.getEmployeeIdFromToken();
            Long branchId = employeeRepository.findBranchIdByEmployeeId(employeeIdFromToken);

            if (branchId == null) {
                throw new IllegalArgumentException("Branch ID not found for the logged-in user.");
            }

            // Fetch the existing entity
            TicketCategoryMaster existingCategory = repository.findById(id)
                    .orElseThrow(() -> new EntityNotFoundException("Category not found for ID: " + id));

            logger.info("Updating category with ID: {}", id);

            // Validate inputs
            if (updatedCategory.getCategory() != null && !updatedCategory.getCategory().isBlank()) {
                existingCategory.setCategory(updatedCategory.getCategory());
            }

            if (updatedCategory.getPriority() != null && !updatedCategory.getPriority().isBlank()) {
                existingCategory.setPriority(updatedCategory.getPriority());
            }

            if (updatedCategory.getEta() != null) {
                existingCategory.setEta(updatedCategory.getEta());
            }
            // If 'isActive' is passed in the update, apply it
            if (updatedCategory.getActive() != null) {
                existingCategory.setTicketActive(updatedCategory.getActive());
            }
            // Update auditing fields
            existingCategory.setUpdatedBy(employeeIdFromToken);
            existingCategory.setUpdatedAt(LocalDateTime.now());
            existingCategory.setDepartmentId(updatedCategory.getDepartmentId());
            existingCategory.setBranchId(branchId);
            //existingCategory.setIsActive(true);

            // Save the updated entity
            TicketCategoryMaster savedCategory = repository.save(existingCategory);
            logger.info("Category updated successfully with ID: {}", id);
            return savedCategory;

        } catch (EntityNotFoundException ex) {
            logger.error("Category not found: {}", ex.getMessage());
            throw ex;
        } catch (Exception ex) {
            logger.error("Error updating category: {}", ex.getMessage(), ex);
            throw new RuntimeException("Failed to update category: " + ex.getMessage(), ex);
        }
    }

   //soft delete

    @Override
    public boolean softDeleteCategory(Long id) {
        try {
            Long employeeIdFromToken = tokenService.getEmployeeIdFromToken();
            Optional<TicketCategoryMaster> categoryOptional = repository.findById(id);

            if (categoryOptional.isPresent()) {
                TicketCategoryMaster category = categoryOptional.get();

                if (!Boolean.FALSE.equals(category.getIsActive())) {
                    category.setIsActive(false);
                    category.setUpdatedBy(employeeIdFromToken);
                    category.setUpdatedAt(LocalDateTime.now());
                    repository.save(category);
                    logger.info("Category with ID {} marked as inactive.", id);
                    return true;
                } else {
                    logger.warn("Category with ID {} is already inactive.", id);
                    return false;
                }
            } else {
                logger.warn("Category with ID {} not found.", id);
                return false;
            }
        } catch (Exception ex) {
            logger.error("Error occurred while marking category with ID {} as inactive: {}", id, ex.getMessage(), ex);
            throw new RuntimeException("Failed to delete category", ex);
        }
    }

//lookup branch and department wise
    @Override
    public List<TicketCategoryMaster> getTicketCategoriesByBranchAndDepartment(Long departmentId) {
        try {
            Long employeeIdFromToken = tokenService.getEmployeeIdFromToken();
            Long branchId = employeeRepository.findBranchIdByEmployeeId(employeeIdFromToken);

            if (branchId == null) {
                throw new IllegalArgumentException("Branch ID not found for the logged-in user.");
            }

            List<TicketCategoryMaster> categories = repository.findByBranchIdAndDepartmentId(branchId, departmentId);

            if (categories == null || categories.isEmpty()) {
                return categories;
            }

            return categories;
        } catch (IllegalArgumentException e) {

            throw new IllegalArgumentException("Error retrieving branch ID: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new RuntimeException("An unexpected error occurred while fetching ticket categories.", e);
        }
    }

    @Override
    public TicketCategoryMaster getTicketByID(Long id) {


        return repository.findById(id)
                .orElseThrow(() -> { //testA
                    String message = "Ticket with ID " + id + " not found";
                    System.out.println(message); // Log the message
                    return new RuntimeException(message);
                });
    }


}
