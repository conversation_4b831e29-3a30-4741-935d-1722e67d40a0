import React, { useEffect, useState } from 'react';
import { Doughn<PERSON> } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(ArcE<PERSON>, Tooltip, Legend);

const TicketsChart = ({ data}) => {
  const [chartSize, setChartSize] = useState({ width: '160px', height: '209px' });
  const [ticketNumberSize, setTicketNumberSize] = useState('50px');
  const [percentageSize, setPercentageSize] = useState('14px');

  useEffect(() => {
    const handleResize = () => {
      if (window.screen.width > 1350) {
        setChartSize({ width: '160px', height: '200px' });
        setTicketNumberSize('50px');
        setPercentageSize('12px');
      } else if (window.screen.width > 1100) {
        setChartSize({ width: '100px', height: '150px' });
        setTicketNumberSize('40px');
        setPercentageSize('12px');
      } else if (window.screen.width > 800) {
        setChartSize({ width: '100px', height: '90px' });
        setTicketNumberSize('35px');
        setPercentageSize('10px');
      } else {
        setChartSize({ width: '160px', height: '209px' });
        setTicketNumberSize('50px');
        setPercentageSize('14px');
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);


  // const modifiedChartData = {
  //   ...data.chartData,
  //   datasets: data.chartData.datasets.map((dataset) => ({
  //     ...dataset,
  //     borderColor: ['#E2F5E3', '#F76C6C'], 
  //     borderWidth: 1, 
  //   })),
  // };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '70%',
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
  };


  return (
    <div className="col-12 col-md-6 col-lg-3 mb-4">
      <Link href={data.onClickUrl || "#"}>
      <div className="card shadow-sm border-0" style={{ height: "240px", cursor: "pointer" }} >
        <div className="card-body">
          <div className="text-start">
            <p className="cardHeaderTxt">{data.title}</p> 
          </div>
          <div style={{ display: "flex", flexDirection: "row", alignItems: "center", height: 120 }}>
            <div
              style={{
                fontSize: ticketNumberSize,
                fontWeight: 'bold',
                color: '#4A4A4A',
              }}
            >
              {data.ticketCount} 
            </div>
            <div className="ms-auto">
              <div
                style={{
                  width: chartSize.width,
                  height: chartSize.height,
                  position: 'relative',
                }}
              >
                {/* <Doughnut data={modifiedChartData} options={options} /> */}
                <div
                  style={{
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    transform: 'translate(-50%, -50%)',
                    fontSize: percentageSize,
                    fontWeight: 'bold',
                    color: '#4A4A4A',
                  }}
                >
                  {data.percentage}% 
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </Link>
    </div>
  );
};

export default TicketsChart;
