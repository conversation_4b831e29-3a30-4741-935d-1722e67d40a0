
.team-container {
    padding: 20px;
  }
  
  .slider-container {
    overflow: hidden;
    width: 100%;
  }
  
  .slider {
    display: flex;
    overflow-x: auto;
    scroll-behavior: smooth;
  }
  
  .team-card {
    min-width: 257px;
    margin-right: 20px;
    flex: 0 0 auto;
  }
  
  .slider-buttons {
    display: flex;
    gap: 10px;
    padding-bottom: 10px;
  }
  
  .slider-button {
    background-color: white;
    width: 40px;
    border-radius: 50%;
    height: 40px;
    cursor: pointer;
  }
  .container-team{
    display: flex;
    align-items: center;
    width:100%;
    cursor: pointer;
  }
  
  @media (max-width: 768px) {
    /* .team-card {
      min-width: calc(100% / 1 - 25px)
    } */
  
    .slider-button {
      width: 20px;
      height: 20px;
    }
    .slider-buttons {
        display: flex;
        gap: 10px;
        margin-top: 30px;
    
      }
      .team-card p {
        font-size: 10px !important;
      }
      .team-card {
        /* width:100%; */
        margin-right: 2px;
  
  }
    .team-text{
     font-size: 20px;

  }
  }
  
  @media (min-width: 1024px) {
    .team-card {
      min-width: calc(100% / 3 - 20px); 
    }
  }
  
  .pie-chart-team {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: conic-gradient(var(--red13) var(--percentage), #ffcc80 0);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .pie-chart-team::before {
    content: '';
    position: absolute;
    width: 70%;
    height: 70%;
    background: white;
    border-radius: 50%;
  }

  .pie-chart-team[data-percentage="81%"] {
    --percentage: 81%;
  }
  
  .pie-chart-team[data-percentage="90%"] {
    --percentage: 90%;
  }
  
  .pie-chart-team[data-percentage="62%"] {
    --percentage: 62%;
  }
  
  .pie-chart-center-team {
    position: absolute;
    font-size: 1rem;
    font-weight: bold;
    color: var(--grey1);
  }
  
  .mr-3 {
    margin-right: 1rem;
  }
  
  .ml-3 {
    margin-left: 1rem;
  }
  
  .mb-4 {
    margin-bottom: 1.5rem;
  }
  
  .mt-2 {
    margin-top: 0.5rem;
  }
  
  .mt-3 {
    margin-top: 1rem;
  }
  
 