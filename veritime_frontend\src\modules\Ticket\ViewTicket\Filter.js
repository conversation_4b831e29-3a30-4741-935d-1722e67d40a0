import React, { useState } from 'react';
//import 'bootstrap/dist/css/bootstrap.min.css';
import './Filter.css';

const Filter = () => {
  const [selected, setSelected] = useState('All');

  return (
    <div className="container d-flex justify-content-center">
      <div className="btn-group" role="group" aria-label="Filter options">
        <button
          type="button"
          className={`btn filter-btn ${selected === 'All' ? 'active' : ''}`}
          onClick={() => setSelected('All')}
        >
          <span className="color-box active-box"></span> All
        </button>
        <button
          type="button"
          className={`btn filter-btn ${selected === 'Assigned' ? 'active' : ''}`}
          onClick={() => setSelected('Assigned')}
        >
          <span className="color-box"></span> Assigned To me
        </button>
        <button
          type="button"
          className={`btn filter-btn ${selected === 'Created' ? 'active' : ''}`}
          onClick={() => setSelected('Created')}
        >
          <span className="color-box"></span> Created by me
        </button>
      </div>
    </div>
    
  );
};

export default Filter;
