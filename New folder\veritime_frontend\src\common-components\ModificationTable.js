import React, { useEffect, useState } from "react";
import Swal from "sweetalert2";
import {
  getRequest,
  postRequest,
  postRequestWithSecurity,
  getRequestWithSecurity,
} from "../services/apiService";
import TableFilter from "./TableFilter";
import { API_URLS } from "../constants/apiConstants";
import VisibilityIcon from "@mui/icons-material/Visibility";
import "../common-components/ModificationTable.css";
import { margin, style } from "@mui/system";

const ModificationTable = ({ showActionCol, fetchCount }) => {
  const [rows, setRows] = useState([]);
  const [imageErrors, setImageErrors] = useState({});

  const getInitials = (name) => {
    return name?.charAt(0)?.toUpperCase() || "U";
  };

  const handleImageError = (index) => {
    setImageErrors((prevErrors) => ({
      ...prevErrors,
      [index]: true,
    }));
  };

  useEffect(() => {
    const fetchModificationHistory = async () => {
      fetchCount("modification");
      try {
        let apiurl = API_URLS?.MODIFICATION_HISTORY;
        if (window.location.pathname === "/employeeDashboard/viewHistory")
          apiurl += "?type=history";

        const data = await getRequestWithSecurity(apiurl);
        if (data) {
          // Assign serial numbers dynamically
          const rowsWithSrNo = data.map((row, index) => ({
            ...row,
            srNo: index + 1,
          }));
          setRows(rowsWithSrNo);
        }
      } catch (error) {}
    };

    fetchModificationHistory();
  }, []);

  const updateSrNo = (updatedRows) => {
    return updatedRows.map((row, index) => ({
      ...row,
      srNo: index + 1,
    }));
  };

  const handleAccept = async (row, actionType) => {
    const leaveHistoryId = row.id; // Use the 'id' from the row data
    if (!leaveHistoryId) {
      return;
    }

    const { value: reason } = await Swal.fire({
      title: "Approve Modification Request",
      text: "Do you want to approve this Modification Request?",
      input: "textarea",
      inputPlaceholder: "Enter your approval reason...",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, approve it!",
      cancelButtonText: "No, keep it pending",
      inputValidator: (value) => {
        if (!value) {
          return "Please mention a reason!";
        }
      },
    });

    if (reason) {
      const payload = {
        regularizationId: leaveHistoryId,
        approvalStatus: actionType,
        actionReason: reason,
      };

      try {
        const response = await postRequestWithSecurity(
          API_URLS.ATTENDANCE_APPROVE,
          payload
        );

        if (response) {
          Swal.fire(
            "Success!",
            "The Modification Request has been approved.",
            "success"
          );
          setRows((prevRows) =>
            updateSrNo(
              prevRows.map((row) =>
                row.id === leaveHistoryId
                  ? {
                      ...row,
                      approvalStatus: actionType,
                      updatedBy: "Supervisor",
                      reason,
                    }
                  : row
              )
            )
          );
        }
      } catch (error) {
        Swal.fire(
          "Error!",
          "Failed to approve the Modification Request.",
          "error"
        );
      }
    }
  };

  const handleDeny = async (row, actionType = "REJECTED") => {
    const { value: reason } = await Swal.fire({
      title: "Deny Modification Request",
      text: "Do you want to deny this application?",
      input: "textarea",
      inputPlaceholder: "Enter your denial reason...",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, deny it!",
      cancelButtonText: "No, keep it pending",
      inputValidator: (value) => {
        if (!value) {
          return "Please mention a reason!";
        }
      },
    });

    if (reason) {
      const payload = {
        regularizationId: row.id,
        approvalStatus: actionType,
        actionReason: reason,
      };

      try {
        const response = await postRequestWithSecurity(
          API_URLS.ATTENDANCE_DENY,
          payload
        );

        if (response) {
          Swal.fire(
            "Denied!",
            "The Modification Request has been denied.",
            "success"
          );
          setRows((prevRows) =>
            updateSrNo(
              prevRows.map((r) =>
                r.id === row.id
                  ? {
                      ...r,
                      approvalStatus: actionType,
                      updatedBy: "Supervisor",
                      reason,
                    }
                  : r
              )
            )
          );
        }
      } catch (error) {
        Swal.fire(
          "Error!",
          "Failed to deny the Modification Request.",
          "error"
        );
      }
    }
  };
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  const handleDetail = (row) => {
    const comment = row.comment
      ? JSON.parse(row.comment).reason
      : "No comments available";

    const multiDates = Array.isArray(row.multiDates) ? row.multiDates : [];
    const S3URL = process.env.NEXT_PUBLIC_S3_URL;

    const initials = getInitials(row.empName);

    const multiDatesBadges = multiDates
      .map(
        (date) => `
      <span style="display: inline-block; padding: 3px 10px; background-color: grey; color: white; border-radius: 12px; font-size: 14px; font-weight: bold; text-align: center; margin-right: 5px;margin-bottom: 7px;">
        ${date}
      </span>
    `
      )
      .join("");

    Swal.fire({
      html: `
        <div style="font-size: 18px; line-height: 1.6; color: #333; display: flex; flex-direction: column; gap: 20px; max-width: 700px;">
          <div style="display: flex; justify-content: center; align-items: center; gap: 20px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0;">
            <img 
             src="${S3URL}${row.employeeImage}"
              alt="Employee Image" 
              style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover; border: 2px solid #ddd;" 
              onerror="this.onerror=null; this.style.display='none'; this.nextElementSibling.style.display='flex';"
            >
            <div style="display: none; align-items: center; justify-content: center; width: 60px; height: 60px; background-color: #ccc; color: #fff; font-weight: bold; border-radius: 50%;">
              ${getInitials(row.employeeName)}
            </div>
            <div style="display: flex; flex-direction: column; align-items: center;">
              <span style="font-size: 22px; font-weight: bold; color: #1a1a1a;">${
                row.employeeName
              }</span>
              <span style="font-size: 14px; color: #777;">${
                row.employeeDesignation
              }</span>
            </div>
          </div>

          <div style="display: flex; flex-direction: column; gap: 15px; padding-bottom: 15px;">
            <div style="display: flex; justify-content: ; align-items: center; padding-bottom: 10px; margin-bottom: -14px;">
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px;">Designation:</strong>
                <span>${row.employeeDesignation}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;width: 78px;margin-right: 2px">Reason:</strong>
                <span>${row.reason || "N/A"}</span>
              </div>
            </div>

            <div style="display: flex; justify-content: ; padding-bottom: 10px; margin-bottom: -11px;">
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">Time Range:</strong>
                <span>${row.timeRange}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: ;">
                <strong style="font-weight: bold;margin-right: 8px">Action Reason:</strong>
                <span>${row.actionReason || "N/A"}</span>
              </div>
            </div>
<div style="display: flex; justify-content: space-between; padding-bottom: 10px; align-items: flex-start;">
  <!-- Dates Section -->
  <div style="flex: 1; display: flex; align-items: flex-start; min-width: 0;">
    <strong style="font-weight: bold; margin-right: 10px; white-space: nowrap; flex-shrink: 0;">Dates:</strong>
    <div style="display: flex; flex-wrap: wrap; gap: 5px;">
      ${multiDatesBadges}
    </div>
  </div>

  <div style="margin: 0 20px;"></div>

  <!-- Status Section -->
  <div style="flex: 1; display: flex; align-items: flex-start; min-width: 0;">
    <strong style="font-weight: bold; margin-right: 8px; white-space: nowrap; flex-shrink: 0;">Status:</strong>
    <span style="white-space: nowrap;">${row.approvalStatus}</span>
  </div>
</div>


             <div style="display: flex; justify-content: center; gap: 10px;">
          ${
            window.location.pathname !== "/employeeDashboard/viewHistory" &&
            row.approvalStatus === "PENDING"
              ? `
            <button id="approveBtn" class="approve-button">Approve</button>
            <button id="denyBtn" class="deny-button">Decline</button>
          `
              : ""
          }
        </div>
      </div>
      `,
      icon: "",
      showCloseButton: true,
      showCancelButton: false,
      showConfirmButton: false,
      customClass: {
        container: "custom-swal-container",
        popup: "custom-swal-popup",
        title: "custom-swal-title",
        content: "custom-swal-content",
      },
      didOpen: () => {
        const approveBtn = document.getElementById("approveBtn");
        const denyBtn = document.getElementById("denyBtn");

        if (approveBtn) {
          approveBtn.addEventListener("click", () => {
            handleAccept(row, "APPROVED");
          });
        }

        if (denyBtn) {
          denyBtn.addEventListener("click", () => {
            handleDeny(row, "REJECTED");
          });
        }
      },
    });
  };

  const columns = [
    { field: "srNo", headerName: "Sr. No", width: 70 },
    {
      field: "employeeName",
      headerName: "Name",
      width: 250,
      renderCell: (params) => {
        const imageUrl = params.row.employeeImage;
        const name = params.row.employeeName;

        return (
          <div style={{ display: "flex", alignItems: "center" }}>
            {!imageUrl || imageErrors[params.row.id] ? (
              <div
                className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                style={{
                  width: "50px",
                  height: "50px",
                  backgroundColor: "#ccc",
                  color: "#fff",
                  fontWeight: "bold",
                }}
              >
                {getInitials(name)}
              </div>
            ) : (
              <img
                src={`${S3URL}${imageUrl}`}
                // src={`${baseURL}${imageUrl}`}
                alt={name}
                className="rounded-circle me-2"
                style={{ width: "50px", height: "50px" }}
                onError={() => handleImageError(params.row.id)}
              />
            )}
            <span>{name}</span>
          </div>
        );
      },
    },
    //   {
    //     field: 'multiDates',
    //     headerName: 'Date',
    //     width: 150,
    //     renderCell: (params) => (
    //       <div style={{ whiteSpace: 'pre-wrap' }}>
    //         {params.value ? params.value.toString().split(',').join('\n') : 'No dates available'}
    //       </div>
    //     ),
    //   },
    {
      field: "timeRange",
      headerName: "Time Range",
      width: 200,
      renderCell: (params) => (
        <div style={{ marginTop: "14px" }}>{params.value}</div>
      ),
    },
    {
      field: "approvalStatus",
      headerName: "Status",
      width: 200,
      renderCell: (params) => (
        <div style={{ marginTop: "14px" }}>{params.value}</div>
      ),
    },
    // { field: 'reason', headerName: 'Reason', width: 200 },
  ];

  if (
    showActionCol ||
    window.location.pathname === "/employeeDashboard/viewHistory"
  ) {
    columns.push({
      field: "action",
      headerName: "Action",
      width: 100,
      renderCell: (params) => (
        <button
          className="view-button"
          onClick={() => handleDetail(params.row)}
          style={{
            border: "none",
            background: "transparent",
            cursor: "pointer",
          }}
        >
          <VisibilityIcon
            style={{ cursor: "pointer", color: "black", marginTop: "10px" }}
          />
        </button>
      ),
    });
  }

  return (
    <div id="tableWrapper" style={{ marginTop: -24 }}>
      <TableFilter columns={columns} rows={rows} autoRowHeight={true} />
    </div>
  );
};

export default ModificationTable;
