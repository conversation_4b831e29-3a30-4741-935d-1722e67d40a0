import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap-icons/font/bootstrap-icons.css";

import "../app/dashboards/Admin.css";
import "../css/statuscard.css";

import { useEffect } from "react";
import OneSignal from "react-onesignal";

// Simple flag to track if initialization has been attempted
let initializationAttempted = false;

function MyApp({ Component, pageProps }) {
  useEffect(() => {
    // Initialize OneSignal only once on client side
    if (typeof window !== "undefined" && !initializationAttempted) {
      initializationAttempted = true;

      OneSignal.init({
        appId: "************************************",
        safari_web_id:
          "web.onesignal.auto.************************************",
        // notifyButton: {
        //   enable: true,
        // },
        // Uncomment the below line to run on localhost. See: https://documentation.onesignal.com/docs/local-testing
        allowLocalhostAsSecureOrigin: true,
      })
        .then(() => {
          // OneSignal is now ready - set up global listeners
          OneSignal.Notifications.requestPermission().catch((error) => {
            // Silently handle permission errors (user blocked notifications)
            console.log("Notification permission not granted:", error.message);
          });
          OneSignal.Notifications.addEventListener("click", () => {});
          OneSignal.Notifications.addEventListener(
            "foregroundWillDisplay",
            () => {}
          );
          OneSignal.Notifications.addEventListener(
            "permissionChange",
            () => {}
          );
        })
        .catch((error) => {
          console.error("OneSignal initialization failed:", error);
        });
    }
  }, []);

  return <Component {...pageProps} />;
}

export default MyApp;
