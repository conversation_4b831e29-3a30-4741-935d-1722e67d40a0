package com.company.wfm.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.SmsDataRequest;
import com.company.wfm.service.SmsService;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/sms")
@CrossOrigin(origins = "*")
@Slf4j
public class SmsController {

    private final SmsService smsService;

    public SmsController(SmsService smsService) {
        this.smsService = smsService;
    }

    /**
     * Endpoint to send an SMS request.
     *
     * @param request SMS data request payload.
     * @return ResponseEntity with status and message.
     */
    @PostMapping("/send")
    public ResponseEntity<String> sendSms(@RequestBody SmsDataRequest request) {
        log.info("Received SMS request: {}", request);

        try {
        	log.info("SMS processing initiated successfully.");
            smsService.sendMessage(request);
            return ResponseEntity.ok("SMS request has been submitted successfully.");
        } catch (JsonProcessingException e) {
            log.error("Error while processing SMS request: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("Failed to process SMS request.");
        } catch (Exception e) {
            log.error("Unexpected error occurred while sending SMS: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("An unexpected error occurred while sending the SMS.");
        }
    }
}
