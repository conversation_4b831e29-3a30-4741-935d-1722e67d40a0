package com.company.wfm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Data
@Table(name = "t_shift_swap")
public class ShiftSwap {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SWAP_REQUEST_ID")
    private Long swapRequestId;

    @Column(name = "ORIGINAL_SHIFT_ASSIGNMENT_ID", nullable = true)
    private Long originalShiftAssignmentId;

    @Column(name = "SWAP_REQUESTED_BY_EMPLOYEE_ID", nullable = true)
    private Long swapRequestedByEmployeeId;

    @Column(name = "SWAP_REQUESTED_TO_EMPLOYEE_ID", nullable = true)
    private Long swapRequestedToEmployeeId = 0L;

    @Column(name = "SWAP_REQUEST_DATE", nullable = false)
    private LocalDate swapRequestDate;

    @Column(name = "STATUS", nullable = false)
    private String status;

    @Column(name = "START_DATE", nullable = false)
    private LocalDate startDate;

    @Column(name = "END_DATE", nullable = false)
    private LocalDate endDate;

    @Column(name = "REQUESTED_SCHEDULE", nullable = false)
    private String requestedSchedule;

    @Column(name = "REASON", nullable = false)
    private String reason;

    @Column(name = "ACTION_REASON")
    private String actionReason;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by", nullable = true)
    private Long createdBy;

    @Column(name = "updated_by", nullable = true)
    private Long updatedBy;

    @Column(name = "updated_at", nullable = true)
    private LocalDateTime updatedAt;

    @Column(name = "is_active", nullable = false)
    private boolean isActive = true;

}