package com.company.wfm.entity;


import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "t_employee_termination")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeTermination {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "termination_id")
    private Long terminationId; // BIGINT equivalent for termination_id

    @Column(name = "emp_id")
    private Long empId; // BIGINT equivalent for emp_id

    @Column(name = "termination_type")
    private String terminationType;

    @Column(name = "supervisor_emp_id")
    private Long supervisorEmpId; // BIGINT equivalent for supervisor_emp_id

    @Column(name = "termination_reason")
    private String terminationReason;

    @Column(name = "last_working_date")
    private LocalDate lastWorkingDate;

    @Column(name = "notice_start_date")
    private LocalDate noticeStartDate;

    @Column(name = "created_by")
    private Long createdBy; // BIGINT equivalent for created_by

    @Column(name = "updated_by")
    private Long updatedBy; // BIGINT equivalent for updated_by

    @Column(name = "created_time")
    private LocalDateTime createdTime; // DATETIME equivalent for created_time

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
}
