.container-id {
    display: flex;
    justify-content: center;
    padding-left: 0px; 
    width: 110%;
    max-width: 1450px; 
  }

  @media (min-width: 1450px) and (max-width: 1500px) {
    .container-id {
    padding-left: 190px;
      max-width: 1100px;
    }
  }

  @media (min-width: 1350px) and (max-width: 1450px) {
    .container-id {
    padding-left: 160px;
      max-width: 1100px;
    }
  }

  @media (min-width: 1200px) and (max-width: 1350px)  {
    .container-id {
    padding-left: 100px; 
      max-width: 1120px;
    }
  }
  
  @media (min-width: 992px) and (max-width: 1199px) {
    .container-id {
    padding-left: 100px; 
      max-width: 1200px;
    }
  }

@media (max-width: 768px) {
    .container-id {
        padding-left:15px;
    }
    
}
