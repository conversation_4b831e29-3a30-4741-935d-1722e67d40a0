# Veritime API Postman Collection

## Overview

This comprehensive Postman collection provides complete API testing capabilities for the Veritime Workforce Management System. It includes all endpoints with proper authentication, role-based access control, and automated testing scripts.

## Files Included

- `Veritime_API_Collection.postman_collection.json` - Main API collection
- `Veritime_Environment.postman_environment.json` - Environment variables
- `Postman_Collection_README.md` - This documentation

## Quick Setup

### 1. Import Collection and Environment

1. Open Postman
2. Click **Import** button
3. Import `Veritime_API_Collection.postman_collection.json`
4. Import `Veritime_Environment.postman_environment.json`
5. Select "Veritime API Environment" from environment dropdown

### 2. Configure Environment Variables

The environment comes pre-configured with AES encryption credentials. Update these variables:

- `testUsername` - Your test login username (default: <EMAIL>)
- `testPassword` - Your test login password (default: admin123)
- `aesPassword` - AES encryption key (pre-configured from NEXT_PUBLIC_AES_PASSWORD)
- `aesSalt` - AES salt (pre-configured from NEXT_PUBLIC_AES_SALT)
- `baseUrl` - API base URL (default: https://www.xpertlyte.tech/api/v1)
- `baseUrl2` - API base URL v2 (default: https://www.xpertlyte.tech/api)

### 3. Authentication Flow with AES Encryption

1. **Set your credentials** in environment variables (`testUsername`, `testPassword`)
2. **Run the Login request** - The pre-request script will:
   - Automatically encrypt your username/password using AES
   - Send encrypted payload to the backend
   - Decrypt the response and extract JWT token
   - Store authentication data in environment variables
3. **All subsequent requests** will use the JWT token automatically

#### 🔐 How the Encryption Works

- **Pre-request Script**: Encrypts login payload using AES-256 with your credentials
- **Request Body**: Contains `{"encryptedData": "encrypted_payload"}`
- **Response**: Backend returns encrypted JWT token and user details
- **Test Script**: Automatically decrypts response and stores token
- **Environment**: AES credentials are pre-configured from your `.env.local` file

## Collection Structure

### 🔐 Authentication

- **Login** - Authenticate and get JWT token
- **Get Current User Profile** - Retrieve current user details

### 👥 Employee Management

- **Get All Employees** - List employees (role-filtered)
- **Get Employee by ID** - Retrieve specific employee
- **Create Employee** - Add new employee
- **Update Employee** - Modify employee details
- **Get Present Employees** - List currently present employees
- **Get Employee Names and IDs** - Simplified employee list

### ⏰ Attendance Management

- **Get Attendance List** - Retrieve attendance records
- **Get Team Attendance** - Team attendance summary
- **Approve Attendance** - Approve/deny attendance
- **Get Biometric Data** - Raw biometric punch data
- **Create Attendance Regularization** - Submit attendance correction

### 🏖️ Leave Management

- **Apply Leave** - Submit leave application
- **Get Leave History** - View leave applications
- **Approve Leave** - Approve leave request
- **Deny Leave** - Deny leave request
- **Get Leave Types** - Available leave types
- **Get Leave Counts** - Leave balance information

### 📊 Dashboard & Reports

- **Get Dashboard Widgets** - Dashboard data
- **Get Side Menu** - Role-based navigation menu
- **Download Report** - Export reports

### 🏢 Department & Branch Management

- **Get All Departments** - List departments
- **Create Department** - Add new department
- **Get Branches** - List branches
- **Create Branch** - Add new branch

## Role-Based Testing

### Available Roles

- **CEO** - Full system access
- **SuperAdmin** - Company-level access
- **Admin** - Branch-level access
- **Supervisor** - Team-level access
- **Employee** - Self-access only

### Testing Different Roles

1. Login with different user credentials
2. The `userRole` environment variable will be set automatically
3. API responses will be filtered based on the user's role
4. Some endpoints will return 403 Forbidden for insufficient permissions

## Environment Variables

### Authentication Variables

- `accessToken` - JWT token (auto-set after login)
- `userRole` - Current user role (auto-set after login)
- `empId` - Current user employee ID (auto-set after login)
- `employeeName` - Current user name (auto-set after login)

### Test Data Variables

- `createdEmpId` - ID of newly created employee
- `leaveHistoryId` - ID of leave application for testing
- `attendanceId` - ID of attendance record for testing
- `departmentId` - Default department ID (1)
- `branchId` - Default branch ID (1)
- `designationId` - Default designation ID (1)

### Date Variables

- `currentDate` - Current date (YYYY-MM-DD)
- `fromDate` - Start date for range queries
- `toDate` - End date for range queries

## Automated Testing Features

### Pre-request Scripts

- Automatic token management
- Common header injection
- Authentication validation

### Test Scripts

- Response time validation (< 5000ms)
- JSON response validation
- Status code verification
- Response schema validation
- Environment variable extraction

### Example Test Results

```javascript
✓ Response time is less than 5000ms
✓ Response has valid JSON
✓ Login successful
✓ Response contains access token
✓ User role is set
```

## API Endpoint Examples

### Authentication

```http
POST {{baseUrl2}}/auth/login
Content-Type: application/json

{
  "encryptedData": "encrypted_payload_containing_username_and_password"
}
```

### Employee Management

```http
GET {{baseUrl}}/employee
Authorization: Bearer {{accessToken}}
```

```http
POST {{baseUrl}}/employee
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "empName": "John Doe",
  "empCode": "EMP001",
  "email": "<EMAIL>",
  "departmentId": 1,
  "designationId": 2,
  "branchId": 1
}
```

### Attendance Management

```http
GET {{baseUrl}}/attendance/listByEmployee?fromDate=2024-01-01&toDate=2024-01-31
Authorization: Bearer {{accessToken}}
```

### Leave Management

```http
POST {{baseUrl}}/employees/leave/applications
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "leaveId": 1,
  "startDate": "2024-02-15",
  "endDate": "2024-02-17",
  "appliedLeaveCount": 3,
  "reason": "Family wedding"
}
```

## Error Handling

### Common HTTP Status Codes

- **200 OK** - Successful request
- **201 Created** - Resource created successfully
- **400 Bad Request** - Invalid request data
- **401 Unauthorized** - Invalid or missing token
- **403 Forbidden** - Insufficient permissions
- **404 Not Found** - Resource not found
- **500 Internal Server Error** - Server error

### Error Response Format

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed for field 'email'",
  "path": "/api/v1/employee"
}
```

## Security Considerations

### Data Encryption

- Request/response payloads are encrypted
- JWT tokens for authentication
- Role-based access control
- Rate limiting on authentication endpoints

### Best Practices

- Always use HTTPS endpoints
- Store sensitive data in environment variables
- Regularly rotate test credentials
- Use appropriate roles for testing
- Clear tokens when switching environments

## Troubleshooting

### Common Issues

#### 1. Authentication Failures

- Verify username/password in environment
- Check if token has expired
- Ensure correct base URL

#### 2. Permission Denied (403)

- Verify user role has access to endpoint
- Check role-based filtering rules
- Confirm user hierarchy permissions

#### 3. Invalid Request Data (400)

- Validate request payload format
- Check required fields
- Verify data types and constraints

#### 4. Network Issues

- Confirm API server is running
- Check network connectivity
- Verify SSL certificate validity

### Debug Tips

- Enable Postman Console for detailed logs
- Check environment variable values
- Review pre-request and test script outputs
- Verify request headers and body

## Advanced Usage

### Running Collection with Newman

```bash
# Install Newman
npm install -g newman

# Run collection
newman run Veritime_API_Collection.postman_collection.json \
  -e Veritime_Environment.postman_environment.json \
  --reporters cli,html \
  --reporter-html-export results.html
```

### Continuous Integration

```yaml
# GitHub Actions example
- name: Run API Tests
  run: |
    newman run collection.json \
      -e environment.json \
      --reporters junit \
      --reporter-junit-export results.xml
```

### Data-Driven Testing

- Use CSV files for test data
- Parameterize requests with variables
- Create test scenarios for different roles
- Automate regression testing

## Support and Maintenance

### Updating the Collection

1. Modify requests as needed
2. Update environment variables
3. Test all endpoints
4. Export updated collection
5. Update documentation

### Version Control

- Store collection files in version control
- Track changes to API endpoints
- Maintain environment configurations
- Document breaking changes

For questions or issues, please refer to the backend API documentation or contact the development team.
