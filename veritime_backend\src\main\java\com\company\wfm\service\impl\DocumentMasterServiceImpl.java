package com.company.wfm.service.impl;

import java.time.LocalDateTime;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.company.wfm.dto.DocumentMasterDTO;
import com.company.wfm.entity.DocumentMaster;
import com.company.wfm.repository.DocumentMasterRepository;
import com.company.wfm.service.DocumentMasterService;
import com.company.wfm.service.UserTokenService;

@Service
public class DocumentMasterServiceImpl implements DocumentMasterService {

    Logger logger = LoggerFactory.getLogger(DocumentMasterServiceImpl.class);

    @Autowired
    private DocumentMasterRepository documentMasterRepository;

    @Autowired
    private UserTokenService tokenService;


    // Save document
    @Override
    public DocumentMaster saveDocument(DocumentMasterDTO documentMasterDTO) {

        try {
            logger.info("Saving document: {}", documentMasterDTO);
            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
            DocumentMaster documentMaster = new DocumentMaster();
            documentMaster.setName(documentMasterDTO.getName());
            documentMaster.setType(documentMasterDTO.getType());
            documentMaster.setIsMandatory(documentMasterDTO.getIsMandatory() != null ? documentMasterDTO.getIsMandatory() : false);
           // documentMaster.setIsMandatory(documentMasterDTO.getIsMandatory());
            documentMaster.setCreatedBy(loggedInEmpId);
            documentMaster.setCreatedAt(LocalDateTime.now());
            documentMaster.setUpdatedBy(null);
            documentMaster.setUpdatedAt(null);
            documentMaster.setIsActive(true);
            logger.info("Document created with ID: {}", documentMaster.getDocumentId());
            return documentMasterRepository.save(documentMaster);
        }catch (Exception e) {
            // Log the exception (for debugging)
            logger.error("Error occurred while saving document: {}", e.getMessage(), e);
            // Throw or return a meaningful exception if needed
            throw new RuntimeException("An error occurred while saving the document: " + e.getMessage(), e);
        }
    }


    //listing
    @Override
    public Page<DocumentMaster> getDocuments(Pageable pageable) {
        try {
            // Fetch documents from the repository with pagination and sorting
            return documentMasterRepository.findAll(pageable);
        } catch (Exception e) {
            // Log the error for debugging
            logger.error("Error occurred while fetching documents: {}", e.getMessage(), e);
            throw new RuntimeException("Error occurred while fetching documents: " + e.getMessage());
        }
    }


    //update
    @Override
    public DocumentMaster updateDocument(Long documentId, DocumentMasterDTO updateDTO) {
        try {
            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
            // Retrieve the existing document from the database
            Optional<DocumentMaster> existingDocumentOptional = documentMasterRepository.findById(documentId);
            if (!existingDocumentOptional.isPresent()) {
                logger.error("Document with ID {} not found", documentId);
                return null;  // Return null or handle with an error response
            }

            DocumentMaster existingDocument = existingDocumentOptional.get();

            // Apply updates from the DTO to the existing entity
            if (updateDTO.getName() != null) {
                existingDocument.setName(updateDTO.getName());
            }
            if (updateDTO.getType() != null) {
                existingDocument.setType(updateDTO.getType());
            }
            if (updateDTO.getIsMandatory() != null) {
                existingDocument.setIsMandatory(updateDTO.getIsMandatory());
            }
            existingDocument.setUpdatedBy(loggedInEmpId);
            existingDocument.setUpdatedAt(LocalDateTime.now());
            existingDocument.setIsActive(true);

            // Save the updated document back to the repository
            return documentMasterRepository.save(existingDocument);
        } catch (Exception e) {
            // Log the error for debugging
            logger.error("Error occurred while updating document with ID {}: {}", documentId, e.getMessage(), e);

            // Handle the exception (e.g., return a custom error message or rethrow)
            throw new RuntimeException("An error occurred while updating the document: " + e.getMessage(), e);
        }
    }




}
