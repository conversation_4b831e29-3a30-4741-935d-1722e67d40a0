package com.company.wfm.dto;

import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AttendanceResponseDTO {

    private Long attendanceId;
    private Long empId;
    private String empName;
    private Time checkInTime;
    private Time checkOutTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate date;
    private String multiDate;
    private String modeType;
    private Long createdBy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    private Long updatedBy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
    private BigDecimal overtime;
    private String logout_status;
    private Time actualShiftStartTime;
    private Time  actualShiftEndTime;

    public String getOvertime() {
        return overtime != null ? overtime + " hrs" : "null"; // Handle null case
    }


}
