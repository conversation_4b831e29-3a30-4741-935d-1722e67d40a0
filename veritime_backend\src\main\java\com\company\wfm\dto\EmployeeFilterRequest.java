package com.company.wfm.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeFilterRequest {
    private List<Long> hospitalId;
    private List<Long> branchIds;
    private List<Long> departmentIds;
    private List<Long> designationIds;
    private String query;
    private String type;
  //  private Boolean inService;  // Use Boolean instead of boolean
    private int offset = 0;  // Default value
    private int limit = 10;  // Default value
    private boolean download;
    private Integer inService;  // Change from boolean to Integer



}
