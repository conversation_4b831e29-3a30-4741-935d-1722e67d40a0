package com.company.wfm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmployeeDocumentVO {
    private Long id;
    private Long documentId;
   // private Long createdBy;
   // private LocalDateTime createdAt;
   // private Long updatedBy;
  //  private LocalDateTime updatedAt;
  //  private Boolean isActive;
   private String documentName;
   private String documentType;
   private String filePath;


}
