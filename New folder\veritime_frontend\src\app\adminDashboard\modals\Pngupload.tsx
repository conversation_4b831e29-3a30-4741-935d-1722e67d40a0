import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Form } from "react-bootstrap";
import { postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants.js";
import { showSuccessAlert2, showErrorAlert } from "@/services/alertService";

const BulkUploadModal = ({
  show,
  onClose,
  onUploadSuccess,
  handleClose,
}: any) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files ? Array.from(e.target.files) : [];

    if (files.length > 0) {
      setSelectedFiles((prevFiles) => [...prevFiles, ...files]);
    }
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      showErrorAlert("Please select files first!");
      return;
    }

    const formData = new FormData();
    selectedFiles.forEach((file) => formData.append("files", file));

    try {
      const response = await postRequest(API_URLS.BULK_UPLOAD, formData);

      if (response) {
        showSuccessAlert2(response.message || "Files uploaded successfully!");
        onUploadSuccess();
        resetForm();
        handleClose();
      } else {
        showErrorAlert(
          response.message || "File upload failed. Please try again."
        );
      }
    } catch (error: any) {
      let errorMessage = "An unknown error occurred.";

      if (error.response) {
        if (typeof error.response.data === "string") {
          errorMessage = error.response.data;
        } else if (error.response.data?.message) {
          errorMessage = error.response.data.message;
        }
      }

      showErrorAlert(errorMessage);
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const resetForm = () => {
    setSelectedFiles([]);
  };

  const handleModalClose = () => {
    resetForm();
    handleClose();
  };

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>Bulk Profile Upload</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form>
          <Form.Group controlId="formFile">
            <Form.Label>Select Files</Form.Label>
            <Form.Control
              type="file"
              multiple
              onChange={handleFileChange} // Handle file change event
            />
          </Form.Group>

          {selectedFiles.length > 0 && (
            <div className="mt-3">
              <h5>Selected Files:</h5>
              <ul>
                {selectedFiles.map((file, index) => (
                  <li key={index}>
                    {file.name}{" "}
                    <button
                      type="button"
                      onClick={() => handleRemoveFile(index)}
                      className="btn btn-link text-danger"
                    >
                      x
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={handleClose}>
          Close
        </Button>
        <Button variant="primary" onClick={handleUpload}>
          Upload
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default BulkUploadModal;
