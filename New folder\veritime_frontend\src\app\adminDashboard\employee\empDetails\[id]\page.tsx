"use client";
import { useEffect, useState } from "react";
import { Container, Row, Col } from "react-bootstrap";
import SwipeableViews from "react-swipeable-views";
import { useTheme } from "@material-ui/core/styles";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import PersonalDetailsForm from "../../PersonalDetailsForm.js";
import AddressDetailsForm from "../../AddressDetailsForm.js";
import OfficialDetailsForm from "../../OfficialDetailsForm.js";
import ContactDetailsForm from "../../ContactDetails.js";
import DocumentDetailsForm from "../../DocumentDetailsForm.js";

import LeaveForm from "../../Leave.js";
import { a11yProps, TabPanel } from "../../../../../common-components/utils.js";
import Layout from "@/components/Layout";
import {
  getRequest,
  getRequestWithSecurity,
  postRequest,
  postRequestWithOnlyResponseDecrypted,
  postRequestWithSecurity,
  putRequest,
  putRequestWithSecurity,
} from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import DetailsForm from "../../Details.js";
import {
  showErrorAlert,
  showSuccessAlert,
  showSuccessAlert2,
} from "@/services/alertService.js";
import moment from "moment";
import axios from "axios";

const NewEmployee = ({ toggleMenu, expanded, isFromhistory }: any) => {
  const theme = useTheme();
  const [value, setValue] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const [personalDetails, setPersonalDetails] = useState({});
  const [addressDetails, setAddressDetails] = useState({});
  const [officialDetails, setOfficialDetails] = useState({});
  const [contactDetails, setContactDetails] = useState<any>({});
  const [Details, setDetails] = useState({});
  const [Leave, setLeave] = useState({});
  const [empId, setEmpId] = useState("");
  const [companyId, setCompanyId] = useState<any>("");
  const [branchId, setBranchId] = useState<any>("");
  const [documentsUploaded, setDocumentsUploaded] = useState<any>([]);
  const [isCreated, setIsCreated] = useState(false); // Track if employee is created
  const [profileImg, setProfileImg] = useState(null);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);

    const hospitalId = urlParams.get("hospitalId");
    if (hospitalId) {
      const decodedHospitalId = atob(hospitalId); // Decrypt the hospitalId
      setCompanyId(decodedHospitalId);
    } else {
      setCompanyId(localStorage.getItem("companyId"));
    }

    const branchId = urlParams.get("branchId");
    if (branchId) {
      const decodedBranchId = atob(branchId); // Decrypt the branchId
      setBranchId(decodedBranchId);
      console.log(decodedBranchId, "branchid from employeedetail");
    } else {
      setBranchId(localStorage.getItem("branchId"));
    }

    let empId = atob(window.location.pathname.split("/")?.[4]);
    if (empId != "new") {
      getEmpDetails(empId);
    } else {
      setIsLoading(false);
    }
  }, []);
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  const getEmpDetails = async (empId: any) => {
    try {
      setIsLoading(true);
      const empDetails = await getRequestWithSecurity(
        API_URLS?.EMPLOYEE_LIST + "/" + empId,
        true
      );
      console.log("Fetched probationPeriod:", empDetails);
      console.log("Fetched probationPeriod:", empDetails?.probationPeriod);

      setIsEditing(true);
      // localStorage.removeItem("empDetailsToEdit");
      // empDetails = JSON.parse(empDetails);
      setEmpId(empDetails?.empId);
      setBranchId(empDetails?.branchId);
      setDocumentsUploaded(empDetails?.documents);

      const nameParts = empDetails?.empName?.split(" ") || [];

      const firstName = nameParts[0] || "";
      const middleName = nameParts.length === 3 ? nameParts[1] : "";
      const lastName =
        nameParts.length === 3
          ? nameParts[2]
          : nameParts.length === 2
          ? nameParts[1]
          : "";

      setPersonalDetails({
        firstName,
        middleName,
        lastName,
        gender: empDetails?.gender,
        married: empDetails?.married?.toString(),
        birthday: moment(empDetails?.birthday).format("YYYY-MM-DD"),
        empId: empDetails?.empId,
        ethnicity: empDetails?.ethnicity,
        imgUre: S3URL + empDetails?.imgUre,
        branchId: empDetails?.branchId,
        nationalId: empDetails?.nationalId,
      });
      setAddressDetails({
        countryId: empDetails?.countryId,
        provinceId: empDetails?.provinceId,
        // subDistrictName: empDetails?.subDistrictName,
        // districtName: empDetails?.districtName,

        city: empDetails?.regionId,
        suburb: empDetails?.city,
        street: empDetails?.street,
        unitNumber: empDetails?.unitNumber,
        // zipCode: empDetails?.zipCode,
        zipCode:
          empDetails?.zipCode && /^[0-9]+$/.test(empDetails?.zipCode)
            ? empDetails?.zipCode
            : "",
      });
      setOfficialDetails({
        reportingPerson: empDetails?.upperId,
        workZone: empDetails?.workZone,
        empCode: empDetails?.empCode,
        defaultTimeSlotId: empDetails?.defaultTimeSlotId,
        biometricID: empDetails?.biometricID,

        departmentId: empDetails?.departmentId,
        designationId: empDetails?.designationId,
        hireDate: moment(empDetails?.hireDate).format("YYYY-MM-DD"),
        leaveOnDays: empDetails?.leaveOnDays?.split(","),
        leaveOnDaysList: empDetails?.leaveOnDays
          ?.split(",")
          ?.map((item: any) => {
            const data = { value: item, label: item };
            return data;
          }),
        probationPeriod: empDetails?.probationPeriod, // Ensure this line exists

        isEditing: true,
        branchId: empDetails?.branchId,
        companyId: empDetails?.companyId,
      });
      setContactDetails({
        mobileNo: empDetails?.mobileNo,
        alternateNumber: empDetails?.alternateNumber,
        email: empDetails?.email,
        alternateEmail: empDetails?.alternateEmail,
        emergencyContact1: empDetails?.emergencyContact1,
        emergencyContact2: empDetails?.emergencyContact2,
        emergencyContactname1: empDetails?.emergencyContactname1,
        emergencyContactname2: empDetails?.emergencyContactname2,
        empId: empDetails?.empId,
      });
      console.log("Fetched probationPeriod:", empDetails?.probationPeriod);

      setIsLoading(false);
    } catch (error) {
      console.log("error::: ", error);
      setIsLoading(false);
    }
  };
  const handleProfileImageChange = (newImageUrl: string) => {
    setPersonalDetails((prevDetails) => ({
      ...prevDetails,
      imgUre: newImageUrl, // Ensure correct image key
    }));
  };

  const getEmpDocuments = async (empId: any) => {
    try {
      setIsLoading(true);
      const empDetails = await getRequestWithSecurity(
        API_URLS?.EMPLOYEE_LIST + "/" + empId,
        true
      );
      setDocumentsUploaded(empDetails?.documents);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  const handleChange = (event: any, newValue: any) => {
    setValue(newValue);
  };

  const handleChangeIndex = (index: any) => {
    setValue(index);
  };

  const goToNextTab = () => {
    // console.log("");
    setValue((prevValue) => prevValue + 1);
  };

  const handlePersonalDetailsSubmit = (data: any) => {
    const finalData = {
      ...data,
      empName: `${data.firstName} ${
        data.middleName ? data.middleName + " " : ""
      }${data.lastName}`,
    };

    setPersonalDetails(finalData);
    goToNextTab();

    if (isEditing) {
      let payload = {
        ...finalData,
        ...addressDetails,
        ...officialDetails,
        ...contactDetails,
        companyId: companyId,
        inService: true,
      };
      createEditEmployee(payload, false);
      showSuccessAlert("employee data updated");
    }
  };

  const handleAddressDetailsSubmit = (data: any) => {
    const finalData = {
      ...data,
      countryId: parseInt(data?.countryId),
      provinceId: parseInt(data?.provinceId),
    };
    setAddressDetails(finalData);
    goToNextTab();
    if (isEditing) {
      let payload = {
        ...personalDetails,
        ...finalData,
        ...officialDetails,
        ...contactDetails,
        companyId: companyId,
        inService: true,
      };
      createEditEmployee(payload, false);
      showSuccessAlert("employee data updated");
    }
  };

  const handleOfficialDetailsSubmit = (data: any) => {
    const finalData = {
      ...data,
      designationId: parseInt(data?.designationId),
      departmentId: parseInt(data?.departmentId),
    };
    setOfficialDetails(finalData);
    goToNextTab();
    if (isEditing) {
      let payload = {
        ...personalDetails,
        ...addressDetails,
        ...finalData,
        ...contactDetails,
        companyId: companyId,
        inService: true,
      };
      createEditEmployee(payload, false);
      showSuccessAlert("employee data updated");
    }
  };

  const handleContactDetailsSubmit = async (data: any) => {
    setContactDetails(data);
    // goToNextTab();
    let payload = {
      ...personalDetails,
      ...addressDetails,
      ...officialDetails,
      ...data,
      companyId: companyId,
      inService: true,
    };
    createEditEmployee(payload,true);
  };

  const createEditEmployee = (payload: any, redirect = true) => {
    if (branchId) {
      payload = {
        ...payload,
        branchId: branchId,
        middleName: "",
      };
    }

    try {
      if (isEditing) {
        const token = localStorage.getItem("accessToken");
        payload.empName = `${payload.firstName} ${payload.middleName || ""} ${
          payload.lastName || ""
        }`.trim();
        payload.empId = empId;
        delete payload.base64EncodeFile;
        delete payload.fileName;
        delete payload.fileSize;
        delete payload.forpostfile;
        delete payload.leaveOnDaysList;
        delete payload.isEditing;

        const formdata2: any = new FormData();

        formdata2.append("file", profileImg);
        console.log("data", payload);
        formdata2.append(
          "employeeDTO",
          new Blob([JSON.stringify(payload)], { type: "application/json" })
        );

        const response = axios.post(
          API_URLS.EMPLOYEE_UPDATE(empId),
          formdata2,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        )
        .then((res) => {
          if (redirect) {
            showSuccessAlert2("Employee updated successfully!")
            window.location.href = "/adminDashboard/employee";
          }
        });
      } else {
        // console.log('payload::: ', payload);
        const formdata2: any = new FormData();

        if (payload?.forpostfile) {
          formdata2.append("file", payload?.forpostfile);
        }
        delete payload.forpostfile;

        formdata2.append(
          "employeeDTO",
          new Blob([JSON.stringify(payload)], { type: "application/json" })
        );
        const response = postRequestWithOnlyResponseDecrypted(
          API_URLS.CREATE_EMPLOYEE_DETAILS,
          formdata2
        ).then((res) => {
          setEmpId(res?.empId);
          setDetails({
            userCode: res.empCode,
            username: res.email,
            employeeId: res.empId,
            role: res.role,
          });
        });

        showSuccessAlert2("Employee created successfully!");
        setIsCreated(true);
        goToNextTab();
      }
    } catch (error) {
      // console.error("Error submitting employee data:", error);
    }
  };

  const handleLeaveSubmit = async (data: any) => {
    // setLeave(data);
    goToNextTab();
  };
  const handleDocumentSubmit = async (data: any) => {
    goToNextTab();
  };

  const handleDetailsSubmit = async (data: any) => {
    delete data.rePassword;
    const action = data.action;
    delete data.action;

    const payload = {
      ...data,
    };

    // console.log("Payload for API submission:", payload);

    try {
      const response = await postRequest(API_URLS.USER_CREATION, payload);
      // console.log("API Response:", response);

      if (response) {
        if (action === "saveAndCreateMore") {
          showSuccessAlert2("User created successfully.").then(() => {
            window.location.reload();
          });
        } else if (action === "save") {
          showSuccessAlert2("User created successfully.").then(() => {
            window.history.back();
          });
        }
      } else {
        showErrorAlert("User creation failed. Please try again.");
      }
    } catch (error) {
      showErrorAlert("Error submitting employee data.");
    }
  };

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  //   const [heading, setHeading] = useState("New Employee");

  //   useEffect(() => {
  //     const isEditing = localStorage.getItem("isEditing");
  //     if (isEditing === "true") {
  //       setHeading("Edit Employee");
  //     }
  //   }, []);

  const tabConfigEditingTrue = [
    {
      label: "Personal Details",
      component: PersonalDetailsForm,
      props: {
        data: personalDetails,
        setProfileImg,
        goToNextTab,
        onSubmit: handlePersonalDetailsSubmit,
      },
      disabled: isCreated,
    },
    {
      label: "Address Details",
      component: AddressDetailsForm,
      props: {
        data: addressDetails,
        goToNextTab,
        onSubmit: handleAddressDetailsSubmit,
      },
      disabled: isCreated,
    },
    {
      label: "Work Details",
      component: OfficialDetailsForm,
      props: {
        data: officialDetails,
        goToNextTab,
        onSubmit: handleOfficialDetailsSubmit,
        companyName: companyId, // hospitalId to companyName
        empDetail: branchId, //empDetail is branchId
      },
      disabled: isCreated,
    },
    {
      label: "Contact Details",
      component: ContactDetailsForm,
      props: {
        data: contactDetails,
        onSubmit: handleContactDetailsSubmit,
        isEditing,
      },
      disabled: isCreated,
    },
    {
      label: "Upload Document",
      component: DocumentDetailsForm,
      props: {
        data: documentsUploaded,
        empId,
        onSubmit: handleDocumentSubmit,
        refresh: () => getEmpDocuments(empId),
        disabled: false,
      },
    },
  ];

  const tabConfigEditingFalse = [
    ...tabConfigEditingTrue.slice(0, 4),
    // {
    //   label: "Leaves",
    //   component: LeaveForm,
    //   props: { empid: empId, data: Leave, onSubmit: handleLeaveSubmit },
    //   disabled: false,
    // },
    {
      label: "Upload Document",
      component: DocumentDetailsForm,
      props: {
        data: documentsUploaded,
        empId,
        onSubmit: handleDocumentSubmit,
        refresh: () => getEmpDocuments(empId),
        disabled: false,
      },
    },
    {
      label: "Login Details",
      component: DetailsForm,
      props: { data: Details, onSubmit: handleDetailsSubmit },
      disabled: false,
    },
  ];

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <h3 style={{ marginLeft: "15px" }}>
          {isEditing ? "Edit Employee" : "New Employee"}
        </h3>
        {isLoading ? (
          <p></p>
        ) : (
          <Row className="mt-4">
            <Col md={2}>
              <Tabs
                value={value}
                onChange={handleChange}
                indicatorColor="secondary"
                textColor="inherit"
                variant="fullWidth"
                aria-label="full width tabs example"
                className="tab-pannel-test"
                style={{ backgroundColor: "#6097b4", marginLeft: "15px" }}
              >
                {(isEditing ? tabConfigEditingTrue : tabConfigEditingFalse).map(
                  (tab, index) => (
                    <Tab
                      key={index}
                      label={tab.label}
                      {...a11yProps(index)}
                      style={{
                        color: "#000",
                        backgroundColor: "#ccc",
                        boxShadow: "none",
                        // fontFamily: "inherit",
                      }}
                      disabled={tab.disabled}
                    />
                  )
                )}
              </Tabs>
            </Col>

            <Col md={10}>
              <SwipeableViews
                axis={theme.direction === "rtl" ? "x-reverse" : "x"}
                index={value}
                onChangeIndex={handleChangeIndex}
              >
                {(isEditing ? tabConfigEditingTrue : tabConfigEditingFalse).map(
                  (tab: any, index) => (
                    <TabPanel
                      key={index}
                      value={value}
                      index={index}
                      dir={theme.direction}
                    >
                      <Row>
                        <tab.component {...tab.props} />
                      </Row>
                    </TabPanel>
                  )
                )}
              </SwipeableViews>
            </Col>
          </Row>
        )}
      </Container>
    </Layout>
  );
};

export default NewEmployee;

const grey = {
  50: "#F3F6F9",
  100: "#E5EAF2",
  200: "#DAE2ED",
  300: "#C7D0DD",
  400: "#B0B8C4",
  500: "#9DA8B7",
  600: "#6B7A90",
  700: "#434D5B",
  800: "#303740",
  900: "#1C2025",
};

const blue = {
  200: "#99CCFF",
  300: "#66B2FF",
  400: "#3399FF",
  500: "#007FFF",
  600: "#0072E5",
  700: "#0066CC",
};
