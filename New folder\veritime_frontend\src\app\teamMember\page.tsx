"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/Layout";
import { Container, Row, Col, Card } from "react-bootstrap";
import { appConstants } from "@/constants/appConstants.js";
import useLocalStorage from "@/services/localstorage";
import { FaArrowRight } from "react-icons/fa";
import { decryptData } from "@/utils/encryption";
import { getRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
// import './teamMember.css'
interface TeamMember {
  name: string;
  role: string;
  email: string;
  team: TeamMember[];
  photo: string;
}

const colors = [
  "#31511E",
  "#78B3CE",
  "#3D0301",
  "#9B7EBD",
  "#3B1E54",
  "#4C4B16",
  "#898121",
  "#605678",
  "#605678",
  "#091057",
];

const getBackgroundColor = (index: any) => {
  return colors[index % colors.length];
};

const TeamMembers = () => {
  const [role, setRole] = useLocalStorage("role", "");
  const [teamMembers, setTeamMembers] = useState<any>({
    hierarchy: [],
    team: [],
  });
  const [imgUrl, setImgUrl] = useState<string>("");

  useEffect(() => {
    (async () => {
      const userData = await decryptData(localStorage.getItem("userData"));
      if (userData?.empId) {
        getHierarchy(userData?.empId);
      }
    })();
    async () => {
      const userData = await decryptData(localStorage.getItem("userData"));
      if (userData?.empId) {
        getHierarchy(userData?.empId);
      }
    };
  }, []);

  const getHierarchy = async (empId: any) => {
    try {
      const data = await getRequest(`${API_URLS.TEAM_MEMBERS}/${empId}`, true);
      setTeamMembers(data);

      // setImgUrl(member.avatar || "");
      console.log("Team member", data);
    } catch (error) {
      // console.error("Error fetching hierarchy data:", error);
    }
  };
  const [imageErrors, setImageErrors] = useState<any>({});

  const getInitials = (name: any) => {
    return name?.charAt(0)?.toUpperCase() || "U";
  };
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  const handleImageError = (index: any) => {
    setImageErrors((prevErrors: any) => ({
      ...prevErrors,
      [index]: true,
    }));
  };

  const TeamCard = ({ member, index }: any) => (
    <div
      className="responsive-card"
      style={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        backgroundColor: "#EEE9DA",
        padding: "10px",
        borderRadius: "10px",
        boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
        color: "black",
        cursor: "pointer",
        justifyContent: "center",
        textAlign: "center",
        width: index === teamMembers?.hierarchy.length - 1 ? "180px" : "170px",
        height: index === teamMembers?.hierarchy.length - 1 ? "80px" : "80px",
      }}
      onClick={() => getHierarchy(member?.emp_id)}
    >
      {!member.avatar || imageErrors[index] ? (
        <div
          className="rounded-circle me-2 d-flex align-items-center justify-content-center"
          style={{
            width: "50px",
            height: "50px",
            backgroundColor: getBackgroundColor(index),
            color: "#fff",
            fontWeight: "bold",
          }}
        >
          {getInitials(member.name)}
        </div>
      ) : (
        <img
          // src={member.avatar}
          src={`${S3URL}${member.avatar}`}
          alt={member.name}
          className="rounded-circle me-2"
          style={{
            width: "50px",
            height: "50px",
            borderRadius: "50%",
            objectFit: "cover",
            marginRight: "10px",
          }}
          onError={() => handleImageError(index)}
        />
      )}
      <div title={`${member.name} - ${member.designation}`} >
        <strong style={{ fontSize: "13px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",display:"block",maxWidth:"90px" }}>{member.name}</strong>
        <div style={{ fontSize: "11px", marginTop: "5px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",display:"block",maxWidth:"90px" }}>
          {member.designation}
        </div>
      </div>
    </div>
  );
  const TeamCard2 = ({ member, index }: any) => (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        backgroundColor: "#EEE9DA",
        padding: "10px",
        borderRadius: "10px",
        boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
        width: "190px",
        height: "80px",
        marginRight: "20px",
        marginBottom: "12px",
      }}
      onClick={() => getHierarchy(member?.emp_id)}
    >
      {!member.avatar || imageErrors[index] ? (
        <div
          className="rounded-circle me-2 d-flex align-items-center justify-content-center"
          style={{
            width: "50px",
            height: "50px",
            backgroundColor: getBackgroundColor(index),
            color: "#fff",
            fontWeight: "bold",
          }}
        >
          {getInitials(member.name)}
        </div>
      ) : (
        <img
          src={`${S3URL}${member.avatar}`}
          alt={member.name}
          className="rounded-circle me-2"
          style={{
            width: "50px",
            height: "50px",
            borderRadius: "50%",
            objectFit: "cover",
            marginRight: "10px",
          }}
          onError={() => handleImageError(index)}
        />
      )}
      <div title={`${member.name} - ${member.designation}`} >
        <strong
          style={{
            fontSize: "13px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "block",
            maxWidth: "90px",
          }}
        >
          {member.name}
        </strong>
        <div
          style={{
            fontSize: "11px",
            marginTop: "5px",
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "block",
            maxWidth: "90px",
          }}
        >
          {member.designation}
        </div>
      </div>
    </div>
  );

  return (
    <Layout>
      <Container
        fluid
        style={{
          marginTop: "50px",
          height: "700px",
          overflowY: "hidden",
        }}
      >
        <div
          style={{
            marginBottom: "20px",
            marginLeft: "22px",
            textAlign: "left",
          }}
        >
          <h4>Team Members</h4>
        </div>

        <div
          style={{
            display: "flex",
            gap: "10px",
          }}
        >
          <div
            className="hierarchy-card"
            style={{
              flex: "1",
              maxWidth: "30%",
              marginLeft: "25px",
              height: "400px",
              position: "relative",
            }}
          >
            <Card
              style={{
                padding: "24px",
                boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                height: "180%",
              }}
            >
              <h5
                className="hierarchy-header"
                style={{
                  marginBottom: "20px",
                  borderBottom: "1px solid black",
                  paddingBottom: "8px",
                  fontWeight: "bold",
                  // fontFamily: "Arial, sans-serif",
                }}
              >
                Hierarchy
              </h5>
              <div
                style={{
                  height: "500px",
                  position: "relative",
                  scrollbarWidth: "thin",
                  msOverflowStyle: "none",
                  overflowY: "auto",
                  overflowX: "hidden",
                  overflow: "visible",
                }}
              >
                <ul
                  style={{
                    listStyleType: "none",
                    padding: "0",
                    margin: "0",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                  }}
                >
                  {teamMembers?.hierarchy?.map((member: any, index: number) => (
                    <li
                      key={index}
                      className="hierarchy-list-item"
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        position: "relative",
                        marginBottom: "20px",
                      }}
                    >
                      {index > 0 && (
                        <div
                          style={{
                            width: "2px",
                            height: "30px",
                            backgroundColor: "black",
                            marginTop: "-20px",
                          }}
                        ></div>
                      )}
                      <TeamCard member={member} index={index} />
                      {index === teamMembers?.hierarchy.length - 1 && (
                        <div
                          style={{
                            position: "absolute",
                            right: "-35px",
                            top: "55%",
                            transform: "translateY(-50%)",
                            fontSize: "30px",
                            fontWeight: "bold",
                            color: "black",
                            zIndex: 100,
                            padding: "2px",
                          }}
                        >
                          &rarr;
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            </Card>
          </div>

          <div
            className="team-details-card"
            style={{
              flex: "4",
              maxWidth: "80%",
              height: "400px",
            }}
          >
            <Card
              style={{
                padding: "30px",
                boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
                height: "180%",
              }}
            >
              <h5
                style={{
                  marginBottom: "20px",
                  borderBottom: "1px solid black",
                  paddingBottom: "3px",
                  fontWeight: "bold",
                  // fontFamily: "Arial, sans-serif",
                }}
              >
                Team Details
              </h5>
              <div
                style={{
                  overflowY: "auto",
                  overflowX: "hidden",
                  position: "relative",
                  scrollbarWidth: "thin",
                  msOverflowStyle: "none",
                  display: "flex",
                  flexDirection: "row",
                  flexWrap: "wrap",
                  cursor: "pointer",
                  justifyContent: "flex-start",
                }}
              >
                {teamMembers?.team?.map((member: any, idx: number) => (
                  <Row key={idx} className="mb-3">
                    <Col md={4}>
                      <TeamCard2 member={member} index={idx} />
                      {/**/}
                    </Col>
                  </Row>
                ))}
                {teamMembers?.team?.length == 0 ? (
                  <p>No team members found.</p>
                ) : null}
              </div>
            </Card>
          </div>
        </div>
      </Container>
    </Layout>
  );
};

export default TeamMembers;
