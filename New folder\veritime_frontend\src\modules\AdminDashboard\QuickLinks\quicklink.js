import React from 'react';
//import 'bootstrap/dist/css/bootstrap.min.css';
import './quicklink.css'; 

const QuickLinksCard = ({ title, links }) => {
  return (
    <>
    <div className="container d-flex justify-content-center align-items-center">
      <div className="card shadow-sm custom-card-links" >
        <div className="card-body">
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="card-title-quick">{title}</h5>
            <div className="dropdown">
              <i className="bi bi-three-dots-vertical"></i>
              <ul className="dropdown-menu" aria-labelledby="dropdownMenuButton">
                <li><a className="dropdown-item" href="#">Action</a></li>
                <li><a className="dropdown-item" href="#">Another action</a></li>
                <li><a className="dropdown-item" href="#">Something else here</a></li>
              </ul>
            </div>
          </div>
          <ul className="list-unstyled mt-4">
            {links.map((link, index) => (
              <li key={index} className="mb-2">
                <a href={link.url} className="quick-link">{link.text}</a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
    </>

  );
};

const QuickLink = () => {
  const links = [
    { text: "New Facility", url: "./adminDashboard/branchList" },
    { text: "New Department", url: "./adminDashboard/departmentList" },
    { text: "New Designation", url: "./new-designation" },
    { text: "New Schedule", url: "./new-schedule" },
    { text: "New Employee", url: "./adminDashboard/employee/empDetails" }
  ];
  
  return (
   
      <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-2">

      <QuickLinksCard 
        title="Quick Links"
        links={links}
      />
      </div>
  );
};

export default QuickLink;
