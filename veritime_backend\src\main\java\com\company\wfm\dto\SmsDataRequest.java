package com.company.wfm.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type") 
@JsonSubTypes({
        @JsonSubTypes.Type(value = SingleSmsDataRequest.class, name = "single"),
        @JsonSubTypes.Type(value = MultipleSmsDataRequest.class, name = "multiple")
})
public class SmsDataRequest {
	private String message;
	private String ems;
	private String userref;
}
