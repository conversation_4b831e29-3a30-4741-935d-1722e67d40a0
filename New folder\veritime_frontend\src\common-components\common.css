.label{
    /* font-family: 'Spline Sans'; */
font-size: 20px;
font-weight: 700;
line-height: 23.7px;
text-align: left;
color: #000000;


}
.inputtype{
  width: 253px;
height: 30px;
border-radius: 10px;
border: 2px;
opacity: 0.4px;
background:#f4f4f4;
border: 2px solid #000000
}

.button-style{
width: 180px;
margin: 20px;
height: auto;
border-radius: 10px;
/* background: #3483F9; */
/* font-family: Spline Sans; */
font-size: 20px;
font-weight: 700;
line-height: 23.7px;
text-align: center;
color: #FFFFFF;
border: none;
cursor: pointer;

}

/* src/App.css */

.App {
    /* font-family: Arial, sans-serif; */
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    flex-direction: column;
  }
  
  form {
    display: flex;
    flex-direction: column;
    width: 300px;
  }
  
  div {
    margin-bottom: 15px;
  }
  
  label {
    margin-bottom: 5px;
  }
  
  input {
    padding: 8px;
    font-size: 1rem;
  }
  
  button {
    padding: 10px;
    font-size: 1rem;
    background-color: #007bff;
    color: white;
    border: none;
    cursor: pointer;
  }
  button-back {
    padding: 10px;
    font-size: 1rem;
    color: white;
    border: none;
    cursor: pointer;
  }

  
  button:hover {
    /* background-color: #0056b3; */
  }
  
  p {
    color: red;
    font-size: 0.9rem;
    margin-top: 5px;
  }
  