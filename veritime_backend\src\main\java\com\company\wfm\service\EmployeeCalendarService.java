package com.company.wfm.service;

import java.sql.Time;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.stereotype.Service;

@Service
public class EmployeeCalendarService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public List<Map<String, Object>> getEmployeeCalendar(Long empId, int year, int month) {
        List<Map<String, Object>> calendarData = new ArrayList<>();
        Map<String, Map<String, Object>> combinedData = new HashMap<>();

        Set<String> workingDays = new HashSet<>();
        Set<String> leaveDays = new HashSet<>();
        Set<String> holidayDays = new HashSet<>();
        Set<String> weekOffDays = new HashSet<>();

       /* String shiftQuery = "SELECT es.date, es.actual_shift AS actualShiftId, es.modified_shift AS modifiedShiftId, ts.time_slot_id AS timeSlotId, ts.start_time, ts.end_time " +
                "FROM t_employee_schedule es " +
                "LEFT JOIN t_time_slot ts ON es.actual_shift = ts.time_slot_id " +
                "WHERE es.emp_id = ? AND YEAR(es.date) = ? AND MONTH(es.date) = ?";

        jdbcTemplate.query(shiftQuery, new Object[]{empId, year, month}, (rs) -> {
            String date = rs.getDate("date").toString();
            Map<String, Object> data = new HashMap<>();
            data.put("date", date);
            data.put("shiftTime", (rs.getTime("start_time") != null && rs.getTime("end_time") != null)
                    ? rs.getTime("start_time") + " - " + rs.getTime("end_time")
                    : "-");
            data.put("shift_type", "working");
            combinedData.put(date, data);
            workingDays.add(date);
        });*/

        String shiftQuery = "SELECT es.date, " +
                "es.actual_shift AS actualShiftId, " +
                "es.modified_shift AS modifiedShiftId, " +
                "ts1.time_slot_id AS actualTimeSlotId, ts1.start_time AS actualStartTime, ts1.end_time AS actualEndTime, " +
                "ts2.time_slot_id AS modifiedTimeSlotId, ts2.start_time AS modifiedStartTime, ts2.end_time AS modifiedEndTime " +
                "FROM t_employee_schedule es " +
                "LEFT JOIN t_time_slot ts1 ON es.actual_shift = ts1.time_slot_id " +
                "LEFT JOIN t_time_slot ts2 ON es.modified_shift = ts2.time_slot_id " +
                "WHERE es.emp_id = ? AND YEAR(es.date) = ? AND MONTH(es.date) = ?";

        jdbcTemplate.query(shiftQuery, new Object[]{empId, year, month}, (rs) -> {
            String date = rs.getDate("date").toString();
            Map<String, Object> data = new HashMap<>();

            // Actual shift details
            String actualShiftTime = (rs.getTime("actualStartTime") != null && rs.getTime("actualEndTime") != null)
                    ? rs.getTime("actualStartTime") + " - " + rs.getTime("actualEndTime")
                    : "-";

            // Modified shift details
            String modifiedShiftTime = (rs.getTime("modifiedStartTime") != null && rs.getTime("modifiedEndTime") != null)
                    ? rs.getTime("modifiedStartTime") + " - " + rs.getTime("modifiedEndTime")
                    : null;

            // Populate data map
            data.put("date", date);
            data.put("shiftTime", actualShiftTime);
            data.put("modified_shift", modifiedShiftTime != null ? modifiedShiftTime : null);
            data.put("shift_type", "working");

            // Add to combined data and working days
            combinedData.put(date, data);
            workingDays.add(date);
        });



        /*String leaveQuery = "SELECT start_date, end_date, reason " +
                "FROM t_employee_leave_history " +
                "WHERE emp_id = ? AND approval_status = 'APPROVED' AND " +
                "((YEAR(start_date) = ? AND MONTH(start_date) = ?) OR (YEAR(end_date) = ? AND MONTH(end_date) = ?))";

        jdbcTemplate.query(leaveQuery, new Object[]{empId, year, month, year, month}, (RowCallbackHandler) rs -> {
            LocalDate startDate = rs.getDate("start_date").toLocalDate();
            LocalDate endDate = rs.getDate("end_date").toLocalDate();
            String reason = rs.getString("reason");

            LocalDate date = startDate;
            while (!date.isAfter(endDate)) {
                String dateString = date.toString();
                leaveDays.add(dateString);

                Map<String, Object> data = combinedData.computeIfAbsent(dateString, k -> new HashMap<>());
                data.put("date", dateString);
                data.put("leaveReason", reason);
                data.put("shift_type", "leave");

                date = date.plusDays(1);
            }
        });*/

      //  added new logic for leave name added

        // Leave query to fetch leave type name
        String leaveQuery = "SELECT elh.start_date, elh.end_date, elh.reason, lm.type AS leaveTypeName " +
                "FROM t_employee_leave_history elh " +
                "INNER JOIN t_leave_master lm ON elh.leave_id = lm.leave_id " +
                "WHERE elh.emp_id = ? AND elh.approval_status = 'APPROVED' " +
                "AND ((YEAR(elh.start_date) = ? AND MONTH(elh.start_date) = ?) " +
                "OR (YEAR(elh.end_date) = ? AND MONTH(elh.end_date) = ?))";

        jdbcTemplate.query(leaveQuery, new Object[]{empId, year, month, year, month}, (RowCallbackHandler) rs -> {
            LocalDate startDate = rs.getDate("start_date").toLocalDate();
            LocalDate endDate = rs.getDate("end_date").toLocalDate();
            String reason = rs.getString("reason");
            String leaveTypeName = rs.getString("leaveTypeName");

            LocalDate date = startDate;
            while (!date.isAfter(endDate)) {
                String dateString = date.toString();
                leaveDays.add(dateString);

                Map<String, Object> data = combinedData.computeIfAbsent(dateString, k -> new HashMap<>());
                data.put("date", dateString);
                data.put("leaveReason", reason);
                data.put("leaveType", leaveTypeName); // Add leave type name
              //  data.put("shift_type", "leave");

                date = date.plusDays(1);
            }
        });

        String holidayQuery = "SELECT date, holiday AS holidayName FROM holiday_schedule " +
                "WHERE YEAR(date) = ? AND MONTH(date) = ?";

        jdbcTemplate.query(holidayQuery, new Object[]{year, month}, (rs) -> {
            String date = rs.getDate("date").toString();
            holidayDays.add(date);
            Map<String, Object> data = combinedData.computeIfAbsent(date, k -> new HashMap<>());
            data.put("date", date);
            data.put("holidayName", rs.getString("holidayName"));
            data.put("shift_type", "holiday");
        });

    // checkIn checkOut time time different logic.
      /*  String attendanceQuery = "SELECT date, CHECK_IN_TIME, CHECK_OUT_TIME FROM t_attendance " +
                "WHERE EMP_ID = ? AND YEAR(date) = ? AND MONTH(date) = ?";
        jdbcTemplate.query(attendanceQuery, new Object[]{empId, year, month}, (rs) -> {
            String date = rs.getDate("date").toString();
            Time checkInTime = rs.getTime("CHECK_IN_TIME");
            Time checkOutTime = rs.getTime("CHECK_OUT_TIME");

            Map<String, Object> data = combinedData.computeIfAbsent(date, k -> new HashMap<>());
            data.put("checkInTime", checkInTime != null ? checkInTime.toString() : "-");
            data.put("checkOutTime", checkOutTime != null ? checkOutTime.toString() : "-");

            // Extract shift start time
            String shiftTime = (String) data.get("shiftTime");
            if (shiftTime != null && !shiftTime.equals("-") && checkInTime != null) {
                // Parse the shift start time from the string (e.g., "09:00:00 - 16:00:00")
                String[] shiftTimes = shiftTime.split(" - ");
                Time shiftStartTime = Time.valueOf(shiftTimes[0]); // Parse "09:00:00"

                // Calculate time difference
                long timeDifference = checkInTime.getTime() - shiftStartTime.getTime();
                long minutesDifference = Math.abs(timeDifference) / (1000 * 60);

                if (timeDifference > 0) {
                    // Employee is late
                    data.put("attendanceStatus", "late");
                    data.put("time", minutesDifference + " minutes");
                } else if (timeDifference < 0) {
                    // Employee is early
                    data.put("attendanceStatus", "early");
                    data.put("time", minutesDifference + " minutes");  // No late minutes for early arrival
                } else {
                    // Employee is exactly on time
                    data.put("attendanceStatus", "on time");
                   // data.put("time", "0 minutes");  // No late minutes
                }
            } else {
                data.put("attendanceStatus", "no data");
               // data.put("time", "0 minutes");  // No late minutes if no shift data
            }
        });*/


        String attendanceQuery = "SELECT date, CHECK_IN_TIME, CHECK_OUT_TIME FROM t_attendance " +
                "WHERE EMP_ID = ? AND YEAR(date) = ? AND MONTH(date) = ?";
        jdbcTemplate.query(attendanceQuery, new Object[]{empId, year, month}, (rs) -> {
            String date = rs.getDate("date").toString();
            Time checkInTime = rs.getTime("CHECK_IN_TIME");
            Time checkOutTime = rs.getTime("CHECK_OUT_TIME");

            Map<String, Object> data = combinedData.computeIfAbsent(date, k -> new HashMap<>());
            data.put("checkInTime", checkInTime != null ? checkInTime.toString() : "-");
            data.put("checkOutTime", checkOutTime != null ? checkOutTime.toString() : "-");

            // Extract shift start time
            String shiftTime = (String) data.get("shiftTime");

            // Condition 1: If employee is on leave, holiday, or week off, and check-in time exists
            if ((leaveDays.contains(date) || holidayDays.contains(date) || weekOffDays.contains(date)) && checkInTime != null) {
                // Show the shift time for leave, holiday, or week off
                if (shiftTime != null && !shiftTime.equals("-")) {
                    data.put("shiftTime", shiftTime); // Show the shift time if available
                }
            }

            // Condition 2: Existing logic for lateness, early arrival, or on-time status
            else if (shiftTime != null && !shiftTime.equals("-") && checkInTime != null) {
                String[] shiftTimes = shiftTime.split(" - ");
                Time shiftStartTime = Time.valueOf(shiftTimes[0]); // Parse "09:00:00"
                long timeDifference = checkInTime.getTime() - shiftStartTime.getTime();
                long minutesDifference = Math.abs(timeDifference) / (1000 * 60);

                if (timeDifference > 0) {
                    // Employee is late
                    data.put("attendanceStatus", "late");
                    data.put("time", minutesDifference + " minutes");
                } else if (timeDifference < 0) {
                    // Employee is early
                    data.put("attendanceStatus", "early");
                    data.put("time", minutesDifference + " minutes");
                } else {
                    // Employee is on time
                    data.put("attendanceStatus", "on time");
                }
            } else {
                data.put("attendanceStatus", "no data");
            }
        });


//check for attendance  entry present or not
        String isPresentQuery = "SELECT COUNT(*) FROM t_attendance WHERE emp_id = ? AND date = ?";

     /*   YearMonth yearMonthObject = YearMonth.of(year, month);
        for (int day = 1; day <= yearMonthObject.lengthOfMonth(); day++) {
            LocalDate currentDate = LocalDate.of(year, month, day);
            String dateString = currentDate.toString();

            Map<String, Object> data = combinedData.getOrDefault(dateString, new HashMap<>());
            data.putIfAbsent("date", dateString);
            data.putIfAbsent("shift_type", workingDays.contains(dateString) ? "working" :
                    leaveDays.contains(dateString) ? "leave" :
                            holidayDays.contains(dateString) ? "holiday" : "week_off");

            // Remove shiftTime if the shift_type is holiday
            if ("holiday".equals(data.get("shift_type"))) {
                data.remove("shiftTime");
            }

            // Check if the employee has an attendance record for the day
            int attendanceCount = jdbcTemplate.queryForObject(isPresentQuery, new Object[]{empId, currentDate}, Integer.class);
            data.put("isPresent", attendanceCount > 0);

            calendarData.add(data);
        }

        return calendarData;
    }*/

        //week off is also calculating

        YearMonth yearMonthObject = YearMonth.of(year, month);

        for (int day = 1; day <= yearMonthObject.lengthOfMonth(); day++) {
            LocalDate currentDate = LocalDate.of(year, month, day);
            // If the day is a Saturday or Sunday, it's a week off
            if (currentDate.getDayOfWeek() == DayOfWeek.SATURDAY || currentDate.getDayOfWeek() == DayOfWeek.SUNDAY) {
                weekOffDays.add(currentDate.toString());  // Add the date as a week off
            }
        }
        for (int day = 1; day <= yearMonthObject.lengthOfMonth(); day++) {
            LocalDate currentDate = LocalDate.of(year, month, day);
            String dateString = currentDate.toString();

            Map<String, Object> data = combinedData.getOrDefault(dateString, new HashMap<>());
            data.putIfAbsent("date", dateString);

            // Determine shift type based on conditions
            if (leaveDays.contains(dateString)) {
                if (weekOffDays.contains(dateString)) {
                    data.putIfAbsent("shift_type", "week_off");
                    data.remove("leaveType");  // Remove any leaveType as it's a week off
                    data.remove("leaveReason"); // Remove leave reason if the day is a week off
                } else {
                    data.putIfAbsent("shift_type", "leave");
                }
            } else if (weekOffDays.contains(dateString)) {
                data.putIfAbsent("shift_type", "week_off");
                data.remove("leaveType");  // Remove any leaveType if it's a week off
                data.remove("leaveReason"); // Remove leave reason if it's a week off
            } else if (holidayDays.contains(dateString)) {
                data.putIfAbsent("shift_type", "holiday");
                data.remove("shiftTime");  // Remove shift time for holidays
            } else {
                data.putIfAbsent("shift_type", "working"); // Default is working
            }

            // Check if attendance exists for the date
            int attendanceCount = jdbcTemplate.queryForObject(isPresentQuery, new Object[]{empId, currentDate}, Integer.class);
            data.put("isPresent", attendanceCount > 0);

            // Add processed data to the final calendar data list
            calendarData.add(data);
        }

        return calendarData;
    }
}
