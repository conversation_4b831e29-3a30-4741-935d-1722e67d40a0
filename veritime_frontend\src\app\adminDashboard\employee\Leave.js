import { API_URLS } from "@/constants/apiConstants";
import {
  showErrorAlert,
  showSuccess<PERSON>lert,
  showSuccessAlert2,
} from "@/services/alertService";
import { getRequest, postRequest } from "@/services/apiService";
import React, { useEffect, useState } from "react";
import { Form, Button, Table, Alert } from "react-bootstrap";

const LeaveForm = ({ empid, data, onSubmit }) => {
  const [leaveTypes, setLeaveTypes] = useState();
  const [leaveCounts, setLeaveCounts] = useState({});
  const [errors, setErrors] = useState({});
  const [formError, setFormError] = useState("");

  useEffect(() => {
    const fetchLeaveTypes = async () => {
      try {
        const data = await getRequest(API_URLS.FETCH_LEAVE_TYPES);
        if (data) setLeaveTypes(data);

        const obj = data.reduce((acc, leaveType) => {
          acc[leaveType.leaveId] = leaveType.leaveCount;
          return acc;
        }, {});
        setLeaveCounts(obj);
        
      } catch (error) {
        setLeaveTypes();
      }
    };

    fetchLeaveTypes();
  }, []);

  const handleCountChange = (id, value) => {
    const numericValue = parseInt(value, 10);

    if (isNaN(numericValue) || numericValue <= 0) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [id]: "Count must be a valid positive number",
      }));
    } else {
      setErrors((prevErrors) => ({ ...prevErrors, [id]: "" }));
      setLeaveCounts((prevCounts) => ({ ...prevCounts, [id]: numericValue }));
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    const hasErrors = Object.values(errors).some((error) => error !== "");
    const allFieldsFilled = leaveTypes.every(
      (leaveType) =>
        leaveCounts[leaveType.leaveId] !== undefined &&
        leaveCounts[leaveType.leaveId] !== null
    );

    if (hasErrors || !allFieldsFilled) {
      setFormError(
        "Please correct the errors and ensure all fields are filled."
      );
    } else {
      setFormError("");

      const leaveBalanceData = leaveTypes.map((leaveType) => ({
        leaveTypeId: leaveType.leaveId,
        assignedLeave: leaveType.leaveCount,
        balanceLeave: leaveType.leaveCount, 
        empId: empid,
      }));

      try {
        const response = await postRequest(
          API_URLS.CREATE_LEAVE_BALANCE,
          leaveBalanceData
        );
        // console.log("Leave balances saved successfully:", response);
        if (response) {
          onSubmit();
          showSuccessAlert2("Leave balance saved successfully");
        }
      } catch (error) {
        // console.error("Error saving leave balances:", error);
        showErrorAlert("ERROR");
        onSubmit();
      }
    }
  };

  return (
  
    <div style={{ width: "70%", margin: "20px auto" }}>
        <h2 style={headingStyle}>Leave Details </h2>
      <Form onSubmit={handleSubmit}>
        {formError && <Alert variant="danger">{formError}</Alert>}
        <Table bordered>
          <thead>
            <tr>
              <th>Leave Type</th>
              <th>Days</th>
            </tr>
          </thead>
          <tbody>
            {leaveTypes?.map((leaveType) => (
              <tr key={leaveType.leaveId}>
                <td>{leaveType.type}</td>
                <td>
                  <Form.Control
                    type="text"
                    size="sm"
                    value={leaveType.leaveCount}
                    onChange={(e) =>
                      handleCountChange(leaveType.leaveId, e.target.value)
                    }
                    isInvalid={!!errors[leaveType.leaveId]}
                    style={{ width: "90px" }}
                  />
                  <Form.Control.Feedback type="invalid">
                    {errors[leaveType.leaveId]}
                  </Form.Control.Feedback>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
        {Object.values(errors).some((error) => error !== "") && (
          <Alert variant="danger">Please fix the errors in the form.</Alert>
        )}
        <div style={formRowStyle}>
          <button type="submit" style={submitButtonStyle}>
            Save
          </button>
        </div>
      </Form>
    </div>
  );
};

const formRowStyle = {
  width: "100%",
  display: "flex",
  marginBottom: "20px",
};

const submitButtonStyle = {
  width: "150px",
  padding: "10px 20px",
  borderRadius: "4px",
  border: "none",
  backgroundColor: "#007bff",
  color: "#fff",
  fontSize: "16px",
  cursor: "pointer",
  textAlign: "center",
};

const headingStyle = {
  fontSize: "24px", // Adjust size as necessary
  fontWeight: "bold",
  color: "#333", // Adjust color as needed
  marginBottom: "20px", // Adjust space below the heading
};

export default LeaveForm;
