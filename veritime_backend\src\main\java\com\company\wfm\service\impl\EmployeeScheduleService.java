package com.company.wfm.service.impl;
import java.time.LocalDate;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.dto.ShiftChangeDto;
import com.company.wfm.entity.Employee;
import com.company.wfm.entity.EmployeeSchedule;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.EmployeeScheduleRepository;

@Service
public class EmployeeScheduleService {

	private static final Logger logger = LoggerFactory.getLogger(EmployeeScheduleService.class);

	@Autowired
	private EmployeeScheduleRepository employeeScheduleRepository;

	@Autowired
	private EmployeeRepository employeeRepository;

//	@Scheduled(cron = "0 0 21 * * ?") // Runs at 9:00 PM every day
	@Transactional
	public void scheduledCreateSchedulesForNextFifteenDays() {
		createSchedulesForNextFifteenDays();
	}

	@Transactional
	public void createSchedulesForNextFifteenDays() {
		logger.info("Starting schedule creation for the next 15 days");

		LocalDate startDate = LocalDate.now().plusDays(7); // 1 week from now
		LocalDate endDate = startDate.plusDays(14); // 15 days period

		List<Employee> employees = employeeRepository.findAll();
		int schedulesCreated = 0;

		for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
			for (Employee employee : employees) {
				EmployeeSchedule schedule = new EmployeeSchedule();
				schedule.setEmployee(employee);
				schedule.setDate(date);
				schedule.setActualShift("DEFAULT_SHIFT"); // Set a default shift
				schedule.setModifiedShift("DEFAULT_SHIFT");
				employeeScheduleRepository.saveAndFlush(schedule);
				schedulesCreated++;
			}
		}

		logger.info("Schedule creation completed. Created {} schedules for {} employees from {} to {}",
				schedulesCreated, employees.size(), startDate, endDate);
	}

	public List<EmployeeSchedule> getSchedulesForDateRange(LocalDate startDate, LocalDate endDate) {
		return employeeScheduleRepository.findByDateBetween(startDate, endDate);
	}

	@Transactional
	public EmployeeSchedule changeEmployeeShift(ShiftChangeDto request) {
		EmployeeSchedule schedule = employeeScheduleRepository
				.findByEmployeeEmpIdAndDate(request.getEmployeeId(), request.getDate())
				.orElseThrow(() -> new RuntimeException("Schedule not found for the given employee and date"));

		if (request.getSwapWithEmployeeId() == null) {
			// Simple shift change
			schedule.setModifiedShift(request.getNewShift());
		} else {
			// Shift swap
			EmployeeSchedule swapSchedule = employeeScheduleRepository
					.findByEmployeeEmpIdAndDate(request.getSwapWithEmployeeId(), request.getDate())
					.orElseThrow(() -> new RuntimeException("Schedule not found for the swap employee"));

			// Swap shifts
			String tempShift = swapSchedule.getModifiedShift();
			swapSchedule.setModifiedShift(schedule.getActualShift());
			schedule.setModifiedShift(tempShift);

			// Update swapped employee IDs
			schedule.setSwapedWithEmpId(request.getSwapWithEmployeeId());
			swapSchedule.setSwapedWithEmpId(schedule.getEmployee().getEmpId());

			employeeScheduleRepository.saveAndFlush(swapSchedule);
		}

		return employeeScheduleRepository.saveAndFlush(schedule);
	}
}
