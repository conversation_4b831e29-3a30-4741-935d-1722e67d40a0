package com.company.wfm.dto;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EscalationDetailsDTO {
    private Long createdBy;            // ID of the person who created the escalation
    private String createdByName;        // Name of the person who created the escalation
    private LocalDateTime createdAt;
    private Long escalatedTo;            // ID of the person to whom the ticket is escalated.
    private String escalatedToName;

}
