import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';

export default function DataTable({ columns, rows, autoRowHeight = false, onPageChange, totalCount, pageSize }) {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [filteredRows, setFilteredRows] = React.useState(rows);

  React.useEffect(() => {
    const lowercasedSearchTerm = searchTerm.toLowerCase();
    const newFilteredRows = rows.filter((row) =>
      columns.some((column) =>
        String(row[column.field])
          .toLowerCase()
          .includes(lowercasedSearchTerm)
      )
    );
    setFilteredRows(newFilteredRows);
  }, [searchTerm, rows, columns]);

  const handlePaginationChange = (paginationModel) => {
    onPageChange(paginationModel);
  };

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <DataGrid
        rows={filteredRows}
        columns={columns}
        initialState={{
          pagination: {
            paginationModel: { page: 0, pageSize: pageSize },
          },
        }}
        pageSizeOptions={[10, 20,50,100]}
        paginationMode="server" 
        rowCount={totalCount}
        onPaginationModelChange={handlePaginationChange}
        style={{ background: 'white' }}
        getRowHeight={autoRowHeight ? () => 'auto' : undefined}
      />
    </div>
  );
}
