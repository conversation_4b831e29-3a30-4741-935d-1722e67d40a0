
.notification-card {
    width: 300px;
    height: 349px;
    border-radius: 20px;
    background: var(--blue7);


  }
  
  .notification-card .card-body {
    background: white;
    border-radius: 10px;
    padding: 20px;
  }
  
  .list-group-item {
    border: none;
    font-size: 20px;
    color: var(--red3);
  }
  
  .divider {
    border: 1px solid var(--red5);
    margin: 0;
  }
  
  .notification-card .badge {
    font-size: 12px;
    /* font-family: <PERSON>, sans-serif; */
    line-height: 18px;
  }
  
  .notification-card .text-primary {
    color: var(--cyan1) !important;
  }
  .fonts{
    font-size: 12px;

  }
.notification-container {
  display: table;
  margin: auto;
  position: absolute;
  right: 150px;
  top: 40px;
  overflow-x: hidden;
  width: 350px;
  border-radius: 10px;
}

@media (max-width: 768px) {
  .notification-container {
      position: fixed;
      position: fixed;
      right: 10px;
      right: 10px;
      top: 60px;
      width: 90%;
      max-width: 400px;
      margin: 0 auto;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

@media (max-width: 480px) {
  .notification-container {
      width: 95%;
      right: 5px;
  }

}

  