package com.company.wfm.service.impl;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.company.wfm.entity.LogHistory;
import com.company.wfm.repository.LogHistoryRepository;
import com.company.wfm.service.LogHistoryService;

@Service
public class LogHistoryServiceImpl implements LogHistoryService {

	@Autowired
	private LogHistoryRepository logHistoryRepository;

	@Override
	public long saveLoginLog(Long empId, String status, String remark, String fbToken, String sessionId) {
		LogHistory logHistory = new LogHistory();
		logHistory.setEmpId(empId);
		logHistory.setStatus(status);
		logHistory.setRemark(remark);
		logHistory.setFbToken(fbToken);
		logHistory.setSessionId(sessionId);
		logHistory.setCreatedAt(LocalDateTime.now());
		logHistory.setUpdatedAt(null); // As it is first login attempt, updated_at is null initially
		logHistory.setIsActive(true); // As per your table structure, default is active

		logHistoryRepository.save(logHistory);
		return logHistory.getId();
	}

}
