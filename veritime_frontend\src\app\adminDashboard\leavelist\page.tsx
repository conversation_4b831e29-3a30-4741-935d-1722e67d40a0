"use client";
import { useState, useEffect } from "react";
import { Container, Row, Col, Button, Form } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter.js";
import { useTheme } from "@material-ui/core/styles";
import CreateLeaveModal from "../modals/CreateLeaveModal";
import EditIcon from "@mui/icons-material/Edit";
import { TabPanel } from "../../../common-components/utils.js";
import Layout from "@/components/Layout";
import { appConstants } from "@/constants/appConstants.js";
import DeleteIcon from "@mui/icons-material/Delete";
import { deleteRequest, getRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import EyeIcon from "@mui/icons-material/Visibility";
import LeaveDetailModal from "../modals/LeaveDetailModal";
import { showErrorAlert, showSuccessAlert2 } from "@/services/alertService.js";

const LeaveList = ({ toggleMenu, expanded }: any) => {
  const [username, setUsername] = useState("");
  const [rows, setRows] = useState<any>([]);
  const [rowToEdit, setRowToEdit] = useState<any>({});
  const [role, setRole] = useState("");

  const [openModal, setOpenModal] = useState(false);
  const handleOpen = () => {
    setRowToEdit(null);
    setOpenModal(true);
  };

  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedLeave, setSelectedLeave] = useState<any>(null);

  const [statusFilter, setStatusFilter] = useState<string>("all"); // 'all', 'active', 'inactive'

  const handleViewDetails = (row: any) => {
    setSelectedLeave(row);
    setShowDetailModal(true);
  };

  useEffect(() => {
    const storedRole = localStorage.getItem(appConstants?.role);
    if (storedRole) {
      setRole(storedRole);
    }

    fetchLeaveData();
  }, [statusFilter]);

  const fetchLeaveData = async () => {
    try {
      let url = API_URLS.MASTER_LEAVE_LIST;

      if (statusFilter !== "all") {
        url = `${API_URLS.MASTER_LEAVE_LIST}?isActive=${
          statusFilter === "active" ? "true" : "false"
        }`;
      }
      const raw = await getRequest(url);

      const endOfArray = raw.lastIndexOf("]");
      if (endOfArray === -1) {
        console.error("Response does not contain a JSON array");
        return;
      }

      const jsonArrayString = raw.slice(0, endOfArray + 1);

      let leaveList: any[];
      try {
        leaveList = JSON.parse(jsonArrayString);
      } catch (e) {
        console.error("Failed to JSON.parse array:", e);
        return;
      }

      // Map the parsed data
      const formattedData = leaveList.map((leave: any, index: number) => {
        console.log("Mapping leave:", leave.leaveId);
        return {
          id: leave.leaveId,
          srno: index + 1,
          leaveName: leave.type,
          availableDays: leave.leaveCount,
          availedDays: (leave.daysAppliedBefore ?? 0) + " days",
          createdDate: leave.createdTime
            ? `${leave.createdTime[0]}-${String(leave.createdTime[1]).padStart(
                2,
                "0"
              )}-${String(leave.createdTime[2]).padStart(2, "0")}`
            : "N/A",
          modifiedDate: leave.updatedTime
            ? `${leave.updatedTime[0]}-${String(leave.updatedTime[1]).padStart(
                2,
                "0"
              )}-${String(leave.updatedTime[2]).padStart(2, "0")}`
            : "N/A",
          createdByName: leave.createdByName || "N/A",
          updatedByName: leave.updatedByName || "N/A",
          leaveDescription: leave.description,
          leaveCredited: leave.leaveCount || 0,
          allowFileUpload: leave.allowFileUpload || false,
          daysApplied: leave.daysAppliedBefore || 0,
          descriptionList: leave.description,
          leaveExclusion: leave.leaveExclusion || [],
          leaveCreditBasis: leave.leaveCreditBasis,
          leaveCreditMethod: leave.leaveCreditMethod,
          effectiveLeaveDate: leave.effectiveLeaveDate || "",
        };
      });

      console.log("Formatted data saved:", formattedData);
      setRows(formattedData);
    } catch (error) {}
  };

  const handleEdit = (row: any) => {
    setOpenModal(true);
    setRowToEdit(row);
  };

  const handleSave = async (updatedRow: any) => {
    await fetchLeaveData();
    setOpenModal(false);
  };

  const theme = useTheme();
  const [value, setValue] = useState(0);

  const handleDelete = async (id: number) => {
    try {
      const response = await deleteRequest(`${API_URLS.DELETE_LEAVE_LIST(id)}`);
      if (response) {
        setRows(rows.filter((row: any) => row.id !== id));
        showSuccessAlert2(response);
      } else {
        showErrorAlert("Failed to delete the leave.");
      }
    } catch (error) {}
  };

  const columns = [
    { field: "srno", headerName: "Sr.no", width: 100 },
    { field: "leaveName", headerName: "Leave Type", width: 200 },
    { field: "availableDays", headerName: "Default Count", width: 150 },
    { field: "availedDays", headerName: "Leave Request Lead Time", width: 250 },
    { field: "descriptionList", headerName: "Description List", width: 250 },
    // { field: "leaveCreditMethod", headerName: "leave CreditMethod", width: 250 },
    // { field: "leaveCreditBasis", headerName: "leaveCreditBasis", width: 250 },
    ...(role !== "admin" && role !== "superadmin"
      ? [
          {
            field: "actions",
            headerName: "Actions",
            width: 200,
            renderCell: (params: any) => (
              <strong>
                <EyeIcon
                  onClick={() => handleViewDetails(params.row)}
                  style={{ cursor: "pointer", marginRight: 8 }}
                />
                &nbsp; &nbsp;&nbsp;
                <EditIcon
                  onClick={() => handleEdit(params.row)}
                  style={{ cursor: "pointer", marginRight: 2 }}
                />
              </strong>
            ),
          },
        ]
      : []),
  ];

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <Row className="my-3">
          <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
            <h4>{"Leave List"}</h4>
          </Col>
          <Col md={8}>
            {role === "ceo" && (
              <Row style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  style={{
                    width: "150px",
                    background: "green",
                    marginRight: "15px",
                  }}
                  onClick={handleOpen}
                >
                  + Add Leave
                </Button>
              </Row>
            )}
          </Col>
        </Row>

        {/* Add filter for active/inactive/all */}
        <Row className="my-3">
          <Col md={4} style={{ marginLeft: "27px", width: "20%" }}>
            <Form.Control
              as="select"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </Form.Control>
          </Col>
        </Row>

        <Row>
          <Col md={12}>
            <TabPanel value={value} index={0}>
              <TableFilter rows={rows} columns={columns} />
            </TabPanel>
          </Col>
        </Row>

        {openModal && (
          <CreateLeaveModal
            show={openModal}
            handleClose={() => setOpenModal(false)}
            handleSave={handleSave}
            rowToEdit={rowToEdit}
          />
        )}
        {showDetailModal && (
          <LeaveDetailModal
            show={showDetailModal}
            handleClose={() => setShowDetailModal(false)}
            leaveDetails={selectedLeave}
          />
        )}
      </Container>
    </Layout>
  );
};

export default LeaveList;
