package com.company.wfm.service.impl;

import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

public class LocalDateTimeAdapter implements JsonSerializer<Object>, JsonDeserializer<Object> {

  //  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    private static final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

   /* @Override
    public JsonElement serialize(LocalDateTime src, Type typeOfSrc, JsonSerializationContext context) {
        return new JsonPrimitive(src.format(formatter));
    }

    @Override
    public LocalDateTime deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        return LocalDateTime.parse(json.getAsString(), formatter);
    }*/

    @Override
    public JsonElement serialize(Object src, Type typeOfSrc, JsonSerializationContext context) {
        if (src instanceof LocalDateTime) {
            return new JsonPrimitive(((LocalDateTime) src).format(dateTimeFormatter));
        } else if (src instanceof LocalTime) {
            return new JsonPrimitive(((LocalTime) src).format(timeFormatter));
        } else if (src instanceof LocalDate) {
            return new JsonPrimitive(((LocalDate) src).format(dateFormatter));
        } else {
            throw new JsonParseException("Unsupported type for serialization: " + typeOfSrc.getTypeName());
        }
    }

    @Override
    public Object deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        if (typeOfT.equals(LocalDateTime.class)) {
            return LocalDateTime.parse(json.getAsString(), dateTimeFormatter);
        } else if (typeOfT.equals(LocalTime.class)) {
            return LocalTime.parse(json.getAsString(), timeFormatter);
        }  else if (typeOfT.equals(LocalDate.class)) {
            return LocalDate.parse(json.getAsString(), dateFormatter);
        }else {
            throw new JsonParseException("Unsupported type for deserialization: " + typeOfT.getTypeName());
        }
    }
}
