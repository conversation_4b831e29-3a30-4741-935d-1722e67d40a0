package com.company.wfm.entity;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Data;


@Entity
@Data
@Table(name = "feeder_hospitals")
public class Hospital {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "hospital_name", nullable = false)
    private String hospitalName;

    @Column(name = "short_code", nullable = false)
    private String shortCode;

    @ManyToOne
    @JoinColumn(name = "province_id", referencedColumnName = "id", nullable = false)
    private Province province;  // Mapping to Province entity

    @ManyToOne
    @JoinColumn(name = "district_id", referencedColumnName = "id", nullable = false)
    @JsonIgnore
    private District district;  // Mapping to District entity

   /* @ManyToOne
    @JoinColumn(name = "sub_district_id", referencedColumnName = "id", nullable = false)
    private SubDistrict subDistrict;  // Mapping to SubDistrict entity*/
   @ManyToOne
   @JoinColumn(name = "sub_district_id", referencedColumnName = "id", nullable = false)
   private SubDistrict subDistrict;  // Update this line to use the correct column name


    @Column(name = "address_line1", nullable = false)
    private String addressLine1;

    @Column(name = "address_line2")
    private String addressLine2;

    @Column(name = "lat")
    private Double lat; // Optional

    @Column(name = "lng")
    private Double lng; // Optional

    @Column(name = "validity", nullable = false)
    private LocalDateTime validity;

    @Column(name = "created_by", nullable = false)
    private Long createdBy;  // Store user ID as Long

    @Column(name = "updated_by", nullable = false)
    private Long updatedBy;  // Store user ID as Long

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "hospital_type", nullable = false)
    private String hospitalType;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive;

    @Column(name = "cluster_name", nullable = true) // New field
    private String clusterName;


    @OneToMany(mappedBy = "company", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Branch> branches;

    public List<Branch> getBranches() {
        return branches;
    }

    public void setBranches(List<Branch> branches) {
        this.branches = branches;
    }

    @Override
    public String toString() {
        return "Hospital{" +
                "id=" + id +
                ", hospitalName='" + hospitalName + '\'' +
                ", shortCode='" + shortCode + '\'' +
                ", addressLine1='" + addressLine1 + '\'' +
                ", lat=" + lat +
                ", lng=" + lng +
                ", validity=" + validity +
                ", createdBy=" + createdBy +
                ", updatedBy=" + updatedBy +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", hospitalType='" + hospitalType + '\'' +
                ", isActive=" + isActive +
                ", clusterName='" + clusterName + '\'' +
                '}';

    }

}
