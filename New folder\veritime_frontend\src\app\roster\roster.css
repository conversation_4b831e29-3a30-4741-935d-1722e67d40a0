
.grid-layout-container-roaster {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    background-color: var(--blue10);
  }
  
  .controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    width: 100%;
    justify-content: center;
  }
  
  .header-menu-roaster {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: var(--yellow2);
    border-radius: 14px;
    height: 40px;
    padding: 0 10px;
  }
  
  .go-button-roaster {
    width: 100%;
    background-color: #efe9da;
    border: none;
    border-radius: 10px;
    color: var(--blue9);
    font-weight: bold;
  }
  .go-button-roaster:hover {
    background-color: #efe9da; 
    color: var(--blue9); 
}


p.h8 {
  font-size: 10px;
}


@media (max-width: 600px) {
  p.h8 {
    font-size: 10px;
  }
}


@media (min-width: 601px) and (max-width: 1024px) {
  p.h8 {
    font-size: 10px;
  }
}


@media (min-width: 1200px) {
  p.h8 {
    font-size: 14px;
  }
}

  

  