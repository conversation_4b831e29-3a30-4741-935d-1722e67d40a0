package com.company.wfm.repository;


import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.company.wfm.entity.ShiftSwap;

public interface ShiftSwapRepository extends JpaRepository<ShiftSwap, Long> {
    List<ShiftSwap> findByRequestedScheduleAndSwapRequestDateBetween(
            String requestedSchedule, LocalDate startDate, LocalDate endDate);
    Optional<ShiftSwap> findBySwapRequestId(Long swapRequestId);


   /* List<ShiftSwap> findByEmployeeIdIn(List<Long> employeeIds);
    List<ShiftSwap> findByEmployeeId(String employeeId);*/

    List<ShiftSwap> findBySwapRequestedByEmployeeIdIn(List<Long> employeeIds);
    List<ShiftSwap> findBySwapRequestedByEmployeeId(Long employeeId);
}
