package com.company.wfm.dto;


import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TicketCategoryDTO {

    private Long id;
    private String category;
    private String priority;
    private Long createdBy;
    private String createdByName;
    private LocalDateTime createdAt;
    private Long updatedBy;
    private String updatedByName;
    private LocalDateTime updatedAt;
    private Boolean isActive;
    private BigDecimal eta;
    private Long branchId;
    private String branchName;
    private Long departmentId;
    private String departmentName;

}
