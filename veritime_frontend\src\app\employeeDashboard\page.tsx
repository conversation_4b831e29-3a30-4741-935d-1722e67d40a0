"use client"
import EmployeeCalender from "../../modules/EmployeeDashboard/EmployeeCalender";
import WeeklyWorkingHours from "../../modules/EmployeeDashboard/HoursCheck";
import LeaveBalanceChart from "../../modules/EmployeeDashboard/LeaveChart";
import TicketAssigned from "../../modules/EmployeeDashboard/TicketAssigned";
import MyTicketsChart from "../../modules/EmployeeDashboard/TicketComponent";
import WeeklyAttendance from "../../modules/EmployeeDashboard/Weeklywise";
import Layout from "../../components/Layout";
import React, { useEffect, useState } from 'react';
import './EmployeeDashboard.css'
import { colors } from '../../constants/colors';



const EmployeeDashboard = () => {
  const [mediumScreen, setMedimScreen] = useState(false)

  useEffect(() => {
    const handleResize = () => {
      if (window.screen.width >= 768 && window.screen.width <= 992) {
        setMedimScreen(true)
      } else {
        setMedimScreen(false)
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const [isMounted, setIsMounted] = useState(false);
    useEffect(() => {
    setIsMounted(true);
    }, []);
      
    if (!isMounted) return null;

  return (
    <Layout>
      <div className="container-fluid p-2 pt-4" style={{ backgroundColor: colors.blue12 }}>
        <div className="col-12 mt-4 mb-4">
          <p className="dashboardHeader" style={{ paddingLeft: "20px" }}>Dashboard</p>
        </div>
        <div className="row">
            <LeaveBalanceChart />

            <WeeklyAttendance />


            {/* <MyTicketsChart data /> */}


        </div>

        <div className="row">

            <EmployeeCalender />

   

            <TicketAssigned />
        </div>
        <WeeklyWorkingHours />
      </div>
    </Layout>
  );
}

export default EmployeeDashboard;
