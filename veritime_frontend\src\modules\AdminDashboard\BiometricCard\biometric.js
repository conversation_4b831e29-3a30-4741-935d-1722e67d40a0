import React from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import "./biometric.css";

const DynamicCard = ({ title, value1, label1, value2, label2 }) => {
  return (
    <div className="container mt-3 mb-4 p-0">
      <div className="card shadow-sm w-100" style={{ height: "270px" }}>
        <div className="card-body">
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="card-title-biometric fw-bold">
              Biometric Enrollments
            </h5>
            <div className="dropdown">
              <i className="bi bi-three-dots-vertical"></i>
              <ul
                className="dropdown-menu"
                aria-labelledby="dropdownMenuButton"
              >
                <li>
                  <a className="dropdown-item" href="#">
                    Action
                  </a>
                </li>
                <li>
                  <a className="dropdown-item" href="#">
                    Another action
                  </a>
                </li>
                <li>
                  <a className="dropdown-item" href="#">
                    Something else here
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="row">
            <div className="col-12 col-md-6 text-center p-4 border-end">
              <h2 className="display-4 text-secondary fw-bold value-text">
                {value1}
              </h2>
              <p className="text-muted fw-bold label-text">{label1}</p>
            </div>
            <div className="col-12 col-md-6 text-center p-4">
              <h2 className="display-4 text-success fw-bold value-text">
                {value2}
              </h2>
              <p className="text-muted fw-bold label-text">{label2}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const Biometric = ({ totalEnrollmentsCount, totalEmpCount }) => {
  return (
    <>
    <div className="col-12 col-lg-6 col-xl-6 col-md-12 mb-3">

      <div>
        <DynamicCard
          value1={totalEnrollmentsCount || 0}
          label1="Total Employee"
          value2={totalEmpCount || 0}
          label2="Total Enrollments"
        />
      </div>
    </div>
    </>
    );
};

export default Biometric;
