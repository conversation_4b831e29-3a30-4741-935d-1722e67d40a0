package com.company.wfm.service.impl;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.company.wfm.dto.AttendanceAuditDetailsDTO;
import com.company.wfm.dto.AttendanceAuditDetailsListDTO;
import com.company.wfm.dto.AttendanceAuditDto;
import com.company.wfm.dto.AttendanceAuditResponseDTO;
import com.company.wfm.dto.AttendanceDto;
import com.company.wfm.dto.AttendanceResponseDTO;
import com.company.wfm.dto.EmployeeAttendanceDTO;
import com.company.wfm.dto.EmployeePastPunchDataDTO;
import com.company.wfm.dto.EmployeePunchDTO;
import com.company.wfm.dto.EmployeePunchDataDTO;
import com.company.wfm.entity.AttendanceAuditEntity;
import com.company.wfm.entity.AttendanceEntity;
import com.company.wfm.entity.AttendanceInfo;
import com.company.wfm.entity.Employee;
import com.company.wfm.repository.AttendanceAuditRepository;
import com.company.wfm.repository.AttendanceRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.EmployeeScheduleRepository;
import com.company.wfm.row.mapper.AttendanceInfoMapper;
import com.company.wfm.service.AttendanceAuditService;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.util.CommonConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of the AttendanceAuditService interface that handles attendance audit operations.
 * This service is responsible for processing attendance records, syncing attendance data,
 * and managing attendance audit information for employees.
 * 
 * Key responsibilities:
 * - Processing daily attendance records
 * - Syncing attendance data from various sources
 * - Managing attendance audit information
 * - Handling attendance regularization
 * - Providing attendance reports and analytics
 */
@Service
@Slf4j
public class AttendanceAuditServiceImpl implements AttendanceAuditService {

	@Autowired
	AttendanceAuditRepository attendanceAuditRepository;

	@Autowired
	AttendanceRepository attendanceRepository;

	@Autowired
	EmployeeRepository employeeRepository;

	@Autowired
	UserTokenService tokenService;

	@Autowired
	private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

	@Autowired
	private JdbcTemplate jdbcTemplate;
	
	@Autowired
	private EmployeeScheduleRepository employeeScheduleRepository;

	private final static int BATCH_SIZE = 5000;

	/**
	 * Retrieves the first and last punch records for the previous day.
	 * This method is used to generate attendance records for the previous day's data.
	 * 
	 * @return List of EmployeePunchDataDTO containing employee ID, first punch time, last punch time, and date
	 */
	@Override
	public List<EmployeePunchDataDTO> getFirstAndLastSwapsForPreviousDay() {
		LocalDateTime startDate = LocalDate.now().minusDays(1).atStartOfDay();
		LocalDateTime endDate = startDate.toLocalDate().atTime(LocalTime.MAX);

		log.info("Scheduler started to generate attendance records from {} to {}", startDate, endDate);

		List<Object[]> results = attendanceAuditRepository.findFirstAndLastPunches(startDate, endDate);

		return results.stream().map(r -> new EmployeePunchDataDTO((Long) r[0], (Time) r[1], (Time) r[2], (java.util.Date) r[3])).toList();
	}

	/**
	 * Inserts attendance records for the provided list of employee punch data.
	 * This method processes each punch record, checks if an attendance record already exists,
	 * and creates new attendance records only for employees without existing records for the given date.
	 * 
	 * @param punchDataList List of EmployeePunchDataDTO containing employee punch information
	 */
	@Override
	@Transactional
	public void insertAttendanceRecords(List<EmployeePunchDataDTO> punchDataList) {
		log.info("received attendance data {}",punchDataList);
		List<AttendanceEntity> results = new ArrayList<>();
		punchDataList.forEach(punchData -> {
			var empId = punchData.empId();
			var firstPunch = punchData.firstSwap();
			var lastPunch = punchData.lastSwap();
			var punchDate = punchData.date();

			LocalDate localDate;

			if (punchDate instanceof java.sql.Date) {
				localDate = ((java.sql.Date) punchDate).toLocalDate();
			} else {
				localDate = punchDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			}

			var employee = getEmployeeById(empId);

			if (isAttendanceRecordExists(employee, localDate)) {
				log.info("Attendance record already exists for Employee ID: {} for {}", empId, localDate);
			} else {
				AttendanceEntity attendance = createAttendanceEntity(employee, firstPunch, lastPunch, localDate);
				results.add(attendance);
				log.info("Attendance record inserted for Employee ID: {} for {}", empId, localDate);
			}
		});

		if (!Collections.isEmpty(results)) {
			attendanceRepository.saveAllAndFlush(results);
			log.info("Saved the attendance details");
		}

	}


	/**
	 * Retrieves attendance records for the currently logged-in employee within the specified date range.
	 * 
	 * @param toDate End date of the range
	 * @param fromDate Start date of the range
	 * @return List of AttendanceResponseDTO containing attendance information
	 */
	@Override
	public List<AttendanceResponseDTO> getEmployeeAttendanceByDateRange(LocalDate toDate, LocalDate fromDate) {
		Long empId = tokenService.getEmployeeIdFromToken();
		return attendanceRepository.findByEmpIdAndDateRange(empId, toDate, fromDate);
	}


	/**
	 * Retrieves attendance records for multiple employees within the specified date range.
	 * 
	 * @param param AttendanceDto containing list of employee IDs and date range
	 * @return List of AttendanceEntity containing attendance information
	 */
	@Override
	public List<AttendanceEntity> getMultipleEmployeeAttendanceByDateRange(AttendanceDto param) {
		return attendanceRepository.findByMultipleEmpIdAndDateRange(param.getEmployeeList(), param.getFromDate(),
				param.getToDate());
	}


	/**
	 * Retrieves attendance audit records for the currently logged-in employee within the specified date range.
	 * 
	 * @param fromDate Start date of the range
	 * @param toDate End date of the range
	 * @return List of AttendanceAuditResponseDTO containing attendance audit information
	 */
	@Override
	public List<AttendanceAuditResponseDTO> getEmployeeAttendanceAuditByDateRange(LocalDate fromDate, LocalDate toDate) {
		List<AttendanceAuditEntity> attendanceAuditEntities = attendanceAuditRepository.findEmployeeFirstAndLastPunches(tokenService.getEmployeeIdFromToken(), fromDate, toDate);

		// Convert the list of AttendanceAuditEntities to AttendanceAuditResponseDTOs
		return attendanceAuditEntities.stream()
				.map(entity -> new AttendanceAuditResponseDTO(
						entity.getId(),
						entity.getEmpId() != null ? entity.getEmpId().getEmpId() : null, // Map empId
						entity.getEmpId() != null ? entity.getEmpId().getEmpName() : null,
						entity.getPunchInTime(),
						entity.getDeviceType(),
						entity.getCreatedBy(),
						entity.getCreatedDate(),
						entity.getDate()
				))
				.collect(Collectors.toList());
	}


	/**
	 * Retrieves attendance audit records for multiple employees within the specified date range.
	 * 
	 * @param param AttendanceAuditDto containing list of employee IDs and date range
	 * @return List of AttendanceAuditEntity containing attendance audit information
	 */
	@Override
	public List<AttendanceAuditEntity> getMultipleEmployeeAttendanceAuditByDateRange(AttendanceAuditDto param) {
		return attendanceAuditRepository.findByMultipleEmpIdAndDateRange(param.employeeList, param.getFromDate(),
				param.getToDate());
	}


	/**
	 * Retrieves an employee by their ID.
	 * 
	 * @param empId Employee ID
	 * @return Employee entity
	 * @throws RuntimeException if employee is not found
	 */
	private Employee getEmployeeById(Long empId) {
		return employeeRepository.findById(empId)
				.orElseThrow(() -> new RuntimeException("Employee not found with ID: " + empId));
	}

	/**
	 * Checks if an attendance record already exists for the given employee and date.
	 * 
	 * @param empId Employee entity
	 * @param date Date to check
	 * @return true if record exists, false otherwise
	 */
	private boolean isAttendanceRecordExists(Employee empId, LocalDate date) {
		return attendanceRepository.findByEmpIdAndDate(empId, date).isPresent();
	}

	/**
	 * Creates a new attendance entity with the provided information.
	 * 
	 * @param employee Employee entity
	 * @param checkInTime Check-in time
	 * @param checkOutTime Check-out time
	 * @param date Attendance date
	 * @return AttendanceEntity with the provided information
	 */
	private AttendanceEntity createAttendanceEntity(Employee employee, Time checkInTime, Time checkOutTime,
													LocalDate date) {
		return AttendanceEntity.builder().empId(employee).checkInTime(checkInTime).checkOutTime(checkOutTime)
				.createdBy(00L).createdTime(LocalDateTime.now()).updatedBy(null).updatedTime(null)
				.modeType("Biometric Device").date(date).build();
	}


	/**
	 * Retrieves attendance details for a specific date range and processes them in batches.
	 * This method handles large datasets by processing them in chunks to avoid memory issues.
	 * 
	 * @param queryDate Date for which to retrieve attendance details
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void getAttendanceDetailByDateRange(String queryDate) {

		String query = CommonConstant.AttendanceConstant.ATTENDANCE_SYNC_QUERY;
		List<AttendanceInfo> attendanceInfoList = new ArrayList<>();
		MapSqlParameterSource params = new MapSqlParameterSource();
		params.addValue("attendance_date", queryDate);

		// Get the total record to process for given query date
		int totalRecord = namedParameterJdbcTemplate
				.queryForObject(CommonConstant.AttendanceConstant.ATTENDANCE_SYNC_COUNT_QUERY, params, Integer.class);
		if (totalRecord > BATCH_SIZE) {
			query += "OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY";
			for (int i = 0; i < getTotalPage(totalRecord, BATCH_SIZE); i++) {

				params.addValue("limit", BATCH_SIZE);
				params.addValue("offset", ((i * BATCH_SIZE)));
				attendanceInfoList = namedParameterJdbcTemplate.query(query, params, new AttendanceInfoMapper());
				performSyncingOfInfoToAudit(attendanceInfoList, query);
			}
		} else if(totalRecord > 0){
			attendanceInfoList = namedParameterJdbcTemplate.query(query, params, new AttendanceInfoMapper());
			performSyncingOfInfoToAudit(attendanceInfoList, query);
		} else {
			log.info("No attendance info to process for today!!");
		}
	}

	/**
	 * Performs synchronization of attendance information to the audit table.
	 * This method maps employee IDs to UIDs, inserts records into the attendance audit table,
	 * and updates the consumed records in the attendance info table.
	 * 
	 * @param attendanceInfoList List of AttendanceInfo to synchronize
	 * @param queryDate Date for which the synchronization is performed
	 */
	@Transactional
	public void performSyncingOfInfoToAudit(List<AttendanceInfo> attendanceInfoList, String queryDate) {
		Map<String, Employee> uidToEmpIdMap = getEmployeeIdToUidMap(
				attendanceInfoList.stream().map(AttendanceInfo::getId).collect(Collectors.toList()));
		batchInsertRecordsToAttendanceAuditTable(attendanceInfoList, uidToEmpIdMap);
		batchUpdateConsumedRecord(attendanceInfoList.stream().map(AttendanceInfo::getId).collect(Collectors.toList()),
				queryDate);
	}
	
	/**
	 * Retrieves a mapping of employee UIDs to Employee entities.
	 * 
	 * @param record List of employee UIDs
	 * @return Map of employee UID to Employee entity
	 */
	public Map<String, Employee> getEmployeeIdToUidMap(List<String> record) {
		return namedParameterJdbcTemplate.query(CommonConstant.AttendanceConstant.EMPLOYEE_DETAIL_QUERY,
				(new MapSqlParameterSource()).addValue("UID", record), (rs, rowNum) -> {
					Employee emp = new Employee();
					emp.setUid(rs.getString(1));
					emp.setEmpId(rs.getLong(2));
					return emp;
				}).stream().collect(Collectors.toMap(Employee::getUid, Function.identity()));
	}

	/**
	 * Performs batch insertion of attendance records into the attendance audit table.
	 * 
	 * @param attendanceInfoList List of AttendanceInfo to insert
	 * @param uidToEmpIdMap Map of employee UID to Employee entity
	 */
	public void batchInsertRecordsToAttendanceAuditTable(List<AttendanceInfo> attendanceInfoList,Map<String, Employee> uidToEmpIdMap) {
		String sql = CommonConstant.AttendanceConstant.ATTENDANCE_SYNC_INSERT_QUERY;

		jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
			@Override
			public void setValues(PreparedStatement ps, int i) throws SQLException {
				AttendanceInfo attendance = attendanceInfoList.get(i);
				ps.setLong(1, uidToEmpIdMap.get(attendance.getId()).getEmpId());
				ps.setObject(2, attendance.getTime(), java.sql.Types.TIME);
				ps.setString(3, "Scanner");
				ps.setInt(4, 2);
				try {
					ps.setObject(5, (new ObjectMapper()).writeValueAsString(attendance));
				} catch (JsonProcessingException e) {
					log.error("Failed to parse the object into a string {}", attendance, e);
				} catch (SQLException e) {
					log.error("Exception occured while parsing {}", attendance, e);
				}
				ps.setObject(6, attendance.getDate(), java.sql.Types.DATE);
			}

			@Override
			public int getBatchSize() {
				return attendanceInfoList.size();
			}
		});
	}

	/**
	 * Updates the consumed records in the attendance info table.
	 * 
	 * @param record List of record IDs to update
	 * @param queryDate Date for which the update is performed
	 */
	public void batchUpdateConsumedRecord(List<String> record, String queryDate) {
		MapSqlParameterSource params = new MapSqlParameterSource();
		params.addValue("consume_status", 1);
		params.addValue("attendance_date", queryDate);
		params.addValue("id", record);
		int updatedRowCount = namedParameterJdbcTemplate
				.update(CommonConstant.AttendanceConstant.ATTENDANCE_SYNC_UPDATE_QUERY, params);
		log.info("Total {} records updated for recieved record {}", updatedRowCount, record.size());
	}

	/**
	 * Calculates the total number of pages needed for batch processing.
	 * 
	 * @param total Total number of records
	 * @param countOfRecordInBatch Number of records per batch
	 * @return Total number of pages
	 */
	public int getTotalPage(int total, int countOfRecordInBatch) {
		return (int) Math.ceil((double) total / (double) countOfRecordInBatch);
	}

	/**
	 * Retrieves the first and last punch records for past days.
	 * This method is used to generate attendance records for historical data.
	 * 
	 * @return List of EmployeePastPunchDataDTO containing employee ID, first punch time, last punch time, and date
	 */
	@Override
	public List<EmployeePastPunchDataDTO> getFirstAndLastSwapsForOldDay() {
		LocalDate StartDate = LocalDate.now().minusDays(1);
		Date yesterday = Date.from(StartDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
		log.info("Scheduler started to generate attendance records from {} to {}", StartDate);

		List<Object[]> results = attendanceAuditRepository.findPastFirstAndLastPunches(yesterday);

		return results.stream().map(r -> new EmployeePastPunchDataDTO((Long) r[0], (Time) r[1], (Time) r[2], (LocalDate) r[3])).toList();
	}

	/**
	 * Inserts attendance records for past days based on the provided punch data.
	 * This method processes each punch record, checks if an attendance record already exists,
	 * and creates new attendance records only for employees without existing records for the given date.
	 * 
	 * @param punchDataList List of EmployeePastPunchDataDTO containing employee punch information
	 */
	@Override
	public void insertPastAttendanceRecords(List<EmployeePastPunchDataDTO> punchDataList) {
		List<AttendanceEntity> results = new ArrayList<>();
		punchDataList.forEach(punchData -> {
			var empId = punchData.empId();
			var firstPunch = punchData.firstSwap();
			var lastPunch = punchData.lastSwap();

			var employee = getEmployeeById(empId);

			if (isAttendanceRecordExists(employee, punchData.date())) {
				log.info("Attendance record already exists for Employee ID: {} for {}", empId, punchData.date());
			} else {
				AttendanceEntity attendance = createAttendanceEntity(employee, firstPunch, lastPunch, punchData.date());
				results.add(attendance);
				log.info("Attendance record inserted for Employee ID: {} for {}", empId, punchData.date());
			}
		});

		if (!Collections.isEmpty(results)) {
			attendanceRepository.saveAllAndFlush(results);
			log.info("Saved the attendance details");
		}

	}

	/**
	 * Retrieves attendance audit details for a specific employee and date.
	 * 
	 * @param attendanceAuditDetailsDTO DTO containing employee ID and date
	 * @return List of AttendanceAuditDetailsListDTO containing attendance audit details
	 */
	@Override
	public List<AttendanceAuditDetailsListDTO> retrieveByEmpIdandDate(AttendanceAuditDetailsDTO attendanceAuditDetailsDTO) {
		Long empId = attendanceAuditDetailsDTO.getEmpId();
		LocalDate date = attendanceAuditDetailsDTO.getDate();
		List<AttendanceAuditDetailsListDTO> records = attendanceAuditRepository.retrieveByEmpIdandDate(empId, date);
		log.info("Response in the Service:{} ", records);
		return records;
	}
	
	/**
	 * Processes daily attendance for employees working in shifts.
	 * This method calculates attendance based on employee schedules and punch records.
	 * 
	 * @param jobRunDate Date for which to process attendance
	 */
	public void processDailyAttendance(LocalDate jobRunDate) {
	    log.info("Starting attendance calculation for employees working in shifts");

	    // Fetch employee attendance for the previous day
	    List<Object[]> result = employeeScheduleRepository.getEmployeeAttendance(jobRunDate);
	    
		List<EmployeeAttendanceDTO> punchInDetails = result.stream()
				.filter(row -> null != row[4]).map(row -> new EmployeeAttendanceDTO(
	            ((Number) row[0]).longValue(),   // branchId
	            ((Number) row[1]).longValue(),   // employeeScheduleId
	            ((Number) row[2]).longValue(),   // empId
	            formatDate(row[3]),              // shiftDate
	            ((Number) row[4]).longValue(),   // timeSlotId
	            formatDate(row[5]),              // calculatedStartDate
	            formatTime(row[6]),              // startTime
	            formatDate(row[7]),              // calculatedEndDate
	            formatTime(row[8])               // endTime
	        )).toList();
	    
	    // Group employees by Branch ID → Time Slot ID
	    Map<Long, Map<Long, List<EmployeeAttendanceDTO>>> groupedAttendance = punchInDetails.stream()
	            .collect(Collectors.groupingBy(EmployeeAttendanceDTO::getBranchId,
	                    Collectors.groupingBy(EmployeeAttendanceDTO::getTimeSlotId)));

	    groupedAttendance.forEach((branchId, shiftMap) -> shiftMap.forEach((timeSlotId, punches) -> processShiftAttendance(punches)));

	    log.info("Finished processing attendance for all employees in shifts");
	}

	/**
	 * Processes attendance for a specific shift.
	 * This method calculates attendance based on employee punch records for a specific shift.
	 * 
	 * @param punches List of EmployeeAttendanceDTO containing employee attendance information
	 */
	private void processShiftAttendance(List<EmployeeAttendanceDTO> punches) {
	    if (punches.isEmpty()) {
	        return;
	    }

	    // Pick first entry for common shift start and end times
	    EmployeeAttendanceDTO shiftData = punches.get(0);
	    List<Long> empIds = punches.stream().map(EmployeeAttendanceDTO::getEmpId).toList();

	    // Fetch punch-in and punch-out details
	    
	    List<Object[]>  results = attendanceAuditRepository.getEmployeePunchDetails(
	            empIds, shiftData.getCalculatedStartDate(), shiftData.getStartTime(),
	            shiftData.getCalculatedEndDate(), shiftData.getEndTime());
	    
	    List<EmployeePunchDTO> punchInDetails = results.stream().map(row -> {
            return new EmployeePunchDTO(
                    ((Number) row[0]).longValue(), // empId
                    row[1] != null ? Time.valueOf(row[1].toString()) : null, // firstCheckIn
                    row[2] != null ? LocalDate.parse(row[2].toString()) : null, // firstCheckInDate
                    row[3] != null ? Time.valueOf(row[3].toString()) : null, // lastCheckOut
                    row[4] != null ? LocalDate.parse(row[4].toString()) : null, // lastCheckOutDate
                    row[5] != null ? new BigDecimal(row[5].toString()) : BigDecimal.ZERO, // overtimeHours,
                    Time.valueOf(shiftData.getStartTime()), // actualShiftStartTime,
                    Time.valueOf(shiftData.getEndTime()) // actualShiftEndTime
                );
            }).toList();
	    
	    // Convert punch details to AttendanceEntity
	    List<AttendanceEntity> attendanceRecords = punchInDetails.stream()
	            .map(this::mapToAttendanceEntity)
	            .filter(Objects::nonNull)
	            .toList();

	    if (!attendanceRecords.isEmpty()) {
	        saveAttendanceRecords(attendanceRecords);
	    }
	}

	/**
	 * Maps EmployeePunchDTO to AttendanceEntity.
	 * This method creates an AttendanceEntity with the information from the EmployeePunchDTO.
	 * 
	 * @param dto EmployeePunchDTO containing employee punch information
	 * @return AttendanceEntity with the information from the DTO, or null if employee is not found
	 */
	private AttendanceEntity mapToAttendanceEntity(EmployeePunchDTO dto) {
	    return Optional.ofNullable(employeeRepository.findById(dto.getEmpId()).orElse(null))
	            .map(employee -> {
	                AttendanceEntity entity = new AttendanceEntity();
	                entity.setEmpId(employee);
	                entity.setCheckInTime(dto.getFirstCheckIn());
	                entity.setCheckOutTime(dto.getLastCheckOut());
	                entity.setDate(dto.getFirstCheckInDate());
	                entity.setCheckOutDate(dto.getLastCheckOutDate());
	                entity.setCreatedBy(1L); // System user
	                entity.setCreatedTime(LocalDateTime.now());
	                entity.setOvertime(dto.getOvertimeHours());
	                entity.setModeType(CommonConstant.SCHEDULER);
	                entity.setActualShiftStartTime(dto.getActualShiftStartTime());
	                entity.setActualShiftEndTime(dto.getActualShiftEndTime());
	                return entity;
	            })
	            .orElse(null);
	}

	/**
	 * Saves attendance records in the database.
	 * This method checks if an attendance record already exists for the given employee and date,
	 * and only saves records that don't already exist.
	 * 
	 * @param records List of AttendanceEntity to save
	 */
	private void saveAttendanceRecords(List<AttendanceEntity> records) {
	    try {
	        log.info("Processing {} attendance records", records.size());
	        List<AttendanceEntity> newRecords = new ArrayList<>();
	        
	        for (AttendanceEntity record : records) {
	            Optional<AttendanceEntity> existingRecord = attendanceRepository.findByEmpIdAndDate(
	                record.getEmpId(), 
	                record.getDate()
	            );
	            
	            if (existingRecord.isEmpty()) {
	                newRecords.add(record);
	                log.info("New attendance record will be saved for Employee ID: {} for date: {}", 
	                    record.getEmpId().getEmpId(), record.getDate());
	            } else {
	                log.info("Attendance record already exists for Employee ID: {} for date: {}", 
	                    record.getEmpId().getEmpId(), record.getDate());
	            }
	        }
	        
	        if (!newRecords.isEmpty()) {
	            log.info("Saving {} new attendance records", newRecords.size());
	            attendanceRepository.saveAll(newRecords);
	            log.info("New attendance records saved successfully");
	        } else {
	            log.info("No new attendance records to save");
	        }
	    } catch (Exception e) {
	        log.error("Error saving attendance records", e);
	    }
	}
	
	/**
	 * Formats a date object to a string.
	 * 
	 * @param date Date object to format
	 * @return Formatted date string, or null if date is not a java.sql.Date
	 */
	private String formatDate(Object date) {
	    return (date instanceof java.sql.Date) ? date.toString() : null;
	}

	/**
	 * Formats a time object to a string.
	 * 
	 * @param time Time object to format
	 * @return Formatted time string, or null if time is not a java.sql.Time
	 */
	private String formatTime(Object time) {
	    return (time instanceof java.sql.Time) ? time.toString() : null;
	}
}
