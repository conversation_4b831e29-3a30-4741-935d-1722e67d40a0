import React, { useState, useEffect } from 'react';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import ListItemText from '@mui/material/ListItemText';
import Select from '@mui/material/Select';
import Checkbox from '@mui/material/Checkbox';
import TextField from '@mui/material/TextField';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

export default function MultipleSelectCheckmarks({ names, data, setData, keyName, placeholder }) {
  const [selectedIds, setSelectedIds] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    setData({ ...data, [keyName]: selectedIds });
  }, [selectedIds]);

  const handleChange = (event) => {
    const { target: { value } } = event;

    if (value.includes('select-all')) {
      setSelectedIds(selectedIds.length === names.length ? [] : names.map(item => item.id));
    } else {
      setSelectedIds(typeof value === 'string' ? value.split(',') : value);
    }
  };

  // Filter names based on search term
  const filteredNames = names.filter(item =>
    item.branchName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const isAllSelected = selectedIds.length === names.length;

  const selectedNames = names
    .filter(item => selectedIds.includes(item.id))
    .map(item => item.branchName);

  return (
    <FormControl sx={{ width: '100%' }}>
      <InputLabel sx={{ marginTop: '-8px' }} id="demo-multiple-checkbox-label">{`Select ${placeholder}`}</InputLabel>
      <Select
        labelId="demo-multiple-checkbox-label"
        id="demo-multiple-checkbox"
        multiple
        value={selectedIds} 
        onChange={handleChange}
        input={<OutlinedInput label="Tag" />}
        renderValue={() => selectedNames.join(', ')}
        MenuProps={MenuProps}
        placeholder={`Select ${placeholder}`}
      >
        <MenuItem style={{ paddingBottom: 0, cursor: 'default' }}>
          <TextField
            size="small"
            placeholder="Search..."
            fullWidth
            variant="outlined"
            onChange={(e) => setSearchTerm(e.target.value)}
            value={searchTerm}
            autoFocus
          />
        </MenuItem>
        
        <MenuItem value="select-all">
          <Checkbox
            checked={isAllSelected}
            indeterminate={selectedIds.length > 0 && !isAllSelected}
          />
          <ListItemText primary={isAllSelected ? "Deselect All" : "Select All"} />
        </MenuItem>
        {filteredNames?.map((item) => (
          <MenuItem key={item.id} value={item.id}>
            <Checkbox checked={selectedIds?.includes(item.id)} />
            <ListItemText primary={item.branchName} />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}
