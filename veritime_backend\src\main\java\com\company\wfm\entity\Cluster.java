package com.company.wfm.entity;

import lombok.Data;

//@Entity
@Data
/*
 * @Getter
 *
 * @Setter
 *
 * @Table(name = "clusters")
 */
public class Cluster {
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//    private String name;
//
//    @ManyToOne
//    @JoinColumn(name = "district_id")
//    private District district;
//
//    @OneToMany(mappedBy = "cluster", cascade = CascadeType.ALL)
//    private List<SubDistrict> subDistricts = new ArrayList<>();

}