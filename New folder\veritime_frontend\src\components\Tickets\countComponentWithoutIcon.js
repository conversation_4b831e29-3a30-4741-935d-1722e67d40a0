import React from "react";
import TicketCard from "./TicketCard";
import "../Tickets/TicketCard.css";
import Link from "next/link";

const CountComponent2 = ({ data }) => {
  return (
    <>
      <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-3">
        <Link href={data?.onClickPath || "#"}>
          <div
            className="card shadow-sm"
            style={{ height: "270px", cursor: "pointer" }}
          >
            <div className="card-body">
              <p className="text fw-bold">{data?.name}</p>
              <div
                style={{ position: "relative", width: "100%", height: "250px" }}
              >
                <TicketCard
                  count={data?.count ?? 0}
                  countColor={data?.countColor}
                />
              </div>
            </div>
          </div>
        </Link>
      </div>
    </>
  );
};

export default CountComponent2;
