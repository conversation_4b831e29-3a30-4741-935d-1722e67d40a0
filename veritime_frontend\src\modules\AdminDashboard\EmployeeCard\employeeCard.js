import React, { useState } from 'react'
import { Card } from 'react-bootstrap';

const EmployeeCard=({data})=> {

  return (
    <div
    className="col-12 col-md-6 col-lg-3 mb-3"
  >
    <Link href={"./adminDashboard/employee"}>
    <Card className="shadow-sm">
      <Card.Body
        style={{ height: 150, cursor: "pointer" }}
        className="d-flex justify-content-center align-items-center"
      >
        <div
          style={{
            backgroundColor: "#FEFFCD",
            padding: 15,
            borderRadius: 60,
            marginRight: 15,
          }}
        >
          <img
            src="/image/enployee.png"
            alt="Foreground Icon"
            className="foreground-icon"
          />
        </div>
        <div>
          <div className="h2">{data}</div>
          <div className="small text-muted">Employee</div>
        </div>
      </Card.Body>
    </Card>
    </Link>
  </div>
  )
}

export default EmployeeCard