package com.company.wfm.repository;


import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.AttendanceRegulationCountDTO;
import com.company.wfm.entity.AttendanceRegularization;

@Repository
public interface AttendanceRegularizationRepository extends JpaRepository<AttendanceRegularization, Long> {
    @Query("SELECT new com.company.wfm.dto.AttendanceRegulationCountDTO(" +
            "COUNT(ar), " +
            "SUM(CASE WHEN ar.approvalStatus = 'APPROVED' THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN ar.approvalStatus = 'PENDING' THEN 1 ELSE 0 END)) " +
            "FROM AttendanceRegularization ar")
    AttendanceRegulationCountDTO getAttendanceRegulationCounts();
    @Query("SELECT new com.company.wfm.dto.AttendanceRegulationCountDTO(" +
            "COUNT(ar), " +
            "SUM(CASE WHEN ar.approvalStatus = 'APPROVED' THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN ar.approvalStatus = 'PENDING' THEN 1 ELSE 0 END)) " +
            "FROM AttendanceRegularization ar " +
            "WHERE ar.createdAt BETWEEN :startDate AND :endDate")
    AttendanceRegulationCountDTO getAttendanceRegulationCounts(@Param("startDate") LocalDateTime startDate,
                                                               @Param("endDate") LocalDateTime endDate);

    List<AttendanceRegularization> findByEmployeeIdIn(List<Long> employeeIds);
    List<AttendanceRegularization> findByEmployeeId(String employeeId);


}