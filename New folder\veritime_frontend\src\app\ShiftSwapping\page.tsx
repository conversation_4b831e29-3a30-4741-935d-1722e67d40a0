"use client";
import { showErrorAlert } from "@/services/alertService";
import React, { useEffect, useState } from "react";
import GridLayoutComponent from "../../modules/ShiftSwapping/GridLayout/GridLayoutComponent";
import "./ShiftSwapping.css";
import Layout from "../../components/Layout";
import { getRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import DatePicker from "react-datepicker";
import { Button , Form} from "react-bootstrap";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";
import Select from "react-select";

const ShiftSwapping = () => {
  const [selectedDate, setSelectedDate] = useState<any>("");
  const [selectedDept, setSelectedDept] = useState<any>("");
  const [department, setDepartment] = useState([]);
  const [data, setData] = useState<any>({});

  const handleDateChange = (date: any) => {
    setSelectedDate(date);
  };

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const departmentResponse = await getRequest(`${API_URLS.DEPT_LOOKUP}`);
        if (departmentResponse) setDepartment(departmentResponse);
      } catch (error) {}
    };
    fetchOptions();
  }, []);

  const handleDepartmentChange = (selectedOption: any) => {
    setSelectedDept(selectedOption ? selectedOption.value : "");
  };

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const deptId = urlParams.get("deptId");
    const date = urlParams.get("date");

    if (deptId) {
      setSelectedDept(deptId);
    }

    if (date) {
      setSelectedDate(new Date(date));
    }
    if (deptId && date) {
      fetchData(deptId, date);
    }
  }, []);

  const fetchData = async (selectedDept: any, selectedDate: any) => {
    if (selectedDate && selectedDept) {
      try {
        const newUrl = `${window.location.pathname}?deptId=${btoa(
          selectedDept
        )}&date=${moment(selectedDate).format("YYYY-MM-DD")}`;
        window.history.pushState({ path: newUrl }, "", newUrl);

        const response = await getRequest(
          API_URLS.GET_SHIFTSWAPPING_LIST(
            selectedDept,
            moment(selectedDate).format("YYYY-MM-DD")
          )
        );
        if (response) {
          setData(response);
        } else {
        }
      } catch (error) {}
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (selectedDept && selectedDate) {
        fetchData(selectedDept, selectedDate);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [selectedDept, selectedDate]);

  const getData = () => {
    const newUrl = `${window.location.pathname}?deptId=${atob(
      selectedDept
    )}&date=${moment(selectedDate).format("YYYY-MM-DD")}`;
    window.history.pushState({ path: newUrl }, "", newUrl);
    window.location.reload();
  };

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  // Transforming department data for react-select
  const departmentOptions = department.map((dept: any) => ({
    value: dept.departmentId,
    label: dept.departmentName,
  }));

  return (
    <Layout>
      <>
        {/* Ensure the heading takes full width */}
        <div
          className="col-2 mb-1"
          style={{ display: "flex", justifyContent: "center" }}
        >
          <h3
            className="h3 text"
            style={{ marginTop: "20px", marginRight: "17%" }}
          >
            Shift Swap
          </h3>
        </div>

        <div className="filter-container">
          <div className="filter-item">
            <Select
              className="filter-se"
              value={departmentOptions.find(
                (dept) => dept.value === selectedDept
              )}
              onChange={handleDepartmentChange}
              options={departmentOptions}
              placeholder="Select Department"
              isClearable
              styles={{
                control: (base) => ({
                  ...base,
                  height: "40px", // Ensure the height matches the DatePicker
                  borderRadius: "8px", // Matching border-radius
                  backgroundColor: "transparent", // Remove background color
                  borderColor: "#ccc",
                  fontSize: "14px", // Consistent font size
                  color: "#1F609A", // Text color
                }),
                dropdownIndicator: (base) => ({
                  ...base,
                  color: "#1F609A", // Set dropdown icon color
                }),
                clearIndicator: (base) => ({
                  ...base,
                  color: "#1F609A", // Set clear icon color
                }),
                singleValue: (base) => ({
                  ...base,
                  color: "#1F609A", // Set selected text color
                }),
              }}
            />
          </div>

          <div className="filter-item">
            
          <Form.Control
                type="date"
                value={selectedDate}
                onChange={(e)=>{
                  if (moment(e.target.value).year().toString().length > 4) {
                                      showErrorAlert("The maximum allowed year is 4 digits. Please enter a valid year")
                                      
                                    } else {
                                      setSelectedDate(moment(e.target.value).format("YYYY-MM-DD"));
                                    }
                  }}
                style={{
                  width: "180px",
                  color: selectedDate ? "black" : "#6c757d",
                }}
              />
            
            
            {/* <DatePicker
              selected={selectedDate}
              onChange={handleDateChange}
              dateFormat="yyyy-MM-dd"
              className="date-picker"
              placeholderText="Select a date"
            /> */}
          </div>

          <div className="filter-item">
            <Button
              onClick={() => fetchData(selectedDept, selectedDate)}
              className="go-button-shift"
            >
              Apply
            </Button>
          </div>
        </div>

        {Object.keys(data).length > 0 ? (
          data.empList && data.empList.length > 0 ? (
            <GridLayoutComponent
              shiftTimings={data.shiftTimings}
              empList={data.empList}
              onDateChange={selectedDate}
            />
          ) : (
            <p
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flex: 1,
              }}
            >
              No data found
            </p>
          )
        ) : (
          <p
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flex: 1,
            }}
          >
            Please select date and department for swapping
          </p>
        )}
      </>
    </Layout>
  );
};

export default ShiftSwapping;
