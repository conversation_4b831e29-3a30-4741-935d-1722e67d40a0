package com.company.wfm.repository;

import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.DocumentMaster;

@Repository
public interface DocumentMasterRepository extends JpaRepository<DocumentMaster, Long> {

    @Override
	Page<DocumentMaster> findAll(Pageable pageable);

    @Override
	Optional<DocumentMaster> findById(Long documentId);

}
