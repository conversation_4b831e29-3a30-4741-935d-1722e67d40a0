package com.company.wfm.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.company.wfm.dto.BranchFilterDTO;
import com.company.wfm.dto.CreateDepartmentDTO;
import com.company.wfm.dto.DepartmentDTO;
import com.company.wfm.dto.DepartmentIdNameDTO;
import com.company.wfm.dto.UpdateDepartmentDTO;
import com.company.wfm.entity.Department;
import com.company.wfm.vo.DepartmentBranchVO;

public interface DepartmentService {

    Department createDepartment(CreateDepartmentDTO departmentDTO);

    void createDepartmentBranch(CreateDepartmentDTO departmentDTO);

   // List<BranchVO> getAllBranches(BranchFilterDTO filter);
   public Object getAllBranches(BranchFilterDTO filter, Pageable pageable);

    List<String> getAllDepartmentNames();

    List<DepartmentDTO> getAllDepartment();

    List<DepartmentIdNameDTO> getAllActiveDepartmentIdsAndNames();

    Page<DepartmentBranchVO> searchDepartments(BranchFilterDTO filter);

   // List<CategoryNameDTO> getAllCategory();
   public void deleteDepartment(Long departmentId);

    void updateDepartmentBranch(UpdateDepartmentDTO departmentDTO);

    public boolean isDepartmentUser(Long empId, Long departmentId);

    public List<DepartmentIdNameDTO> getDepartmentsForLoggedInUser() ;
    public List<DepartmentIdNameDTO> getDepartmentsForLoggedInUser1(Long branchId) ;

}
