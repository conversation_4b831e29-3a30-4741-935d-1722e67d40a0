import React from 'react';
//import 'bootstrap/dist/css/bootstrap.min.css';
import 'react-datepicker/dist/react-datepicker.css';
import { Col, Row } from 'react-bootstrap';
import { BsCalendar } from 'react-icons/bs';
import DatePicker from 'react-datepicker';


const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().split('T')[0];  
};

const DateSelector = ({ fromDate, toDate, setFromDate, setToDate }) => {

  const CustomFromDateInput = ({ value, onClick }) => (
    <div className="input-group">
      <input
        type="text"
        className="form-control"
        value={value}
        onClick={onClick}
        placeholder="From Date"
        readOnly
        style={{ border: '1px solid #000' }}
      />
      <button
        type="button"
        className="btn btn-outline-secondary"
        onClick={onClick}
        style={{ border: '1px solid #000' }}
      >
        <BsCalendar color="#5EAAE6" />
      </button>
    </div>
  );

  const CustomToDateInput = ({ value, onClick }) => (
    <div className="input-group">
      <input
        type="text"
        className="form-control"
        value={value}
        onClick={onClick}
        placeholder="To Date"
        readOnly
        style={{ border: '1px solid #000' }}
      />
      <button
        type="button"
        className="btn btn-outline-secondary"
        onClick={onClick}
        style={{ border: '1px solid #000' }}
      >
        <BsCalendar color="#5EAAE6" />
      </button>
    </div>
  );

  return (
    <Row className="d-flex ">
      <Col className="col-lg-6 col-md-1 col-sm-6 ">
        <div className="me-2">
          <DatePicker
            selected={fromDate ? new Date(fromDate) : null}  
            onChange={(date) => setFromDate(formatDate(date))}  
            customInput={<CustomFromDateInput value={fromDate} />}  
            dateFormat="dd/MM/yyyy"
          />
        </div>
      </Col>
      <Col className="col-lg-6 col-md-1 col-sm-6 ">
        <div className="me-2">
          <DatePicker
            selected={toDate ? new Date(toDate) : null}  
            onChange={(date) => setToDate(formatDate(date))} 
            customInput={<CustomToDateInput value={toDate} />}  
            dateFormat="dd/MM/yyyy"
          />
        </div>
      </Col>
    </Row>
  );
};

export default DateSelector;
