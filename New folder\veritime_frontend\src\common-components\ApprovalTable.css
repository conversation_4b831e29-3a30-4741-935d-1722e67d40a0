
div:where(.swal2-container).swal2-center>.swal2-popup {
    grid-column: 2;
    grid-row: 2;
    place-self: center center;
    width: 762px;
}
button:not(:disabled) {
    cursor: pointer;
    max-width: 186px;
}
.approve-button {
    background-color: #c6f6d5;
    color: #38a169;
    padding: 3px;
width: 90px;}

.deny-butto {
    background-color: #fed7d7;
    color: #e53e3e;
    width: 137px;
    height: 28px;
    border-radius: 10px;
    border: none;
    margin-top: 12px;
    margin-left: -7px;
}

    .download-button {
        background-color: lightblue;
        border-radius: 10px;
        border: none;
        color: #2AA9FD;
        width: 104px;
        margin-top: 12px;
        height: 27px;
    }

    
    
    /* @media (min-width: 1024px) and (max-width: 1100px) {
        div:where(.swal2-container).swal2-center>.swal2-popup {
            width: 75%;
            font-size: 15px; 
        }
    }
    
    @media (min-width: 1101px) and (max-width: 1200px) {
        div:where(.swal2-container).swal2-center>.swal2-popup {
            width: 70%; 
            font-size: 16px; 
        }
    }
    
    @media (min-width: 1201px) and (max-width: 1300px) {
        div:where(.swal2-container).swal2-center>.swal2-popup {
            width: 75%; 
            font-size: 17px; 
        }
    }
    
    @media (min-width: 1301px) and (max-width: 1400px) {
        div:where(.swal2-container).swal2-center>.swal2-popup {
            width: 65%; 
            font-size: 18px; 
        }
    }
    
    @media (min-width: 1401px) {
        div:where(.swal2-container).swal2-center>.swal2-popup {
            width: 60%; 
            font-size: 19px; 
        }
    }
     */
    