package com.company.wfm.dto;

import java.time.Instant;

public class NotificationDTO {

    private String title;
    private String body;
    private String type;
    private String sentById;
    private Instant createTime;
    private Integer read_status;
    private Integer id;

    // Constructor without read_status
    public NotificationDTO(String title, String body, String type, String sentById, Instant createTime) {
        this(title, body, type, sentById, createTime, null,null); // Default read_status to null
    }

    // Constructor with read_status
    public NotificationDTO(String title, String body, String type, String sentById, Instant createTime, Integer read_status, Integer id) {
        this.title = title;
        this.body = body;
        this.type = type;
        this.sentById = sentById;
        this.createTime = createTime;
        this.read_status = read_status;
        this.id = id;
    }

    // Getters and setters
    public Integer getId() {
        return id;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSentById() {
        return sentById;
    }

    public void setSentById(String sentById) {
        this.sentById = sentById;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

    public Integer getRead_status() {
        return read_status;
    }

    public void setRead_status(Integer read_status) {
        this.read_status = read_status;
    }

    @Override
    public String toString() {
        return "NotificationDTO{" +
                "title='" + title + '\'' +
                ", body='" + body + '\'' +
                ", type='" + type + '\'' +
                ", sentById=" + sentById +
                ", createTime=" + createTime +
                ", read_status=" + read_status +
                ", id=" + id +
                '}';
    }
}
