package com.company.wfm.dto;

public class AuthResponseWrapper {
    private JwtAuthResponse data;
    private String errorMessage;


    public AuthResponseWrapper(JwtAuthResponse data) {
        this.data = data;
    }


    public AuthResponseWrapper(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public JwtAuthResponse getData() {
        return data;
    }

    public void setData(JwtAuthResponse data) {
        this.data = data;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
