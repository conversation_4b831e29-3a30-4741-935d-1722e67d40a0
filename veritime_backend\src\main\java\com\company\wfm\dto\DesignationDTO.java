package com.company.wfm.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DesignationDTO {

    private Long id;
    private Long empId;
    private String name;
    private String code;
    private Long level;
    private Long createdBy;
    private String createdByName;
    private LocalDateTime createdAt;
    private Long updatedBy;
    private String updatedByName;
    private LocalDateTime updatedAt;
    private Long departmentId;
    private String departmentName;
    private String role;
    private BigDecimal noticePeriod;


}
