import React, { useEffect, useState } from "react";
import Swal from "sweetalert2";
import TableFilter from "./TableFilter";
import { API_URLS } from "../constants/apiConstants";
import {
  fileDownload,
  getRequest,
  postRequest,
  postRequestWithSecurity,
  getRequestWithSecurity,
} from "@/services/apiService";
import VisibilityIcon from "@mui/icons-material/Visibility";

const Employee = ({ showActionCol, isFromHistory, fetchCount }) => {
  const [rows, setRows] = useState([]);
  const [imageErrors, setImageErrors] = useState({});

  const getInitials = (name) => {
    return name?.charAt(0)?.toUpperCase() || "U";
  };
  const handleImageError = (index) => {
    setImageErrors((prevErrors) => ({
      ...prevErrors,
      [index]: true,
    }));
  };

  function formatDateArray(dateArray) {
    const [year, month, day] = dateArray;
    const date = new Date(year, month - 1, day); // Month is 0-based in JavaScript Date
    const options = { day: "2-digit", month: "2-digit", year: "numeric" };
    return date.toLocaleDateString("en-GB", options); // Formats the date as dd/mm/yyyy
  }

  useEffect(() => {
    const fetchLeaveHistory = async () => {
      fetchCount("leaverequest");
      try {
        let apiurl = API_URLS.RESIGNATION_LIST;

        if (window.location.pathname === "/employeeDashboard/viewHistory") {
          apiurl += "?type=history";
        }

        const requestBody = {
          offset: 0,
          limit: 10,
        };

        const data = await postRequest(apiurl, requestBody);
        console.log("API Data:", data.content);

        if (data) {
          const rowsWithFormattedDates = data.content.map((row, index) => {
            const formattedNoticeStartDate = formatDateArray(
              row.noticeStartDate
            );
            console.log(`Row ${index} Data:`, row); // Log each row for debugging
            const formattedLastWorkingDate = formatDateArray(
              row.lastWorkingDate
            );

            return {
              ...row,
              srno: index + 1,
              empName: row.employeeName || row.fullName, // Ensure name is set
              imageUrl: row.imageUrl || row.profilePic, // Ensure image is set
              noticeStartDate: formattedNoticeStartDate,
              lastWorkingDate: formattedLastWorkingDate,
            };
          });

          setRows(rowsWithFormattedDates);
        }
      } catch (error) {}
    };

    fetchLeaveHistory();
  }, []);

  const handlePullBack = async (leaveHistoryId) => {
    // Static reason value
    const staticReason = "Request canceled due to internal review.";

    const confirmResult = await Swal.fire({
      title: "Cancel The Request",
      text: "Do you want to cancel the request?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, cancel!",
      cancelButtonText: "No",
    });

    if (confirmResult.isConfirmed) {
      try {
        console.log("Canceling Resignation for ID:", leaveHistoryId);
        const url = API_URLS.EMPLOYEE_CANCEL(leaveHistoryId);

        // Send static reason
        await postRequest(url, { reason: staticReason });

        setRows((prevRows) =>
          prevRows.map((row) =>
            row.id === leaveHistoryId ? { ...row, status: "canceled" } : row
          )
        );

        Swal.fire("Canceled!", "The resignation has been canceled.", "success");
      } catch (error) {
        Swal.fire("Error!", "Failed to cancel the resignation.", "error");
      }
    }
  };

  const handleAccept = async (leaveHistoryId) => {
    const { value: remark } = await Swal.fire({
      title: "Approve Resignation Request",
      text: "Do you want to approve this Resignation Request?",
      input: "textarea",
      inputPlaceholder: "Enter your approval remark...",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, approve it!",
      cancelButtonText: "No, keep it pending",
      inputValidator: (value) => {
        if (!value) return "Please mention a remark!";
      },
    });

    if (remark) {
      //  FIXED: Check 'remark' instead of 'reason'
      try {
        const url = API_URLS.EMPLOYEE_APPROVE(leaveHistoryId);
        const payload = { remark }; // Sending 'remark' correctly

        console.log("Approving Resignation for ID:", leaveHistoryId);
        console.log("Payload:", payload);
        console.log("API URL:", url);

        await postRequest(url, payload, {
          headers: { "Content-Type": "application/json" }, //  Ensure JSON format
        });

        await Swal.fire({
          title: "Approved!",
          text: "The resignation has been approved.",
          icon: "success",
        });

        window.location.reload();
      } catch (error) {
        console.error("Error Approving Resignation:", error);

        //  Ensure error message is a string for SweetAlert2
        const errorMessage =
          error?.response?.data?.message || "Failed to approve resignation.";

        Swal.fire({
          title: "Error!",
          text: errorMessage,
          icon: "error",
        });
      }
    }
  };

  const handleDeny = async (leaveHistoryId) => {
    const { value: remark } = await Swal.fire({
      title: "Deny Resignation Application",
      text: "Do you want to deny this Resignation?",
      input: "textarea",
      inputPlaceholder: "Enter your denial remark...",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, decline it!",
      cancelButtonText: "No, keep it pending",
      inputValidator: (value) => {
        if (!value) return "Please mention a remark!";
      },
    });

    if (remark) {
      try {
        const url = API_URLS.EMPLOYEE_REJECT(leaveHistoryId);
        console.log("Reject API URL:", url);

        // Send { remark } in the payload
        const response = await postRequest(
          url,
          { remark },
          {
            headers: { "Content-Type": "application/json" },
          }
        );

        console.log("Denial Response:", response);

        await Swal.fire({
          title: "Denied!",
          text: "The resignation has been denied.",
          icon: "success",
        });

        window.location.reload();
      } catch (error) {
        console.error("Error Rejecting Resignation:", error);
        const errorMessage =
          error?.response?.data?.message || "Failed to reject resignation.";

        await Swal.fire({
          title: "Error!",
          text: errorMessage,
          icon: "error",
        });
      }
    }
  };

  const isCancelAllowed = (lastWorkingDate) => {
    const endDate = new Date(lastWorkingDate.split("/").reverse().join("-"));
    const currentDate = new Date();

    const cancelDeadline = new Date(endDate.getTime() - 48 * 60 * 60 * 1000);

    console.log("End Date:", endDate);
    console.log("Current Date:", currentDate);
    console.log("Cancel Deadline (48 hours before):", cancelDeadline);

    const isAllowed = currentDate <= cancelDeadline;

    console.log(`Cancel Button Allowed: ${isAllowed}`);
    return isAllowed;
  };

  function formatDate(date) {
    const options = { day: "2-digit", month: "2-digit", year: "numeric" };
    const formattedDate = new Date(date).toLocaleDateString("en-GB", options);
    return formattedDate;
  }

  function formaTime(dateArray) {
    // Check if the dateArray is in the expected format
    if (Array.isArray(dateArray) && dateArray.length === 7) {
      const [year, month, day, hour, minute, second, millisecond] = dateArray;
      // Create the Date object using the array values
      const date = new Date(
        year,
        month - 1,
        day,
        hour,
        minute,
        second,
        millisecond
      );
      const options = { day: "2-digit", month: "2-digit", year: "numeric" };
      return date.toLocaleDateString("en-GB", options); // Formats as dd/mm/yyyy
    }
    return "Invalid Date"; // Return a default message if the date is not in expected format
  }
  const S3URL = process.env.NEXT_PUBLIC_S3_URL;

  const handleDetail = (row) => {
    const isHistory = window.location.pathname.includes("viewHistory");
    const initials = getInitials(row.employeeName);
    const canCancel = isCancelAllowed(row.lastWorkingDate);

    console.log("Row Data:", row);
    console.log("Termination Reason:", row.terminationReason);
    console.log("Remark:", row.remark);
    console.log("image:", row.imageUrl);

    Swal.fire({
      html: `
        <div style="font-size: 18px; line-height: 1.6; color: #333; display: flex; flex-direction: column; gap: 20px; max-width: 700px;">
      
          <!-- Employee Info Section -->
          <div style="display: flex; justify-content: center; align-items: center; gap: 20px; padding-bottom: 15px; border-bottom: 1px solid #f0f0f0;">
            <img 
             src="${row.imageUrl}"
              alt="Employee Image" 
                 style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover; border: 2px solid #ddd;"
                 onerror="this.onerror=null; this.src=''; this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div style="display: none; align-items: center; justify-content: center; width: 60px; height: 60px; background-color: #ccc; color: #fff; font-weight: bold; border-radius: 50%;">
              ${initials}
            </div>
            <div style="display: flex; flex-direction: column; align-items: center;">
              <span style="font-size: 22px; font-weight: bold; color: #1a1a1a;">${
                row.employeeName
              }</span>
              
            </div>
          </div>
      

 <div style="display: flex; justify-content: space-between; padding-bottom: 10px; margin-bottom: 0px;width: 718px;">
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">Start Date :</strong>
                <span>${row.noticeStartDate || ""}</span>
              
              </div>
              <div style="margin: 0 20px;"></div>
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">End Date:</strong>
              <span>${row.lastWorkingDate || ""}</span>
              </div>
              <div style="flex: 1; display: flex; justify-content: flex-start;margin-right: 20px;">
                <strong style="font-weight: bold; margin-right: 8px;">Status:</strong>
              <span>${row.status || ""}</span>
              </div>
            </div>

            
          </div>


      
<div style="display: flex; align-items: flex-start; padding-bottom: 10px; margin-bottom: 9px; width: 718px;">
  <strong style="font-weight: bold; margin-right: 8px; white-space: nowrap;">Remark:</strong>
  <span style="word-break: break-word; overflow-wrap: anywhere; text-align: left; flex-grow: 1;">
    ${row.remark || ""}
  </span>
</div>

<div style="display: flex; align-items: flex-start; padding-bottom: 10px; margin-bottom: 9px; width: 718px;">
  <strong style="font-weight: bold; margin-right: 8px; white-space: nowrap;">Reason:</strong>
  <span style="word-break: break-word; overflow-wrap: anywhere; text-align: left; flex-grow: 1;">
    ${row.terminationReason || ""}
  </span>
</div>

          <div style="display: flex; flex-direction: column; gap:2px; padding-bottom: 15px;">
            <div style="display: flex; justify-content: space-between; padding-bottom: 10px; margin-bottom: -4px;width: 718px;">
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">Updated By:</strong>
                <span>${row.updatedByName || ""}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <!-- Right Side: Created Time (Aligned to Right) -->
  <div style="display: flex; align-items: center; justify-content: flex-end; flex: 1;margin-right: 20px;">
    <strong style="font-weight: bold; margin-right: 8px;">Updated On:</strong>
    <span>${formaTime(row.updatedTime) || ""}</span>
  </div>
</div>
            </div>


          <div style="display: flex; flex-direction: column; gap:2px; padding-bottom: 15px;">
            <div style="display: flex; justify-content: space-between; padding-bottom: 10px; margin-bottom: -4px;width: 718px;">
              <div style="flex: 1; display: flex; justify-content: flex-start;">
                <strong style="font-weight: bold; margin-right: 8px;">Created By:</strong>
                <span>${row.createdByName || ""}</span>
              </div>
              <div style="margin: 0 20px;"></div>
              <!-- Right Side: Created Time (Aligned to Right) -->
  <div style="display: flex; align-items: center; justify-content: flex-end; flex: 1;margin-right: 20px;">
    <strong style="font-weight: bold; margin-right: 8px;">Created Time:</strong>
    <span>${formaTime(row.createdTime) || ""}</span>
  </div>
</div>
            </div>


          <div style="display: flex; justify-content: center; gap: 10px;">
            ${
              !isHistory && row.status === "Pending"
                ? ` 
              <button id="approveBtn" class="approve-button">Approve</button>
              <button id="denyBtn" class="deny-button">Decline</button>
            `
                : ""
            }
            ${
              isHistory && row.status === "Pending"
                ? `
                        <button id="cancelBtn" ${
                          !canCancel ? "disabled" : ""
                        } class="deny-button">
                  Cancel
                </button>
            `
                : ""
            }
           
          </div>

        </div>
      `,
      icon: "",
      showCloseButton: true,
      showCancelButton: false,
      // isHistory && row.status === "approved" ||
      showConfirmButton: false,
      customClass: {
        container: "custom-swal-container",
        popup: "custom-swal-popup",
        title: "custom-swal-title",
        content: "custom-swal-content",
      },
      didOpen: () => {
        const downloadImage = document.querySelector('img[alt="Download"]');
        if (downloadImage) {
          downloadImage.addEventListener("click", () =>
            handleDownload(row.filePath)
          );
        }

        const cancelBtn = document.getElementById("cancelBtn");
        if (cancelBtn) {
          cancelBtn.addEventListener("click", () => handlePullBack(row.id));
        }

        const approveBtn = document.getElementById("approveBtn");
        const denyBtn = document.getElementById("denyBtn");
        // const cancelBtn = document.getElementById("cancelBtn");

        if (approveBtn) {
          approveBtn.addEventListener("click", () => handleAccept(row.id));
        }
        if (denyBtn) {
          denyBtn.addEventListener("click", () => handleDeny(row.id));
        }
      },
      allowOutsideClick: true,
    });
  };

  const columns = [
    { field: "srno", headerName: "Sr.no", width: 70 },
    {
      field: "empName",
      headerName: "Name",
      width: 200,
      renderCell: (params) => {
        const imageUrl = params.row.imageUrl;
        const name = params.row.employeeName;
        const designation = params.row.designationName;

        return (
          <div style={{ display: "flex", alignItems: "center" }}>
            {!imageUrl || imageErrors[params.row.id] ? (
              <div
                className="rounded-circle me-2 d-flex align-items-center justify-content-center"
                style={{
                  width: "50px",
                  height: "50px",
                  backgroundColor: "#ccc",
                  color: "#fff",
                  fontWeight: "bold",
                  marginBottom: "22px",
                }}
              >
                {getInitials(name)}
              </div>
            ) : (
              <img
                //src={`$`}
                // src="${{baseURL}${imageUrl} || 'https://via.placeholder.com/60'}"
                src={`${S3URL}${imageUrl}`}
                alt={name}
                className="rounded-circle me-2"
                style={{ width: "50px", height: "50px" }}
                onError={() => handleImageError(params.row.id)}
              />
            )}

            <div
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
              }}
            >
              <span
                style={{
                  fontWeight: "bold",
                  fontSize: "11px",
                  marginRight: "-92px",
                  marginBottom: "23px",
                }}
              >
                {name}
              </span>
              <span
                style={{
                  fontSize: "12px",
                  color: "grey",
                  marginTop: "11px",
                  marginLeft: "23px",
                }}
              >
                {designation}
              </span>
            </div>
          </div>
        );
      },
    },

    { field: "noticeStartDate", headerName: "Start Date", width: 150 },
    { field: "lastWorkingDate", headerName: "End Date", width: 150 },
    // { field: "appliedLeaveCount", headerName: "Leave Days", width: 100 },
    // { field: "status", headerName: "Status", width: 100 },
    // { field: "remark", headerName: "Remark", width: 200 },
    {
      field: "status",
      headerName: "Status",
      width: 100,
      renderCell: (params) => {
        // console.log("STATUS VALUE:", params.value);

        const statusText = String(params.value || "").toLowerCase();
        const statusMapping = {
          canceled: "canceled",
          rejected: "canceled",
        };

        return <span>{statusMapping[statusText] || params.value}</span>;
      },
    },
  ];

  columns.push({
    field: "View",
    headerName: "View",
    width: 100,
    renderCell: (params) => (
      <button
        className="view-button"
        onClick={() => handleDetail(params.row)}
        style={{ border: "none", background: "transparent", cursor: "pointer" }}
      >
        <VisibilityIcon
          style={{ cursor: "pointer", color: "bl", marginBottom: "5px" }}
        />
      </button>
    ),
  });

  return (
    <div id="tableWrapper" style={{ marginTop: -24 }}>
      <TableFilter columns={columns} rows={rows} />
    </div>
  );
};

export default Employee;
