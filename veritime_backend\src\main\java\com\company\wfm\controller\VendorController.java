package com.company.wfm.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.VendorProductsRequestDTO;
import com.company.wfm.entity.VendorProduct;
import com.company.wfm.service.VendorProductService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/vendors")
@CrossOrigin(origins = "*")
@Slf4j
public class VendorController {


    @Autowired
    private VendorProductService vendorProductService;

    @PostMapping("/products")
    public ResponseEntity<?> getProductsByVendor(@RequestBody VendorProductsRequestDTO request) {

        int offset = request.getOffset(); // Uses default of 0 if null
        int limit = request.getLimit();

        try {
            Page<VendorProduct> products = vendorProductService.getProductsByVendorId(request.getVendorId(), offset, limit);
            return ResponseEntity.ok(products);
        } catch (RuntimeException e) {
            // Log the error if needed
        	log.error("Exception while getProductsByVendor", e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        } catch (Exception e) {
            // Handle other exceptions
			log.error("Exception while getProductsByVendor", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while retrieving products: " + e.getMessage());
        }
    }


}
