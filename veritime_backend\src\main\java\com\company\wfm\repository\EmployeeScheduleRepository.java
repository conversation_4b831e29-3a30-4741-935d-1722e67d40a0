package com.company.wfm.repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.EmployeeSchedule;

@Repository
public interface EmployeeScheduleRepository extends JpaRepository<EmployeeSchedule, Long> {

    Optional<EmployeeSchedule> findByEmployeeEmpIdAndDate(Long empId, LocalDate date);

    List<EmployeeSchedule> findByDateBetween(LocalDate startDate, LocalDate endDate);

    List<EmployeeSchedule> findByEmployeeEmpIdAndDateBetween(Long empId, LocalDate startDate, LocalDate endDate);
    
    @Query(value = """
			SELECT
			    dpetBranch.branch_id AS branchId,
			    es.id AS employeeScheduleId,
			    es.emp_id AS empId,
			    es.date AS shiftDate,
			    ts.time_slot_id AS timeSlotId,
			    es.date AS calculatedStartDate,
			    ts.start_time AS startTime,
			    CASE
			        WHEN ts.start_time > ts.end_time THEN DATEADD(DAY, 1, es.date)
			        ELSE es.date
			    END AS calculatedEndDate,
			    ts.end_time AS endTime
			FROM t_employee_schedule es
			JOIN t_employee emp ON es.emp_id = emp.emp_id
			JOIN department_branch dpetBranch ON emp.department_id = dpetBranch.department_id and dpetBranch.branch_id = emp.BRANCH_ID 
			LEFT JOIN t_time_slot ts ON TRY_CAST(LTRIM(RTRIM(COALESCE(NULLIF(es.modified_shift, ''), es.actual_shift))) AS BIGINT) = ts.time_slot_id
            WHERE es.date = :jobRunDate
        """, nativeQuery = true)
    	List<Object[]> getEmployeeAttendance(@Param("jobRunDate") LocalDate  jobRunDate);


	@Query("SELECT es FROM EmployeeSchedule es WHERE es.employee.id = :empId AND es.date = :date")
	Optional<EmployeeSchedule> findByEmpIdAndDate(@Param("empId") Long empId, @Param("date") LocalDate date);
}