package com.company.wfm.vo;

import com.company.wfm.entity.TimeSlot;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimeSlotVO {
    private Long id;
    private String startTime;
    private String endTime;
    private int isActive;
    private int isDefault;
    private long scheduleId;

    public TimeSlotVO(TimeSlot timeSlot) {
        this.id = timeSlot.getId();
        this.startTime = timeSlot.getStartTime().toString(); // Convert LocalTime to String
        this.endTime = timeSlot.getEndTime().toString();     // Convert LocalTime to String
        this.isActive = timeSlot.getIsActive();
        this.isDefault = timeSlot.getIsDefault();
        this.scheduleId = timeSlot.getSchedule().getId(); // Get schedule ID from TimeSlot
    }
}
