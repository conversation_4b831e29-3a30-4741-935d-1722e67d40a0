// src/services/apiService.js
import axios from 'axios';
import { showErrorAlert } from '../services/alertService';

// Function to get data (GET request)
export const getData = async (endpoint) => {
    
    try {
        const response = await axios.get(`${process.env.API_BASE_URL}/${endpoint}`);
        return response;
    } catch (error) {
        // console.error('Error fetching data:', error);
        handleError(error);
        // throw error;
    }
};

// Function to create new data (POST request)
export const createData = async (endpoint, data) => {
    try {
        const response = await axios.post(`${process.env.API_BASE_URL}/${endpoint}`, data);
        return response.data;
    } catch (error) {
        // console.error('Error creating data:', error);
        handleError(error);
        throw error;
    }
};

// Function to update existing data (PUT request)
export const updateData = async (endpoint, data) => {
    try {
        const response = await axios.put(`${process.env.API_BASE_URL}/${endpoint}`, data);
        return response.data;
    } catch (error) {
        // console.error('Error updating data:', error);
        throw error;
    }
};

// Function to delete data (DELETE request)
export const deleteData = async (endpoint) => {
    try {
        const response = await axios.delete(`${process.env.API_BASE_URL}/${endpoint}`);
        return response.data;
    } catch (error) {
        // console.error('Error deleting data:', error);
        throw error;
    }
};


const handleError = (error) => {
    if (error.response) {
      const status = error.response.status;
      if (status === 400 || status === 500) {
        showErrorAlert(`Error ${status}: ${error.response.data.msg || 'Something went wrong.'}`);
      }
    } else {
        if (error.code === 'ECONNABORTED') {
            showErrorAlert('Request timed out error');
        } else {
            showErrorAlert('An unexpected error occurred:');
        }
      //showErrorAlert('An unexpected error occurred');
    }
  };
  