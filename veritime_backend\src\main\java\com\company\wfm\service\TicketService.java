package com.company.wfm.service;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.EmployeeDepartmentDTO;
import com.company.wfm.dto.EmployeeDetailsDto;
import com.company.wfm.dto.TicketDTO;
import com.company.wfm.dto.TicketDetailsResponseDTO;
import com.company.wfm.dto.TicketEscalationRequestDTO;
import com.company.wfm.dto.TicketEscalationResponseDTO;
import com.company.wfm.dto.TicketFilterRequestDTO;
import com.company.wfm.dto.TicketResponseDTO;
import com.company.wfm.dto.VendorEmployeeRequestDTO;
import com.company.wfm.entity.Ticket;
import com.company.wfm.entity.Vendor;

import jakarta.servlet.http.HttpServletRequest;

public interface TicketService {
    public TicketResponseDTO createTicket(TicketDTO ticketDTO, MultipartFile[] file) ;

   public Page<Ticket> listAllTickets1(Pageable pageable,TicketFilterRequestDTO ticketFilterRequestDTO);
    public TicketResponseDTO updateTicket(Long ticketId, TicketDTO ticketDTO, MultipartFile file);

  //  public void saveMappings(Long departmentBranchId, List<Long> userIds);
  public void saveMappings(Long branchId, Long departmentId, List<Long> userIds);

   public Page<EmployeeDepartmentDTO> getAllEmployeesByDepartmentBranch(Pageable pageable,String type);

   /*public List<EmployeeDetailsDto> getAllEmployees();
    List<Vendor> getAllVendors();*/

    Page<EmployeeDetailsDto> getAllEmployees(VendorEmployeeRequestDTO request, Pageable pageable);
    Page<Vendor> getAllVendors(Pageable pageable);

  //  public Optional<Ticket> getTicketById(Long ticketId);
   public Optional<TicketDetailsResponseDTO> getTicketDetails(Long ticketId);

    //public Page<TicketDetailsResponseDTO> getTicketDetails(Long ticketId, int offset, int limit);

    public Page<Ticket> listAllTickets(Pageable pageable);
  // public Page<Ticket> listFilteredTickets(Pageable pageable, TicketFilterRequestDTO filter);
  public Page<Ticket> listTicketsWithFilters(Pageable pageable, TicketFilterRequestDTO filterRequest);

    public void escalateTicket(TicketEscalationRequestDTO request, HttpServletRequest request1);
   // public List<TicketResponseDTO> getTicketsWithDetailsByEmployeeId();
   public Page<TicketEscalationResponseDTO> getTicketsWithDetailsByEmployeeId(int page, int size);
}
