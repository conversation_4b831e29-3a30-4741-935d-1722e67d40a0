import React, { useState } from "react";

const Avatar = ({
  intial,
  profileImg,
  className,
  styleInital = {},
  styleImg = {},
}) => {
  const [imgError, setImgError] = useState(false);

  return (
    <div className={className} style={{ position: "relative" }}>
      {!imgError && profileImg && profileImg.includes("https") ? (
        <img
          src={profileImg}
          alt="Avatar"
          style={{
            objectFit: "cover",
            borderRadius: "50%",
            ...styleImg,
          }}
          onError={() => setImgError(true)} // Handle broken images
        />
      ) : (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            borderRadius: "50%",
            // fontWeight: "bold",
            // fontSize: "1.2em",
            // backgroundColor: "#AABFD5", // Default background
            // color: "#000", // Text color
            ...styleInital,
          }}
        >
          {intial}
        </div>
      )}
    </div>
  );
};

export default Avatar;
