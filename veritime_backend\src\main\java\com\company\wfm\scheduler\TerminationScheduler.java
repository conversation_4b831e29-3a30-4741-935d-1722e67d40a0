package com.company.wfm.scheduler;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.company.wfm.entity.Employee;
import com.company.wfm.entity.EmployeeTermination;
import com.company.wfm.entity.ResignationStatus;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.EmployeeTerminationRepository;
import com.company.wfm.repository.ResignationStatusRepository;

@EnableScheduling
@Configuration
public class TerminationScheduler {


    private static final Logger logger = LoggerFactory.getLogger(TerminationScheduler.class);

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private EmployeeTerminationRepository employeeTerminationRepository;

    @Autowired
    private ResignationStatusRepository resignationStatusRepository;

   /* @Scheduled(cron = "0 0 0 * * ?")  // Runs every day at midnight
    public void checkResignationNoticePeriod() {
        LocalDate today = LocalDate.now();

        // Fetch employees with active resignations
        List<EmployeeTermination> resignations = employeeTerminationRepository.findByTerminationType("Resign");

        for (EmployeeTermination termination : resignations) {
            if (termination.getLastWorkingDate().isEqual(today) || termination.getLastWorkingDate().isBefore(today)) {
                Optional<Employee> employeeOpt = employeeRepository.findById(termination.getEmpId());
                if (employeeOpt.isPresent()) {
                    Employee employee = employeeOpt.get();
                    employee.setInService(false);  // Mark as not in service
                   // employee.setLastWorkingDate(termination.getLastWorkingDate());  // Set last working date
                    employeeRepository.save(employee);  // Save updated employee status
                }
            }
        }
    }*/


    // @Scheduled(cron = "0 */5 * * * ?")  // Runs every 5 minutes
   /* @Scheduled(cron = "0 0 0 * * ?")
    public void checkResignationNoticePeriod() {
        LocalDate today = LocalDate.now();
        logger.info("Starting resignation check at {}", today);

        // Fetch employees with active resignations
        List<EmployeeTermination> resignations = employeeTerminationRepository.findByTerminationType("Resign");
        logger.info("Total resignations fetched: {}", resignations.size());

        for (EmployeeTermination termination : resignations) {
            logger.info("Processing termination for Employee ID: {}, Last Working Date: {}", termination.getEmpId(), termination.getLastWorkingDate());

            if (termination.getLastWorkingDate().isEqual(today) || termination.getLastWorkingDate().isBefore(today)) {
                Optional<Employee> employeeOpt = employeeRepository.findById(termination.getEmpId());

                if (employeeOpt.isPresent()) {
                    Employee employee = employeeOpt.get();
                    logger.info("Marking Employee ID {} as 'Not in Service'", employee.getEmpId());

                    employee.setInService(false);  // Mark as not in service
                    employeeRepository.save(employee);  // Save updated employee status

                    logger.info("Successfully updated Employee ID {} status", employee.getEmpId());
                } else {
                    logger.warn("Employee with ID {} not found in Employee table", termination.getEmpId());
                }
            }
        }

        logger.info("Resignation check completed.");
    }*/



    @Scheduled(cron = "0 0 0 * * ?")
    public void checkResignationNoticePeriod() {
        LocalDate today = LocalDate.now();
        logger.info("Starting resignation check at {}", today);

        // Fetch employees with active resignations
        List<EmployeeTermination> resignations = employeeTerminationRepository.findByTerminationType("Resign");
        logger.info("Total resignations fetched: {}", resignations.size());

        for (EmployeeTermination termination : resignations) {
            logger.info("Processing termination for Employee ID: {}, Last Working Date: {}", termination.getEmpId(), termination.getLastWorkingDate());

            // Get the resignation status for the employee
            Optional<ResignationStatus> resignationStatusOpt = resignationStatusRepository.findByEmpId(termination.getEmpId());
            if (resignationStatusOpt.isPresent()) {
                ResignationStatus resignationStatus = resignationStatusOpt.get();

                // Skip if resignation status is 'Rejected'
                if ("Rejected".equalsIgnoreCase(resignationStatus.getStatus())|| "Cancelled".equalsIgnoreCase(resignationStatus.getStatus())) {
                    logger.info("Skipping resignation for Employee ID: {} because resignation is rejected", termination.getEmpId());
                    continue;
                }
            }

            // Process the resignation if the status is not 'Rejected' and the last working date matches
            if (termination.getLastWorkingDate().isEqual(today) || termination.getLastWorkingDate().isBefore(today)) {
                Optional<Employee> employeeOpt = employeeRepository.findById(termination.getEmpId());

                if (employeeOpt.isPresent()) {
                    Employee employee = employeeOpt.get();
                    logger.info("Marking Employee ID {} as 'Not in Service'", employee.getEmpId());

                    employee.setInService(false);  // Mark as not in service
                    employeeRepository.save(employee);  // Save updated employee status

                    logger.info("Successfully updated Employee ID {} status", employee.getEmpId());
                } else {
                    logger.warn("Employee with ID {} not found in Employee table", termination.getEmpId());
                }
            }
        }

        logger.info("Resignation check completed.");
    }



}
