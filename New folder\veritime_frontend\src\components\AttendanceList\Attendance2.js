import React, { useEffect, useState } from 'react';
import TableFilter from '../../common-components/TableFilter';
import { postRequest } from '@/services/apiService';
import { API_URLS } from '@/constants/apiConstants';
import '../../components/AttendanceList/teamAttendance.css'; 

const AttendanceTable = ({ fromDate, toDate, goButton }) => {
  const [filteredRows, setFilteredRows] = useState([]);

  useEffect(() => {
    if (goButton) {
      fetchAttendance();
    }
  }, [goButton, fromDate, toDate]); // Fetch data whenever `goButton` is toggled or dates change

  const fetchAttendance = async () => {
    try {
      // Correcting API call, passing required params
      const response = await postRequest(API_URLS.TEAM_ATTENDANCE(fromDate, toDate));

      if (response && Array.isArray(response)) {
        // Format the rows based on the API response
        const formattedRows = response.map((item, index) => ({
          id: index + 1,
          Name: item.empname,  // Make sure the field names match the response
          Date: item.date,
          Status: item.status,  // Add Status to table columns if required
        }));

        setFilteredRows(formattedRows);
      }
    } catch (error) {
    }
  };

  const columns = [
    { field: 'id', headerName: 'Sr.no', width: 120 },
    { field: 'Name', headerName: 'Name', width: 150 },
    { field: 'Date', headerName: 'Date', width: 150 },
    { field: 'Status', headerName: 'Status', width: 120 },  // Show status in the table
  ];

  return (
    <div id="tableWrapper">
      <TableFilter columns={columns} rows={filteredRows} getRowId={(row) => row.id} />
    </div>
  );
};

export default AttendanceTable;
