package com.company.wfm.entity;

public enum DesignationRole {
    admin, supervisor, employee, superadmin;
    public static DesignationRole fromString(String role) {
        for (DesignationRole r : DesignationRole.values()) {
            if (r.name().equalsIgnoreCase(role)) {
                return r;
            }
        }
        throw new IllegalArgumentException("No constant with text " + role + " found");
    }
}