package com.company.wfm.repository;


import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.VendorProduct;

@Repository
public interface VendorProductRepository extends JpaRepository<VendorProduct, Long> {
   // List<VendorProduct> findByVendorId(Long vendorId);
   Page<VendorProduct> findByVendorId(Long vendorId, Pageable pageable);
}
