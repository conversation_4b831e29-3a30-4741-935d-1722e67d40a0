package com.company.wfm.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TicketUserMappingRequest {
  //  private Long departmentBranchId; // The ID of the department branch
    private Long branchId;
    private Long departmentId;
    private List<Long> userIds; // List of user IDs to associate with the department branch
}
