package com.company.wfm.service;

import org.springframework.data.domain.Page;

import com.company.wfm.dto.PaginationRequestDTO;
import com.company.wfm.dto.ResignationStatusDTO;
import com.company.wfm.dto.ResignationStatusResponseDTO;

public interface ResignationService {

   // public Page<ResignationStatus> getResignationStatusList(PaginationRequestDTO filter);
   public void rejectResignation(Long resignationId , String remark );
 public void approveResignation(Long resignationId,String remark);

    public Page<ResignationStatusResponseDTO> getSelfHistory(PaginationRequestDTO filter);

    public Page<ResignationStatusResponseDTO> getAllStatusList(PaginationRequestDTO filter);
 public void cancelResignation(Long resignationId, ResignationStatusDTO resignationStatusDTO , String remark);

}
