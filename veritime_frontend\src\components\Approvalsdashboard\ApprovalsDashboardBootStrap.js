import React, { useState, useEffect } from "react";
import "../../css/style.css";
import { Container, Row, Col, Button } from "react-bootstrap";
import { useTheme } from "@material-ui/core/styles";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import SwipeableViews from "react-swipeable-views";
import StatCards from "../../common-components/StatCards";
import ApprovalTable from "../../common-components/ApprovalTable";
import { getRequest } from "../../services/apiService";
import VisibilityIcon from "@mui/icons-material/Visibility";

function TabPanel(props) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 4 }}>{children}</Box>}
    </div>
  );
}
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import ModificationTable from "../../common-components/ModificationTable.js";
import ShiftChangeList from "../../common-components/shiftChange.js";
import Employee from "../../common-components/Employee.js";
import { API_URLS } from "@/constants/apiConstants";

ChartJS.register(ArcElement, Tooltip, Legend);
function a11yProps(index) {
  return {
    id: `full-width-tab-${index}`,
    "aria-controls": `full-width-tabpanel-${index}`,
  };
}

const ApprovalsDashboardBootStrap = ({
  toggleMenu,
  expanded,
  isFromHistory,
}) => {
  const [blocksData, setBlocksData] = useState([]);

  const fetchCountData = async (type) => {
    try {
      const adjustedType = isFromHistory ? `my${type}` : type;
      const response = await getRequest(
        `${API_URLS.DASHBOARDS}?type=${adjustedType}`
      );
      setBlocksData(response);
      console.log("Fetched data:", response);
    } catch (error) {}
  };

  const theme = useTheme();
  const [value, setValue] = React.useState(0);
  const [anchorEl, setAnchorEl] = React.useState(null);

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleChangeIndex = (index) => {
    setValue(index);
  };

  const handleClick = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Container fluid style={{ marginTop: "50px" }}>
      <Row className="my-3">
        <Col md={2} sm={12}>
          <Row>
            <Col style={{ marginLeft: "15px" }}>
              <h3>{isFromHistory ? "History" : "Approvals"}</h3>
            </Col>
            {/* <Col>
            <Button style={{width:'100px',    margin: 'auto',
             marginRight: '0px'}} aria-describedby={id}  variant="contained" className={"carousal-container"} onClick={handleClick}>
            <FilterAltIcon />
            </Button>
            <Popover
              id={id}
              open={open}
              anchorEl={anchorEl}
              onClose={handleClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'left',
              }}
            >
              <div>
                <select className="filter-select">
                  <option disabled selected>Select Department</option>
                  <option>Account</option>
                  <option>IT</option>
                  <option>Pharmacy</option>
                  <option>Nursing</option>
                </select>
              </div>
              <hr />
              <div>
                <select className="filter-select">
                  <option disabled selected>Select Designation</option>
                  <option>Juniour HR</option>
                  <option>Pharmist</option>
                  <option>Doctor</option>
                  <option>Nurse</option>
                </select>
              </div>
              <hr />

              <div>
                <select className="filter-select">
                  <option disabled selected>Select Schedule</option>
                  <option>10:00Am - 7:00Pm</option>
                  <option>7:00 - 3:00Pm</option>
                  <option>3:00Am - 1:00Pm</option>
                  <option>Other</option>
                </select>
              </div>
            </Popover>
          </Col> */}
          </Row>
        </Col>
        <Col md={10}>
          {/* <Row className='filter-container' style={{marginLeft: "15px"}}>
            <Col md={3}>
              <select className="filter-select" value={department} onChange={handleDepartmentChange}>
                <option value="" disabled>Select Department</option>
                {departments.map((dept, index) => (
                  <option key={index} value={dept}>{dept}</option>
                ))}
              </select>
            </Col>
            <Col md={3}>
              <select className="filter-select" value={designation} onChange={handleDesignationChange}>
                <option value="" disabled>Select Designation</option>
                {designations.map((desig, index) => (
                  <option key={index} value={desig.designationId}>{desig.designationName}</option>
                ))}
              </select>
            </Col>
            <Col md={3}>
              <select className="filter-select" value={schedule} onChange={handleScheduleChange}>
                <option value="" disabled>Select Schedule</option>
                {schedules.map((sched, index) => (
                  <option key={index} value={sched}>{sched}</option>
                ))}
              </select>
            </Col>
            <Col md={3}>
              <Button
                variant="primary"
                style={{
                  background: '#EEE9DA',
                  border: 'none',
                  color: '#1F5F9A',
                }}
                className="mx-2"
                onClick={handleSubmit}
              >
                GO
              </Button>
            </Col>
          </Row> */}
        </Col>
        <Col></Col>
      </Row>
      <Row className="my-0">
        <Col md={2} className="p-0" style={{ backgroundColor: "#6097b4" }}>
          <Tabs
            value={value}
            onChange={handleChange}
            indicatorColor="secondary"
            textColor="inherit"
            variant="fullWidth"
            aria-label="full width tabs example"
            className="tab-pannel-test "
            style={{ backgroundColor: "#6097b4" }}
          >
            <Tab
              label="Leave Request"
              {...a11yProps(0)}
              style={{
                color: "#000",
                backgroundColor: "#ccc",
                boxShadow: "none",
              }}
            />
            <Tab
              label="Modification"
              {...a11yProps(1)}
              style={{
                color: "#000",
                backgroundColor: "#ccc",
                boxShadow: "none",
              }}
            />
            <Tab
              label="Shift Change"
              {...a11yProps(2)}
              style={{
                color: "#000",
                backgroundColor: "#ccc",
                boxShadow: "none",
              }}
            />
            <Tab
              label="Resignation"
              {...a11yProps(3)}
              style={{
                color: "#000",
                backgroundColor: "#ccc",
                boxShadow: "none",
              }}
            />

            {/* {isFromHistory && <Tab label="Attendance" {...a11yProps(3)}style={{ color: "#000", backgroundColor: "#ccc", boxShadow: "none", fontFamily: "inherit" }} />} */}
          </Tabs>
          {/* <Row className='carousal-container'>
            <CarouselComp setValue={setValue}  arr={isFromHistory ? ["Leave Request","Modification", "Attendance" , "Shift Change"] : ["Leave Request","Modification","Attendance", "Shift Change"]} />
            </Row> */}
        </Col>
        <Col md={10}>
          <SwipeableViews
            axis={theme.direction === "rtl" ? "x-reverse" : "x"}
            index={value}
            onChangeIndex={handleChangeIndex}
          >
            <TabPanel value={value} index={0} dir={theme.direction}>
              <Row>
                {blocksData.length > 0 ? (
                  <StatCards key={1} arr={blocksData} />
                ) : null}
                <ApprovalTable
                  showActionCol={!isFromHistory}
                  isFromHistory={isFromHistory}
                  fetchCount={fetchCountData}
                />
              </Row>
            </TabPanel>
            <TabPanel value={value} index={1} dir={theme.direction}>
              <Row>
                {blocksData.length > 0 ? (
                  <StatCards key={1} arr={blocksData} />
                ) : null}
                <ModificationTable
                  showActionCol={!isFromHistory}
                  fetchCount={fetchCountData}
                />
              </Row>
            </TabPanel>
            <TabPanel value={value} index={2} dir={theme.direction}>
              <Row>
                <Row>
                  {blocksData.length > 0 ? (
                    <StatCards key={1} arr={blocksData} />
                  ) : null}
                  <ShiftChangeList
                    showActionCol={!isFromHistory}
                    fetchCount={fetchCountData}
                  />
                </Row>
              </Row>
            </TabPanel>
            <TabPanel value={value} index={3} dir={theme.direction}>
              <Row>
                <Row>
                  <Employee
                    showActionCol={!isFromHistory}
                    isFromHistory={isFromHistory}
                    fetchCount={fetchCountData}
                  />
                </Row>
              </Row>
            </TabPanel>
            {/* <TabPanel value={value} index={3} dir={theme.direction}>
              <Row>
                <StatCards key={4} arr={[{
                  title: "Shift Change", value: blocksData?.totalshiftreq, percentage: "25", color: "#D2E1EB", pathcolor: '#007DE0'
                }, {
                  title: "Shift Swap", value: blocksData?.totalshiftreq, percentage: "10", color: "#D2E1EB", pathcolor: '#007DE0'
                },
                { title: "Shift Change Request", value: blocksData?.totalshiftreq, percentage: "2", color: "#D2E1EB", pathcolor: '#007DE0' }]} />
                <ShiftChangeList showActionCol={!isFromHistory} />
              </Row>
            </TabPanel> */}
          </SwipeableViews>
        </Col>
      </Row>
    </Container>
  );
};

export default ApprovalsDashboardBootStrap;
