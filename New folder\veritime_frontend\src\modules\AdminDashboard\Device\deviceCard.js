import React, { useState } from 'react'
import { Card } from 'react-bootstrap'

const DeviceCard=({data})=> {
   
  return (
    <div className="col-12 col-md-6 col-lg-3 mb-3">
    <Card className="shadow-sm">
      <Card.Body
        style={{ height: 150 }}
        className="d-flex justify-content-center align-items-center"
      >
        <div
          style={{
            backgroundColor: "#BFBBD9",
            padding: 15,
            borderRadius: 60,
            marginRight: 15,
          }}
        >
          <img
            src="/image/device.png"
            alt="Foreground Icon"
            className="foreground-icon"
          />
        </div>
        <div>
          <div className="h2">{data}</div>
          <div className="small text-muted">Devices</div>
        </div>
      </Card.Body>
    </Card>
  </div>
  )
}

export default DeviceCard