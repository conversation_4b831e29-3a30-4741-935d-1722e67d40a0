package com.company.wfm.repository;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.entity.TimeSlot;

@Repository
public interface TimeSlotRepository extends JpaRepository<TimeSlot, Long> {

	@Query(value = "select count(*) from t_timeslot where schedule_id = :scheduleId and start_time = :startTime and end_time=:endTime", nativeQuery = true)
	public int isScheduleTimeSlotAlreadyExist(@Param("scheduleId") final long scheduleId,
			@Param("startTime") final String startTime, @Param("endTime") final String endTime);
}
