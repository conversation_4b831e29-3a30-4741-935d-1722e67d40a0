package com.company.wfm.entity;
import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
@Entity
@Data
@Getter
@Setter
@Table(name = "feeder_hospitals")
public class FeederHospital {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "hospital_name")
    private String name;
    @Column(name = "address_line1")
    private String address;
    @Column(name = "hospital_type", nullable = false)
    private String type;
    private String facility;
    private String province;
    private String district;
    private String subDistrict;
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    private String adminId;
    private String password;
    @Column(name = "short_code", nullable = false)
    private String shortCode;

    @ManyToOne
    @JoinColumn(name = "sub_district_id")
    private SubDistrict subDistrictEntity;


}