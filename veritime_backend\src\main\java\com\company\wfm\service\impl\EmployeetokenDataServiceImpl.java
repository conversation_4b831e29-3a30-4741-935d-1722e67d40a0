package com.company.wfm.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Component;

import com.company.wfm.repository.UserRepository;
import com.company.wfm.service.UserTokenService;

@Component
public class EmployeetokenDataServiceImpl implements UserTokenService {

	@Autowired
	private UserRepository userRepository;

	@Override
	public Long getEmployeeIdFromToken() {
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		User user = (User) authentication.getPrincipal();
		com.company.wfm.entity.User userDetails = userRepository.findByUsername(user.getUsername());
		return userDetails.getEmployee().getEmpId();

	}

	@Override
	public com.company.wfm.entity.User getEmployeeFromToken() {
		// TODO Auto-generated method stub
		Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
		User user = (User) authentication.getPrincipal();
		com.company.wfm.entity.User userDetails = userRepository.findByUsername(user.getUsername());
		return userDetails;
	}
}
