import React, { useState } from 'react';
import './Masters.css';
import CreateDesignationModal from '@/app/adminDashboard/modals/CreateDesignationModal';
import CreateScheduleModal from '@/app/adminDashboard/modals/CreateScheduleModal';

const MastersCard1 = ({ arrowDirection }: any) => {
  const [openDesigntionModal, setDesigntionModal] = React.useState(false);
  const handleDesigntionModalOpen = () => setDesigntionModal(true);
  const handleDesigntionModalClose = () => setDesigntionModal(false);

  const [openSchedulenModal, setScheduleModal] = React.useState(false);
  const handleScheduleModalOpen = () => setScheduleModal(true);
  const handleScheduleModalClose = () => setScheduleModal(false);
  return (
    <div className={`custom-card-masters ${arrowDirection}`}>
      <div className="card-body">
        <ul className="list-unstyled">
          {/* <li className="d-flex align-items-center mb-2">
            <a href="/adminDashboard/departmentList" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/icon2.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Department</span>
            </a>
          </li> */}
          {/* <li className="d-flex align-items-center mb-2">
            <a href="/adminDashboard/designationList" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/icon2.0.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Designation</span>
            </a>
          </li> */}
          <li className="d-flex align-items-center mb-2">
            <a href="/adminDashboard/branchList" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/icon3.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Branch</span>
            </a>
          </li>
          {/* <li className="d-flex align-items-center mb-2">
            <a href="/adminDashboard/employee" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/icon4.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Employee</span>
            </a>
          </li> */}
          {/* <li className="d-flex align-items-center mb-2">
            <a href="/adminDashboard/scheduleList" className="d-flex align-items-center"  >
              <div className="icon-placeholder">
                <img src='/image/icon5.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Schedule</span>
            </a>
          </li> */}
          <li className="d-flex align-items-center mb-2">
            <a href="/adminDashboard/leavelist" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/icon1.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Leaves</span>
            </a>
          </li>
          {/* <li className="d-flex align-items-center mb-2">
            <a href="/adminDashboard/holidayList" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/holiday.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Holidays</span>
            </a>
          </li> */}
          <li className="d-flex align-items-center mb-2">
            <a href="/adminDashboard/hospitalList" className="d-flex align-items-center">
              <div className="icon-placeholder">
                <img src='/image/holiday.png' style={{ width: "24px", height: "24px" }} />
              </div>
              <span className="link-text ms-2">Hospitals</span>
            </a>
          </li>
        </ul>
      </div>
      {openDesigntionModal && <CreateDesignationModal show={openDesigntionModal} handleClose={handleDesigntionModalClose} />}

      {openSchedulenModal && <CreateScheduleModal show={openSchedulenModal} handleClose={handleScheduleModalClose} />}

    </div>
  );
};

export default MastersCard1;
