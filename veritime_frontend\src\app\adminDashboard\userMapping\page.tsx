"use client";
import { useState, useEffect } from "react";
import { Con<PERSON>er, <PERSON>, Col, Modal, Button, Form } from "react-bootstrap";
import TableFilter from "../../../common-components/TableFilter.js";
import { useTheme } from "@material-ui/core/styles";
import EditIcon from "@mui/icons-material/Edit";
import { TabPanel } from "../../../common-components/utils.js";
import Layout from "@/components/Layout";
import { appConstants } from "@/constants/appConstants.js";
import { postRequest } from "@/services/apiService.js";
import { API_URLS } from "@/constants/apiConstants.js";
import { showErrorAlert, showSuccessAlert2 } from "@/services/alertService.js";
import CustomMultiSelectModal from "../../../components/Dropdowns/CustomMultiSelectModal.js";
import VisibilityIcon from "@mui/icons-material/Visibility";

// Define types
interface User {
  empId: number;
  empName: string;
  support_person: boolean;
}

interface Department {
  branchName: string;
  departmentName: string;
  userList: User[];
  departmentBranchId: number;
  departmentId: number;
  branchId: number;
}

const UserMappingList = () => {
  const [username, setUsername] = useState("");
  const [rows, setRows] = useState<any>([]);
  const [showSupportPersonModal, setShowSupportPersonModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [selectedBranch, setSelectedBranch] = useState<any>(null);
  const [selectedUsers, setSelectedUsers] = useState<any>([]);
  const [userList, setUserList] = useState<any>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [departmentDetails, setDepartmentDetails] = useState<Department | null>(null);
  const [value, setValue] = useState<number>(0);

  useEffect(() => {
    const storedRole = localStorage.getItem(appConstants?.role);
    if (storedRole) {
      setUsername(storedRole);
    }
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      const data = await postRequest(API_URLS.TICKET_BRANCHWISE_DEPARTMENT, {});
      const formattedData = data.content.map((department: any, index: number) => ({
        id: index + 1,
        branchName: department.branchName,
        departmentName: department.departmentName,
        userList: department.userList,
        departmentBranchId: department.departmentBranchId,
        departmentId: department.departmentId,
        branchId: department.branchId,
      }));
      setRows(formattedData);
    } catch (error) {
    }
  };

  const handleSupportIconClick = (department: any, editMode = false) => {
    setSelectedDepartment(department);
    setSelectedBranch(department);
    const supportPersonIds = department.userList
      .filter((user: any) => user.support_person)
      .map((user: any) => user.empId);
    setSelectedUsers(supportPersonIds);
    setUserList(department.userList);
    setIsEditMode(editMode);
    setShowSupportPersonModal(true);
  };

  const toggleUserSelection = (empId: number) => {
    setSelectedUsers((prevSelectedUsers: any) =>
      prevSelectedUsers.includes(empId)
        ? prevSelectedUsers.filter((id: any) => id !== empId)
        : [...prevSelectedUsers, empId]
    );
  };

  const handleSaveSupportPersons = async () => {
    if (!selectedDepartment) return;

    const requestData = {
      departmentId: selectedDepartment?.departmentId,
      branchId: selectedBranch?.branchId,
      userIds: selectedUsers,
    };

    try {
      await postRequest(API_URLS.USER_MAPPING_SAVE, requestData);
      showSuccessAlert2("User updated successfully");
      setShowSupportPersonModal(false);
      fetchUserData();
    } catch (error) {
      showErrorAlert("Failed to update User");
    }
  };

  const handleDetail = (department: Department) => {
    setDepartmentDetails(department);
    setShowDetailModal(true);
  };

  const columns = [
    { field: "id", headerName: "Sr.no", width: 100 },
    { field: "branchName", headerName: "Branch", width: 200 },
    { field: "departmentName", headerName: "Department", width: 200 },
    {
      field: "userList",
      headerName: "Support Persons",
      width: 500,
      renderCell: (params: any) => {
        const supportPersons = params.row.userList.filter(
          (user: any) => user.support_person
        );
        const hasSupportPerson = supportPersons.length > 0;

        return (
          <>
            {supportPersons.map((user: any, index: number) => (
              <span key={user.empId} className="btn-info">
                {user.empName}
                {index < supportPersons.length - 1 && ", "}
              </span>
            ))}
            {!hasSupportPerson && params?.row?.userList?.length > 0 && (
              <Button
                variant="link"
                onClick={() => handleSupportIconClick(params.row, false)}
                style={{ padding: 0, color: "blue", marginLeft: "10px" }}
              >
                <img
                  src="/image/supportedperson.png"
                  style={{ width: "25px", height: "25px" }}
                />
              </Button>
            )}
          </>
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 200,
      renderCell: (params: any) => {
        const supportPersons = params.row.userList.filter((user: any) => user.support_person);
        const hasSupportPerson = supportPersons.length > 0;
    
        return (
          <strong>
            {hasSupportPerson && (
              <EditIcon
                onClick={() => handleSupportIconClick(params.row, true)}
                style={{ cursor: "pointer", marginRight: 2 }}
              />
            )}
            
            {hasSupportPerson && (
            <VisibilityIcon
              style={{ cursor: "pointer", marginRight: "10px" }}
              onClick={() => handleDetail(params.row)}
            />
          )}
          </strong>
        );
      },
    }
    
  ];

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Layout>
      <Container fluid style={{ marginTop: "50px" }}>
        <Row className="my-3">
          <Col md={4} sm={12} style={{ width: "30%", marginLeft: "20px" }}>
            <h4>{"User Mapping List"}</h4>
          </Col>
        </Row>
        <Row>
          <Col md={12}>
            <TabPanel value={value} index={0}>
              <TableFilter rows={rows} columns={columns} />
            </TabPanel>
          </Col>
        </Row>

        <CustomMultiSelectModal
          show={showSupportPersonModal}
          onHide={() => setShowSupportPersonModal(false)}
          onSave={handleSaveSupportPersons}
          headerTitle={isEditMode ? "Edit Support User" : "Add Support User"}
          selectTitle="User"
          data={userList}
          selectedValues={selectedUsers}
          setSelectedValues={setSelectedUsers}
          valueKey="empId"
          labelKey="empName"
          imgUrl={"imgUrl"}
        />
        
        <Modal show={showDetailModal} onHide={() => setShowDetailModal(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>Department Details</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {departmentDetails && (
              <>
                <p><strong>Branch:</strong> {departmentDetails.branchName}</p>
                <p><strong>Department:</strong> {departmentDetails.departmentName}</p>
                <p><strong>Support Persons:</strong></p>
                {departmentDetails.userList
                  .filter((user: any) => user.support_person)
                  .map((user: any) => (
                    <span key={user.empId} className="btn-info">
                      {user.empName}
                    </span>
                  ))}
              </>
            )}
          </Modal.Body>
        </Modal>
      </Container>
    </Layout>
  );
};

export default UserMappingList;
