package com.company.wfm.service;

import java.time.LocalDate;
import java.util.List;

import com.company.wfm.dto.AttendanceAuditDetailsDTO;
import com.company.wfm.dto.AttendanceAuditDetailsListDTO;
import com.company.wfm.dto.AttendanceAuditDto;
import com.company.wfm.dto.AttendanceAuditResponseDTO;
import com.company.wfm.dto.AttendanceDto;
import com.company.wfm.dto.AttendanceResponseDTO;
import com.company.wfm.dto.EmployeePastPunchDataDTO;
import com.company.wfm.dto.EmployeePunchDataDTO;
import com.company.wfm.entity.AttendanceAuditEntity;
import com.company.wfm.entity.AttendanceEntity;

public interface AttendanceAuditService {

	public List<EmployeePunchDataDTO> getFirstAndLastSwapsForPreviousDay();

	public void insertAttendanceRecords(List<EmployeePunchDataDTO> punchDataList);

	public List<AttendanceResponseDTO> getEmployeeAttendanceByDateRange(LocalDate toDate, LocalDate fromDate);

	public List<AttendanceEntity> getMultipleEmployeeAttendanceByDateRange(AttendanceDto param);

	//public List<AttendanceAuditEntity> getEmployeeAttendanceAuditByDateRange(LocalDateTime toDate, LocalDateTime fromDate);

	public List<AttendanceAuditEntity> getMultipleEmployeeAttendanceAuditByDateRange(AttendanceAuditDto param);
	
	public void getAttendanceDetailByDateRange(String param);

	public List<AttendanceAuditResponseDTO> getEmployeeAttendanceAuditByDateRange(LocalDate fromDate, LocalDate toDate);

	public List<EmployeePastPunchDataDTO> getFirstAndLastSwapsForOldDay();

	public void insertPastAttendanceRecords(List<EmployeePastPunchDataDTO> punchDataList);

	List<AttendanceAuditDetailsListDTO> retrieveByEmpIdandDate(AttendanceAuditDetailsDTO attendanceAuditDetailsDTO);
	
	public void processDailyAttendance(LocalDate jobRunDate);
}
