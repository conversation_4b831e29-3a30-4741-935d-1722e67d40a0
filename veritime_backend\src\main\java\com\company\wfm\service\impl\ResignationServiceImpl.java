package com.company.wfm.service.impl;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import com.company.wfm.dto.*;
import com.company.wfm.entity.EmployeeTermination;
import com.company.wfm.repository.EmployeeTerminationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

import com.company.wfm.entity.Employee;
import com.company.wfm.entity.ResignationStatus;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.ResignationStatusRepository;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.ResignationService;
import com.company.wfm.service.UserTokenService;

import jakarta.persistence.EntityNotFoundException;
import software.amazon.awssdk.services.amplify.model.UnauthorizedException;


@Service
public class ResignationServiceImpl implements ResignationService {


    private static final Logger logger = LoggerFactory.getLogger(ResignationServiceImpl.class);

    @Autowired
    private ResignationStatusRepository resignationStatusRepository;


    @Autowired
    private UserTokenService tokenService;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private CommonNotificationService notificationService;


    @Autowired
    private EmailService emailService;

    @Autowired
    private EmployeeTerminationRepository employeeTerminationRepository;
    private String terminationReason;

    @Autowired
    private SmsProcessingService smsProcessingService;




    @Override
    public Page<ResignationStatusResponseDTO> getSelfHistory(PaginationRequestDTO filter) {
        logger.info("Initiating call to fetch resignation status details");

        try {
            // Retrieve the employee ID from the token
            Long empId = tokenService.getEmployeeIdFromToken();

            if (empId == null) {
                logger.error("Unauthorized access: Employee ID not found in token");
                throw new RuntimeException("Unauthorized: Employee not found");
            }

            // Optionally log the employee ID
            logger.info("Fetching resignation status for employee ID: " + empId);

            // Pagination and sorting
            Pageable pageable = PageRequest.of(filter.getOffset(), filter.getLimit(), Sort.by("createdTime").descending());
            Page<ResignationStatus> resignationStatusPage = resignationStatusRepository.findByEmpId(empId, pageable);
            // Fetch employee details
            Employee employee = employeeRepository.findById(empId).orElse(null);
            String empName = (employee != null) ? employee.getEmpName() : "Unknown";
            String empImageUrl = (employee != null) ? employee.getImgUre():"default_image_url";


            // Fetch resignation status for the logged-in employee
            logger.info("Fetching resignation statuses for employee ID: " + empId);

            // Convert to DTO
            return resignationStatusPage.map(status -> {
                // Fetch CreatedBy Employee Name
                String createdByName = "Unknown";
                if (status.getCreatedBy() != null) {
                    Employee createdByEmp = employeeRepository.findById(status.getCreatedBy()).orElse(null);
                    createdByName = (createdByEmp != null) ? createdByEmp.getEmpName() : "Unknown";
                }

                String updatedByName = "";
                if (status.getUpdatedBy() != null) {
                    Employee updatedByEmp = employeeRepository.findById(status.getUpdatedBy()).orElse(null);
                    updatedByName = (updatedByEmp != null) ? updatedByEmp.getEmpName() : "";
                }

                return new ResignationStatusResponseDTO(
                        status.getId(),
                        status.getEmpId(),
                        empName,
                        empImageUrl,
                        status.getNoticeStartDate(),
                        status.getLastWorkingDate(),
                        status.getStatus(),
                        status.getCreatedBy(),
                        status.getCreatedTime(),
                        status.getUpdatedBy(),
                        status.getUpdatedTime(),
                        status.getRemark(),
                        createdByName ,// Adding createdByName
                        updatedByName,
                        terminationReason  // Include termination reason

                );
            });


        } catch (Exception e) {
            logger.error("Error while fetching resignation status: ", e);
            throw new RuntimeException("An error occurred while fetching resignation status. Please try again.");
        }
    }


    @Override
    public Page<ResignationStatusResponseDTO> getAllStatusList(PaginationRequestDTO filter) {
        logger.info("Initiating call to fetch resignation status details1");

        try {
            // Retrieve the employee ID from the token
            Long empId = tokenService.getEmployeeIdFromToken();

            if (empId == null) {
                logger.error("Unauthorized access1: Employee ID not found in token");
                throw new RuntimeException("Unauthorized: Employee not found");
            }

            // Fetch employees under the logged-in employee using the empId
            List<Employee> employeesUnderLoggedIn = employeeRepository.findByUpperId(empId);

            // Extract the employee IDs of the employees under the logged-in employee
            List<Long> employeeIdsUnderLoggedIn = employeesUnderLoggedIn.stream()
                    .map(Employee::getEmpId)
                    .collect(Collectors.toList());

            // Add the logged-in employee's ID to the list of employee IDs
            employeeIdsUnderLoggedIn.add(empId);

            // Optionally log the employee IDs
            logger.info("Fetching resignation statuses for employee IDs: " + employeeIdsUnderLoggedIn);

            // Pagination and sorting
            Pageable pageable = PageRequest.of(filter.getOffset(), filter.getLimit(), Sort.by("createdTime").descending());
            Page<ResignationStatus> resignationStatusPage = resignationStatusRepository.findByEmpIdIn(employeeIdsUnderLoggedIn, pageable);
            List<Employee> allEmployees = employeeRepository.findByEmpIdIn(employeeIdsUnderLoggedIn);
            Map<Long, String> employeeImageUrlMap = allEmployees.stream()
                    .collect(Collectors.toMap(
                            Employee::getEmpId,
                            emp -> (emp.getImgUre() != null ? emp.getImgUre() : "default_image_url")
                    ));

            // Fetch employee names in bulk
            Map<Long, String> employeeNamesMap = employeeRepository.findByEmpIdIn(employeeIdsUnderLoggedIn)
                    .stream()
                    .collect(Collectors.toMap(Employee::getEmpId, Employee::getEmpName));


            Set<Long> createdByIds = resignationStatusPage.stream()
                    .map(ResignationStatus::getCreatedBy)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Map<Long, String> createdByNamesMap = employeeRepository.findByEmpIdIn(new ArrayList<>(createdByIds))
                    .stream()
                    .collect(Collectors.toMap(Employee::getEmpId, Employee::getEmpName));


            // Fetch UpdatedBy Employee Names
            Set<Long> updatedByIds = resignationStatusPage.stream()
                    .map(ResignationStatus::getUpdatedBy)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Map<Long, String> updatedByNamesMap = employeeRepository.findByEmpIdIn(new ArrayList<>(updatedByIds))
                    .stream()
                    .collect(Collectors.toMap(Employee::getEmpId, Employee::getEmpName));

            Set<Long> terminateIds = resignationStatusPage.stream()
                    .map(ResignationStatus::getTerminateId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Map<Long, String> terminationReasonMap = employeeTerminationRepository.findByTerminationIdIn(new ArrayList<>(terminateIds))
                    .stream()
                    .collect(Collectors.toMap(EmployeeTermination::getTerminationId, EmployeeTermination::getTerminationReason));






            // Convert to DTO
            return resignationStatusPage.map(status -> new ResignationStatusResponseDTO(
                    status.getId(),
                    status.getEmpId(),
                    employeeNamesMap.getOrDefault(status.getEmpId(), "Unknown"),
                    employeeImageUrlMap.getOrDefault(status.getEmpId(), "default_image_url"),
                    status.getNoticeStartDate(),
                    status.getLastWorkingDate(),
                    status.getStatus(),
                    status.getCreatedBy(),
                    status.getCreatedTime(),
                    status.getUpdatedBy(),
                    status.getUpdatedTime(),
                    status.getRemark(),
                    createdByNamesMap.getOrDefault(status.getCreatedBy(), "") ,
                    updatedByNamesMap.getOrDefault(status.getUpdatedBy(), ""),
                    terminationReasonMap.getOrDefault(status.getTerminateId(), "N/A")

            ));

        } catch (Exception e) {
            logger.error("Error while fetching resignation status: ", e);
            throw new RuntimeException("An error occurred while fetching resignation status. Please try again.");
        }
    }


//    approve and reject api

    @Override
    public void approveResignation(Long resignationId , String remark) {
        try {
            Long empId = tokenService.getEmployeeIdFromToken();
            Optional<ResignationStatus> resignationStatusOpt = resignationStatusRepository.findById(resignationId);
            if (resignationStatusOpt.isPresent()) {
                ResignationStatus resignationStatus = resignationStatusOpt.get();
                if ("pending".equalsIgnoreCase(resignationStatus.getStatus())) {
                    resignationStatus.setStatus("approved");
                    resignationStatus.setUpdatedBy(empId);
                    resignationStatus.setUpdatedTime(LocalDateTime.now());
                    resignationStatus.setRemark(remark);
                    resignationStatus = resignationStatusRepository.save(resignationStatus);
                    notifyEmployeeOnResignationApproval(resignationStatus,remark);
                } else {
                    throw new IllegalStateException("Resignation is not in pending status");
                }
            } else {
                throw new NoSuchElementException("Resignation not found");
            }
        } catch (NoSuchElementException | IllegalStateException e) {
            logger.error("Error in approving resignation: ", e);
            throw new RuntimeException("Approval failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error while approving resignation: ", e);
            throw new RuntimeException("An unexpected error occurred while approving the resignation.");
        }

    }

    private void notifyEmployeeOnResignationApproval(ResignationStatus resignationStatus,String remark) {
        // logger.info("Notifying employee about resignation approval: " + resignationStatus);
        logger.info("Notifying employee about resignation approval:");
        logger.info("Resignation Details: " + resignationStatus);
        logger.info("Approval Remark: " + remark);

        Long LoggedInEmpId=tokenService.getEmployeeIdFromToken();
        long EmpId=resignationStatus.getEmpId();
        Employee employee = employeeRepository.findById(EmpId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + EmpId));
        String empName = employee.getEmpName();


        String emailMessage = "Your resignation has been approved. "
                + "Remark: " + remark + ". Thank you for your service.";

        // Simple notification message
        String notificationMessage = "Your resignation has been approved. Remark: " + remark + ".";


        // Send notification to the employee
        NotificationDTO notificationDTO = new NotificationDTO(
                "Resignation Request Approved",
                notificationMessage,
                "Resign",
                String.valueOf(EmpId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(EmpId, notificationDTO.getTitle(), notificationDTO.getBody(), "Resign", String.valueOf(LoggedInEmpId));
        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Resignation Request Approved");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);


        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }


    }


    @Override
    public void rejectResignation(Long resignationId,String remark) {
        try {
            Long empId = tokenService.getEmployeeIdFromToken();
            Optional<ResignationStatus> resignationStatusOpt = resignationStatusRepository.findById(resignationId);
            if (resignationStatusOpt.isPresent()) {
                ResignationStatus resignationStatus = resignationStatusOpt.get();
                if ("pending".equalsIgnoreCase(resignationStatus.getStatus())) {
                    resignationStatus.setStatus("rejected");
                    resignationStatus.setUpdatedBy(empId);
                    resignationStatus.setUpdatedTime(LocalDateTime.now());
                    resignationStatus.setRemark(remark);

                    resignationStatus=  resignationStatusRepository.save(resignationStatus);


                    notifyEmployeeOnReject(resignationStatus,remark);
                } else {
                    throw new IllegalStateException("Resignation is not in pending status");
                }
            } else {
                throw new NoSuchElementException("Resignation not found");
            }
        } catch (NoSuchElementException | IllegalStateException e) {
            logger.error("Error in rejecting resignation: ", e);
            throw new RuntimeException("Rejection failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error while rejecting resignation: ", e);
            throw new RuntimeException("An unexpected error occurred while rejecting the resignation.");
        }


    }


    private void notifyEmployeeOnReject(ResignationStatus resignationStatus,String remark) {

        Long LoggedInEmpId = tokenService.getEmployeeIdFromToken(); //
        // Retrieve employee information for notification and email

        Long empId = resignationStatus.getEmpId();
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));
        String empName = employee.getEmpName();


        String notificationMessage = "Your resignation request has been rejected.";
        String emailMessage = "Dear " + empName + ",\n\nYour resignation request has been rejected by management.";

        // Send notification to the employee
        NotificationDTO notificationDTO = new NotificationDTO(
                "Resignation Request Rejected",
                notificationMessage,
                "Resign",
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)

        );

        notificationService.sendNotificationToEmployee(empId, notificationDTO.getTitle(), notificationDTO.getBody(), "Resign", String.valueOf(LoggedInEmpId));

        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Resignation Request Rejected");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }

    }



    @Override
    public void cancelResignation(Long resignationId, ResignationStatusDTO resignationStatusDTO , String remark) {
        try {
            Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
            ResignationStatus resignationStatus = resignationStatusRepository.findById(resignationId)
                    .orElseThrow(() -> new NoSuchElementException("Resignation not found for ID: " + resignationId));

            Long empId = resignationStatus.getEmpId();
            String currentStatus = resignationStatus.getStatus();

            // Ensure only pending or approved resignations can be canceled
            if (!"pending".equalsIgnoreCase(currentStatus) && !"approved".equalsIgnoreCase(currentStatus)) {
                throw new IllegalStateException("Only pending or approved resignations can be canceled.");
            }

            // Ensure valid last working date
            if (resignationStatus.getLastWorkingDate() == null) {
                throw new IllegalStateException("Resignation last working date is not set.");
            }

            LocalDateTime lastWorkingDate = resignationStatus.getLastWorkingDate().atStartOfDay();
            LocalDateTime currentDateTime = LocalDateTime.now();
            long hoursDifference = Duration.between(currentDateTime, lastWorkingDate).toHours();

            if (hoursDifference <= 48) {
                throw new IllegalStateException("Resignation can only be canceled at least 48 hours before the last working date.");
            }


            if ("pending".equalsIgnoreCase(currentStatus) && loggedInEmpId.equals(empId)) {
                EmployeeTermination termination = new EmployeeTermination();
                termination.setEmpId(empId);
                termination.setTerminationType("Cancelled");
                termination.setTerminationReason(remark);
                termination.setLastWorkingDate(resignationStatus.getLastWorkingDate());
                termination.setNoticeStartDate(resignationStatus.getNoticeStartDate());
                termination.setCreatedBy(loggedInEmpId);
                termination.setCreatedTime(LocalDateTime.now());

                employeeTerminationRepository.save(termination);

                resignationStatus.setStatus("cancelled");
                resignationStatus.setRemark(remark);
                resignationStatus.setUpdatedBy(loggedInEmpId);
                resignationStatus.setUpdatedTime(LocalDateTime.now());

                resignationStatusRepository.save(resignationStatus);

                Long upperId = employeeRepository.findUpperIdByEmpId(empId);
                if (upperId != null && upperId > 0) {
                    sendNotificationToUpperId(upperId, resignationStatus);
                }

            }

            if (!loggedInEmpId.equals(empId) && ("pending".equalsIgnoreCase(currentStatus) || "approved".equalsIgnoreCase(currentStatus))) {
                resignationStatus.setStatus("cancelled");
                resignationStatus.setRemark(remark);
                resignationStatus.setUpdatedBy(loggedInEmpId);
                resignationStatus.setUpdatedTime(LocalDateTime.now());

                resignationStatusRepository.save(resignationStatus);

                sendNotificationToEmployee(empId, resignationStatus);
            }

            // Notify the employee about the cancellation
            //notifyEmployeeOnCancel(resignationStatus);


        } catch (NoSuchElementException | IllegalStateException e) {
            logger.error("Error in canceling resignation: ", e);
            throw new RuntimeException("Cancellation failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error while canceling resignation: ", e);
            throw new RuntimeException("An unexpected error occurred while canceling the resignation.");
        }




    }

    private void sendNotificationToUpperId(Long upperId, ResignationStatus resignationStatus) {

        Long empId = resignationStatus.getEmpId();
        Employee employee = employeeRepository.findById(upperId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));
        String empName = employee.getEmpName();

        //finding the employee name who canceling the  request

        Employee employee1 = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));
        String empName1 = employee1.getEmpName();


        String notificationMessage ="Resignation Update: Employee " + empName1 + " has withdrawn their resignation request.";
        String emailMessage ="Resignation Update: Employee " + empName1 + " has withdrawn their resignation request.";


        NotificationDTO notificationDTO = new NotificationDTO(
                "Resignation Request Cancelled",
                notificationMessage,
                "Resign",
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(
                upperId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "Resign",
                String.valueOf(empId)
        );

        // Send Email Notification
        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Resignation Request Cancelled");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);


        if (employee.getMobileNo() != null && !employee.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }



    }

    private void sendNotificationToEmployee(Long empId, ResignationStatus resignationStatus) {

        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();
        Employee employee = employeeRepository.findById(loggedInEmpId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));
        String empName = employee.getEmpName();

      // finding the email id to send the email to the employee
        Employee employee1 = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));
        String empName1 = employee.getEmpName();





        String notificationMessage = "Resignation Update: Your resignation request has been canceled by " + empName + ". Please contact HR for further details.";
        String emailMessage = "Dear Employee, your resignation request has been canceled by " + empName + ". If you have any questions, please reach out to HR for clarification.";

        NotificationDTO notificationDTO = new NotificationDTO(
                "Resignation Request Cancelled",
                notificationMessage,
                "Resign",
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(
                empId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "Resign",
                String.valueOf(loggedInEmpId)
        );
        // Send Email Notification
        String emailId = employee1.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Resignation Request Cancelled");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);

        if (employee1.getMobileNo() != null && !employee1.getMobileNo().isEmpty()) {
            logger.info("Sending SMS to: {}", employee1.getMobileNo());

            // Create an SMS request for a single recipient
            SingleSmsDataRequest smsRequest = new SingleSmsDataRequest();
            smsRequest.setMessage(notificationMessage);
            smsRequest.setEms("0");
            smsRequest.setUserref("");
            smsRequest.setTo(employee.getMobileNo());  // Set recipient phone number

            try {
                ResponseEntity<String> response = smsProcessingService.processSmsRequest(smsRequest);
                logger.info("SMS Response: {}", response.getBody());
            } catch (Exception e) {
                logger.error("Failed to send SMS: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("Employee phone number is missing. SMS not sent.");
        }


    }


    private void notifyEmployeeOnCancel(ResignationStatus resignationStatus) {
        Long loggedInEmpId = tokenService.getEmployeeIdFromToken();

        // Retrieve employee information
        Long empId = resignationStatus.getEmpId();
        Employee employee = employeeRepository.findById(empId)
                .orElseThrow(() -> new EntityNotFoundException("Employee not found for ID: " + empId));
        String empName = employee.getEmpName();

        // Construct the notification and email messages
        String notificationMessage = "Your resignation request has been successfully cancelled.";
        String emailMessage = "Dear " + empName + ",\n\nYour resignation request has been successfully cancelled.";


        // if i want to notification save t_notifiaction
//        Notification notification = new Notification(empId, "Resignation Cancelled", notificationMessage, "resignation");
//        notificationRepository.save(notification);

        NotificationDTO notificationDTO = new NotificationDTO(
                "Resignation Cancelled",
                notificationMessage,
                "resignation",
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        notificationService.sendNotificationToEmployee(
                empId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "resignation",
                String.valueOf(loggedInEmpId)
        );


        // Send Email Notification
        String emailId = employee.getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Resignation Cancelled");
        emailData.setMessage(emailMessage);
        emailService.sendEmail(emailData);


    }
}
