import React, { useRef } from 'react';
import { Card } from 'react-bootstrap';
import './Team.css'; 

const Team = () => {
  const sliderRef = useRef(null);

  const scrollLeft = () => {
    sliderRef.current.scrollBy({ left: -sliderRef.current.clientWidth, behavior: 'smooth' });
  };

  const scrollRight = () => {
    sliderRef.current.scrollBy({ left: sliderRef.current.clientWidth, behavior: 'smooth' });
  };

  const teamMembers = [...Array(5)].map((_, index) => (
    <div key={index} className="team-card">
      <Card className="h-100 shadow-sm">
        <Card.Body className="container">
          <div className="flex-grow-1">
            <div className="d-flex align-items-center">
              <img
                src='/image/avatar.png'
                alt="Avatar"
                className="mr-3"
                style={{ width: '40px', height: '40px', borderRadius: '50%' }}
              />
              <div>
                <p className="mb-1 font-weight-bold">Jon<PERSON></p>
                <p className="text-muted mb-1">Human Resource</p>
              </div>
            </div>
            <div className="mt-3">
              <p className="mb-1"><strong>Total Leaves:</strong> 30</p>
              <p className="mb-1"><strong>Pending Leaves:</strong> 10</p>
              <p className="mb-1"><strong>Overtime:</strong> 40 hours</p>
            </div>
            <div className="d-flex justify-content-start mt-3">
              <img src='/image/email.png' alt="Email" className="mr-3" />
              <img src='/image/phone.png' alt="Phone" />
            </div>
          </div>
          <div className="ml-3">
            <div className="pie-chart-team" data-percentage="90%">
              <div className="pie-chart-center-team">10</div>
            </div>
            <p className="mt-3 small font-weight-bold">Pending Leaves</p>
          </div>
        </Card.Body>
      </Card>
    </div>
  ));

  return (
    <div className="team-container">
      <div className="row">
        <div className="col-12 mb-3">
          <div className="d-flex justify-content-between align-items-center">
            <div className="pl-3 pl-md-5 font-size-lg">
              <h4 className='team-text'>Your Team (Total: 735)</h4>
            </div>
            <div className="slider-buttons">
              <img src='/image/rightArrow.png' alt="Scroll Left" className="slider-button left" onClick={scrollLeft} />
              <img src='/image/leftArrow.png' alt="Scroll Right" className="slider-button right" onClick={scrollRight} />
            </div>
          </div>
          <div className="slider-container">
            <div className="slider" ref={sliderRef}>
              {teamMembers}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Team;
