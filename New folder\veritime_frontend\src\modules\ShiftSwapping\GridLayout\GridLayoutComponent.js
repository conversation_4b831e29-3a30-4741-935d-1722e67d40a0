import React, { useEffect, useState } from 'react';
import './GridLayout.css';
import Swal from 'sweetalert2';
import { postRequest } from '@/services/apiService';
import { API_URLS } from '@/constants/apiConstants';
import moment from 'moment';

const GridLayoutComponent = ({ shiftTimings, empList, onDateChange }) => {
  
  const [layout, setLayout] = useState(generateLayout());

  const maxEmployeesPerShift = Math.max(
    ...shiftTimings.map(shift => empList.filter(emp => emp.actualShiftId === shift.shiftId || emp.modifiedShiftId === shift.shiftId).length)
  );

  function generateLayout() {
    const layout = [];
    shiftTimings.forEach((shift) => {
      const employeesInShift = empList.filter(emp => {
        if (emp.modifiedShift !== "-") {
          return emp.modifiedShiftId === shift.shiftId;
        }
        return emp.actualShiftId === shift.shiftId;
      });
      layout.push({
        shiftId: shift.shiftId,
        timing: shift.timing,
        employees: employeesInShift
      });
    });
    return layout;
  }

  useEffect(() => {
    setLayout(generateLayout());
  }, [empList, shiftTimings]);  

  const handleDragStart = (e, emp, shiftId) => {
    e.dataTransfer.setData('emp', JSON.stringify({ emp, shiftId }));
  };

  const handleDragOver = (e) => {
    e.preventDefault(); 
  };

  const handleDrop = (e, targetShiftId, targetEmp = null) => {
    e.preventDefault(); 
    const droppedData = JSON.parse(e.dataTransfer.getData('emp'));
    const { emp: draggedEmp, shiftId: draggedShiftId } = droppedData;

    Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to modify ${draggedEmp.name}'s shift to ${targetEmp ? targetEmp.name : 'an empty slot'}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, modify!',
      cancelButtonText: 'No, cancel!'
    }).then((result) => {
      if (result.isConfirmed) {
        const swapData = {
          fromShiftId: draggedShiftId,
          toShiftId: targetShiftId,
          fromEmpId: draggedEmp.empId,
          toEmpId: targetEmp ? targetEmp.empId : null,
          date: moment(onDateChange).format("YYYY-MM-DD")
        };

        postRequest(API_URLS.SHIFT_SWAP, swapData).then(data => {
          if (data === 'Shift modified successfully!') {
            Swal.fire({
              title: 'Modified!',
              text: `${draggedEmp.name}'s shift has been modified successfully.`,
              icon: 'success',
            });
          } else {
            Swal.fire('Error', 'Failed to modify shifts.', 'error');
          }
        }).catch(error => {
          Swal.fire('Error', 'Failed to modify shifts.', 'error');
        });
      }
    });
  };

  return (
    <div className="grid-layout-container">
      <div className="grid-header">
        {shiftTimings.map((shift, index) => (
          <div key={`shift-${index}`} className="header-cell grid-cell">
            <div className="header-timing">
              {shift.timing} <br />
            </div>
          </div>
        ))}
      </div>
      <div className="grid-content">
        {empList.length > 0 && layout.map((shift, shiftIndex) => (
          <div key={`shift-col-${shiftIndex}`} className="shift-column">
            {[...Array(maxEmployeesPerShift)].map((_, slotIndex) => {
              const emp = shift.employees[slotIndex];
              if (emp && emp.name) {
                emp.name = emp.name.replace(/undefined/g, " ");
              }
              return emp ? (
                <div
                  key={emp.empId + slotIndex}
                  className="grid-cell1 filled"
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, shift.shiftId, emp)}
                >
                  <div
                    className="draggable-item"
                    draggable
                    onDragStart={(e) => handleDragStart(e, emp, shift.shiftId)}
                  >
                    <img style={{ height: 20, borderRadius: 50 }} src={emp.profilePic ?? '/image/profileuser.png'} alt="Person"
                      onError={(e) => { e.target.src = '/image/profileuser.png'; }}  />
                    {emp.name.length > 10 ? `${emp.name.substring(0, 10)}...` : emp.name}<br />
                    {emp.department.length > 10 ? `${emp.department.substring(0, 10)}...` : emp.department}
                    {emp.modifiedShift !== "-" ? (
                      <>
                        <div>
                          <div style={{ textDecoration: 'line-through' }}>
                            {emp.actualShift}
                          </div>
                          <div>
                            <strong>{emp.modifiedShift}</strong>
                          </div>
                        </div>
                      </>
                    ) : (
                      <div>
                        {emp.actualShift}<br />
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div
                  key={`empty-slot-${slotIndex}`}
                  className="grid-cell1 empty"
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, shift.shiftId)}
                />
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
};

export default GridLayoutComponent;
