package com.company.wfm.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.company.wfm.dto.EmailData;
import com.company.wfm.dto.JwtAuthResponse;
import com.company.wfm.dto.LoginDto;
import com.company.wfm.dto.NotificationDTO;
import com.company.wfm.entity.User;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.UserRepository;
import com.company.wfm.security.JwtTokenProvider;
import com.company.wfm.service.AuthService;
import com.company.wfm.service.CommonNotificationService;
import com.company.wfm.service.EmailService;
import com.company.wfm.service.LogHistoryService;

@Component
@DependsOn("appConfigurations")
public class AuthServiceImpl implements AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private UserService userService;

    @Autowired
    private LogHistoryService logHistoryService;




    @Autowired
    private CommonNotificationService notificationService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Value("${account.max.failed.attempt}")
    public String max_failed_attempts;

    @Override
    public JwtAuthResponse login(LoginDto loginDto) {

    	Authentication authentication = null;

        User user = userRepository.findByUsername(loginDto.getUsername());
        if (userService.isUserLocked(user)) {
            throw new LockedException("Your account is locked due to multiple failed login attempts. Please try again later.");
        }

        // Default values for status and remark
        String status = "success";
        String remark = "Login successful";
        // Assuming you have a method to retrieve the employee ID or user ID.
        Long empId = user.getEmployee() != null ? user.getEmployee().getEmpId() : null;
        Long logHistoryId;
        try {
        	authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginDto.getUsername(), loginDto.getPassword())
            );
        	userService.resetFailedAttempts(user); // Reset failed attempts on successful login
		} catch (BadCredentialsException e) {
            status = "failed";
            remark = e.getMessage(); // Use exception message as remark

			 userService.increaseFailedAttempts(user); // Increase failed attempts on incorrect login
	            if (user.getFailedAttempts() >= Integer.parseInt(max_failed_attempts)) {
	                userService.lockUser(user); // Lock user after max failed attempts

                      //mail sending work and notification
                    sendAccountLockAlerts(user);

	                throw new BadCredentialsException("Your account has been locked for 30 minutes due to multiple failed login attempts.");
	            }
            // Log the failed login attempt
            logHistoryId = logHistoryService.saveLoginLog(empId, status, remark, null, null);

	            throw e; // Re-throw exception
		}

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String token = jwtTokenProvider.generateToken(authentication);

        JwtAuthResponse jwtAuthResponse = new JwtAuthResponse();
        jwtAuthResponse.setAccessToken(token);
        jwtAuthResponse.setRole(user.getRole());


        if (user.getEmployee() != null) {
            jwtAuthResponse.setEmployeeName(user.getEmployee().getEmpName());
        } else {
            jwtAuthResponse.setEmployeeName("N/A");
        }

        // Log the successful login attempt
        logHistoryId = logHistoryService.saveLoginLog(empId, status, remark, null, null);
        jwtAuthResponse.setLogHistoryId(logHistoryId);
        return jwtAuthResponse;
    }


    private void sendAccountLockAlerts(User user) {
        // Send account lock email
        Long empId=user.getEmployee().getEmpId();
        String emailId = employeeRepository.getReferenceById(empId).getEmail();
        EmailData emailData = new EmailData();
        emailData.setEmailId(emailId);
        emailData.setSubject("Account Locked Due to Failed Login Attempts");
        emailData.setMessage("Your account has been locked for 30 minutes due to multiple failed login attempts.");
        emailData.setHtml(true);
        emailService.sendEmail(emailData);

        String notificationTitle = "Account Locked Due to Failed Login Attempts";
        String notificationMessage = "Your account has been locked for 30 minutes due to multiple failed login attempts.";


        NotificationDTO notificationDTO = new NotificationDTO(
                notificationTitle,
                notificationMessage,
                "login",
                String.valueOf(empId),
                LocalDateTime.now().toInstant(ZoneOffset.UTC)
        );

        // Send the notification using notificationService
        notificationService.sendNotificationToEmployee(
                empId,
                notificationDTO.getTitle(),
                notificationDTO.getBody(),
                "login",
                String.valueOf(empId)
        );


    }

}
