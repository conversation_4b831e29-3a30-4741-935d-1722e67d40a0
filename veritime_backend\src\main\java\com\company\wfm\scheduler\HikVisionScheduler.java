package com.company.wfm.scheduler;

import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.env.Environment;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

import com.company.wfm.annotation.TrackExecutionTime;
import com.company.wfm.dto.EligibleProfilePictureEmpDTO;
import com.company.wfm.dto.EventCertificateRecordResponse;
import com.company.wfm.dto.EventCertificateRecordResponse.EventCertificateData;
import com.company.wfm.dto.PersonDTO;
import com.company.wfm.dto.PersonDetails;
import com.company.wfm.dto.PersonResponseDTO;
import com.company.wfm.dto.PersonResponseDTO.Data;
import com.company.wfm.dto.PhotoDTO;
import com.company.wfm.entity.EmployeeHikvisionData;
import com.company.wfm.entity.HikVisionErrorLog;
import com.company.wfm.repository.EmployeeHikvisionDataRepository;
import com.company.wfm.repository.HikvisionErrorLogRepository;
import com.company.wfm.util.CommonConstant;
import com.company.wfm.util.EncryptionUtil;
import com.company.wfm.vo.EmployeeVO;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

@Slf4j
@Component
@DependsOn("appConfigurations")
public class HikVisionScheduler {

	@Value("${hikvision.base.url}")
	private String baseUrl;

	@Value("${hikvision.language.code}")
	private String languageCode;

	@Value("${hikvision.access.id}")
	private String accessId;

	@Value("${hikvision.app.key}")
	private String appKey;

	@Value("${hikvision.secret.key}")
	private String secretKey;

	@Autowired
	private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

	private final WebClient.Builder webClientBuilder;

	//private final String baseUrl1 = "https://time.veri-time.co.za/api/v1/hikvision"; // Temporary Basis till 8th March, 2025
	@Autowired
	private EmployeeHikvisionDataRepository hikVisionRepository;

	@Autowired
	private HikvisionErrorLogRepository errorLogRepository;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private Environment environment;

	@Value("${aws.s3.bucket.name}")
	private String bucketName;

	@Autowired
	private S3Client s3Client;

	@Autowired
	private EncryptionUtil encryptionUtil;

	DateTimeFormatter isoFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
	
	private  static final int DEFAULT_DAYS_TO_SYNC = 2;

	public HikVisionScheduler(WebClient.Builder webClientBuilder) {
		this.webClientBuilder = webClientBuilder;
	}
	
	@TrackExecutionTime
	@Scheduled(cron = "0 0 0/8 * * *")
	public void fetchAndInsertAttendance() {
		try {
			log.info("Inititating creation of profiles in hikvision for new employees on date {}", LocalDate.now());
			List<HikVisionErrorLog> errorLogList = new ArrayList<>();
			List<EmployeeHikvisionData> empProfiePicEmpList = new ArrayList<>();

			Data token = generateToken(appKey, secretKey);
			if (token == null) {
				log.error("Failed to generate token. Aborting profile creation push.");
				return;
			}

			namedParameterJdbcTemplate
					.query(CommonConstant.HikVisionQueryConstant.ELIGIBLE_EMP_FOR_HIKVISION_QUERY, (rs, rowNum) -> {
						return EmployeeVO.builder().empCode(rs.getString("EMP_CODE"))
								.empName(rs.getString("EMP_NAME"))
								.mobileNo(rs.getString("MOBILE_NO"))
								.email(rs.getString("EMAIL"))
								.gender(rs.getString("GENDER"))
								.branchId(rs.getLong("BRANCH_ID"))
								.uid(rs.getString("UID")).build();
					}).stream().forEach(person -> {
						try {

							log.info("Processing employee: {}", person);
							// Split the empName into first, middle, and last names
							String[] nameParts = person.getEmpName().split("\\s+");
							String firstName = nameParts[0]; // First part of the name
							String lastName = nameParts.length > 1 ? nameParts[nameParts.length - 1] : ""; // Last part of the name*/

							PersonDTO dto = PersonDTO.builder().id("").groupId("1").firstName(firstName)
									.lastName(lastName)
									.gender(person.getGender().equalsIgnoreCase("MALE") ? 1
											: 0)
									.personCode(person.getEmpCode())
									.phone(person.getMobileNo())
									.email(person.getEmail())
									.description("Created through WFM")
									.startDate(ZonedDateTime.now(ZoneId.systemDefault()).format(isoFormatter))
									.endDate(ZonedDateTime.now(ZoneId.systemDefault()).plusYears(3).format(isoFormatter))
									.build();

							PersonDetails personDetails = PersonDetails.builder()
									.personBaseInfo(dto).build();

							PersonResponseDTO personDetail = pushPersonProfileToHikVision(token, personDetails);
							if (ObjectUtils.isNotEmpty(personDetail)) {
								if (personDetail.getErrorCode().equals("0") || personDetail.getErrorCode().equals("CCF038024")) {
									EmployeeHikvisionData emp = new EmployeeHikvisionData();
									emp.setEmpCode(person.getEmpCode());
									emp.setPersonId(personDetail.getData().getPersonId());
									emp.setBranchId(String.valueOf(person.getBranchId()));
									empProfiePicEmpList.add(emp);

								} else {
									HikVisionErrorLog errorLog = new HikVisionErrorLog();
									errorLog.setEmpCode(person.getEmpCode());
									errorLog.setBranchId(person.getBranchId());
									errorLog.setApiName(
											CommonConstant.HikVisionURIConstant.HIKVISION_PROFILE_ADD_API);
									errorLog.setErrorCode(personDetail.getErrorCode());
									errorLog.setErrorDetail(environment.getProperty(personDetail.getErrorCode()));
									errorLogList.add(errorLog);
								}

							}
							Thread.sleep(2000);
						} catch (Exception e) {
							HikVisionErrorLog errorLog = new HikVisionErrorLog();
							errorLog.setEmpCode(person.getEmpCode());
							errorLog.setBranchId(person.getBranchId());
							errorLog.setApiName(CommonConstant.HikVisionURIConstant.HIKVISION_PROFILE_ADD_API);
							errorLog.setErrorCode("APP_HIKVISION_PROFILE_ADD");
							errorLog.setErrorDetail(e.getMessage());
							errorLogList.add(errorLog);
							log.error("Failed to push  profile details for employee code {} in hikvision device",
									person.getEmpCode(), e);
						}

					});

			Lists.partition(empProfiePicEmpList, 200).stream().forEach(empList->{
				hikVisionRepository.saveAllAndFlush(empList);
			});

			Lists.partition(errorLogList, 200).stream().forEach(empList->{
				errorLogRepository.saveAllAndFlush(empList);
			});
		} catch (Exception e) {
			log.error("Error fetching or inserting attendance records: {} ", e);
		}
	}

	private PersonResponseDTO pushPersonProfileToHikVision(Data token, PersonDetails detail) {

		WebClient webClientName = webClientBuilder
				.baseUrl(token.getAreaDomain()) // Set base URL dynamically
				.build();

		log.info("Payload {}",detail.getPersonBaseInfo().toString());
		log.info(token.toString());
		log.info("Access token {}", token.getAccessToken());
		Gson gson = encryptionUtil.createGson();
		String payload = flattenJson(detail, gson);
		log.info("JSON Data {}", payload);

		return webClientName.post().uri(CommonConstant.HikVisionURIConstant.HIKVISION_PROFILE_ADD_API)
				.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE).header("token", token.getAccessToken())
				.bodyValue(payload).retrieve()
				.onStatus(HttpStatusCode::is4xxClientError, response -> {
					// Handle client errors (4xx)
					return response.bodyToMono(PersonResponseDTO.class).flatMap(errorBody -> {
						log.error("Client error: {}", errorBody.getErrorCode());
						return Mono.error(new RuntimeException("Client error: " + errorBody));
					});
				}).onStatus(HttpStatusCode::is5xxServerError, response -> {
					// Handle server errors (5xx)
					return response.bodyToMono(PersonResponseDTO.class).flatMap(errorBody -> {
						log.error("Server error: {}", errorBody);
						return Mono.error(new RuntimeException("Server error: " + errorBody));
					});
				}).bodyToMono(PersonResponseDTO.class).doOnSuccess(response -> {
					if(!StringUtils.isBlank(response.getErrorCode())) {
						log.info("Response received with a error code: {}", response.getErrorCode());
					} else {
						log.info("Response received with a new personId created: {}", response.getData());
					}
				}).doOnError(error -> {
					log.error("An error occurred: {}", error.getMessage());
				}).block();
	}

	public static String flattenJson(Object wrapperObject, Gson gson) {
		JsonObject jsonObject = JsonParser.parseString(gson.toJson(wrapperObject)).getAsJsonObject();

		// If there's only one key (e.g., "personBaseInfo"), extract its value
		if (jsonObject.entrySet().size() == 1) {
			return gson.toJson(jsonObject.entrySet().iterator().next().getValue());
		}

		return gson.toJson(jsonObject); // If multiple fields, return as-is
	}

	public Data generateToken(String appKey, String secretKey) {

		try {

			WebClient webClientName = webClientBuilder
					.baseUrl(baseUrl) // Set base URL dynamically
					.build();

			String jsonInputString = "{ \"appKey\": \"" + appKey + "\", \"secretKey\": \"" + secretKey + "\" }";
			PersonResponseDTO hikVisionTokenResponse = webClientName.post().uri(CommonConstant.HikVisionURIConstant.HIKVISION_TOKEN_API)
					.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
					.bodyValue(jsonInputString).retrieve()
					.onStatus(HttpStatusCode::is4xxClientError, response -> {
						// Handle client errors (4xx)
						return response.bodyToMono(PersonResponseDTO.class)
								.flatMap(errorBody -> {
									log.error("Client error 4xx series: {}", errorBody);
									return Mono.error(new RuntimeException("Client error: " + errorBody));
								});
					})
					.onStatus(HttpStatusCode::is5xxServerError, response -> {
						// Handle server errors (5xx)
						return response.bodyToMono(PersonResponseDTO.class)
								.flatMap(errorBody -> {
									log.error("Server error 5xx series: {}" , errorBody);
									return Mono.error(new RuntimeException("Server error: " + errorBody));
								});
					})
					.bodyToMono(PersonResponseDTO.class)
					.doOnSuccess(response -> {
						log.info("Token generated successfully");
					})
					.doOnError(error -> {
						log.error("API throw an error: " + error.getMessage());
					})
					.block();
			log.info("Access token {}", hikVisionTokenResponse.getData().getAccessToken());
			return null != hikVisionTokenResponse ? hikVisionTokenResponse.getData() : null;
		} catch (Exception e) {
			log.error("Failed to execute request {}",e);
			return null; // Return null in case of an exception
		}

	}
	@TrackExecutionTime
	@Scheduled(cron = "0 0 0/8 * * *")
	public void uploadProfilePictureToHikVision() {

		List<HikVisionErrorLog> errorLogList = new ArrayList<>();
		List<EmployeeHikvisionData> empProfiePicEmpList = new ArrayList<>();

		try {
			Data token = generateToken(appKey, secretKey);
			if (token == null) {
				log.error("Failed to generate token.");
				return;
			}

			namedParameterJdbcTemplate.query(
					CommonConstant.HikVisionQueryConstant.ELIGIBLE_EMP_FOR_PROFILE_HIKVISION_QUERY, (rs, rowNum) -> {
						return EligibleProfilePictureEmpDTO.builder()
								.empCode(rs.getString("EMP_CODE"))
								.id(rs.getLong("id"))
								.branchId(rs.getLong("BRANCH_ID"))
								.imageUriKey(rs.getString("IMG_URE"))
								.personId(rs.getString("person_id"))
								.build();
					}).stream().forEach(personPic -> {
				try {
					PhotoDTO profilePic = PhotoDTO.builder().personId(personPic.getPersonId())
							.photoData(getImageAsBase64(personPic.getImageUriKey())).build();
					log.info("Profile pic data sent on hikvision:{}", profilePic);
					PersonResponseDTO response = pushPersonProfilePictureToHikVision(token, profilePic);
					if (ObjectUtils.isNotEmpty(response)) {

						if (response.getErrorCode().equals("0")) {
							EmployeeHikvisionData emp = new EmployeeHikvisionData();
							emp.setId(personPic.getId());
							emp.setImageMapped(1);
							emp.setEmpCode(personPic.getEmpCode());
							emp.setBranchId(String.valueOf(personPic.getBranchId()));
							emp.setPersonId(personPic.getPersonId());
							empProfiePicEmpList.add(emp);
						} else {

							HikVisionErrorLog errorLog = new HikVisionErrorLog();
							errorLog.setEmpCode(personPic.getEmpCode());
							errorLog.setBranchId(personPic.getBranchId());
							errorLog.setApiName(
									CommonConstant.HikVisionURIConstant.HIKVISION_PROFILE_PIC_API);
							errorLog.setErrorCode(response.getErrorCode());
							errorLog.setErrorDetail(environment.getProperty(response.getErrorCode()));
							errorLogList.add(errorLog);
						}

					}
				} catch (Exception e) {
					HikVisionErrorLog errorLog = new HikVisionErrorLog();
					errorLog.setEmpCode(personPic.getEmpCode());
					errorLog.setBranchId(personPic.getBranchId());
					errorLog.setApiName(CommonConstant.HikVisionURIConstant.HIKVISION_PROFILE_PIC_API);
					errorLog.setErrorCode("APP_HIKVISION_PROFILE_PIC_UPLOAD");
					errorLog.setErrorDetail(e.getMessage());
					errorLogList.add(errorLog);

					log.error("Failed to push  profile details for employee code {} in hikvision device",
							personPic.getEmpCode(), e);
				}
			});

			Lists.partition(empProfiePicEmpList, 200).stream().forEach(empList->{
				hikVisionRepository.saveAllAndFlush(empList);
			});

			log.info("Updating the tables for hikvision log error", errorLogList);
			Lists.partition(errorLogList, 200).stream().forEach(empList->{
				errorLogRepository.saveAllAndFlush(empList);
			});

			log.info("Inititating attendance data pull operation for empoyess from hikvision device on date {}",
					LocalDate.now());
		} catch (Exception e) {
			log.error("Failed to sync attendance data request {}", e);
		}
	}
	
	@TrackExecutionTime
	@Scheduled(cron = "0 0 0/1 * * *")
	public void fetchAndPrintEventCertificateRecords() {
		String endTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
		try {	        
			Data token = generateToken(appKey, secretKey);
			if (token == null) {
				log.error("Failed to generate token.");
				return;
			}
			
			log.info("Initiating fetching event certificate records from HikVision on date {}", LocalDate.now());
			String beginTime = Optional.ofNullable(getSyncedDateFromConfig()).filter(s -> !s.trim().isEmpty())
					.orElseGet(() -> {
						log.warn("No synced date found or empty value, using current date as beginTime.");
						return LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
					});
			try {
				LocalDateTime parsedBeginTime = LocalDateTime.parse(beginTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
				LocalDateTime calculatedEndTime = parsedBeginTime.plusDays(getSyncedDaysFromConfig());
				endTime = (calculatedEndTime.isAfter(LocalDateTime.now()) ? LocalDateTime.now() : calculatedEndTime)
						.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
				
			} catch (DateTimeParseException e) {
				log.error("Failed to parse beginTime: {}", beginTime, e);
				return;
			}
			log.info("begin time:" + beginTime + " " + endTime);
			processRecords(beginTime, endTime, token);
			log.info("Updating the existing syncing time to new sync time: {}", endTime);
		} catch (Exception e) {
			log.error("Failed to fetch event certificate records: {}", e.getMessage(), e);
		} finally {
			updateSyncedDateInConfig(endTime);
		}
	}

	private void processRecords(String beginTime, String endTime, Data token) throws Exception {
		String requestBody = getRequestBody(1, beginTime, endTime);
		log.info("request body:" + requestBody);
		EventCertificateRecordResponse response = getAttendanceAuditData(token, requestBody);

		if (response == null || response.getData() == null) {
			log.warn("No data found in the response or response is null.");
			return;
		}
		log.info("response from hikvision:" + response);
		EventCertificateData data = response.getData();
		int totalRecords = data.getTotalNum();
		log.info("Total {} scanned data records for given date range from Hikvision.", totalRecords);

		if (totalRecords == 0) {
			log.info("No scanned data present for date range {} and {}", beginTime, endTime);
			return;
		}
		int batchSize = (int) Math.ceil((double) totalRecords / data.getPageSize());
		int actualProcessedBatchSize = 0;
		int actualProcessedRecordCount = 0;
		for (int pageCounter = 1; pageCounter <= batchSize; pageCounter++) {
			try {
				requestBody = getRequestBody(pageCounter, beginTime, endTime);
				response = getAttendanceAuditData(token, requestBody);
				if (response != null && response.getData() != null && response.getData().getRecordList() != null) {
					actualProcessedRecordCount += insertAttendanceLogIntoTable(response.getData().getRecordList());
				}
				actualProcessedBatchSize++;
			} catch (Exception e) {
				log.error("Failed to process batch number {} having total {} records for startTime {} and endTime {}",
						pageCounter, 200, beginTime, endTime);
			}
		}
		log.info("Total {} of records processed in {} batchs out of grand batch total {}", actualProcessedRecordCount,
				actualProcessedBatchSize, batchSize, beginTime, endTime);

	}

	private String getRequestBody(int pageIndex, String beginTime, String endTime) {
		return """
	        { 
	        "pageIndex": %d,
	        "pageSize": 200,
	        "searchCriteria": {
	            "personCondition": {
	                "personName": ""
	            },
	            "beginTime": "%s",
	            "endTime": "%s",
	            "swipeAuthResult":1
	        }
	        }
	    """.formatted(pageIndex, beginTime, endTime);
	}


	/**
	 * This will fetch the details of scan date and time
	 * @param token
	 * @param requestBody
	 * @return
	 */
	private EventCertificateRecordResponse getAttendanceAuditData(Data token, String requestBody) {
		// Send the HTTP request to the HikVision API
		WebClient webClient = webClientBuilder.baseUrl(token.getAreaDomain())
				.exchangeStrategies(ExchangeStrategies.builder()
						.codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(50 * 1024 * 1024)) // 50MB
						.build())
				.build();

		return webClient.post()
				.uri(CommonConstant.HikVisionURIConstant.HIKVISION_EMPLOYEE_CERTIFICATERECORDS_SEARCH_API)
				.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
				.header("Token", token.getAccessToken()).bodyValue(requestBody).retrieve()
				.onStatus(HttpStatusCode::is4xxClientError, clientResponse -> {
					log.error("Client error1: {}", clientResponse.statusCode());
					return clientResponse.createException();
				}).onStatus(HttpStatusCode::is5xxServerError, serverResponse -> {
					log.error("Server error1: {}", serverResponse.statusCode());
					return serverResponse.createException();
				}).bodyToMono(EventCertificateRecordResponse.class)
				.doOnSuccess(res -> log.info("Successfully fetched event certificate records."))
				.doOnError(error -> log.error("An error occurred while fetching records: {}", error.getMessage()))
				.block();
	}

	private int insertAttendanceLogIntoTable(List<EventCertificateRecordResponse.EventRecord> records) throws Exception {

		List<Object[]> batchArgs = new ArrayList<>();
		int recordProcessedCount = 0;
		for (EventCertificateRecordResponse.EventRecord record : records) {
			String personCode = record.getPersonInfo().getBaseInfo().getPersonCode();
			if (personCode == null || personCode.isEmpty()) {
				continue;
			}
			String empId = getEmployeeIdByPersonCode(personCode);

			if (empId == null) {
				log.warn("Skipping record for personCode: {}", personCode);
				continue;
			}

			try {
				String occurTime = record.getDeviceTime();
				ZonedDateTime zonedDateTime = ZonedDateTime.parse(occurTime, DateTimeFormatter.ISO_DATE_TIME);
				LocalDateTime localDateTime = zonedDateTime.toLocalDateTime();
				
				Timestamp punchInDateTime = Timestamp.valueOf(localDateTime);
				Time punchInTime = new Time(punchInDateTime.getTime());
				LocalDate punchInDate = punchInDateTime.toLocalDateTime().toLocalDate();
				String deviceDump = record.toString();

				batchArgs.add(new Object[]{
						empId,
						punchInTime,
						LocalDateTime.now(),
						deviceDump,
						punchInDate
				});
				recordProcessedCount++;
			} catch (IllegalArgumentException e) {
				log.error("Invalid timestamp format for record with personCode {}: {}", personCode, record.getOccurTime());
			} catch (Exception e) {
				log.error("Error processing record for personCode {}: {}", personCode, e.getMessage());
			}
		}

		if (!batchArgs.isEmpty()) {
			jdbcTemplate.batchUpdate(CommonConstant.HikVisionQueryConstant.MERGE_QUERY_ATTENDANCE_AUDIT, batchArgs);
			log.info("Inserted {} records into t_attendance_audit", batchArgs.size());
		}
		return recordProcessedCount;
	}


	private String getEmployeeIdByPersonCode(String personCode) {
		String sql = "SELECT EMP_ID FROM t_employee WHERE EMP_CODE = ?";
		try {
			return jdbcTemplate.queryForObject(sql, String.class, personCode);
		} catch (EmptyResultDataAccessException e) {
			log.error("No employee found with person code: {}", personCode);
			return null; // Handle accordingly if no employee is found
		}
	}

	private String getSyncedDateFromConfig() {
		String sql = "SELECT config_value FROM t_app_configuration WHERE config_key = 'hikvision.synced.date'";
		try {
			String syncedDate = jdbcTemplate.queryForObject(sql, String.class);
			return syncedDate;
		} catch (EmptyResultDataAccessException e) {
			log.error("No synced date found in configuration. First time fetching.");
			return null;
		}
	}
	
	private int getSyncedDaysFromConfig() {
		String sql = "SELECT config_value FROM t_app_configuration WHERE config_key = 'hikvision.synced.days'";
		try {
			int syncedDate = jdbcTemplate.queryForObject(sql, Integer.class);
			return syncedDate;
		} catch (Exception e) {
			log.error("No synced day found in configuration. First time fetching.");
			return DEFAULT_DAYS_TO_SYNC;
		}
	}	
	

	// Helper method to update hikvision.synced.date in the configuration table after each fetch
	private void updateSyncedDateInConfig(String newEndTime) {
		String sql = "UPDATE t_app_configuration SET config_value = ? WHERE config_key = 'hikvision.synced.date'";
		jdbcTemplate.update(sql, newEndTime);
	}

	private PersonResponseDTO pushPersonProfilePictureToHikVision(Data token, PhotoDTO photoPayload) {
		WebClient webClientName = webClientBuilder
				.baseUrl(token.getAreaDomain()) // Set base URL dynamically
				.build();

		Gson gson = encryptionUtil.createGson();
		String payload = flattenJson(photoPayload, gson);
		log.info("payload="+payload);
		PersonResponseDTO result = webClientName.post().uri(CommonConstant.HikVisionURIConstant.HIKVISION_PROFILE_PIC_API)
				.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE).header("token", token.getAccessToken())
				.bodyValue(payload).retrieve().onStatus(HttpStatusCode::is4xxClientError, response -> {
					// Handle client errors (4xx)
					return response.bodyToMono(PersonResponseDTO.class).flatMap(errorBody -> {
						log.error("Client error: {}", errorBody.getErrorCode());
						return Mono.error(new RuntimeException("Client error: " + errorBody));
					});
				}).onStatus(HttpStatusCode::is5xxServerError, response -> {
					// Handle server errors (5xx)
					return response.bodyToMono(PersonResponseDTO.class).flatMap(errorBody -> {
						log.error("Server error: {}", errorBody);
						return Mono.error(new RuntimeException("Server error: " + errorBody));
					});
				}).bodyToMono(PersonResponseDTO.class).doOnSuccess(response -> {
					log.info("Response received with a new personId created: {}", response.getData());
				}).doOnError(error -> {
					log.error("An error occurred: {}", error.getMessage());
				}).block();
		return result;
	}

	/**
	 * Fetch an image file from S3 bucket and encode it in Base64 format.
	 *
	 * @param bucketName The name of the S3 bucket.
	 * @param key The key of the file in the S3 bucket.
	 * @return The Base64 encoded string of the file.
	 */
	public String getImageAsBase64(String key) {
		try {
			// Create a GetObjectRequest
			GetObjectRequest getObjectRequest = GetObjectRequest.builder()
					.bucket(bucketName)
					.key(key)
					.build();

			// Fetch the file as bytes
			ResponseBytes responseBytes = s3Client.getObjectAsBytes(getObjectRequest);

			// Convert to Base64
			return Base64.getEncoder().encodeToString(responseBytes.asByteArray());
		} catch (S3Exception e) {
			throw new RuntimeException("Failed to fetch file from S3: " + e.awsErrorDetails().errorMessage(), e);
		}
	}

//	@Scheduled(fixedRate = 3600000)
//	public void fetchScannedData() {
//		WebClient webClient = webClientBuilder.baseUrl(baseUrl1).build();
//
//		Mono<String> responseMono = webClient.post()
//				.uri("/getScannedData")
//				.header(HttpHeaders.CONTENT_TYPE, "application/json")
//				.header(HttpHeaders.COOKIE, "JSESSIONID=17346F08DA455BC13B683AB38A0E00B3")
//				.retrieve()
//				.bodyToMono(String.class);
//		System.out.println("The schedular is working properly");
//		responseMono.subscribe(response -> System.out.println("API Response: " + response),
//				error -> System.err.println("API Error: " + error.getMessage()));
//		System.out.println("The schedular1 is working properly");
//	}


}

