import React, { useState } from 'react';
//import 'bootstrap/dist/css/bootstrap.min.css';
import '../TicketRaise/Attachments.css';

const Attachments = ({ onFileChange }) => {
  const [fileNames, setFileNames] = useState('No Files Selected');

  const handleFileChange = (e) => {
    const files = e.target.files; 
    if (files.length > 0) {
      const names = Array.from(files).map((file) => file.name); 
      setFileNames(names.join(', '));
      onFileChange(files); 
    } else {
      setFileNames('No Files Selected');
      onFileChange(null);
    }
  };

  return (
    <div className="container mt-3 custom-container-attachment">
      <div className="form-group">
        <label htmlFor="fileUpload" className="ticket-text-primary">Attachments</label>
        <div className="custom-file-input-container">
          <label className="custom-file-label" htmlFor="fileUpload">
            Select Files
          </label>
          <span className="separator">|</span>
          <input
            type="file"
            className="custom-file-input"
            id="fileUpload"
            multiple 
            accept=".pdf,.txt,.doc,.docx,.jpg,.jpeg,.png,.gif" 
            onChange={handleFileChange} 
          />
          <span className="file-selected">{fileNames}</span>
        </div>
      </div>
    </div>
  );
};

export default Attachments;
