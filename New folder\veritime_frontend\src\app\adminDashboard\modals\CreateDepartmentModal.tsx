import React, { useState, useEffect } from "react";
import { Modal, Button, Form, Dropdown } from "react-bootstrap";
import { postRequest, putRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showSuccessAlert2 } from "@/services/alertService";
import "../modals/CreateBranchModal.css"


const CreateDepartmentModal = ({
  show,
  handleClose,
  rowToEdit,
}: any) => {
  const [formData, setFormData] = useState<any>({
    departmentName: "",
    departmentCode: "",
    category: "",
  });

  const categories = [
    "Administration",
    "Allied Medical Services",
    "Internal Medicine",
    "Other",
  ];

  const [errors, setErrors] = useState<any>({});
  const [branches, setBranches] = useState<any>([]);
  const [selectedBranch, setSelectedBranch] = useState<number[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(""); 
  const [filteredBranches, setFilteredBranchesm] = useState<any>([]);


  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const payload = { type: "select" };
        const branchesData = await postRequest(API_URLS.GET_BRANCH, payload);
        setBranches(branchesData || []);
      } catch (error) {
      }
    };

    fetchBranches();
  }, []);

  useEffect(()=>{
    try {
      const filterBranches = branches?.filter((branch: any) =>
        branch.branchName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredBranchesm(filterBranches)
    } catch (error) {
    }

  })

  useEffect(() => {
    if (rowToEdit) {
      setFormData({
        departmentName: rowToEdit.departmentName,
        departmentCode: rowToEdit.departmentCode,
        category: rowToEdit.category || "",
      });
      setSelectedBranch(rowToEdit.branchIds || []);
    }
  }, [rowToEdit]);

  const handleBranchChange = (id: number) => {
    setSelectedBranch((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((branchId) => branchId !== id)
        : [...prevSelected, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedBranch.length === branches.length) {
      setSelectedBranch([]);
    } else {
      setSelectedBranch(branches.map((branch: any) => branch.branchId));
    }
  };

  

  const validateForm = () => {
    const newErrors: any = {};
    if (!formData.departmentName.trim()) {
      newErrors.departmentName = "Department Name is required";
    }
    if (!formData.departmentCode.trim()) {
      newErrors.departmentCode = "Department Code is required";
    }
    if (!formData.category) {
      newErrors.category = "Category is required";
    }
    // if (selectedBranch.length === 0) {
    //   newErrors.branchIds = "At least one branch must be selected";
    // }
    return newErrors;
  };

  const handleSave = async () => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    const postData = {
      departmentName: formData.departmentName,
      departmentCode: formData.departmentCode,
      branchIds: selectedBranch,
      category: formData.category,
    };

    try {
      let response;
      if (rowToEdit) {
        response = await putRequest(
          API_URLS.UPDATE_DEPARTMENT(rowToEdit.id),
          postData
        );
        showSuccessAlert2("Department updated successfully!");
      } else {
        response = await postRequest(API_URLS.CREATE_DEPARTMENT, postData);
        showSuccessAlert2("Department created successfully!");
      }

      if (response) {
        handleClose(response);
      }
    } catch (error) {
    }
  };

  return (
    <Modal show={show} onHide={handleClose}>
      <Modal.Header closeButton>
        <Modal.Title>
          {rowToEdit ? "Edit Department" : "Create Department"}
        </Modal.Title>
      </Modal.Header>
      <label htmlFor="ticketSubject" className="ticket-text-primary" style={{marginTop:'10px', marginLeft:'14px'}}>
                <span className="text-danger">The fields with * marks are mandatory</span>
          </label>
      <Modal.Body>
        <Form>
          <Form.Group className="mb-3" controlId="categorySelect">
            <Form.Label> <label htmlFor="ticketSubject" className="ticket-text-primary" style={{marginTop:'10px', marginLeft:'14px'}}>
            Category <span className="text-danger">*</span>
          </label></Form.Label>
            <Form.Control
              as="select"
              name="category"
              value={formData.category}
              onChange={(e) =>
                setFormData({ ...formData, category: e.target.value })
              }
              isInvalid={!!errors.category}
            >
              <option value="">Select a category</option>
              {categories.map((cat, index) => (
                <option key={index} value={cat}>
                  {cat}
                </option>
              ))}
            </Form.Control>
            <Form.Control.Feedback type="invalid">
              {errors.category}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3" controlId="departmentName">
            <Form.Label><label htmlFor="ticketSubject" className="ticket-text-primary" style={{marginTop:'10px', marginLeft:'14px'}}>
            Department Name<span className="text-danger">*</span>
          </label></Form.Label>
            <Form.Control
              type="text"
              placeholder="Department Name"
              value={formData.departmentName}
              onChange={(e) =>
                setFormData({ ...formData, departmentName: e.target.value })
              }
              isInvalid={!!errors.departmentName}
            />
            <Form.Control.Feedback type="invalid">
              {errors.departmentName}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3" controlId="departmentCode">
            <Form.Label><label htmlFor="ticketSubject" className="ticket-text-primary" style={{marginTop:'10px', marginLeft:'14px'}}>
            Department Code<span className="text-danger">*</span>
          </label></Form.Label>
            <Form.Control
              type="text"
              placeholder="Department Code"
              value={formData.departmentCode}
              onChange={(e) =>
                setFormData({ ...formData, departmentCode: e.target.value })
              }
              isInvalid={!!errors.departmentCode}
            />
            <Form.Control.Feedback type="invalid">
              {errors.departmentCode}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-3" controlId="department">
            <Form.Label><label htmlFor="ticketSubject" className="ticket-text-primary" style={{marginTop:'10px', marginLeft:'14px'}}>
            Facilities<span className="text-danger">*</span>
          </label></Form.Label>
            <Dropdown
              show={isDropdownOpen}
              onToggle={() => setIsDropdownOpen(!isDropdownOpen)}
              style={{ width: "100%" }}
            >
              <Dropdown.Toggle
                as={Button}
                variant="outline-secondary"
                style={{
                  width: "100%",
                  textAlign: "left",
                  overflow: "hidden",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                }}
              >
                {selectedBranch.length > 0
                  ? selectedBranch
                      .map(
                        (id) =>
                          branches.find((branch: any) => branch.branchId === id)
                            ?.branchName
                      )
                      .filter(Boolean)
                      .join(", ")
                  : "Select Facilities"}
              </Dropdown.Toggle>
              <Dropdown.Menu
                style={{ maxHeight: "200px", overflowY: "auto", width: "100%" }}
              >
                <Form.Control
                  type="text"
                  placeholder="Search Facilities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ width: "90%", margin: "10px auto" }}
                />
                <Form.Check
                  type="checkbox"
                  label="Select All"
                  onChange={handleSelectAll}
                  checked={selectedBranch.length === branches.length}
                />
                {filteredBranches.map((branch: any) => (
                  <Form.Check
                    type="checkbox"
                    key={branch.branchId}
                    label={branch.branchName}
                    checked={selectedBranch.includes(branch.branchId)}
                    onChange={() => handleBranchChange(branch.branchId)}
                  />
                ))}
              </Dropdown.Menu>
            </Dropdown>
            {errors.branchIds && (
              <Form.Control.Feedback
                type="invalid"
                style={{ display: "block" }}
              >
                {errors.branchIds}
              </Form.Control.Feedback>
            )}
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={handleSave}>
          {rowToEdit ? "Update" : "Create"}
        </Button>
        <Button variant="secondary" onClick={handleClose}>
          Cancel
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CreateDepartmentModal;
