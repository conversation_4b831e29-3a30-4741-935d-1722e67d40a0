// components/PendingActionCard.js
import React from 'react';
import { Card } from 'react-bootstrap';
import '../PendingAction/PendingAction.css';

const PendingActionCard = () => {
  return (
    <Card className="pending-action-card">
      <Card.Body className="d-flex align-items-center justify-content-between p-2">
        <div className="d-flex align-items-center w-100">
          <img
            src="/image/pending_Icon.png"
            alt="Pending Icon"
            className="me-2"
            style={{ width: '20px', height: '20px',marginLeft:"2px" }}
          />
          <span className="text-danger fw-bold">Pending Action <span className="bg-danger rounded-circle p-1 text-white fs-sm">21</span>
          </span>
        </div>
      </Card.Body>
    </Card>
  );
};

export default PendingActionCard;
