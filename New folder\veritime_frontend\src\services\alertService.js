import Swal from "sweetalert2";

export const showErrorAlert = (message) => {
  const alertContainer = document.getElementById("alert-container");
  if (alertContainer) {
    alertContainer.innerHTML = `
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    `;
    const closeButton = alertContainer.querySelector(".btn-close");
    closeButton.addEventListener("click", () => {
      alertContainer.innerHTML = "";
    });

    setTimeout(() => {
      alertContainer.innerHTML = "";
    }, 10000);
  }
};

export const showSuccessAlert = (message) => {
  const alertContainer = document.getElementById("alert-container");
  if (alertContainer) {
    alertContainer.innerHTML = `
      <div class="alert alert-primary alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    `;
    const closeButton = alertContainer.querySelector(".btn-close");
    closeButton.addEventListener("click", () => {
      alertContainer.innerHTML = "";
    });

    // Auto close after 10 seconds
    setTimeout(() => {
      alertContainer.innerHTML = "";
    }, 10000);
  }
};
export const showSuccessAlert2 = (message) => {
  const style = document.createElement("style");
  style.innerHTML = `
    .wide-button {
      min-width: 50px; 
 
    }
  `;
  document.head.appendChild(style);

  return Swal.fire({
    title: "Success!",
    text: message,
    icon: "success",
    confirmButtonText: "ok",
    customClass: {
      confirmButton: " btn btn-primary wide-button",
    },
    timer: 10000, // Auto close after 10 seconds
    buttonsStyling: false,
  });
};
