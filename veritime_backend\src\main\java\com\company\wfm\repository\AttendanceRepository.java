package com.company.wfm.repository;


import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.company.wfm.dto.AttendanceResponseDTO;
import com.company.wfm.entity.AttendanceEntity;
import com.company.wfm.entity.Employee;

@Repository
public interface AttendanceRepository extends JpaRepository<AttendanceEntity, Long> {

  @Query("SELECT a FROM AttendanceEntity a WHERE a.empId = :employee AND a.date = :date")
  Optional<AttendanceEntity> findByEmpIdAndDate(@Param("employee") Employee employee,
      @Param("date") LocalDate date);

 /* @Query("SELECT a FROM AttendanceEntity a WHERE a.empId = :empId AND a.date >= :fromDate and a.date <=:toDate")
  List<AttendanceEntity> findByEmpIdAndDateRange(@Param("empId") Long empId,
      @Param("fromDate") LocalDate fromDate, @Param("toDate") LocalDate toDate);
  */

 /*@Query("SELECT a FROM AttendanceEntity a WHERE a.empId.empId = :empId AND a.date >= :fromDate and a.date <= :toDate")
 List<AttendanceEntity> findByEmpIdAndDateRange(@Param("empId") Long empId,
                                                @Param("fromDate") LocalDate fromDate, @Param("toDate") LocalDate toDate);*/

 /* @Query("SELECT new com.company.wfm.dto.AttendanceResponseDTO(" +
          "a.id, " +
          "a.empId.id, " +
          "a.empId.empName, " +
          "a.checkInTime, " +
          "a.checkOutTime, " +
          "a.date, " +
          "a.multiDate, " +
          "a.modeType, " +
          "a.createdBy, " +
          "a.createdTime, " +
          "a.updatedBy, " +
          "a.updatedTime, " +
          "a.overtime, " + // Keeping the actual overtime value
          "CASE " +
          "  WHEN a.overtime IS NULL THEN '' " +  // If NULL, show empty string
          "  WHEN a.overtime = 0 THEN 'on Time' " +
          "  WHEN a.overtime < 0 THEN 'early' " +
          "  ELSE 'overtime' " +
          "END " +
          ") FROM AttendanceEntity a " +
          "WHERE a.empId.id = :empId AND a.date BETWEEN :fromDate AND :toDate")
  List<AttendanceResponseDTO> findByEmpIdAndDateRange(@Param("empId") Long empId,
                                                      @Param("fromDate") LocalDate fromDate,
                                                      @Param("toDate") LocalDate toDate);*/


 /* @Query("SELECT new com.company.wfm.dto.AttendanceResponseDTO(" +
          "a.id, " +
          "a.empId.id, " +
          "a.empId.empName, " +
          "a.checkInTime, " +
          "a.checkOutTime, " +
          "a.date, " +
          "a.multiDate, " +
          "a.modeType, " +
          "a.createdBy, " +
          "a.createdTime, " +
          "a.updatedBy, " +
          "a.updatedTime, " +
          "a.overtime, " +
          "CASE " +
          "  WHEN a.overtime IS NULL THEN '' " +
          "  WHEN a.overtime = 0 THEN 'on Time' " +
          "  WHEN a.overtime < 0 THEN 'early' " +
          "  ELSE 'overtime' " +
          "END, " +
          "a.actualShiftStartTime, " +   // <-- Add this
          "a.actualShiftEndTime" +       // <-- Add this
          ") " +
          "FROM AttendanceEntity a " +
          "WHERE a.empId.id = :empId AND a.date BETWEEN :fromDate AND :toDate")
  List<AttendanceResponseDTO> findByEmpIdAndDateRange(
          @Param("empId") Long empId,
          @Param("fromDate") LocalDate fromDate,
          @Param("toDate") LocalDate toDate);*/


  @Query("SELECT new com.company.wfm.dto.AttendanceResponseDTO(" +
          "a.id, " +
          "a.empId.id, " +
          "a.empId.empName, " +
          "a.checkInTime, " +
          "a.checkOutTime, " +
          "a.date, " +
          "a.multiDate, " +
          "a.modeType, " +
          "a.createdBy, " +
          "a.createdTime, " +
          "a.updatedBy, " +
          "a.updatedTime, " +
          "a.overtime, " +
          "CASE " +
          "  WHEN a.overtime IS NULL THEN '' " +
          "  WHEN a.overtime = 0 THEN 'on Time' " +
          "  WHEN a.overtime < 0 THEN 'early' " +
          "  ELSE 'overtime' " +
          "END, " +
          "a.actualShiftStartTime, " +
          "a.actualShiftEndTime" +
          ") " +
          "FROM AttendanceEntity a " +
          "WHERE a.empId.id = :empId " +
          "AND a.date BETWEEN :fromDate AND :toDate " +
          "AND a.createdTime = (" +
          "   SELECT MAX(sub.createdTime) " +
          "   FROM AttendanceEntity sub " +
          "   WHERE sub.empId.id = a.empId.id AND sub.date = a.date" +
          ")")
  List<AttendanceResponseDTO> findByEmpIdAndDateRange(
          @Param("empId") Long empId,
          @Param("fromDate") LocalDate fromDate,
          @Param("toDate") LocalDate toDate);




  @Query("SELECT a FROM AttendanceEntity a WHERE a.empId.empId in (:empIds) AND a.date >= :fromDate and a.date <=:toDate")
  List<AttendanceEntity> findByMultipleEmpIdAndDateRange(@Param("empIds") List<Long> empIds,
      @Param("fromDate") LocalDate fromDate, @Param("toDate") LocalDate toDate);

  @Query("SELECT a FROM AttendanceEntity a WHERE a.empId = :empId AND a.date = :date")
  Optional<AttendanceEntity> findByEmpIdAndDate(@Param("empId") Long empId, @Param("date") LocalDate date);


  @Query("SELECT a FROM AttendanceEntity a WHERE a.empId = :empId AND a.date BETWEEN :startDate AND :endDate")
  List<AttendanceEntity> findByEmpIdAndDateBetweenAndIsActive(
      @Param("empId") Long empId,
      @Param("startDate") LocalDate startDate,
      @Param("endDate") LocalDate endDate);


  @Query(value = "SELECT * FROM t_attendance WHERE emp_id = :empId AND date IN :dates AND is_active = 1",
          countQuery = "SELECT count(*) FROM t_attendance WHERE emp_id = :empId AND date IN :dates AND is_active = 1",
          nativeQuery = true)
  Page<AttendanceEntity> findByEmpIdAndDates(
          @Param("empId") Long empId,
          @Param("dates") List<LocalDate> dates,
          Pageable pageable
  );


  @Query("SELECT a FROM AttendanceEntity a WHERE a.empId.empId = :empId AND a.date = :date")
  Optional<AttendanceEntity> findByEmpIdAndDate1(@Param("empId") Long empId, @Param("date") LocalDate date);


}
