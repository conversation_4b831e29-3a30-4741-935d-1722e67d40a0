package com.company.wfm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.dto.ScheduleDTO;
import com.company.wfm.dto.ScheduleListDTO;
import com.company.wfm.entity.Schedule;
import com.company.wfm.service.impl.ScheduleService;
import com.company.wfm.vo.ScheduleVO;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/v1/schedule")
@CrossOrigin(origins = "*")
public class ScheduleController {

    @Autowired
    private ScheduleService scheduleService;

    @PostMapping("/create")
    public ResponseEntity<?> createSchedule(@RequestBody ScheduleDTO scheduleDTO) {
        try {
        	ScheduleVO schedule = scheduleService.createSchedule(scheduleDTO);
            return new ResponseEntity<>(schedule, HttpStatus.CREATED);
        } catch (Exception e) {
            log.error("Failed to create a schedule {}", e);
            return new ResponseEntity<>("Error creating schedule in branch:  " + "Schedule already exist", HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/edit")
    public ResponseEntity<?> editSchedule(@RequestBody ScheduleDTO scheduleDTO) {
        try {
        	ScheduleVO schedule = scheduleService.createSchedule(scheduleDTO);
            return new ResponseEntity<>(schedule, HttpStatus.CREATED);
        } catch (Exception e) {
            log.error("Failed to create a schedule {}", e);
            return new ResponseEntity<>("Error creating schedule in branch:  " + "Schedule already exist", HttpStatus.BAD_REQUEST);
        }
    }

    // Get all schedules
    @PostMapping("/list")
    public ResponseEntity<?> getAllSchedules( @RequestBody ScheduleListDTO scheduleListDto) {
        try {
            List<ScheduleVO> schedules = scheduleService.getAllSchedulesWithTimeSlots(scheduleListDto);
            return new ResponseEntity<>(schedules, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Failed to create a schedule {}", e);
            return new ResponseEntity<>("Failed to retrieve scheduled Data: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/getScheduledForBranch")
    public ResponseEntity<?> getSchedulesBasedOnBranch(@RequestParam("branchId") int branchId) {
        try {
            List<Schedule> schedules = scheduleService.getSchedulesForSelectedBranch(branchId);
            return new ResponseEntity<>(schedules, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Failed to create a schedule {}", e);
            return new ResponseEntity<>("Failed to retrieve scheduled Data: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/getScheduledForDepartment")
    public ResponseEntity<?> getSchedulesBasedOnDepartment(@RequestParam("departmentId") int departmentId) {
        try {
            List<Schedule> schedules = scheduleService.getSchedulesForSelectedDepartment(departmentId);
            return new ResponseEntity<>(schedules, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Failed to create a schedule {}", e);
            return new ResponseEntity<>("Failed to retrieve scheduled Data: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }


    //update schedule

    @PutMapping("/update/{id}")
    public ResponseEntity<?> updateSchedule(@PathVariable Long id, @RequestBody ScheduleDTO scheduleDTO) {
        try {
            ScheduleVO updatedSchedule = scheduleService.updateSchedule(id, scheduleDTO);
            return new ResponseEntity<>(updatedSchedule, HttpStatus.OK);
        } catch (EntityNotFoundException e) {
            log.error("Schedule not found: {}", e.getMessage());
            return new ResponseEntity<>("Schedule not found", HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("Failed to update schedule: {}", e);
            return new ResponseEntity<>("Error updating schedule Duplicate Are Not Allowed", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
