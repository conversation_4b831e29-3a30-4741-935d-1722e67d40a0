import React, { useState, useEffect, useRef } from 'react';
import './index.css';

function AnimatedModal({ isOpen, closeModal, children }) {
    const [showOverlay, setShowOverlay] = useState(false); 
    const [animateContent, setAnimateContent] = useState(false);
    const modalRef = useRef(null);

    useEffect(() => {
        if (isOpen) {
            setShowOverlay(true); 
            setTimeout(() => setAnimateContent(true), 100); 
        } else {
            setAnimateContent(false); 
            const handleTransitionEnd = () => setShowOverlay(false); 
            if (modalRef.current) {
                modalRef.current.addEventListener('transitionend', handleTransitionEnd, { once: true });
            }
        }
    }, [isOpen]);

    const handleClose = () => {
        setAnimateContent(false);
        const handleTransitionEnd = () => {
            setShowOverlay(false);
            closeModal(); 
        };
        if (modalRef.current) {
            modalRef.current.addEventListener('transitionend', handleTransitionEnd, { once: true });
        }
    };

    return (
        <div
            className="cldr_modal-overlay"
            style={{
                display: showOverlay ? 'flex' : 'none',
                opacity: showOverlay ? 1 : 0,
                transition: 'opacity 0.3s ease',
            }}
            onClick={handleClose}
        >
            <div
                ref={modalRef}
                className="cldr_modal"
                style={{
                    transform: animateContent ? 'translateX(0)' : 'translateX(100%)',
                    transition: 'transform 0.6s ease',
                }}
                onClick={(e) => e.stopPropagation()}
            >
                <div className="cldr_modal_content">{children}</div>
            </div>
        </div>
    );
}

export default AnimatedModal;
