// Debug script for Login request - Add this to pre-request script temporarily

const CryptoJS = require('crypto-js');

// Your credentials
const username = pm.environment.get('testUsername') || '<EMAIL>';
const password = pm.environment.get('testPassword') || 'raj456';
const aesPassword = pm.environment.get('aesPassword') || 'uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabqAU=';

console.log('=== DEBUG LOGIN REQUEST ===');
console.log('Username:', username);
console.log('Password:', password ? '[SET]' : '[NOT SET]');
console.log('AES Password:', aesPassword ? '[SET]' : '[NOT SET]');

// Create payload
const loginPayload = {
    username: username,
    password: password
};

const jsonString = JSON.stringify(loginPayload);
console.log('JSON Payload:', jsonString);

// Encrypt
const encryptedData = CryptoJS.AES.encrypt(jsonString, aesPassword).toString();
console.log('Encrypted Data:', encryptedData);
console.log('Encrypted Length:', encryptedData.length);

// Create request body
const requestBody = {
    encryptedData: encryptedData
};

// Set request body
pm.request.body.mode = 'raw';
pm.request.body.raw = JSON.stringify(requestBody, null, 2);

// Set headers
pm.request.headers.upsert({
    key: 'Content-Type',
    value: 'application/json'
});

console.log('Final Request Body:', JSON.stringify(requestBody, null, 2));
console.log('Request Body Length:', JSON.stringify(requestBody).length);

// Verify request setup
console.log('Request Method:', pm.request.method);
console.log('Request URL:', pm.request.url.toString());
console.log('Request Headers:', pm.request.headers.toObject());
console.log('Request Body Mode:', pm.request.body.mode);
console.log('Request Body Raw Length:', pm.request.body.raw ? pm.request.body.raw.length : 'NOT SET');

console.log('=== END DEBUG ===');
