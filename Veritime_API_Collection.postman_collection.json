{"info": {"name": "Veritime Workforce Management API", "description": "Comprehensive API collection for Veritime backend system with role-based access control, authentication, and complete CRUD operations for workforce management.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script for token management", "if (!pm.environment.get('accessToken') && pm.request.url.path.join('/') !== 'auth/login') {", "    console.log('No access token found. Please login first.');", "}", "", "// Set common headers", "pm.request.headers.add({", "    key: 'Content-Type',", "    value: 'application/json'", "});", "", "if (pm.environment.get('accessToken')) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: 'Bearer ' + pm.environment.get('accessToken')", "    });", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script for common validations", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid JSON', function () {", "    pm.response.to.have.jsonBody();", "});", "", "// Log response for debugging", "console.log('Response Status:', pm.response.status);", "console.log('Response Body:', pm.response.text());"]}}], "variable": [{"key": "baseUrl", "value": "https://www.xpertlyte.tech/api/v1", "type": "string"}, {"key": "baseUrl2", "value": "https://www.xpertlyte.tech/api", "type": "string"}], "item": [{"name": "🔐 Authentication", "description": "Authentication endpoints for login, token validation, and user session management", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Veritime Login - Web Crypto API Encryption (Exact Frontend Match)", "// This uses the exact same encryption as your frontend encryption.js", "", "// Your credentials", "const aesPassword = \"uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabqAU=\";", "const aesSalt = \"uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabBA==\";", "", "// Login credentials - UPDATE THESE", "const testPayload = {", "    username: \"<EMAIL>\",", "    password: \"raj456123\"", "};", "", "console.log('🔐 Starting Web Crypto API encryption (exact frontend match)...');", "", "// Helper functions", "const base64ToBytes = (base64) => {", "    try {", "        // Handle URL-safe base64 by converting to standard base64", "        let standardBase64 = base64.replace(/-/g, '+').replace(/_/g, '/');", "        ", "        // Add padding if needed", "        while (standardBase64.length % 4) {", "            standardBase64 += '=';", "        }", "        ", "        console.log('Converting base64:', base64, '->', standardBase64);", "        ", "        const binaryString = atob(standardBase64);", "        const bytes = new Uint8Array(binaryString.length);", "        for (let i = 0; i < binaryString.length; i++) {", "            bytes[i] = binaryString.charCodeAt(i);", "        }", "        return bytes;", "    } catch (error) {", "        console.error('Base64 decode error for:', base64, 'Error:', error.message);", "        throw error;", "    }", "};", "", "const bytesToBase64 = (bytes) => {", "    let binaryString = '';", "    for (let i = 0; i < bytes.length; i++) {", "        binaryString += String.fromCharCode(bytes[i]);", "    }", "    return btoa(binaryString);", "};", "", "const stringToBytes = (str) => new TextEncoder().encode(str);", "const bytesToString = (bytes) => new TextDecoder().decode(bytes);", "", "// Constants matching frontend", "const IV_LENGTH = 12;", "const ITERATIONS = 100000;", "const KEY_SIZE = 256;", "", "async function testWebCryptoEncryption() {", "    try {", "        console.log(\"=== WEB CRYPTO API TEST ===\");", "        console.log(\"Original payload:\", JSON.stringify(testPayload));", "        ", "        const jsonString = JSON.stringify(testPayload);", "        ", "        // Step 1: Derive key using PBKDF2", "        const passwordBytes = stringToBytes(aesPassword);", "        const saltBytes = base64ToBytes(aesSalt);", "        ", "        console.log(\"Password bytes length:\", passwordBytes.length);", "        console.log(\"Salt bytes length:\", saltBytes.length);", "        ", "        // Import password as key material", "        const keyMaterial = await crypto.subtle.importKey(", "            'raw',", "            passwordBytes,", "            { name: 'PBKDF2' },", "            false,", "            ['deriveBits', 'deriveKey']", "        );", "        ", "        // Derive the actual encryption key", "        const key = await crypto.subtle.deriveKey(", "            {", "                name: 'PBKDF2',", "                salt: saltBytes,", "                iterations: ITERATIONS,", "                hash: 'SHA-256',", "            },", "            keyMaterial,", "            { name: 'AES-GCM', length: KEY_SIZE },", "            false,", "            ['encrypt', 'decrypt']", "        );", "        ", "        console.log(\"✅ Key derived successfully\");", "        ", "        // Step 2: Generate random IV", "        const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));", "        console.log(\"Generated IV length:\", iv.length);", "        ", "        // Step 3: Encrypt data using AES-GCM", "        const encryptedData = await crypto.subtle.encrypt(", "            { name: 'AES-GCM', iv },", "            key,", "            stringToBytes(jsonString)", "        );", "        ", "        console.log(\"✅ Data encrypted with AES-GCM\");", "        ", "        // Step 4: Combine IV + encrypted data", "        const combinedArray = new Uint8Array(iv.length + encryptedData.byteLength);", "        combinedArray.set(iv);", "        combinedArray.set(new Uint8Array(encryptedData), iv.length);", "        ", "        // Step 5: Convert to base64", "        const encryptedBase64 = bytesToBase64(combinedArray);", "        ", "        console.log(\"Final encrypted data:\");", "        console.log(\"Length:\", encryptedBase64.length);", "        console.log(\"Data:\", encryptedBase64);", "        ", "        // Test decryption to verify", "        console.log(\"\\n=== TESTING DECRYPTION ===\");", "        ", "        // Decode the encrypted data", "        const encryptedBytes = base64ToBytes(encryptedBase64);", "        ", "        // Extract IV and encrypted data", "        const extractedIv = encryptedBytes.slice(0, IV_LENGTH);", "        const extractedEncryptedData = encryptedBytes.slice(IV_LENGTH);", "        ", "        console.log(\"Extracted IV length:\", extractedIv.length);", "        console.log(\"Extracted encrypted data length:\", extractedEncryptedData.length);", "        ", "        // Decrypt using AES-GCM", "        const decryptedData = await crypto.subtle.decrypt(", "            { name: 'AES-GCM', iv: extractedIv },", "            key,", "            extractedEncryptedData", "        );", "        ", "        // Convert to string and parse JSON", "        const decryptedText = bytesToString(new Uint8Array(decryptedData));", "        const decryptedJson = JSON.parse(decryptedText);", "        ", "        console.log(\"✅ Decryption successful:\");", "        console.log(\"Decrypted text:\", decryptedText);", "        console.log(\"Decrypted JSON:\", decryptedJson);", "        console.log(\"Round trip success:\", JSON.stringify(decryptedJson) === JSON.stringify(testPayload));", "        ", "        // Create request body", "        const requestBody = {", "            encryptedData: encryptedBase64", "        };", "        ", "        // Update Postman request", "        pm.request.body.mode = 'raw';", "        pm.request.body.raw = JSON.stringify(requestBody, null, 2);", "        ", "        pm.request.headers.upsert({", "            key: 'Content-Type',", "            value: 'application/json'", "        });", "        ", "        console.log(\"\\n✅ Postman request updated with Web Crypto encrypted payload\");", "        console.log(\"Request body:\", JSON.stringify(requestBody, null, 2));", "        ", "        return encryptedBase64;", "        ", "    } catch (error) {", "        console.error(\"❌ Web Crypto encryption failed:\", error);", "        throw error;", "    }", "}", "", "// Execute the test with fallback", "testWebCryptoEncryption().then(result => {", "    console.log(\"\\n🎉 Web Crypto API encryption completed successfully!\");", "    console.log(\"✅ Using exact frontend encryption format\");", "}).catch(error => {", "    console.error(\"\\n❌ Web Crypto API failed:\", error.message);", "    console.log(\"🔄 Falling back to CryptoJS with PBKDF2...\");", "    ", "    // Fallback to CryptoJS with PBKDF2 (synchronous)", "    try {", "        const CryptoJS = require('crypto-js');", "        ", "        const jsonString = JSON.stringify(testPayload);", "        console.log('Fallback payload:', jsonString);", "        ", "        // Convert base64 credentials to WordArrays", "        const passwordWordArray = CryptoJS.enc.Base64.parse(aesPassword);", "        const saltWordArray = CryptoJS.enc.Base64.parse(aesSalt);", "        ", "        // Derive key using PBKDF2 (matching backend)", "        const key = CryptoJS.PBKDF2(passwordWordArray, saltWordArray, {", "            keySize: 256/32,", "            iterations: 100000,", "            hasher: CryptoJS.algo.SHA256", "        });", "        ", "        console.log('✅ PBKDF2 key derived (fallback)');", "        ", "        // Generate random IV (12 bytes)", "        const iv = CryptoJS.lib.WordArray.random(12);", "        ", "        // Encrypt using AES-CTR", "        const encrypted = CryptoJS.AES.encrypt(jsonString, key, {", "            iv: iv,", "            mode: CryptoJS.mode.CTR,", "            padding: CryptoJS.pad.NoPadding", "        });", "        ", "        // Combine IV + encrypted data", "        const ivBase64 = iv.toString(CryptoJS.enc.Base64);", "        const encryptedBase64 = encrypted.ciphertext.toString(CryptoJS.enc.Base64);", "        const ivBytes = CryptoJS.enc.Base64.parse(ivBase64);", "        const encryptedBytes = CryptoJS.enc.Base64.parse(encryptedBase64);", "        const combined = ivBytes.concat(encryptedBytes);", "        const finalEncrypted = combined.toString(CryptoJS.enc.Base64);", "        ", "        console.log('✅ CryptoJS fallback encryption completed');", "        console.log('Fallback encrypted length:', finalEncrypted.length);", "        ", "        // Set request body", "        const requestBody = { encryptedData: finalEncrypted };", "        pm.request.body.mode = 'raw';", "        pm.request.body.raw = JSON.stringify(requestBody, null, 2);", "        pm.request.headers.upsert({ key: 'Content-Type', value: 'application/json' });", "        ", "        console.log('✅ Request updated with CryptoJS fallback');", "        ", "    } catch (fallbackError) {", "        console.error('❌ CryptoJS fallback also failed:', fallbackError.message);", "        ", "        // Final fallback: simple AES", "        console.log('� Using simple AES as final fallback...');", "        const CryptoJS = require('crypto-js');", "        const simpleEncrypted = CryptoJS.AES.encrypt(JSON.stringify(testPayload), aesPassword).toString();", "        ", "        const requestBody = { encryptedData: simpleEncrypted };", "        pm.request.body.mode = 'raw';", "        pm.request.body.raw = JSON.stringify(requestBody, null, 2);", "        pm.request.headers.upsert({ key: 'Content-Type', value: 'application/json' });", "        ", "        console.log('✅ Simple AES fallback completed');", "    }", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Veritime Login Response Test Script", "// Handles encrypted response decryption and authentication data storage", "", "const CryptoJS = require('crypto-js');", "", "console.log('🧪 Testing login response...');", "", "pm.test('Response status is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Check if response contains encrypted data", "let hasEncryptedData = false;", "try {", "    const responseJson = pm.response.json();", "    if (responseJson && responseJson.encryptedData) {", "        hasEncryptedData = true;", "        console.log('✅ Response contains encrypted data');", "    } else {", "        console.log('ℹ️ Response does not contain encrypted data');", "        console.log('Raw response:', pm.response.text());", "    }", "} catch (error) {", "    console.log('ℹ️ Response is not JSON format');", "    console.log('Raw response:', pm.response.text());", "}", "", "pm.test('Response format is valid', function () {", "    if (hasEncryptedData) {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('encryptedData');", "        pm.expect(responseJson.encryptedData).to.be.a('string');", "        pm.expect(responseJson.encryptedData.length).to.be.greaterThan(0);", "    } else {", "        // For non-encrypted responses, just check it's not an error", "        pm.expect(pm.response.text()).to.not.include('error');", "        pm.expect(pm.response.text()).to.not.include('An error occurred');", "    }", "});", "", "// Decrypt response if it contains encrypted data", "if (hasEncryptedData) {", "    console.log('🔓 Attempting to decrypt response...');", "    ", "    try {", "        const responseJson = pm.response.json();", "        const aesPassword = pm.environment.get('aesPassword') || 'uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabqAU=';", "        const aesSalt = pm.environment.get('aesSalt') || 'uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabBA==';", "        ", "        // Method 1: Try CryptoJS decryption (matching our encryption)", "        try {", "            console.log('🔑 Attempting CryptoJS decryption...');", "            ", "            // Convert base64 credentials to WordArrays", "            const passwordWordArray = CryptoJS.enc.Base64.parse(aesPassword);", "            const saltWordArray = CryptoJS.enc.Base64.parse(aesSalt);", "            ", "            // Derive the same key used for encryption", "            const key = CryptoJS.PBKDF2(passwordWordArray, saltWordArray, {", "                keySize: 256/32,", "                iterations: 100000,", "                hasher: CryptoJS.algo.SHA256", "            });", "            ", "            // Decode the encrypted data", "            const encryptedBytes = CryptoJS.enc.Base64.parse(responseJson.encryptedData);", "            ", "            // Extract IV (first 12 bytes) and encrypted data", "            const ivWords = CryptoJS.lib.WordArray.create(encryptedBytes.words.slice(0, 3)); // 12 bytes = 3 words", "            const encryptedDataWords = CryptoJS.lib.WordArray.create(encryptedBytes.words.slice(3));", "            ", "            // Decrypt using AES-CTR", "            const decrypted = CryptoJS.AES.decrypt(", "                { ciphertext: encryptedDataWords },", "                key,", "                {", "                    iv: iv<PERSON><PERSON><PERSON>,", "                    mode: CryptoJS.mode.CTR,", "                    padding: CryptoJS.pad.NoPadding", "                }", "            );", "            ", "            const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);", "            ", "            if (decryptedText && decryptedText.length > 0) {", "                const decryptedJson = JSON.parse(decryptedText);", "                console.log('✅ Response decrypted successfully (CryptoJS):', decryptedJson);", "                ", "                // Store authentication data", "                if (decryptedJson.accessToken) {", "                    pm.environment.set('accessToken', decryptedJson.accessToken);", "                    pm.environment.set('userRole', decryptedJson.role || '');", "                    pm.environment.set('empId', decryptedJson.empId || '');", "                    pm.environment.set('employeeName', decryptedJson.employeeName || '');", "                    pm.environment.set('logHistoryId', decryptedJson.logHistoryId || '');", "                    ", "                    console.log('✅ Authentication data stored:');", "                    console.log('   Access Token: [STORED]');", "                    console.log('   Role:', decryptedJson.role);", "                    console.log('   Employee ID:', decryptedJson.empId);", "                    console.log('   Employee Name:', decryptedJson.employeeName);", "                    ", "                    pm.test('Access token received', function () {", "                        pm.expect(decryptedJson.accessToken).to.be.a('string');", "                        pm.expect(decryptedJson.accessToken.length).to.be.greaterThan(0);", "                    });", "                    ", "                    pm.test('User role is valid', function () {", "                        const validRoles = ['ceo', 'superadmin', 'admin', 'supervisor', 'employee'];", "                        pm.expect(validRoles).to.include(decryptedJson.role.toLowerCase());", "                    });", "                } else {", "                    console.log('ℹ️ No access token in decrypted response');", "                }", "                ", "            } else {", "                console.log('❌ Decryption failed - empty response');", "                ", "                // Try simple AES decryption as fallback", "                console.log('🔄 Trying simple AES decryption...');", "                const simpleDecrypted = CryptoJS.AES.decrypt(responseJson.encryptedData, aesPassword);", "                const simpleDecryptedText = simpleDecrypted.toString(CryptoJS.enc.Utf8);", "                ", "                if (simpleDecryptedText && simpleDecryptedText.length > 0) {", "                    const simpleDecryptedJson = JSON.parse(simpleDecryptedText);", "                    console.log('✅ Simple AES decryption successful:', simpleDecryptedJson);", "                    ", "                    // Store authentication data from simple decryption", "                    if (simpleDecryptedJson.accessToken) {", "                        pm.environment.set('accessToken', simpleDecryptedJson.accessToken);", "                        pm.environment.set('userRole', simpleDecryptedJson.role || '');", "                        pm.environment.set('empId', simpleDecryptedJson.empId || '');", "                        pm.environment.set('employeeName', simpleDecryptedJson.employeeName || '');", "                        console.log('✅ Authentication data stored from simple decryption');", "                    }", "                } else {", "                    console.log('❌ Simple AES decryption also failed');", "                }", "            }", "            ", "        } catch (cryptoError) {", "            console.log('❌ CryptoJS decryption failed:', cryptoError.message);", "            ", "            // Final fallback: try simple AES decryption", "            try {", "                console.log('🔄 Final fallback: simple AES decryption...');", "                const fallbackDecrypted = CryptoJS.AES.decrypt(responseJson.encryptedData, aesPassword);", "                const fallbackText = fallbackDecrypted.toString(CryptoJS.enc.Utf8);", "                ", "                if (fallbackText && fallbackText.length > 0) {", "                    const fallbackJson = JSON.parse(fallbackText);", "                    console.log('✅ Fallback decryption successful:', fallback<PERSON><PERSON>);", "                    ", "                    if (fallbackJson.accessToken) {", "                        pm.environment.set('accessToken', fallbackJson.accessToken);", "                        console.log('✅ Access token stored from fallback decryption');", "                    }", "                } else {", "                    console.log('❌ All decryption methods failed');", "                }", "            } catch (fallbackError) {", "                console.log('❌ Fallback decryption failed:', fallbackError.message);", "            }", "        }", "        ", "    } catch (error) {", "        console.error('❌ Response processing failed:', error.message);", "        console.log('📄 Raw response:', pm.response.text());", "    }", "} else {", "    console.log('ℹ️ No encrypted data to decrypt');", "}", "", "console.log('🏁 Login test script completed');"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"encryptedData\": \"will_be_replaced_by_pre_request_script\"\n}"}, "url": {"raw": "{{baseUrl2}}/auth/login", "host": ["{{baseUrl2}}"], "path": ["auth", "login"]}, "description": "🔐 **Veritime Login with AES Encryption**\n\nAuthenticate user and receive JWT token. This endpoint uses AES encryption for both request and response payloads.\n\n**✅ Working Encryption Method:**\n- **Algorithm:** PBKDF2 + AES-CTR with IV prepending\n- **Key Derivation:** PBKDF2 with 100,000 iterations + SHA-256\n- **IV:** 12 bytes random IV prepended to encrypted data\n- **Format:** Base64(IV + EncryptedData)\n\n**How it works:**\n1. Pre-request script automatically encrypts username/password using PBKDF2 + AES-CTR\n2. Sends encrypted payload to backend in `{\"encryptedData\": \"...\"}`format\n3. Test script attempts multiple decryption methods for response\n4. Stores JWT token and authentication data in environment variables\n\n**Environment Variables (Optional - has defaults):**\n- `testUsername` - Your login username (default: <EMAIL>)\n- `testPassword` - Your login password (default: raj456123)\n- `aesPassword` - AES encryption key (default: uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabqAU=)\n- `aesSalt` - AES salt (default: uuesPKIe3/8elP2KmvmVjHvh3OCpz30vwhs2oVabBA==)\n\n**Access:** Public\n**Encryption:** PBKDF2 + AES-CTR (synchronous, Postman-compatible)\n**Rate Limit:** 5 requests per minute per IP\n\n**Response:** Encrypted JWT token and user details automatically decrypted and stored\n\n**Debugging:** Check console for encryption/decryption logs and stored environment variables"}}, {"name": "Get Current User Profile", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Profile retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Profile contains user details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('empId');", "    pm.expect(responseJson).to.have.property('empName');", "    pm.expect(responseJson).to.have.property('role');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/current", "host": ["{{baseUrl}}"], "path": ["current"]}, "description": "Get current authenticated user's profile details.\n\n**Access:** All authenticated users\n**Returns:** User profile with role, department, branch information"}}]}, {"name": "👥 Employee Management", "description": "Employee CRUD operations, present employees, and employee-related data management", "item": [{"name": "Get All Employees", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Employees retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});", "", "pm.test('Employee objects have required fields', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        pm.expect(responseJson[0]).to.have.property('empId');", "        pm.expect(responseJson[0]).to.have.property('empName');", "        pm.expect(responseJson[0]).to.have.property('empCode');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/employee", "host": ["{{baseUrl}}"], "path": ["employee"]}, "description": "Retrieve all employees with role-based filtering.\n\n**Access:** <PERSON><PERSON>, CEO, SuperAdmin\n**Filtering:** \n- CEO: All employees across organization\n- SuperAdmin: Company-level employees\n- Admin: Branch-level employees\n\n**Response:** Array of EmployeeDetailsDto objects"}}, {"name": "Get Employee by ID", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Employee retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Employee has required details', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('empId');", "    pm.expect(responseJson).to.have.property('empName');", "    pm.expect(responseJson).to.have.property('departmentName');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/employee/{{empId}}", "host": ["{{baseUrl}}"], "path": ["employee", "{{empId}}"]}, "description": "Get specific employee details by ID.\n\n**Access:** All roles (filtered by hierarchy)\n**Path Parameter:** empId - Employee ID to retrieve\n**Filtering:** Users can only access employees within their hierarchy"}}, {"name": "Create Employee", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Employee created successfully', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains employee ID', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('empId');", "    ", "    // Store created employee ID for future tests", "    pm.environment.set('createdEmpId', responseJson.empId);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"empName\": \"<PERSON>\",\n  \"empCode\": \"EMP001\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"departmentId\": 1,\n  \"designationId\": 2,\n  \"branchId\": 1,\n  \"upperId\": 456,\n  \"joiningDate\": \"2024-01-15\",\n  \"salary\": 50000,\n  \"address\": \"123 Main Street, City, State\",\n  \"emergencyContact\": \"+1987654321\",\n  \"bloodGroup\": \"O+\",\n  \"dateOfBirth\": \"1990-05-15\"\n}"}, "url": {"raw": "{{baseUrl}}/employee", "host": ["{{baseUrl}}"], "path": ["employee"]}, "description": "Create a new employee record.\n\n**Access:** <PERSON><PERSON>, CEO, SuperAdmin\n**Required Fields:** empName, empCode, email, departmentId, designationId, branchId\n**Validation:** \n- Unique empCode\n- Valid email format\n- Valid department and designation IDs"}}, {"name": "Update Employee", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Employee updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Update response is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "});"]}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"empName\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567891\",\n  \"departmentId\": 1,\n  \"designationId\": 3,\n  \"salary\": 55000,\n  \"address\": \"456 Updated Street, City, State\"\n}"}, "url": {"raw": "{{baseUrl}}/employee/update/{{empId}}", "host": ["{{baseUrl}}"], "path": ["employee", "update", "{{empId}}"]}, "description": "Update existing employee details.\n\n**Access:** <PERSON><PERSON>, CEO, SuperAdmin\n**Path Parameter:** empId - Employee ID to update\n**Validation:** Same as create employee\n**Note:** Only provided fields will be updated"}}, {"name": "Get Present Employees", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Present employees retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has pagination info', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('employees');", "    pm.expect(responseJson).to.have.property('totalCount');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"offset\": 0,\n  \"limit\": 10\n}"}, "url": {"raw": "{{baseUrl}}/employees/present", "host": ["{{baseUrl}}"], "path": ["employees", "present"]}, "description": "Get paginated list of employees currently present.\n\n**Access:** All roles (filtered by hierarchy)\n**Pagination:** offset and limit parameters\n**Returns:** List of present employees with attendance status"}}, {"name": "Get Employee Names and IDs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/employees/ids-names", "host": ["{{baseUrl}}"], "path": ["employees", "ids-names"]}, "description": "Get simplified list of employee IDs and names for dropdowns.\n\n**Access:** All roles (filtered by hierarchy)\n**Returns:** Array of {empId, empName} objects"}}]}, {"name": "⏰ Attendance Management", "description": "Attendance tracking, approvals, team attendance, and biometric data management", "item": [{"name": "Get Attendance List", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Attendance data retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Attendance records have required fields', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        pm.expect(responseJson[0]).to.have.property('attendanceId');", "        pm.expect(responseJson[0]).to.have.property('empId');", "        pm.expect(responseJson[0]).to.have.property('date');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/attendance/listByEmployee?fromDate=2024-01-01&toDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["attendance", "listByEmployee"], "query": [{"key": "fromDate", "value": "2024-01-01"}, {"key": "toDate", "value": "2024-01-31"}]}, "description": "Get attendance records for date range.\n\n**Access:** All roles (filtered by hierarchy)\n**Parameters:** fromDate, toDate (YYYY-MM-DD format)\n**Returns:** List of AttendanceResponseDTO objects"}}, {"name": "Get Team Attendance", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Team attendance retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Team attendance has employee data', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        pm.expect(responseJson[0]).to.have.property('empName');", "        pm.expect(responseJson[0]).to.have.property('attendanceStatus');", "    }", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userid\": {{empId}},\n  \"fromDate\": \"2024-01-01\",\n  \"toDate\": \"2024-01-31\"\n}"}, "url": {"raw": "{{baseUrl}}/attendance/teamAttendance", "host": ["{{baseUrl}}"], "path": ["attendance", "teamAttendance"]}, "description": "Get attendance data for team members.\n\n**Access:** Supervisor, Ad<PERSON>, CEO, SuperAdmin\n**Body:** userid (supervisor's ID), fromDate, toDate\n**Returns:** Team attendance summary with status"}}, {"name": "Approve Attendance", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Attendance approved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Approval response is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"attendanceId\": 123,\n  \"status\": \"APPROVED\",\n  \"remarks\": \"Approved by supervisor - valid attendance\"\n}"}, "url": {"raw": "{{baseUrl}}/attendance/update-status", "host": ["{{baseUrl}}"], "path": ["attendance", "update-status"]}, "description": "Approve or deny attendance record.\n\n**Access:** Supervisor, <PERSON><PERSON>, CEO, SuperAdmin\n**Body:** attendanceId, status (APPROVED/DENIED), remarks\n**Validation:** User can only approve attendance within their hierarchy"}}, {"name": "Get Biometric Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/attendance/audit/listByEmployee?fromDate=2024-01-01&toDate=2024-01-31", "host": ["{{baseUrl}}"], "path": ["attendance", "audit", "listByEmployee"], "query": [{"key": "fromDate", "value": "2024-01-01"}, {"key": "toDate", "value": "2024-01-31"}]}, "description": "Get raw biometric punch data for audit purposes.\n\n**Access:** <PERSON><PERSON>, CEO, SuperAdmin\n**Parameters:** fromDate, toDate\n**Returns:** Raw biometric attendance data with punch times"}}, {"name": "Create Attendance Regularization", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"date\": \"2024-01-15\",\n  \"checkInTime\": \"09:00:00\",\n  \"checkOutTime\": \"18:00:00\",\n  \"reason\": \"Forgot to punch in due to urgent meeting\",\n  \"remarks\": \"Meeting with client - emergency call\"\n}"}, "url": {"raw": "{{baseUrl}}/attendance/regularize2", "host": ["{{baseUrl}}"], "path": ["attendance", "regularize2"]}, "description": "Create attendance regularization request.\n\n**Access:** All roles\n**Body:** date, checkInTime, checkOutTime, reason, remarks\n**Workflow:** Creates pending request for supervisor approval"}}]}, {"name": "🏖️ Leave Management", "description": "Leave applications, approvals, history, and leave balance management", "item": [{"name": "Apply Leave", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Leave application submitted successfully', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Leave application has ID', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('leaveHistoryId');", "    ", "    // Store for approval tests", "    pm.environment.set('leaveHistoryId', responseJson.leaveHistoryId);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"leaveId\": 1,\n  \"startDate\": \"2024-02-15\",\n  \"endDate\": \"2024-02-17\",\n  \"appliedLeaveCount\": 3,\n  \"reason\": \"Family wedding - need to travel to hometown\",\n  \"uploadedFileName\": \"wedding_invitation.pdf\"\n}"}, "url": {"raw": "{{baseUrl}}/employees/leave/applications", "host": ["{{baseUrl}}"], "path": ["employees", "leave", "applications"]}, "description": "Submit new leave application.\n\n**Access:** All roles\n**Body:** leaveId (leave type), startDate, endDate, appliedLeaveCount, reason, uploadedFileName (optional)\n**Validation:** \n- Valid date range\n- Sufficient leave balance\n- No overlapping leaves"}}, {"name": "Get Leave History", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/leave-applications/history", "host": ["{{baseUrl}}"], "path": ["leave-applications", "history"]}, "description": "Get leave application history.\n\n**Access:** All roles (filtered by hierarchy)\n**Returns:** List of leave applications with status and approval details"}}, {"name": "Approve Leave", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Leave approved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Approval response is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "});"]}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"remarks\": \"Approved - valid reason and sufficient leave balance\"\n}"}, "url": {"raw": "{{baseUrl}}/leave-applications/{{leaveHistoryId}}/approve", "host": ["{{baseUrl}}"], "path": ["leave-applications", "{{leaveHistoryId}}", "approve"]}, "description": "Approve leave application.\n\n**Access:** Super<PERSON>, <PERSON><PERSON>, CEO, SuperAdmin\n**Path Parameter:** leaveHistoryId\n**Body:** remarks (optional)\n**Validation:** Can only approve leaves within hierarchy"}}, {"name": "<PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"remarks\": \"Denied - insufficient leave balance for requested period\"\n}"}, "url": {"raw": "{{baseUrl}}/leave-applications/{{leaveHistoryId}}/deny", "host": ["{{baseUrl}}"], "path": ["leave-applications", "{{leaveHistoryId}}", "deny"]}, "description": "Deny leave application.\n\n**Access:** <PERSON><PERSON>, <PERSON><PERSON>, CEO, SuperAdmin\n**Path Parameter:** leaveHistoryId\n**Body:** remarks (required for denial)\n**Validation:** Can only deny leaves within hierarchy"}}, {"name": "Get Leave Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/leave-applications/types", "host": ["{{baseUrl}}"], "path": ["leave-applications", "types"]}, "description": "Get available leave types.\n\n**Access:** All roles\n**Returns:** List of leave types with details (annual, sick, casual, etc.)"}}, {"name": "Get Leave Counts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/leave-applications/counts", "host": ["{{baseUrl}}"], "path": ["leave-applications", "counts"]}, "description": "Get leave balance and usage counts.\n\n**Access:** All roles\n**Returns:** Leave balance, used leaves, pending applications"}}]}, {"name": "📊 Dashboard & Reports", "description": "Dashboard widgets, reports, and analytics data", "item": [{"name": "Get Dashboard Widgets", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Widget data retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Widget response has data', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/widgets?report=EmployeesOnClockedInToday", "host": ["{{baseUrl}}"], "path": ["widgets"], "query": [{"key": "report", "value": "EmployeesOnClockedInToday"}]}, "description": "Get dashboard widget data.\n\n**Access:** Role-based filtering applied\n**Available Reports:**\n- EmployeesOnClockedInToday\n- EmployeePresent\n- LeaveApprovalsPending\n- LeaveApproved\n- FacilityCount\n\n**Query Parameter:** report - Type of widget data to retrieve"}}, {"name": "Get Side Menu", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Side menu retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Menu items have required structure', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        pm.expect(responseJson[0]).to.have.property('menu_label');", "        pm.expect(responseJson[0]).to.have.property('menu_path');", "        pm.expect(responseJson[0]).to.have.property('is_active');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/sidemenu", "host": ["{{baseUrl}}"], "path": ["sidemenu"]}, "description": "Get role-based sidebar menu structure.\n\n**Access:** All authenticated users\n**Returns:** Menu items filtered by user role with navigation paths and icons"}}, {"name": "Download Report", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"fromDate\": \"2024-01-01\",\n  \"toDate\": \"2024-01-31\"\n}"}, "url": {"raw": "{{baseUrl}}/widgets?report=EmployeePresent&download=true", "host": ["{{baseUrl}}"], "path": ["widgets"], "query": [{"key": "report", "value": "EmployeePresent"}, {"key": "download", "value": "true"}]}, "description": "Download report as file.\n\n**Access:** Role-based filtering applied\n**Query Parameters:** report, download=true\n**Body:** Date range and filters\n**Returns:** File download or download URL"}}]}, {"name": "🏢 Department & Branch Management", "description": "Department and branch CRUD operations and organizational structure", "item": [{"name": "Get All Departments", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/departments/names", "host": ["{{baseUrl}}"], "path": ["departments", "names"]}, "description": "Get list of all departments.\n\n**Access:** All roles (filtered by hierarchy)\n**Returns:** Department names and IDs"}}, {"name": "Create Department", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"departmentName\": \"Information Technology\",\n  \"branchId\": 1,\n  \"description\": \"IT department handling all technology operations\"\n}"}, "url": {"raw": "{{baseUrl}}/departments/create-dept-branch", "host": ["{{baseUrl}}"], "path": ["departments", "create-dept-branch"]}, "description": "Create new department.\n\n**Access:** <PERSON><PERSON>, CEO, SuperAdmin\n**Body:** departmentName, branchId, description\n**Validation:** Unique department name within branch"}}, {"name": "Get Branches", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/departments/branches", "host": ["{{baseUrl}}"], "path": ["departments", "branches"]}, "description": "Get list of all branches.\n\n**Access:** All roles (filtered by hierarchy)\n**Returns:** Branch names and IDs"}}, {"name": "Create Branch", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"branchName\": \"New York Office\",\n  \"address\": \"123 Business Ave, New York, NY 10001\",\n  \"phone\": \"******-0123\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/branches/create-branch-dept", "host": ["{{baseUrl}}"], "path": ["branches", "create-branch-dept"]}, "description": "Create new branch.\n\n**Access:** CEO, SuperAdmin\n**Body:** branchName, address, phone, email\n**Validation:** Unique branch name"}}]}]}