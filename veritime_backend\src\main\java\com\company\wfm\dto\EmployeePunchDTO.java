package com.company.wfm.dto;

import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalDate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmployeePunchDTO {
    private Long empId;
    private Time firstCheckIn;
    private LocalDate firstCheckInDate;
    private Time lastCheckOut;
    private LocalDate lastCheckOutDate;
    private BigDecimal overtimeHours;
    private Time actualShiftStartTime;
    private Time actualShiftEndTime;
}
