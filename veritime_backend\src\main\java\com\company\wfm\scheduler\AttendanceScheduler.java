package com.company.wfm.scheduler;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.company.wfm.annotation.TrackSchedularExecution;
import com.company.wfm.repository.AttendanceAuditRepository;
import com.company.wfm.repository.AttendanceRepository;
import com.company.wfm.repository.EmployeeRepository;
import com.company.wfm.repository.EmployeeScheduleRepository;
import com.company.wfm.repository.TimeSlotRepository;
import com.company.wfm.service.AttendanceAuditService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> @date
 */
@Component
@Slf4j
public class AttendanceScheduler {

	@Autowired
	AttendanceAuditService attendanceAuditService;

	@Autowired
	AttendanceAuditRepository attendanceAuditRepository;
 

	// Runs every day at 1:00 AM
	/**
	 *
	 */
	//@Scheduled(cron = "0 0 1 * * ?")
	public void fetchAndInsertAttendance() {
		try {
			var punchDataList = attendanceAuditService.getFirstAndLastSwapsForPreviousDay();
			log.info("attendance Data:{} ", punchDataList);
			attendanceAuditService.insertAttendanceRecords(punchDataList);
		} catch (Exception e) {
			log.error("Error fetching or inserting attendance records: {} ", e);
		}
	}

	@TrackSchedularExecution(name = "SYNC_ATTEND_INFO_TO_AUDIT_JOB")
	@Scheduled(cron = "0 59 23 * * ?")
	public void syncDeviceDataIntoAttendanceAudit() {
		try {
			String queryDate = DateTimeFormatter.ofPattern(com.company.wfm.util.DateFormatConstant.DAY_FORMAT)
					.format(LocalDate.now());
			attendanceAuditService.getAttendanceDetailByDateRange(queryDate);
		} catch (Exception e) {
			log.error("Error fetching or inserting attendance records: {} ", e);
		}
	}

	// @Scheduled(cron = "0 0 1 * * ?")
	public void fetchAndInsertPastAttendance() {
		try {
			var punchDataList = attendanceAuditService.getFirstAndLastSwapsForOldDay();
			attendanceAuditService.insertPastAttendanceRecords(punchDataList);
		} catch (Exception e) {
			log.error("Error fetching or inserting attendance records: {} ", e);
		}
	}
	
	@TrackSchedularExecution(name = "CALCULATE_ATTENDANCE_ENTRY_FOR_EMPLOYEE")
	@Scheduled(cron = "0 0 14 * * ?") // Runs daily at 2:00 PM
	public void processDailyAttendance() {
		attendanceAuditService.processDailyAttendance(LocalDate.now().minusDays(1));
	}

}
