import React from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import { Pie } from "react-chartjs-2";
import "chart.js/auto";
import "./Branch.css";
import { colors } from "@constants/colors";

const BranchWiseTicketCard = (value) => {
  const data = {
    labels: value?.data?.axisLbl,
    datasets: [
      {
        data: value?.data?.axis,
        backgroundColor: value?.data?.colors,
        // hoverBackgroundColor: [colors.yellow, colors.blue11, colors.pink, colors.green6],
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
  };

  return (
    <div className="container d-flex justify-content-center align-items-center">
      <div className="card shadow-sm custom-card-branch">
        <div className="card-body">
          <div className="d-flex justify-content-between align-items-center">
            <h5 className="card-title-branch">
              Facility Based Ticket Overview
            </h5>
            <div className="dropdown">
              <i className="bi bi-three-dots-vertical"></i>
              <ul
                className="dropdown-menu"
                aria-labelledby="dropdownMenuButton"
              >
                <li>
                  <a className="dropdown-item" href="#">
                    Action
                  </a>
                </li>
                <li>
                  <a className="dropdown-item" href="#">
                    Another action
                  </a>
                </li>
                <li>
                  <a className="dropdown-item" href="#">
                    Something else here
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div style={{ height: "190px" }}>
            <Pie data={data} options={options} />
          </div>
        </div>
      </div>
    </div>
  );
};

const BranchTicket = ({ data }) => {
  return (
    <div className="col-12 col-lg-3 col-xl-3 col-md-12 mb-2">
      <BranchWiseTicketCard data={data} />
    </div>
  );
};

export default BranchTicket;
