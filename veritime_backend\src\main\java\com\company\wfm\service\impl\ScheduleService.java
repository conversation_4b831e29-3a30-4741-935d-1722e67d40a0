package com.company.wfm.service.impl;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.hibernate.service.spi.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.company.wfm.dto.ScheduleDTO;
import com.company.wfm.dto.ScheduleListDTO;
import com.company.wfm.dto.TimeSlotDTO;
import com.company.wfm.entity.Branch;
import com.company.wfm.entity.Department;
import com.company.wfm.entity.Schedule;
import com.company.wfm.entity.TimeSlot;
import com.company.wfm.entity.User;
import com.company.wfm.repository.BranchRepository;
import com.company.wfm.repository.DepartmentRepository;
import com.company.wfm.repository.ScheduleRepository;
import com.company.wfm.service.UserTokenService;
import com.company.wfm.util.CommonConstant;
import com.company.wfm.vo.ScheduleVO;
import com.company.wfm.vo.TimeSlotVO;

import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ScheduleService {

    @Autowired
    private ScheduleRepository scheduleRepository;

    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private UserTokenService userTokenService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private UserTokenService tokenService;


    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    private static final Logger logger = LoggerFactory.getLogger(ScheduleService.class);

    @Transactional
    public ScheduleVO createSchedule(ScheduleDTO scheduleDTO) {
        Long empIdFromToken = tokenService.getEmployeeIdFromToken();
        Long empId = (scheduleDTO.getEmpId() != null) ? scheduleDTO.getEmpId() : empIdFromToken;

        Branch branch = branchRepository.findById(scheduleDTO.getBranchId())
                .orElseThrow(() -> new EntityNotFoundException("Branch not found"));

        Department department = departmentRepository.findById(scheduleDTO.getDepartmentId())
                .orElseThrow(() -> new EntityNotFoundException("Department not found"));

        Schedule schedule = new Schedule();
        schedule.setBranch(branch);
        schedule.setDepartment(department);
        schedule.setCreatedBy(empId);
        schedule.setCreatedAt(LocalDateTime.now());

        List<TimeSlot> timeSlots = scheduleDTO.getTimeSlots().stream().map(slotDTO -> {
            TimeSlot timeSlot = new TimeSlot();
            timeSlot.setStartTime(slotDTO.getStartTime());
            timeSlot.setEndTime(slotDTO.getEndTime());
            timeSlot.setIsDefault(slotDTO.getIsDefault());
            timeSlot.setSchedule(schedule);
            return timeSlot;
        }).collect(Collectors.toList());
        schedule.setTimeSlots(timeSlots);

        try {
            Schedule savedSchedule = scheduleRepository.saveAndFlush(schedule);

            List<TimeSlotVO> resultSlot = savedSchedule.getTimeSlots().stream().map(slot -> {
                return TimeSlotVO.builder()
                        .id(slot.getId())
                        .startTime(slot.getStartTime().toString())
                        .endTime(slot.getEndTime().toString())
                        .isActive(slot.getIsActive())
                        .isDefault(slot.getIsDefault())
                        .scheduleId(slot.getSchedule().getId())
                        .build();
            }).collect(Collectors.toList());

            return ScheduleVO.builder()
                    .id(savedSchedule.getId())
                    .branchId(savedSchedule.getBranch().getId())
                    .branchName(savedSchedule.getBranch().getBranchName())
                    .departmentId(savedSchedule.getDepartment().getDepartmentId())
                    .departmentName(savedSchedule.getDepartment().getDepartmentName())
                    .timeSlots(resultSlot)
                    .build();

        } catch (DataAccessException ex) {
            logger.error("Error while saving schedule: {}", ex.getMessage(), ex);
            throw new ServiceException("Failed to save schedule. Please try again later.", ex);
        } catch (Exception e) {
            logger.error("Unexpected error while creating schedule: {}", e.getMessage(), e);
            throw new ServiceException("An unexpected error occurred while creating the schedule.", e);
        }
    }


    public Schedule getScheduleById(Long id) {
        return scheduleRepository.findById(id).orElseThrow(() -> new EntityNotFoundException("Schedule not found"));
    }

    @SuppressWarnings("deprecation")
    @Transactional(readOnly = true)
    public List<ScheduleVO> getAllSchedulesWithTimeSlots(final ScheduleListDTO scheduleListDto) {
        String query = "";

        log.info("getAllBranches called");
        User employee = userTokenService.getEmployeeFromToken();

        MapSqlParameterSource params = new MapSqlParameterSource();
        query = CommonConstant.ScheduleQuery.SCHEDULE_EXTRACT_QUERY ;

		if (employee.getRole().equalsIgnoreCase("superadmin")) {
			query += " and branch.company_id  = :companyId";
			params.addValue("companyId", employee.getEmployee().getCompanyId());

			if (!ObjectUtils.isEmpty(scheduleListDto.getBranchId())) {
				query += " and hosp.branch_id = :branchId";
				params.addValue("branchId", scheduleListDto.getBranchId());
			}

			if (!ObjectUtils.isEmpty(scheduleListDto.getDepartmentId())) {
				query += " and hosp.department_id =:deptQueryId";
				params.addValue("deptQueryId", scheduleListDto.getDepartmentId());
			}

		} else if (employee.getRole().equalsIgnoreCase("admin")) {
			query += " and hosp.branch_id  = :branchId";
			params.addValue("branchId", employee.getEmployee().getBranch().getId());

			if (!ObjectUtils.isEmpty(scheduleListDto.getDepartmentId())) {
				query += " and hosp.department_id =:deptQueryId";
				params.addValue("deptQueryId", scheduleListDto.getDepartmentId());
			}
        } else if (employee.getRole().equalsIgnoreCase("ceo")) {

			if (!ObjectUtils.isEmpty(scheduleListDto.getHospitalId())) {
				query += " and branch.company_id  = :companyId";
				params.addValue("companyId", scheduleListDto.getHospitalId());
			}
			if (!ObjectUtils.isEmpty(scheduleListDto.getBranchId())) {
				query += " and hosp.branch_id = :branchId";
				params.addValue("branchId", scheduleListDto.getBranchId());
			}

			if (!ObjectUtils.isEmpty(scheduleListDto.getDepartmentId())) {
				query += " and hosp.department_id =:deptQueryId";
				params.addValue("deptQueryId", scheduleListDto.getDepartmentId());
			}
        }

        List<ScheduleVO> scheduleList = namedParameterJdbcTemplate.query(query, params, (rs, rowNum) -> {

        	ScheduleVO schedule = ScheduleVO
        			.builder()
        			.id(rs.getLong("SCHEDULE_ID"))
        			.branchId(rs.getLong("branch_id"))
        			.branchName(rs.getString("branch_name"))
        			.departmentId(rs.getLong("department_id"))
        			.departmentName(rs.getString("department_name"))
        			.build();
        	 return schedule;

        });

        if (scheduleList.isEmpty()) {
            return Collections.emptyList();
        }
     scheduleList.stream().forEach(schedule -> {
            List<TimeSlotVO> subTimeSlots = jdbcTemplate.query(
                    "select TIME_SLOT_ID,START_TIME,END_TIME,is_active, "
                            + "is_default,SCHEDULE_ID from t_time_slot where schedule_id = ?",
                    new Object[]{schedule.getId()}, (rs, rowNum) -> {
                        TimeSlotVO slot = TimeSlotVO
                        		.builder()
                        		.id(rs.getLong("TIME_SLOT_ID"))
                        		.startTime(rs.getString("START_TIME"))
                        		.endTime(rs.getString("END_TIME"))
                        		.isActive(rs.getInt("is_active"))
                        		.isDefault(rs.getInt("is_default"))
                        		.scheduleId(rs.getLong("SCHEDULE_ID"))
                        		.build();

                        return slot;
                    });
            schedule.setTimeSlots(subTimeSlots);
        });

        return scheduleList;
    }

    @Transactional(readOnly = true)
    public List<Schedule> getSchedulesForSelectedBranch(int branchId) {
        List<Schedule> entireSchedule = scheduleRepository.getScheduledBasedOnBranch(branchId);
        return entireSchedule;
    }

    @Transactional(readOnly = true)
    public List<Schedule> getSchedulesForSelectedDepartment(int departmentId) {
        List<Schedule> entireSchedule = scheduleRepository.getScheduledBasedOnDepartment(departmentId);
        return entireSchedule;
    }



    //update schedule

    @Transactional
    public ScheduleVO updateSchedule(Long id, ScheduleDTO scheduleDTO) {
      //  Long empId = tokenService.getEmployeeIdFromToken();
        Long empIdFromToken = tokenService.getEmployeeIdFromToken();
        Long empId;
        if (scheduleDTO.getEmpId() != null) {
            empId = scheduleDTO.getEmpId();
        } else {
            empId = empIdFromToken;
        }
        Schedule existingSchedule = scheduleRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Schedule not found"));

        // Find branch and department based on the provided IDs
        Branch branch = branchRepository.findById(scheduleDTO.getBranchId())
                .orElseThrow(() -> new EntityNotFoundException("Branch not found"));
        Department department = departmentRepository.findById(scheduleDTO.getDepartmentId())
                .orElseThrow(() -> new EntityNotFoundException("Department not found"));

        // Update branch and department for the existing schedule
        existingSchedule.setBranch(branch);
        existingSchedule.setDepartment(department);
        existingSchedule.setUpdatedAt(LocalDateTime.now());
        existingSchedule.setUpdatedBy(empId);

        // Handle time slots update logic
        List<TimeSlot> currentTimeSlots = existingSchedule.getTimeSlots();

        // Clear the current time slots and prepare new ones
        currentTimeSlots.clear(); // Clear existing slots before adding new ones

        for (TimeSlotDTO slotDTO : scheduleDTO.getTimeSlots()) {
            TimeSlot timeSlot = new TimeSlot();
            timeSlot.setStartTime(slotDTO.getStartTime());
            timeSlot.setEndTime(slotDTO.getEndTime());
            timeSlot.setIsDefault(slotDTO.getIsDefault());
            timeSlot.setUpdatedBy(empId);
            timeSlot.setUpdatedAt(LocalDateTime.now());
            timeSlot.setSchedule(existingSchedule); // Link to the schedule
            currentTimeSlots.add(timeSlot); // Add new time slot to current list
        }

        // Save the updated schedule
        Schedule savedSchedule = scheduleRepository.save(existingSchedule);

        // Prepare the response object with updated data
        List<TimeSlotVO> resultSlot = savedSchedule.getTimeSlots().stream()
                .map(TimeSlotVO::new) // Using the constructor you added
                .collect(Collectors.toList());

        return new ScheduleVO(savedSchedule, resultSlot);
    }






}
