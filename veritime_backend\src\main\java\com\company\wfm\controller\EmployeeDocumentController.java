package com.company.wfm.controller;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.company.wfm.dto.ErrorResponse;
import com.company.wfm.service.AmazonS3Service;
import com.company.wfm.service.impl.EmployeeService;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/api/v1/employee-documents")
@CrossOrigin(origins = "*")
public class EmployeeDocumentController {
    Logger logger = LoggerFactory.getLogger(EmployeeDocumentController.class);

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    AmazonS3Service s3Service;

    @PostMapping("/upload")
    public ResponseEntity<String> uploadFile(
            @RequestParam("empId") Long empId,
            @RequestParam("documentId") Long documentId,
            @RequestPart(value = "file", required = false) MultipartFile file,
            HttpServletRequest request) {

        if (file == null || file.isEmpty()) {
            return new ResponseEntity<>("Please select a file to upload.", HttpStatus.BAD_REQUEST);
        }

        try {
            String responseMessage = employeeService.saveFile(empId,documentId,file, request);
            return ResponseEntity.ok(responseMessage);
        } catch (IOException e) {
            return new ResponseEntity<>("File upload failed due to server error.", HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.NOT_FOUND);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>("File size exceeds the maximum limit of 2 MB.", HttpStatus.BAD_REQUEST);
        }
    }




    //download employee uploaded document
    @GetMapping("/download/employeefile")
    public ResponseEntity<String> downloadUploadedFile(@RequestParam("name") String fileName, HttpServletResponse response) {
        try {
            // Log the file name being downloaded
            System.out.println("Downloading file: " + fileName);

            // Set HTTP headers for file download
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // Stream the file directly to the response output stream
            s3Service.downloadFile(fileName, response.getOutputStream());

            return ResponseEntity.ok("File downloaded successfully: " + fileName);
        } catch (IOException e) {
            logger.error("Error while downloading file: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to download the file: " + fileName);
        }
    }


    @DeleteMapping("/delete")
    public ResponseEntity<?> deleteDocument( @RequestParam Long empId,
                                             @RequestParam Long documentId) {
        // Call the service method to perform soft delete
        String responseMessage = employeeService.softDeleteDocument(empId,documentId);

        // Return response based on the result of the soft delete operation
        if (responseMessage.contains("not found")) {
            ErrorResponse errorResponse = new ErrorResponse("Document not found with ID: " + documentId);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);  // Document not found
        }

        return ResponseEntity.ok(responseMessage);  // Document successfully soft deleted
    }



    @GetMapping("/download/local")
    public ResponseEntity<String> downloadFileToLocal(@RequestParam("name") String fileName, @RequestParam("path") String localFilePath) {
        try {
            String decodedPath = URLDecoder.decode(localFilePath, StandardCharsets.UTF_8);
            // Ensure the directory exists
            File directory = new File(decodedPath);
            if (!directory.exists() && !directory.mkdirs()) {
                throw new IOException("Unable to create directory: " + decodedPath);
            }
            // Log the file name and local path
            logger.info("Downloading file '{}' to local path '{}'", fileName, localFilePath);

            // Call the service to download the file locally
            s3Service.downloadFileToLocal(fileName, decodedPath);

            return ResponseEntity.ok("File downloaded locally to: " + localFilePath);
        } catch (Exception e) {
            logger.error("Error downloading file locally: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to download file locally: " + e.getMessage());
        }
    }











}