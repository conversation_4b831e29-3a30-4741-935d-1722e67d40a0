import Link from 'next/link';
import { useState } from 'react';

const DynamicLink = ({ initialHref, beforeNavigate, children, ...props }) => {
  const [currentHref, setCurrentHref] = useState(initialHref);

  const handleClick = (event) => {
    debugger;
    if (beforeNavigate) {
      const result = beforeNavigate();
      if (result === false) {
        event.preventDefault(); // Prevent navigation if logic returns false
      } else if (typeof result === 'string') {
        setCurrentHref(result); // Update href if a new href is returned
      }
    }
  };

  return (
    <Link href={currentHref} passHref {...props}>
      <div onClick={handleClick}>{children}</div>
    </Link>
  );
};

export default DynamicLink;
