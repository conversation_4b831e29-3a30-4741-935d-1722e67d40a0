package com.company.wfm.controller;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import com.company.wfm.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.company.wfm.entity.AttendanceAuditEntity;
import com.company.wfm.entity.AttendanceEntity;
import com.company.wfm.entity.AttendanceRegularization;
import com.company.wfm.service.AttendanceAuditService;
import com.company.wfm.service.impl.AttendanceRegularizationService;
import com.company.wfm.util.EncryptionUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

@RestController
@RequestMapping("/api/v1/attendance")
@CrossOrigin(origins = "*")
@Slf4j
public class AttendanceRegularizationController {

	@Autowired
	private EncryptionUtil encryptionUtil;

	@Autowired
    private  AttendanceRegularizationService service;

	@Autowired
    private AttendanceAuditService attendanceService;


    @PostMapping("/regularize")
    public ResponseEntity<AttendanceRegularization> regularizeAttendance(@RequestBody AttendanceRegularizationRequest request) {
        AttendanceRegularization savedRegularization = service.submitRegularization(request);
        return ResponseEntity.ok(savedRegularization);
    }
    /*@PostMapping("/regularize2")
    public ResponseEntity<AttendanceRegularization> regularizeAttendance2(@RequestBody AttendanceRegularizationRequest request) {
        AttendanceRegularization savedRegularization = service.submitRegularization2(request);

        return ResponseEntity.ok(savedRegularization);
    }*/

    @PostMapping("/regularize2")
    public ResponseEntity<EncryptedResponse> regularizeAttendance2(@RequestBody EncryptedRequest encryptedRequest) throws Exception {
        String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

        // Using the custom Gson created earlier
        Gson gson = encryptionUtil.createGson();

        try {
            AttendanceRegularizationRequest request = gson.fromJson(decryptedData, AttendanceRegularizationRequest.class);

            // Process the request and save the regularization
            AttendanceRegularization savedRegularization = service.submitRegularization2(request);

            // Serialize the saved entity into JSON and encrypt it
            String encryptedResponseData = encryptionUtil.encrypt(gson.toJson(savedRegularization),encryptionUtil.generateKey());

            // Wrap the encrypted response in an EncryptedResponse object
            EncryptedResponse encryptedResponse = new EncryptedResponse(encryptedResponseData);

            // Return the encrypted response
            return ResponseEntity.ok(encryptedResponse);
        } catch (JsonSyntaxException e) {
            throw new RuntimeException("Invalid decrypted data format", e);
        }
    }



    //Supervisor will approve or deny the
    /*@PostMapping("/update-status")
    public ResponseEntity<AttendanceRegularization> updateApprovalStatus(@RequestBody ApprovalRequest request) {
        AttendanceRegularization result = service.updateApprovalStatus(request);
        return ResponseEntity.ok(result);
    }*/

    @PostMapping("/update-status")
    public ResponseEntity<EncryptedResponse> updateApprovalStatus(@RequestBody EncryptedRequest encryptedRequest) throws Exception {
        try {
            // Decrypt the incoming request data
            String decryptedData = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(),encryptionUtil.generateKey());

            // Deserialize the decrypted data into ApprovalRequest
            Gson gson = encryptionUtil.createGson();
            ApprovalRequest request = gson.fromJson(decryptedData, ApprovalRequest.class);

            // Process the request using the service
            AttendanceRegularization result = service.updateApprovalStatus(request);

            // Serialize the result object to JSON
            String responseJson = gson.toJson(result);

            // Encrypt the JSON response
            String encryptedResponse = encryptionUtil.encrypt(responseJson,encryptionUtil.generateKey());

            // Wrap the encrypted response in a custom object and return it
            EncryptedResponse response = new EncryptedResponse(encryptedResponse);
            return ResponseEntity.ok(response);

        }catch (RuntimeException e) {
            // Create error response JSON
            String errorJson = "{\"error\":\"" + e.getMessage() + "\"}";
            // Encrypt the JSON response
            String encryptedResponse = encryptionUtil.encrypt(errorJson, encryptionUtil.generateKey());

            // Wrap the encrypted response in a custom object and return it
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new EncryptedResponse(encryptedResponse));
        } catch (Exception e) {
            // Create generic error response JSON
            String errorJson = "{\"error\":\"Unexpected server error. Please try again later.\"}";
            // Encrypt the JSON response
            String encryptedResponse = encryptionUtil.encrypt(errorJson, encryptionUtil.generateKey());
            // Wrap the encrypted response in a custom object and return it
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new EncryptedResponse(encryptedResponse));
        }
    }

    @GetMapping("/regularizations")
    public ResponseEntity<EncryptedResponse> getRegularizations(
            @RequestParam(value = "type", required = false) String type) {
        try {
            // Retrieve attendance regularizations based on the request type
            List<AttendanceRegularizationResponse> regularizations;

            if ("history".equalsIgnoreCase(type)) {
                // Call the service to get data for the logged-in employee only
                regularizations = service.getRegularizationsForLoggedInEmployee();
            } else {
                // Call the service to get all regularizations
                regularizations = service.getAllRegularizations();
            }

            // Convert the regularizations list to JSON
            Gson gson = encryptionUtil.createGson();
            String jsonResponse = gson.toJson(regularizations);

            // Encrypt the JSON response
            String encryptedResponse = encryptionUtil.encrypt(jsonResponse,encryptionUtil.generateKey());

            // Return the encrypted response
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));
        } catch (Exception e) {
            // Handle errors during encryption or data retrieval
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new EncryptedResponse("Error encrypting response: " + e.getMessage()));
        }
    }



    //Dashboards api
    @GetMapping("/counts")
    public ResponseEntity<AttendanceRegulationCountDTO> getAttendanceRegulationCounts() {
        AttendanceRegulationCountDTO counts = service.getAttendanceRegulationCounts();
        return ResponseEntity.ok(counts);
    }
    @GetMapping("/bydate/counts")
    public ResponseEntity<AttendanceRegulationCountDTO> getAttendanceRegulationCounts(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        AttendanceRegulationCountDTO counts = service.getAttendanceRegulationCounts(startDate, endDate);
        return ResponseEntity.ok(counts);
    }

        @GetMapping("/listByEmployee")
        public ResponseEntity<List<AttendanceResponseDTO>> getEmployeeAttendanceByDateRange(
                @RequestParam("fromDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                @RequestParam("toDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
            List<AttendanceResponseDTO> result = attendanceService.getEmployeeAttendanceByDateRange(startDate, endDate);
            return ResponseEntity.ok(result);
        }



    @PostMapping("/list")
    public ResponseEntity<List<AttendanceEntity>> getMultipleEmployeeAttendanceByDateRange(@RequestBody AttendanceDto attendance) {
    	List<AttendanceEntity> result = attendanceService.getMultipleEmployeeAttendanceByDateRange(attendance);
        return ResponseEntity.ok(result);
    }

   /* @GetMapping("/audit/listByEmployee")
    public ResponseEntity<List<AttendanceAuditEntity>> getEmployeeAttendanceAuditByDateRange(
            @RequestParam("fromDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam("toDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
    	List<AttendanceAuditEntity> result = attendanceService.getEmployeeAttendanceAuditByDateRange(startDate, endDate);
        return ResponseEntity.ok(result);
    }*/

    @GetMapping("/audit/listByEmployee")
    public ResponseEntity<List<AttendanceAuditResponseDTO>> getEmployeeAttendanceAuditByDateRange(
            @RequestParam("fromDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDate startDate,
            @RequestParam("toDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDate endDate) {
        // Get the list of AttendanceAuditResponseDTOs from the service
        List<AttendanceAuditResponseDTO> result = attendanceService.getEmployeeAttendanceAuditByDateRange(startDate, endDate);
        return ResponseEntity.ok(result);
    }


    @PostMapping("/audit/list")
    public ResponseEntity<List<AttendanceAuditEntity>> getMultipleEmployeeAttendanceAuditByDateRange(@RequestBody AttendanceAuditDto attendance) {
    	List<AttendanceAuditEntity> result = attendanceService.getMultipleEmployeeAttendanceAuditByDateRange(attendance);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/audit/details")
    public ResponseEntity<List<AttendanceAuditDetailsListDTO>> getDetailsByEmpIDandDate(@RequestBody AttendanceAuditDetailsDTO attendance) {
        List<AttendanceAuditDetailsListDTO> result = attendanceService.retrieveByEmpIdandDate(attendance);
        System.out.println("Response in controller: "+result);
        return ResponseEntity.ok(result);
    }


    @PostMapping("/attendance/dateWise")
    public ResponseEntity<EncryptedResponse> getAttendancePaginatedEncrypted(@RequestBody EncryptedRequest encryptedRequest) throws Exception {
        try {
            // Validate encrypted data
            if (encryptedRequest.getEncryptedData() == null) {
                String msg = encryptionUtil.encrypt("Encrypted data is missing.", encryptionUtil.generateKey());
                return ResponseEntity.badRequest().body(new EncryptedResponse(msg));
            }

            // Decrypt request
            String decryptedJson = encryptionUtil.decrypt(encryptedRequest.getEncryptedData(), encryptionUtil.generateKey());
            if (decryptedJson == null) {
                String msg = encryptionUtil.encrypt("Decryption failed. Invalid data.", encryptionUtil.generateKey());
                return ResponseEntity.badRequest().body(new EncryptedResponse(msg));
            }

            // Deserialize request
            Gson gson = encryptionUtil.createGson();
            AttendanceSearchRequest request = gson.fromJson(decryptedJson, AttendanceSearchRequest.class);

            // Validate input
            if (request.getOffset() < 0) {
                String msg = encryptionUtil.encrypt("Offset cannot be negative", encryptionUtil.generateKey());
                return ResponseEntity.badRequest().body(new EncryptedResponse(msg));
            }
            if (request.getLimit() <= 0) {
                String msg = encryptionUtil.encrypt("Limit must be greater than zero", encryptionUtil.generateKey());
                return ResponseEntity.badRequest().body(new EncryptedResponse(msg));
            }

            // Service call
            Map<String, Object> serviceResponse = service.getAttendancePaginated(
                    request.getDates(),
                    request.getEmpId(),
                    request.getOffset(),
                    request.getLimit()
            );

            // Check for empty data
            List<?> dataList = (List<?>) serviceResponse.get("data");
            if (dataList == null || dataList.isEmpty()) {
                String msg = encryptionUtil.encrypt("No attendance records found.", encryptionUtil.generateKey());
                return ResponseEntity.ok(new EncryptedResponse(msg));
            }

            // Encrypt and return the response
            String jsonResponse = gson.toJson(serviceResponse);
            String encryptedResponse = encryptionUtil.encrypt(jsonResponse, encryptionUtil.generateKey());
            return ResponseEntity.ok(new EncryptedResponse(encryptedResponse));

        } catch (Exception e) {
            log.error("Error in encrypted attendance controller", e);
            String msg = encryptionUtil.encrypt("An error occurred: " + e.getMessage(), encryptionUtil.generateKey());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new EncryptedResponse(msg));
        }
    }

}
