import React, { useState, useCallback } from "react";
import { <PERSON><PERSON>, Button, Form, Col, Alert } from "react-bootstrap";
import { postRequest } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import { showSuccessAlert2 } from "@/services/alertService";
import { FaDownload } from "react-icons/fa";
import "./LeaveExcelUploadModal.css";

const UploadLeaveExcel = ({
  show,
  handleClose,
  fetchItems,
  downloadType,
}: any) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState("");

  const handleFileUpload = (event: any) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile && selectedFile.type == "xls") {
        setError("Please upload a valid file!");
        setFile(null);
      } else {
        setError("");
        setFile(selectedFile);
      }
    }
  };

  const uploadExcelFile = async () => {
    if (!file) {
      setError("Please select a file first!");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    setIsUploading(true);
    try {
      console.log("going to call leave api");
      const response = await postRequest(API_URLS.UPLOAD_EXCEL_NEW, formData);
      console.log("leave api called ");
      if (response) {
        showSuccessAlert2("File uploaded successfully");
        handleClose();
        setFile(null);
        fetchItems(); // Refresh leave data
      } else {
        setError("File upload failed! Please try again.");
      }
    } catch (error) {
      console.error("Upload Error:", error);
      setError("An error occurred while uploading the file.");
    } finally {
      setIsUploading(false);
    }
  };

  // Dynamic File Download based on `downloadType`
  const handleDownload = useCallback(() => {
    // Mapping different file formats
    const fileMap: { [key: string]: string } = {
      holiday: "/Holiday_Format.xlsx",
      hospital: "/Hospital_Format.xlsx",
      Employee: "/employee_Format.xlsx",
      leave: "/employee-leave-data-upload.xlsx", // Leave format
    };
    const fileUrl = fileMap[downloadType]; // Default to leave format if not found
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = `${
      downloadType.charAt(0).toUpperCase() + downloadType.slice(1)
    }_Format.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, [downloadType]);

  return (
    <Modal show={show} onHide={handleClose} className="leave-excel-modal">
      <Modal.Header closeButton>
        <Modal.Title>Leave Upload </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && <Alert variant="danger">{error}</Alert>}
        <Col md={3}>
          <Form.Group controlId="uploadLeaveExcel" className="mb-3">
            <Form.Control
              type="file"
              onChange={handleFileUpload}
              className="upload-input"
            />
            <div style={{ marginTop: "15px" }}>
              <Button
                variant="primary"
                onClick={uploadExcelFile}
                disabled={isUploading}
                className="upload-button"
                style={{ width: "190px" }}
              >
                {isUploading ? "Uploading..." : `Upload Leave`}
              </Button>
            </div>
          </Form.Group>
        </Col>

        <div className="note-container">
          <h1 className="note-title">Note: Download the sample file.</h1>

          <div onClick={handleDownload} className="download-container">
            <FaDownload size={30} className="download-icon" />
            <span className="download-text">
              Download{" "}
              {downloadType.charAt(0).toUpperCase() + downloadType.slice(1)}{" "}
              Format
            </span>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default UploadLeaveExcel;
