package com.company.wfm.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventCertificateRecord {

    @Id
    @Column(name = "record_guid")
    private String recordGuid;

    @Column(name = "element_id", nullable = false)
    private String elementId;

    @Column(name = "element_name", nullable = false)
    private String elementName;

    @Column(name = "element_pic_url")
    private String elementPicUrl;

    @Column(name = "event_time")
    private LocalDateTime eventTime;  // Assuming this is the event timestamp

    @Column(name = "event_type")
    private String eventType;  // Optional, depending on your use case

    @Column(name = "card_no")
    private String cardNo;  // Optional, depending on your use case
}
