import React, { useState } from 'react';
import { <PERSON><PERSON>, Button, Form } from 'react-bootstrap';
import Select, { components } from 'react-select';

const CustomOption = (props) => (
  <components.Option {...props}>
    <input type="checkbox" checked={props.isSelected} readOnly style={{ marginRight: 8 }} />
    {props.data.imgUrl && <img src={props.data.imgUrl} alt="avatar" style={{ width: 20, height: 20, marginRight: 5 }} />}
    <label>{props.label}</label>
  </components.Option>
);

const CustomMultiSelectModal = ({
  show,
  onHide,
  onSave,
  headerTitle,
  selectTitle,
  data,
  selectedValues,
  setSelectedValues,
  valueKey,
  labelKey,
  imgUrl
}) => {

  const handleChange = (selectedOptions) => {
    const isSelectAllClicked = selectedOptions.some((option) => option.value === 'select-all');
    if (isSelectAllClicked) {
      const allValues = selectedValues.length === data.length ? [] : data.map((item) => item[valueKey]);
      setSelectedValues(allValues);
    } else {
      const selectedIds = selectedOptions ? selectedOptions.map((option) => option.value) : [];
      setSelectedValues(selectedIds);
    }
  };

  const options = [
    { label: selectedValues.length === data.length ? 'Deselect All' : 'Select All', value: 'select-all' },
    ...data.map((item) => ({
      label: item[labelKey],
      value: item[valueKey],
      imgUrl: item[imgUrl]
    })),
  ];

  const getSelectedOptions = () => {
    return options.filter((option) => selectedValues.includes(option.value));
  };

  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>{headerTitle}</Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ paddingBottom: '80px' }}>
        {data.length > 0 ? (
          <Form.Group className="mt-3">
            <Form.Label>Select {selectTitle}</Form.Label>
            <Select
              isMulti
              closeMenuOnSelect={false}
              hideSelectedOptions={false}
              components={{ Option: CustomOption }}
              options={options}
              value={getSelectedOptions()}
              onChange={handleChange}
              placeholder={`Select ${selectTitle}`}
              styles={{
                menu: (provided) => ({ 
                  ...provided, 
                  maxHeight: '150px', 
                  overflowY: 'auto' 
                }),
                menuList: (provided) => ({
                  ...provided,
                  maxHeight: '150px',
                  overflowY: 'auto',
                }),
                option: (provided, { isSelected }) => ({
                  ...provided,
                  padding: '10px', 
                  display: 'flex', 
                  alignItems: 'center',
                  backgroundColor: isSelected ? 'transparent' : 'transparent', 
                  color: 'black',
                  ':hover': {
                    backgroundColor: '#e0e0e0',
                  },
                }),
                control: (provided) => ({ 
                  ...provided, 
                  overflowX: 'auto', 
                  flexWrap: 'nowrap' 
                }),
                valueContainer: (provided) => ({ 
                  ...provided, 
                  maxHeight: '40px', 
                  overflowX: 'auto', 
                  flexWrap: 'nowrap' 
                }),
                multiValue: (provided) => ({
                  ...provided,
                  backgroundColor: 'transparent',
                  padding: '2px',
                }),
                multiValueLabel: (provided) => ({
                  ...provided,
                  color: '#333', 
                }),
                multiValueRemove: (provided) => ({
                  ...provided,
                  color: '#999', 
                  ':hover': {
                    backgroundColor: 'transparent',
                    color: 'red',
                  },
                }),

              }}
            />
          </Form.Group>
        ) : (
          <p>No {selectTitle.toLowerCase()} to show</p>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button variant="primary" onClick={onSave}>
          Save
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default CustomMultiSelectModal;
