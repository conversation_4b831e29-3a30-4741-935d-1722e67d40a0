<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* THEMlyfe Template Styling */
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 40px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #4a90e2;
            color: #fff;
            padding: 10px 0;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            text-align: center;
        }
        .header h2 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 20px;
            line-height: 1.6;
        }
        .content p {
            margin: 0 0 15px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin-top: 20px;
            background-color: #4a90e2;
            color: #fff;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
        }
        .footer {
            background-color: #f4f4f4;
            color: #777;
            padding: 10px;
            text-align: center;
            font-size: 12px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <p th:text="'Hi ' + ${name} + ','">Hi [Customer's Name],</p>
            <p>
                We received a request to reset the password for your account. 
                If you did not make this request, you can safely ignore this email. 
                Otherwise, please click the button below to reset your password:
            </p>
            <a href="#" class="button" th:href="${resetLink}" th:text="'Reset Password'">Reset Password</a>
            <p>
                This link will expire in 30 minutes. If you need a new link, please request a password reset again.
            </p>
            <p>
                For your security, never share your login credentials with anyone. If you encounter any issues, feel free to 
                contact our support team.
            </p>
            <p>Thank you,</p>
            <p>Support Team</p>
        </div>
    </div>
</body>
</html>
