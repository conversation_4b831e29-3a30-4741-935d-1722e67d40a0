import React, { useState, useRef, useEffect } from "react";
//import "bootstrap/dist/css/bootstrap.min.css";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Form, InputGroup } from "react-bootstrap";
import { BsCalendar } from "react-icons/bs";
import { getRequest, postRequest, postRequestWithOnlyResponseDecrypted } from "../../services/apiService";
import { API_URLS } from "../../constants/apiConstants";
import { showErrorAlert, showSuccessAlert2 } from "../../services/alertService";

const ApplyLeave = ({ closeApplyModal }) => {
  const [leaveTypes, setLeaveTypes] = useState([]);
  const today = new Date(); 
  const twoMonthsBefore = new Date(
    today.getFullYear(),
    today.getMonth() - 2,
    today.getDate()
  );
  const twoMonthsAfter = new Date(
    today.getFullYear(),
    today.getMonth() + 2,
    today.getDate()
  );

  useEffect(() => {
    const fetchLeaveTypes = async () => {
      try {
        const data = await getRequest(API_URLS.FETCH_LEAVE_TYPES);
        if (data) setLeaveTypes(data);
      } catch (error) {
        setLeaveTypes(data);
      }
    };

    fetchLeaveTypes();
  }, []);

  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [leaveType, setLeaveType] = useState(""); // set empty string by default

  const [reason, setReason] = useState("");
  const [errors, setErrors] = useState({});
  const [file, setFile] = useState(null); 
  const startdateInputRef = useRef(null);
  const enddateInputRef = useRef(null);

  const handleDateIconClick = () => {
    startdateInputRef.current.setFocus();
  };

  const handleEndDateIconClick = () => {
    enddateInputRef.current.setFocus();
  };

  const validateForm = () => {
    const newErrors = {};

    if (!leaveType) {
      newErrors.leaveType = "Leave Type is required";
    }

    if (!startDate) {
      newErrors.startDate = "Start Date is required";
    } else if (isNaN(startDate.getTime())) {
      newErrors.startDate = "Invalid date format";
    }

    if (!endDate) {
      newErrors.endDate = "End Date is required";
    } else if (isNaN(endDate.getTime())) {
      newErrors.endDate = "Invalid date format";
    }

    if (!reason.trim()) {
      newErrors.reason = "Reason is required";
    }

    if (startDate && endDate && startDate > endDate) {
      newErrors.endDate = "End Date cannot be earlier than Start Date";
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0;
  };

  const handleLeaveTypeChange = (e) => {
    setLeaveType(e.target.value);
    if (errors.leaveType) {
      setErrors((prevErrors) => ({ ...prevErrors, leaveType: "" }));
    }
  };

  const handleStartDateChange = (date) => {
    if (date && isValidDate(date)) {
      setStartDate(date);
      if (errors.startDate) {
        setErrors((prevErrors) => ({ ...prevErrors, startDate: "" }));
      }
      if (endDate && date > endDate) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          endDate: "End Date cannot be earlier than Start Date",
        }));
      } else {
        setErrors((prevErrors) => ({ ...prevErrors, endDate: "" }));
      }
    // } else {
    //   setErrors((prevErrors) => ({
    //     ...prevErrors,
    //     startDate: "Invalid or out-of-range Start Date",
    //   }));
    }
  };
  
  const handleEndDateChange = (date) => {
    if (date && isValidDate(date)) {
      setEndDate(date);
      if (errors.endDate) {
        setErrors((prevErrors) => ({ ...prevErrors, endDate: "" }));
      }
      if (startDate && date < startDate) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          endDate: "End Date cannot be earlier than Start Date",
        }));
      }
    // } else {
    //   setErrors((prevErrors) => ({
    //     ...prevErrors,
    //     endDate: "Invalid or out-of-range End Date",
    ;
    }
  };

  const handleReasonChange = (e) => {
    setReason(e.target.value);
    if (errors.reason) {
      setErrors((prevErrors) => ({ ...prevErrors, reason: "" }));
    }
  };

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
  };

  const formatDate = (date) => {
    if (!date) return null;
    const year = date.getFullYear();
    const month = ("0" + (date.getMonth() + 1)).slice(-2); 
    const day = ("0" + date.getDate()).slice(-2); 
    return `${year}-${month}-${day}`;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return; // Validation failed, don't submit
    }
  
    const selectedLeaveType = leaveTypes.find((type) => type.type === leaveType);
    
   
    const daysAppliedBefore = selectedLeaveType?.daysAppliedBefore;
  
    
    const today = new Date();
    const formattedToday = today.setHours(0, 0, 0, 0); 
    const selectedStartDate = new Date(startDate).setHours(0, 0, 0, 0);  
  

    if (daysAppliedBefore !== null) {
      const minApplicationDate = new Date(today); 
      minApplicationDate.setDate(minApplicationDate.getDate() + daysAppliedBefore); 
  
    
      if (selectedStartDate < minApplicationDate) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          startDate: `Leave must be applied at least ${daysAppliedBefore} days before the start date.`,
        }));
        return;
      }
    }
  
    if (daysAppliedBefore === null && selectedStartDate === formattedToday) {
 
    }
  
    try {
      const formData = new FormData();
  
      if (file) {
        formData.append("file", file);
      }
  
      const ticketData = {
        leaveType,
        startDate: formatDate(startDate),
        endDate: formatDate(endDate),
        reason,
      };
  
      const ticketBlob = JSON.stringify(ticketData);
      formData.append("leaveApplicationDTO", ticketBlob);
  
      const response = await postRequestWithOnlyResponseDecrypted(API_URLS.APPLY_LEAVE, formData);
      if (response) {
        showSuccessAlert2(response);
      }
      closeApplyModal();
    } catch (error) {
    } finally {
      // closeApplyModal();
    }
  };

  const isValidDate = (date) => {
    const currentDate = new Date();
    const maxDate = new Date(currentDate.getFullYear() + 100, 11, 31); 
    return date instanceof Date && !isNaN(date) && date <= maxDate;
  };
  

  const selectedLeaveType = leaveTypes.find((type) => type.type === leaveType);
  const canUploadFile = selectedLeaveType ? selectedLeaveType.allowFileUpload : false;

  return (
    <div className="container mt-4" onClick={(e) => e.stopPropagation()}>
      <div className="row justify-content-center">
        <div className="col-12 col-md-6 col-lg-4">
          <div className="card shadow-sm border-0" style={{ borderRadius: "8px" }}>
            <div className="card-body p-4 cldr-modal-body" style={{ maxHeight: '500px', overflowY: 'auto' , overflowX: 'hidden'}}>
              <h5 className="card-title text-center fw-bold mb-4" style={{ fontSize: "18px" }}>
                Apply Leave
              </h5>
              <label htmlFor="ticketSubject" className="ticket-text-primary">
                <span className="text-danger">The fields with * marks are mandatory</span>
              </label>

              <span onClick={closeApplyModal} style={{ position: 'absolute', top: -1, color: 'black', right: 5, cursor: 'pointer' }}>
                X
              </span>

              <div className="mb-3">
                <label htmlFor="ticketSubject" className="ticket-text-primary">
                  Leave Type<span className="text-danger">*</span>
                </label>
                <select
                  className={`form-select ${errors.leaveType ? "is-invalid" : ""}`}
                  style={{
                    fontSize: "16px",
                    backgroundColor: "#E6ECF7",
                    borderColor: "#E6ECF7",
                    color: "#000000",
                    width: "100%",
                  }}
                  value={leaveType}
                  onChange={handleLeaveTypeChange}
                >
                  <option value="">
                    <label htmlFor="ticketSubject" className="ticket-text-primary">
                      Select Leave Type<span className="text-danger"></span>
                    </label>
                  </option>
                  {leaveTypes.map((type, index) => (
                    <option key={index} value={type.type}>
                      {type.type}
                    </option>
                  ))}
                </select>
                {errors.leaveType && (
                  <div className="invalid-feedback">{errors.leaveType}</div>
                )}
              </div>

              <Form.Group className="mb-3" controlId="formStartDate">
                <Form.Label className="fw-bold" style={{ fontSize: "14px" }}>
                  <label htmlFor="ticketSubject" className="ticket-text-primary">
                    Start Date<span className="text-danger">*</span>
                  </label>
                </Form.Label>
                <InputGroup>
                  <DatePicker
                    selected={startDate}
                    onChange={(date) => handleStartDateChange(date)}
                    onChangeRaw={(e) => {
                      const typeDate = e.target.value;
                      const regex = /^\d{0,4}(-\d{0,2})?(-\d{0,2})?$/;
                      if (regex.test(typeDate)) {
                        handleStartDateChange(new Date(typeDate));
                      } else {
                        e.preventDefault();
                      }
                    }}
                    className={`form-control ${
                      errors.startDate ? "is-invalid" : ""
                    }`}
                    dateFormat="dd-MM-yyyy"
                    minDate={
                      leaveType === "Sick leave" ? twoMonthsBefore : today
                    }
                    maxDate={leaveType === "Sick leave" ? twoMonthsAfter : null}
                    ref={startdateInputRef}
                    showPopperArrow
                  />
                  <InputGroup.Text
                    onClick={handleDateIconClick}
                    style={{ cursor: "pointer" }}
                  >
                    <BsCalendar />
                  </InputGroup.Text>
                  {errors.startDate && (
                    <div className="invalid-feedback d-block">{errors.startDate}</div>
                  )}
                </InputGroup>
              </Form.Group>

              <Form.Group className="mb-3" controlId="formEndDate">
                <Form.Label className="fw-bold" style={{ fontSize: "14px" }}>
                  <label htmlFor="ticketSubject" className="ticket-text-primary">
                    End Date<span className="text-danger">*</span>
                  </label>
                </Form.Label>
                <InputGroup>
                <DatePicker
                    selected={endDate}
                    onChange={(date) => handleEndDateChange(date)}
                    onChangeRaw={(e) => {
                      const typeDate = e.target.value;
                      const regex = /^\d{0,4}(-\d{0,2})?(-\d{0,2})?$/;
                      if (regex.test(typeDate)) {
                        handleEndDateChange(new Date(typeDate));
                      } else {
                        e.preventDefault();
                      }
                    }}
                    className={`form-control ${
                      errors.endDate ? "is-invalid" : ""
                    }`}
                    minDate={startDate}
                    maxDate={leaveType === "Sick leave" ? twoMonthsAfter : null}
                    dateFormat="dd-MM-yyyy"
                    ref={enddateInputRef}
                    showPopperArrow
                  />
                  <InputGroup.Text
                    onClick={handleEndDateIconClick}
                    style={{ cursor: "pointer" }}
                  >
                    <BsCalendar />
                  </InputGroup.Text>
                  {errors.endDate && (
                    <div className="invalid-feedback d-block">{errors.endDate}</div>
                  )}
                </InputGroup>
              </Form.Group>

              {canUploadFile && leaveType === "Sick leave" && (
                <div className="mb-3">
                  <label htmlFor="file" className="ticket-text-primary">
                    Upload File 
                  </label>
                  <input
                    type="file"
                    className="form-control"
                    onChange={handleFileChange}
                  />
                </div>
              )}

              <div className="mb-3">
                <label htmlFor="reason" className="ticket-text-primary">
                  Reason<span className="text-danger">*</span>
                </label>
                <textarea
                  id="reason"
                  className={`form-control ${errors.reason ? "is-invalid" : ""}`}
                  value={reason}
                  onChange={handleReasonChange}
                  rows={3}
                />
                {errors.reason && <div className="invalid-feedback">{errors.reason}</div>}
              </div>

              <button
                onClick={handleSubmit}
                className="btn btn-primary w-100"
                style={{
                  backgroundColor: "#2B2E4A",
                  fontSize: "16px",
                  padding: "10px",
                }}
              >
                Submit
              </button>

              <button
                onClick={closeApplyModal}
                className="btn btn-primary w-100"
                style={{
                  marginTop: 5,
                  backgroundColor: "white",
                  color: "#2B2E4A",
                  fontSize: "16px",
                  padding: "10px",
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplyLeave;
