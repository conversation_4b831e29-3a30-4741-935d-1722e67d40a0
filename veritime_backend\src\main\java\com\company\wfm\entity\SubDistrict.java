package com.company.wfm.entity;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Entity
@Data
@Getter
@Setter
@Table(name = "sub_districts")
public class SubDistrict {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;

    @ManyToOne
    @JoinColumn(name = "district_id")
    private District district;

   /* @OneToMany(mappedBy = "subDistrictEntity", cascade = CascadeType.ALL)
    private List<FeederHospital> hospitals = new ArrayList<>();*/


    @OneToMany(mappedBy = "subDistrict", cascade = CascadeType.ALL)
    @JsonIgnore
    private List<Hospital> hospitals;


    @Override
    public String toString() {
        return "SubDistrict{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}