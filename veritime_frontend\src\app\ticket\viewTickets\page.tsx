"use client";
import Layout from "../../../components/Layout";
import React, { useEffect, useState } from "react";
import "./viewticket.css";
import { getRequest, getRequestWithSecurity, postRequest ,postRequestWithSecurity } from "@/services/apiService";
import { API_URLS } from "@/constants/apiConstants";
import Table from "../../../modules/Ticket/ViewTicket/Table";
import { Container, Row, Col, Button, Form } from "react-bootstrap";
import Select from "react-select";
import Link from "next/link";
import { wrap } from "module";
import { BsTextWrap } from "react-icons/bs";
import { colors } from "@constants/colors";

interface Employee {
  value: string; // empId
  label: string; // empName
}

const ViewTicket = () => {
  const [selectedEmployeeID, setSelectedEmployeeID] = useState(null);
  const [isMounted, setIsMounted] = useState(false);
  const [status, setStatus] = useState<string | null>("");
  const [assignedTo, setAssignedTo] = useState<string | null>(null);

  const [createdBy, setCreatedBy] = useState<string | null>(null);
  const [myDepartmentTicket, setMyDepartmentTicket] = useState<any>(null);
  const [tickets, setTickets] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<any[]>([]);
const [departmentId, setDepartmentId] = useState<string | null>(null);
  const [triggerFetch, setTriggerFetch] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [loggedInUser, setLoggedInUser] = useState<any>(null);

  const fetchTickets = async () => {
    setLoading(true);
    try {
      const request = {
        offset: 0,
        limit: 10,
        departmentId: departmentId,
        status: status,
        createdBy: createdBy || null,
        myDepartmentTicket: "",

        query: searchTerm, // Add search term to the request
      };
      if (myDepartmentTicket) {
        // request.assignedTo =
        //   myDepartmentTicket === "AssignedToMe"
        //     ? selectedEmployeeID
        //     : request.assignedTo;
        request.createdBy =
          myDepartmentTicket === "CreatedByMe"
            ? selectedEmployeeID
            : request.createdBy;
      }
      const response = await postRequestWithSecurity(
        API_URLS.FILTER_TICKETS,
        request,
        true
      );
      setTickets(response.content);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const departmentsData = await getRequest(API_URLS.DEPT_LOOKUP);
      if (departmentsData) setDepartments(departmentsData);
    } catch (error) {
    }
  };

  const fetchLoggedInUserData = async () => {
    try {
      const userData = await getRequestWithSecurity(API_URLS.PROFILE_DETAILS);
      if (userData) {
        setLoggedInUser(userData);
        //setDepartmentId(userData.departmentId || null);   //04.04.25
        setCreatedBy(userData.empId || null);
      }
    } catch (error) {
    }
  };

  const fetchEmployees = async () => {
    try {
      const request = {
        branchId: "",
        offset: 0,
        limit: 100,
      };
      const response = await postRequest(API_URLS.ASSIGNED_TO, request, true);
      const employeesData = response.content.map((emp: any) => ({
        value: emp.empId,
        label: emp.empName,
      }));
      setEmployees(employeesData);
    } catch (error) {
    }
  };

  const handleAssignedToChange = (selected: any) => {
    setAssignedTo(selected?.value || null);
  };

  const handleCreatedByChange = (selected: any) => {
    setCreatedBy(selected?.value || null);
  };

  const handleEyeClick = (employeeID: any) => {
    setSelectedEmployeeID(employeeID);
  };

  const resetFilters = () => {
    setSelectedDepartment([]);
    setStatus(null);
    setDepartmentId(null);
    setAssignedTo(null);
    setCreatedBy(null);
    setMyDepartmentTicket(null);
    setSelectedEmployeeID(null);
    setSearchTerm("");
  };

  const handleGoClick = () => {
    if (selectedDepartment.length > 0) {
      const selectedIds = selectedDepartment.map((dept: any) => dept.value);
      setDepartmentId(selectedIds.join(",")); 
    } else {
      setDepartmentId(departmentId); 
    }
  
    setTriggerFetch((prev) => !prev); 
  };
  
  

  const handleDepartmentChange = (selected: any) => {
    setSelectedDepartment(selected ? [selected] : []);
    setDepartmentId(selected?.value || null); 
  };
  

  useEffect(() => {
    setIsMounted(true);
    fetchLoggedInUserData();
    fetchDepartments();
    fetchEmployees();
  }, []);

  useEffect(() => {
    if (triggerFetch) {
      fetchTickets();
      setTriggerFetch(false);
    }
  }, [
    triggerFetch,
    departmentId,
    status,
    assignedTo,
    createdBy,
    myDepartmentTicket,
    searchTerm,
  ]);

  if (!isMounted) return null;

  const departmentOptions = departments.map((department: any) => ({
    value: department.departmentId,
    label: department.departmentName,
  }));

  const statusOptions = [
    { value: "open", label: "Open" },
    { value: "close", label: "Closed" },
    { value: "hold", label: "Hold" },
    { value: "reopen", label: "Reopened" },
  ];

  const myDepartmentTicketOptions = [
    { value: "AssignedToMe", label: "Assigned To Me" },
    { value: "CreatedByMe", label: "Created By Me" },
  ];

  const customSelectStyles = {
    control: (provided: any) => ({
      ...provided,
      minHeight: "30px",
    }),
    placeholder: (provided: any) => ({
      ...provided,
      whiteSpace: "normal",
      textAlign: "left",
      display: "flex",
      flexDirection: "column",
    }),
    option: (provided: any) => ({
      ...provided,
      padding: "10px",
      fontSize: "12px",
      textAlign: "left",
    }),
    singleValue: (provided: any) => ({
      ...provided,
      textAlign: "left",
    }),
  };

  return (
    <Layout>
      <div className="container-fluid bg-custom p-4 pt-5">
        <div className="d-flex justify-content-between align-items-center">
          <h1 className="h3">Ticket</h1>
          <Link href="/ticket" prefetch={false}>
            <button
              className="d-flex flex-row p-2 round-3 "
              style={{
                backgroundColor: colors.purple,
                height: "40px",
                width: "130px",
                border: "0px",
                marginBottom: "0px",
                marginLeft: "-25%",
              }}
            >
              Create Tickets
            </button>
          </Link>
        </div>
        <Container
          className="dropdown-tickets-container "
          fluid
          style={{
            marginTop: "10px",
            marginBottom: "1px",
            // marginLeft: "2%",
          }}
        >
          <Row className="align-items-center">
            <Col style={{ maxWidth: "300px" }}>
              <Form.Control
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search "
                className="cusm-form-control-searchterm-view"
                style={{
                  // minHeight: "45px",
                  // height: "38px",
                  textAlign: "left",
                  paddingLeft: "10px",
                  // marginBottom: "9px",
                  // fontSize: "14px",
                  // ...customSelectStyles.control({}),
                }}
              />
            </Col>
            <Col>
              <Select
                id="department"
                options={departmentOptions}
                value={
                  departmentId
                    ? departmentOptions.find(
                        (dept) => dept.value === departmentId
                      )
                    : null
                }
                onChange={handleDepartmentChange}
                placeholder="Department"
                isClearable
                classNamePrefix="react-select"
                styles={customSelectStyles}
              />
            </Col>
            <Col>
              <Select
                options={statusOptions}
                value={
                  status
                    ? statusOptions.find((option) => option.value === status)
                    : null
                }
                onChange={
                  (selectedOption) => setStatus(selectedOption?.value || null) 
                }
                placeholder="Status"
                isClearable
                classNamePrefix="react-select"
                styles={customSelectStyles}
              />
            </Col>
            <Col>
            <Select
  options={employees}
  onChange={handleAssignedToChange}
  value={
    assignedTo
      ? {
          value: assignedTo,
          label: employees.find((emp) => emp.value === assignedTo)?.label,
        }
      : null
  }
  placeholder="Assigned To"
  isClearable
  classNamePrefix="react-select"
  styles={customSelectStyles}
/>

            </Col>
            <Col>
              <Select
                options={employees}
                value={
                  createdBy
                    ? {
                        value: createdBy,
                        label: employees.find((emp) => emp.value === createdBy)
                          ?.label,
                      }
                    : null
                }
                onChange={handleCreatedByChange}
                placeholder=" Created By"
                isClearable
                classNamePrefix="react-select"
                styles={customSelectStyles}
              />
            </Col>{" "}
            {/* <Col>
              <Select
                options={myDepartmentTicketOptions}
                value={myDepartmentTicketOptions.find(
                  (option) => option.value === myDepartmentTicket
                )}
                onChange={(selected) => setMyDepartmentTicket(selected?.value)}
                placeholder="Dept. Tickets"
                isClearable
                classNamePrefix="react-select"
                styles={customSelectStyles}
              />
            </Col> */}
            {/* Go and Reset Buttons */}
            <Col xs="auto" className="buttons-container">
              <Row>
                <Col className="unique-go-button">
                  <Button variant="primary" onClick={handleGoClick}>
                    Apply
                  </Button>
                </Col>
                <Col className="unique-reset-button">
                  <Button variant="secondary" onClick={resetFilters}>
                    Reset
                  </Button>
                </Col>
              </Row>
            </Col>
          </Row>
        </Container>

        <Container pt-1 fluid style={{ width: "100%" }}>
          <Row>
            <Col>
              {loading ? (
                <p>Loading tickets...</p>
              ) : (
                <div className="container-fluid bg-custom p-3 pt-4">
                  <div className="container-fluid bg-white p-3">
                    <Table
                      onEyeClick={handleEyeClick}
                      departmentId={departmentId}
                      status={status}
                      assignedTo={assignedTo}
                      createdBy={createdBy}
                      myDepartmentTicket={myDepartmentTicket}
                      query={searchTerm}
                      currentUser={loggedInUser}
                    />
                  </div>
                </div>
              )}
            </Col>
          </Row>
        </Container>
      </div>
    </Layout>
  );
};

export default ViewTicket;
